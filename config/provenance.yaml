# Person Suit Provenance Configuration
# ====================================

provenance:
  # Backend selection: memory | redpanda | native
  # - memory: In-memory only (default, no persistence)
  # - redpanda: Kafka/Redpanda streaming (requires Docker)
  # - native: WaveTrace file-based (zero dependencies)
  sink: native
  
  # Native Backend (WaveTrace) Configuration
  native:
    # Directory for segment files
    data_dir: "./data/provenance"
    
    # Segment rotation settings
    segment_size_mb: 10        # Rotate after this size
    segment_duration_seconds: 300  # Or after 5 minutes
    max_segments: 1000         # Keep last N segments
    
    # Compression: NONE | ZSTD | LZ4
    compression: ZSTD
    compression_level: 3
    
    # Writer settings
    queue_size: 10000          # Max in-memory buffer
    flush_size: 100            # Batch size for writes
    flush_time_ms: 5000        # Max time between flushes
    
    # ACF settings
    wave_threshold: 600000     # Load threshold for wave mode (0.6)
    
  # Redpanda Backend Configuration
  redpanda:
    brokers: "localhost:9092"
    topic: "person_suit.provenance"
    
  # Health check settings
  health_check:
    enabled: true
    interval_seconds: 30
    failure_threshold: 3       # Mark unhealthy after N failures
    
  # Retention policy
  retention:
    max_age_days: 30          # Delete segments older than this
    max_size_gb: 100          # Total size limit
    
  # Query settings
  query:
    max_results: 10000        # Max records per query
    timeout_seconds: 30       # Query timeout
    
  # Monitoring
  monitoring:
    emit_metrics: true        # Publish to monitoring system
    log_statistics: true      # Log stats periodically
    statistics_interval: 60   # Seconds between stats logs 