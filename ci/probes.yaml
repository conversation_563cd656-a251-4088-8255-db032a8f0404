name: counter-evidence-probes

on:
  push:
    branches: [ main ]
  pull_request:
    branches: [ main ]

jobs:
  probes:
    runs-on: ubuntu-latest
    continue-on-error: true
    steps:
      - uses: actions/checkout@v4
      - name: Set up Python
        uses: actions/setup-python@v5
        with:
          python-version: "3.11"
      - name: Install dependencies (core only)
        run: |
          python -m pip install --upgrade pip
          pip install -r requirements/core.txt
      - name: Run counter-evidence probes (soft-fail)
        run: |
          python scripts/counter_evidence/trace_continuity.py || true
          python scripts/counter_evidence/actor_metrics.py || true
          python scripts/counter_evidence/provenance_durability.py || true
          python scripts/counter_evidence/latency_baseline.py || true 