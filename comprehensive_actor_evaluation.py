#!/usr/bin/env python3
"""
Comprehensive Actor System & Choreography Evaluation
====================================================

This script conducts a thorough evaluation of the Person Suit actor system
and adaptive choreography implementation, assessing CAW paradigm compliance.
"""

import asyncio
import logging
import sys
import time
from pathlib import Path
from typing import Dict, List, Any, Optional

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class ActorSystemEvaluator:
    """Comprehensive evaluator for the Person Suit actor system."""
    
    def __init__(self):
        self.evaluation_results = {}
        self.actor_inventory = {}
        self.choreography_status = {}
        self.caw_compliance_issues = []
        
    async def run_comprehensive_evaluation(self) -> Dict[str, Any]:
        """Run the complete evaluation suite."""
        logger.info("🔍 Starting Comprehensive Actor System & Choreography Evaluation")
        logger.info("=" * 80)
        
        # Phase 1: Actor System Analysis
        await self._evaluate_actor_system()
        
        # Phase 2: CAW Paradigm Compliance
        await self._evaluate_caw_compliance()
        
        # Phase 3: Choreography System Evaluation
        await self._evaluate_choreography_system()
        
        # Phase 4: Generate Final Report
        return self._generate_final_report()
    
    async def _evaluate_actor_system(self):
        """Phase 1: Actor System Analysis"""
        logger.info("\n📊 PHASE 1: ACTOR SYSTEM ANALYSIS")
        logger.info("-" * 50)
        
        # 1.1 Actor Inventory & Status
        await self._inventory_actors()
        
        # 1.2 Operational Health Check
        await self._check_operational_health()
        
        # 1.3 Lifecycle Management Assessment
        await self._assess_lifecycle_management()
    
    async def _inventory_actors(self):
        """1.1 Count and categorize all active actors."""
        logger.info("🔍 1.1 Actor Inventory & Status")
        
        try:
            # Test if we can import the actor system
            from person_suit.core.actors.actor_system import ActorSystem
            from person_suit.core.actors.foundation_actors import (
                FoundationSupervisorActor, HealthMonitorActor, MetricsCollectorActor,
                RuntimeVerificationActor, EnergyManagementActor, DashboardActor
            )
            
            # Foundation actors expected
            foundation_actors = {
                "FoundationSupervisorActor": FoundationSupervisorActor,
                "HealthMonitorActor": HealthMonitorActor,
                "MetricsCollectorActor": MetricsCollectorActor,
                "RuntimeVerificationActor": RuntimeVerificationActor,
                "EnergyManagementActor": EnergyManagementActor,
                "DashboardActor": DashboardActor,
            }
            
            self.actor_inventory = {
                "foundation_actors": foundation_actors,
                "foundation_count": len(foundation_actors),
                "meta_system_actors": {},  # To be populated
                "total_actor_classes": len(foundation_actors),
                "import_status": "SUCCESS"
            }
            
            logger.info(f"  ✅ Foundation Actors: {len(foundation_actors)} classes found")
            for name in foundation_actors.keys():
                logger.info(f"    - {name}")
                
        except Exception as e:
            logger.error(f"  ❌ Actor import failed: {e}")
            self.actor_inventory = {"import_status": "FAILED", "error": str(e)}
    
    async def _check_operational_health(self):
        """1.2 Verify which actors are successfully initialized and running."""
        logger.info("\n🔍 1.2 Operational Health Check")
        
        try:
            # Try to create a minimal actor system instance
            from person_suit.core.actors.actor_system import ActorSystem
            
            # Test actor system creation
            actor_system = ActorSystem()
            logger.info("  ✅ ActorSystem instantiation: SUCCESS")
            
            # Test if we can access the actor registry
            if hasattr(actor_system, '_actors'):
                logger.info(f"  ✅ Actor registry accessible: {len(actor_system._actors)} actors")
            else:
                logger.warning("  ⚠️ Actor registry not accessible")
            
            self.evaluation_results["actor_system_health"] = {
                "instantiation": "SUCCESS",
                "registry_accessible": hasattr(actor_system, '_actors'),
                "current_actor_count": len(getattr(actor_system, '_actors', {}))
            }
            
        except Exception as e:
            logger.error(f"  ❌ Actor system health check failed: {e}")
            self.evaluation_results["actor_system_health"] = {
                "instantiation": "FAILED",
                "error": str(e)
            }
    
    async def _assess_lifecycle_management(self):
        """1.3 Assess supervision strategies and lifecycle management."""
        logger.info("\n🔍 1.3 Actor Lifecycle Management Assessment")
        
        try:
            # Check supervision strategy implementation
            from person_suit.core.actors.supervision import SupervisionStrategyImplementation
            from person_suit.core.actors.actor_system_supervision import ActorSystemSupervision
            
            logger.info("  ✅ Supervision system imports: SUCCESS")
            
            # Check if supervision strategies are properly defined
            supervision_features = {
                "supervision_strategy_base": SupervisionStrategyImplementation is not None,
                "actor_system_supervision": ActorSystemSupervision is not None,
            }
            
            self.evaluation_results["lifecycle_management"] = {
                "supervision_imports": "SUCCESS",
                "features": supervision_features
            }
            
            for feature, status in supervision_features.items():
                status_icon = "✅" if status else "❌"
                logger.info(f"    {status_icon} {feature}: {status}")
                
        except Exception as e:
            logger.error(f"  ❌ Lifecycle management assessment failed: {e}")
            self.evaluation_results["lifecycle_management"] = {
                "supervision_imports": "FAILED",
                "error": str(e)
            }
    
    async def _evaluate_caw_compliance(self):
        """Phase 2: CAW Paradigm Compliance Assessment"""
        logger.info("\n🌊 PHASE 2: CAW PARADIGM COMPLIANCE")
        logger.info("-" * 50)
        
        # 2.1 Context Flow Assessment
        await self._assess_context_flow()
        
        # 2.2 Capability-Based Security
        await self._assess_capability_security()
        
        # 2.3 Wave-Particle Duality
        await self._assess_wave_particle_duality()
        
        # 2.4 Message-Based Communication
        await self._assess_message_based_communication()
    
    async def _assess_context_flow(self):
        """2.1 Verify UnifiedContext propagation through StandardActorMessage."""
        logger.info("🔍 2.1 Context Flow Assessment")
        
        try:
            from person_suit.shared.context.unified import UnifiedContext
            from person_suit.core.actors.base import StandardActorMessage
            
            # Test context creation
            context = UnifiedContext.create_default("test_evaluation")
            logger.info("  ✅ UnifiedContext creation: SUCCESS")
            
            # Check if StandardActorMessage supports context
            message_fields = StandardActorMessage.__annotations__ if hasattr(StandardActorMessage, '__annotations__') else {}
            has_context_field = 'context' in message_fields
            
            logger.info(f"  {'✅' if has_context_field else '❌'} StandardActorMessage context support: {has_context_field}")
            
            self.evaluation_results["context_flow"] = {
                "unified_context_creation": "SUCCESS",
                "message_context_support": has_context_field,
                "context_properties": len(context.properties) if hasattr(context, 'properties') else 0
            }
            
        except Exception as e:
            logger.error(f"  ❌ Context flow assessment failed: {e}")
            self.evaluation_results["context_flow"] = {"status": "FAILED", "error": str(e)}
            self.caw_compliance_issues.append(f"Context Flow: {e}")
    
    async def _assess_capability_security(self):
        """2.2 Check capability validation and CBS patterns."""
        logger.info("\n🔍 2.2 Capability-Based Security Assessment")
        
        try:
            from person_suit.core.security.capabilities import CapabilityToken, verify_capability, Permission
            
            logger.info("  ✅ Capability system imports: SUCCESS")
            
            # Test capability verification
            test_result = verify_capability(None, Permission.READ, None)
            logger.info(f"  ✅ Capability verification callable: {test_result is not None}")
            
            self.evaluation_results["capability_security"] = {
                "imports": "SUCCESS",
                "verification_callable": True,
                "permission_enum_available": hasattr(Permission, 'READ')
            }
            
        except Exception as e:
            logger.error(f"  ❌ Capability security assessment failed: {e}")
            self.evaluation_results["capability_security"] = {"status": "FAILED", "error": str(e)}
            self.caw_compliance_issues.append(f"Capability Security: {e}")
    
    async def _assess_wave_particle_duality(self):
        """2.3 Assess wave-particle duality processing."""
        logger.info("\n🔍 2.3 Wave-Particle Duality Assessment")
        
        try:
            from person_suit.core.infrastructure.dual_wave.wave_function import DualWaveFunction
            
            # Test wave function creation
            wave_func = DualWaveFunction(amplitude=1.0, phase=0.0)
            logger.info("  ✅ DualWaveFunction creation: SUCCESS")
            
            # Check wave-particle ratio functionality
            has_ratio = hasattr(wave_func, 'wave_particle_ratio')
            logger.info(f"  {'✅' if has_ratio else '❌'} Wave-particle ratio support: {has_ratio}")
            
            self.evaluation_results["wave_particle_duality"] = {
                "dual_wave_function": "SUCCESS",
                "wave_particle_ratio": has_ratio,
                "amplitude": getattr(wave_func, 'amplitude', None),
                "phase": getattr(wave_func, 'phase', None)
            }
            
        except Exception as e:
            logger.error(f"  ❌ Wave-particle duality assessment failed: {e}")
            self.evaluation_results["wave_particle_duality"] = {"status": "FAILED", "error": str(e)}
            self.caw_compliance_issues.append(f"Wave-Particle Duality: {e}")
    
    async def _assess_message_based_communication(self):
        """2.4 Confirm actors use HybridMessageBus exclusively."""
        logger.info("\n🔍 2.4 Message-Based Communication Assessment")
        
        try:
            from person_suit.core.infrastructure.hybrid_message_bus import get_message_bus, HybridMessageBus
            
            # Test message bus access
            bus = get_message_bus()
            logger.info("  ✅ HybridMessageBus access: SUCCESS")
            
            # Check if it's a singleton
            bus2 = get_message_bus()
            is_singleton = bus is bus2
            logger.info(f"  {'✅' if is_singleton else '❌'} Singleton pattern: {is_singleton}")
            
            self.evaluation_results["message_based_communication"] = {
                "bus_access": "SUCCESS",
                "singleton_pattern": is_singleton,
                "bus_type": type(bus).__name__
            }
            
        except Exception as e:
            logger.error(f"  ❌ Message-based communication assessment failed: {e}")
            self.evaluation_results["message_based_communication"] = {"status": "FAILED", "error": str(e)}
            self.caw_compliance_issues.append(f"Message-Based Communication: {e}")
    
    async def _evaluate_choreography_system(self):
        """Phase 3: Choreography System Evaluation"""
        logger.info("\n💃 PHASE 3: CHOREOGRAPHY SYSTEM EVALUATION")
        logger.info("-" * 50)
        
        # 3.1 Adaptive Capabilities
        await self._assess_adaptive_capabilities()
        
        # 3.2 Evolution Mechanisms
        await self._assess_evolution_mechanisms()
        
        # 3.3 Engine Status
        await self._assess_choreography_engine()
        
        # 3.4 Formal Verification
        await self._assess_formal_verification()
    
    async def _assess_adaptive_capabilities(self):
        """3.1 Analyze adaptive choreography capabilities."""
        logger.info("🔍 3.1 Adaptive Capabilities Assessment")
        
        try:
            from person_suit.core.actors.choreography.engine import ChoreographyEngine
            from person_suit.core.actors.choreography.examples import ADAPTIVE_DATA_REQUEST_CHOREO
            
            logger.info("  ✅ Choreography engine imports: SUCCESS")
            
            # Check if adaptive choreography examples exist
            has_adaptive_example = ADAPTIVE_DATA_REQUEST_CHOREO is not None
            logger.info(f"  {'✅' if has_adaptive_example else '❌'} Adaptive choreography examples: {has_adaptive_example}")
            
            self.choreography_status["adaptive_capabilities"] = {
                "engine_import": "SUCCESS",
                "adaptive_examples": has_adaptive_example,
                "example_id": getattr(ADAPTIVE_DATA_REQUEST_CHOREO, 'choreography_id', None)
            }
            
        except Exception as e:
            logger.error(f"  ❌ Adaptive capabilities assessment failed: {e}")
            self.choreography_status["adaptive_capabilities"] = {"status": "FAILED", "error": str(e)}
    
    async def _assess_evolution_mechanisms(self):
        """3.2 Determine choreography evolution and learning support."""
        logger.info("\n🔍 3.2 Evolution Mechanisms Assessment")
        
        try:
            # Check for choreography modification capabilities
            from person_suit.core.actors.choreography.engine import ChoreographyEngine
            
            engine = ChoreographyEngine()
            
            # Check available methods for evolution
            evolution_methods = {
                "register_choreography": hasattr(engine, 'register_choreography'),
                "start_choreography": hasattr(engine, 'start_choreography'),
                "stop_choreography": hasattr(engine, 'stop_choreography'),
                "modify_choreography": hasattr(engine, 'modify_choreography'),  # Likely not implemented
                "learn_from_execution": hasattr(engine, 'learn_from_execution'),  # Likely not implemented
            }
            
            self.choreography_status["evolution_mechanisms"] = evolution_methods
            
            for method, available in evolution_methods.items():
                status_icon = "✅" if available else "❌"
                logger.info(f"    {status_icon} {method}: {available}")
                
        except Exception as e:
            logger.error(f"  ❌ Evolution mechanisms assessment failed: {e}")
            self.choreography_status["evolution_mechanisms"] = {"status": "FAILED", "error": str(e)}
    
    async def _assess_choreography_engine(self):
        """3.3 Verify ChoreographyEngine integration."""
        logger.info("\n🔍 3.3 Choreography Engine Status")
        
        try:
            from person_suit.core.actors.choreography.engine import ChoreographyEngine
            
            # Test engine instantiation
            engine = ChoreographyEngine()
            logger.info("  ✅ ChoreographyEngine instantiation: SUCCESS")
            
            # Check integration points
            integration_status = {
                "actor_system_integration": hasattr(engine, '_actor_system'),
                "message_bus_integration": hasattr(engine, '_message_bus'),
                "running_instances": hasattr(engine, '_running_instances'),
                "definitions": hasattr(engine, '_definitions')
            }
            
            self.choreography_status["engine_status"] = {
                "instantiation": "SUCCESS",
                "integration": integration_status
            }
            
            for integration, status in integration_status.items():
                status_icon = "✅" if status else "❌"
                logger.info(f"    {status_icon} {integration}: {status}")
                
        except Exception as e:
            logger.error(f"  ❌ Choreography engine assessment failed: {e}")
            self.choreography_status["engine_status"] = {"status": "FAILED", "error": str(e)}
    
    async def _assess_formal_verification(self):
        """3.4 Check formal verification capabilities."""
        logger.info("\n🔍 3.4 Formal Verification Assessment")
        
        try:
            from person_suit.core.formal_methods.verification import verify_choreography_property
            
            logger.info("  ✅ Formal verification imports: SUCCESS")
            
            # Check if verification is implemented or just placeholder
            import inspect
            source = inspect.getsource(verify_choreography_property)
            is_placeholder = "TODO" in source or "placeholder" in source.lower()
            
            logger.info(f"  {'⚠️' if is_placeholder else '✅'} Implementation status: {'PLACEHOLDER' if is_placeholder else 'IMPLEMENTED'}")
            
            self.choreography_status["formal_verification"] = {
                "imports": "SUCCESS",
                "implementation_status": "PLACEHOLDER" if is_placeholder else "IMPLEMENTED"
            }
            
        except Exception as e:
            logger.error(f"  ❌ Formal verification assessment failed: {e}")
            self.choreography_status["formal_verification"] = {"status": "FAILED", "error": str(e)}
    
    def _generate_final_report(self) -> Dict[str, Any]:
        """Phase 4: Generate comprehensive final report."""
        logger.info("\n📋 PHASE 4: FINAL EVALUATION REPORT")
        logger.info("=" * 80)
        
        # Calculate overall scores
        actor_system_score = self._calculate_actor_system_score()
        caw_compliance_score = self._calculate_caw_compliance_score()
        choreography_score = self._calculate_choreography_score()
        
        overall_score = (actor_system_score + caw_compliance_score + choreography_score) / 3
        
        # Generate summary
        logger.info(f"🎯 OVERALL EVALUATION SCORE: {overall_score:.1f}%")
        logger.info(f"   📊 Actor System: {actor_system_score:.1f}%")
        logger.info(f"   🌊 CAW Compliance: {caw_compliance_score:.1f}%")
        logger.info(f"   💃 Choreography: {choreography_score:.1f}%")
        
        if self.caw_compliance_issues:
            logger.warning(f"\n⚠️ CAW COMPLIANCE ISSUES FOUND: {len(self.caw_compliance_issues)}")
            for issue in self.caw_compliance_issues:
                logger.warning(f"   - {issue}")
        
        return {
            "overall_score": overall_score,
            "actor_system_score": actor_system_score,
            "caw_compliance_score": caw_compliance_score,
            "choreography_score": choreography_score,
            "actor_inventory": self.actor_inventory,
            "evaluation_results": self.evaluation_results,
            "choreography_status": self.choreography_status,
            "caw_compliance_issues": self.caw_compliance_issues,
            "timestamp": time.time()
        }
    
    def _calculate_actor_system_score(self) -> float:
        """Calculate actor system health score."""
        score = 0.0
        max_score = 100.0
        
        # Actor inventory (25 points)
        if self.actor_inventory.get("import_status") == "SUCCESS":
            score += 25.0
        
        # Operational health (35 points)
        health = self.evaluation_results.get("actor_system_health", {})
        if health.get("instantiation") == "SUCCESS":
            score += 20.0
        if health.get("registry_accessible"):
            score += 15.0
        
        # Lifecycle management (40 points)
        lifecycle = self.evaluation_results.get("lifecycle_management", {})
        if lifecycle.get("supervision_imports") == "SUCCESS":
            score += 40.0
        
        return min(score, max_score)
    
    def _calculate_caw_compliance_score(self) -> float:
        """Calculate CAW paradigm compliance score."""
        score = 0.0
        max_score = 100.0
        
        # Each CAW component worth 25 points
        components = ["context_flow", "capability_security", "wave_particle_duality", "message_based_communication"]
        
        for component in components:
            result = self.evaluation_results.get(component, {})
            if result.get("status") != "FAILED" and "error" not in result:
                score += 25.0
        
        return min(score, max_score)
    
    def _calculate_choreography_score(self) -> float:
        """Calculate choreography system score."""
        score = 0.0
        max_score = 100.0
        
        # Each choreography component worth 25 points
        components = ["adaptive_capabilities", "evolution_mechanisms", "engine_status", "formal_verification"]
        
        for component in components:
            result = self.choreography_status.get(component, {})
            if result.get("status") != "FAILED" and "error" not in result:
                score += 25.0
        
        return min(score, max_score)

async def main():
    """Run the comprehensive evaluation."""
    evaluator = ActorSystemEvaluator()
    
    try:
        results = await evaluator.run_comprehensive_evaluation()
        
        # Print final summary
        print(f"\n🏆 EVALUATION COMPLETE")
        print(f"Overall Score: {results['overall_score']:.1f}%")
        
        if results['overall_score'] >= 80:
            print("🎉 EXCELLENT: System demonstrates strong CAW compliance and functionality")
        elif results['overall_score'] >= 60:
            print("✅ GOOD: System is functional with some areas for improvement")
        elif results['overall_score'] >= 40:
            print("⚠️ FAIR: System has significant issues that need attention")
        else:
            print("❌ POOR: System has critical issues requiring immediate attention")
        
        return results
        
    except Exception as e:
        logger.error(f"💥 Evaluation failed: {e}")
        return {"error": str(e), "overall_score": 0.0}

if __name__ == "__main__":
    results = asyncio.run(main())
    sys.exit(0 if results.get("overall_score", 0) >= 60 else 1)
