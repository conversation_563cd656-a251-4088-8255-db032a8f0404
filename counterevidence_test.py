#!/usr/bin/env python3
"""
Counterevidence Test: Challenge Bootstrap Unification Claims
===========================================================

This script systematically tests the claims made about bootstrap unification
to find counterevidence and hidden issues.
"""

import sys
import time
import traceback
from pathlib import Path

def test_basic_imports():
    """Test if basic imports work without hanging."""
    print("🔍 Testing basic imports...")
    
    try:
        print("  - Testing person_suit import...")
        import person_suit
        print("  ✅ person_suit imported")
        
        print("  - Testing main module import...")
        from person_suit import main
        print("  ✅ main module imported")
        
        print("  - Testing config import...")
        from person_suit.core.config.loader import load_config
        print("  ✅ config loader imported")
        
        return True
    except Exception as e:
        print(f"  ❌ Import failed: {e}")
        traceback.print_exc()
        return False

def test_circular_dependencies():
    """Test for remaining circular dependencies."""
    print("\n🔍 Testing for circular dependencies...")
    
    try:
        print("  - Testing UnifiedContext import...")
        from person_suit.shared.context.unified import UnifiedContext
        print("  ✅ UnifiedContext imported")
        
        print("  - Testing ActorSystem import...")
        from person_suit.core.actors.actor_system import ActorSystem
        print("  ✅ ActorSystem imported")
        
        print("  - Testing message bus import...")
        from person_suit.core.infrastructure.hybrid_message_bus import get_message_bus
        print("  ✅ Message bus imported")
        
        return True
    except Exception as e:
        print(f"  ❌ Circular dependency detected: {e}")
        traceback.print_exc()
        return False

def test_entry_points():
    """Test for remaining unauthorized entry points."""
    print("\n🔍 Testing for unauthorized entry points...")
    
    person_suit_dir = Path("person_suit")
    if not person_suit_dir.exists():
        print("  ❌ person_suit directory not found")
        return False
    
    entry_points = []
    for py_file in person_suit_dir.rglob("*.py"):
        try:
            content = py_file.read_text(encoding='utf-8')
            if 'if __name__ == "__main__":' in content:
                # Skip allowed entry points
                if str(py_file) not in ["person_suit/main.py", "person_suit/__main__.py"]:
                    entry_points.append(str(py_file))
        except Exception:
            continue
    
    if entry_points:
        print(f"  ❌ Found {len(entry_points)} unauthorized entry points:")
        for ep in entry_points[:10]:
            print(f"    - {ep}")
        if len(entry_points) > 10:
            print(f"    ... and {len(entry_points) - 10} more")
        return False
    else:
        print("  ✅ No unauthorized entry points found")
        return True

def test_lazy_imports():
    """Test if lazy imports actually work."""
    print("\n🔍 Testing lazy import implementation...")
    
    try:
        # Check if the lazy imports are actually in place
        with open("person_suit/main.py", "r") as f:
            content = f.read()
        
        # Look for the lazy import pattern
        if "from person_suit.core.actors.actor_system import ActorSystem" in content:
            print("  ❌ ActorSystem is still imported at module level (not lazy)")
            return False
        
        if "from person_suit.core.context.unified import UnifiedContext" in content:
            print("  ❌ UnifiedContext is still imported at module level (not lazy)")
            return False
        
        # Check for lazy import pattern
        if "async def _initialize_core_infrastructure" in content:
            print("  ✅ Lazy import structure found")
            return True
        else:
            print("  ❌ Lazy import structure not found")
            return False
            
    except Exception as e:
        print(f"  ❌ Error checking lazy imports: {e}")
        return False

def test_config_circular_fix():
    """Test if config circular dependency is actually fixed."""
    print("\n🔍 Testing config circular dependency fix...")
    
    try:
        with open("person_suit/core/config/__init__.py", "r") as f:
            content = f.read()
        
        if "from .client import request_config" in content:
            print("  ❌ Problematic config import still present")
            return False
        else:
            print("  ✅ Problematic config import removed")
            return True
            
    except Exception as e:
        print(f"  ❌ Error checking config fix: {e}")
        return False

def test_foundation_actors():
    """Test if foundation actors can actually be imported."""
    print("\n🔍 Testing foundation actor imports...")
    
    try:
        from person_suit.core.actors.foundation_actors import (
            FoundationSupervisorActor,
            HealthMonitorActor,
            MetricsCollectorActor,
            RuntimeVerifierActor,
            EnergyManagementActor,
            DashboardActor
        )
        print("  ✅ All foundation actors imported successfully")
        return True
    except Exception as e:
        print(f"  ❌ Foundation actor import failed: {e}")
        traceback.print_exc()
        return False

def main():
    """Run all counterevidence tests."""
    print("🕵️ COUNTEREVIDENCE INVESTIGATION: Bootstrap Unification Claims")
    print("=" * 70)
    
    tests = [
        ("Basic Imports", test_basic_imports),
        ("Circular Dependencies", test_circular_dependencies),
        ("Entry Points", test_entry_points),
        ("Lazy Imports", test_lazy_imports),
        ("Config Fix", test_config_circular_fix),
        ("Foundation Actors", test_foundation_actors),
    ]
    
    results = {}
    
    for test_name, test_func in tests:
        try:
            start_time = time.time()
            result = test_func()
            end_time = time.time()
            results[test_name] = {
                "passed": result,
                "time": end_time - start_time
            }
        except Exception as e:
            print(f"\n❌ {test_name} test crashed: {e}")
            results[test_name] = {
                "passed": False,
                "time": 0,
                "error": str(e)
            }
    
    # Summary
    print("\n" + "=" * 70)
    print("🔍 COUNTEREVIDENCE SUMMARY")
    print("=" * 70)
    
    passed = 0
    total = len(tests)
    
    for test_name, result in results.items():
        status = "✅ PASS" if result["passed"] else "❌ FAIL"
        time_str = f"({result['time']:.2f}s)"
        print(f"{status} {test_name} {time_str}")
        if result["passed"]:
            passed += 1
        elif "error" in result:
            print(f"    Error: {result['error']}")
    
    print(f"\nOverall: {passed}/{total} tests passed ({passed/total*100:.1f}%)")
    
    if passed < total:
        print("\n🚨 COUNTEREVIDENCE FOUND: Bootstrap unification claims are NOT fully accurate!")
        print("The system has remaining issues that contradict the completion claims.")
    else:
        print("\n✅ No counterevidence found: Claims appear to be accurate.")
    
    return passed == total

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
