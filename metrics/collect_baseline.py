#!/usr/bin/env python3
"""Collect baseline throughput and latency metrics for the Hybrid Message Bus.

Usage::

    python metrics/collect_baseline.py  -n 1000 -r 100

Which sends *n* messages at *r* rate (msgs/sec) and records processing latency.
The results are dumped to `metrics/baseline_<timestamp>.json`.
"""
from __future__ import annotations

import argparse
import asyncio
import json
import time
from pathlib import Path
from typing import Any, Dict

from person_suit.core.infrastructure.hybrid_message import HybridMessage  # type: ignore
from person_suit.core.infrastructure.hybrid_message_bus import (
    get_message_bus,
    stop_message_bus,
)
from person_suit.shared.context.unified import UnifiedContext

DEFAULT_RATE = 100  # msgs/sec
DEFAULT_TOTAL = 1000


def _make_message(i: int) -> HybridMessage:  # type: ignore[name-defined]
    return HybridMessage(
        message_type="EVENT",
        channel="test.baseline",
        payload={"seq": i},
        context=UnifiedContext.create_default(domain="baseline"),
        response_expected=False,
    )


async def _run(total: int, rate: int) -> Dict[str, Any]:
    bus = await get_message_bus()

    send_interval = 1.0 / rate
    latencies: list[float] = []

    for i in range(total):
        start = time.perf_counter()
        await bus.send(_make_message(i))
        latencies.append((time.perf_counter() - start) * 1000)
        await asyncio.sleep(send_interval)

    await stop_message_bus()

    return {
        "messages": total,
        "rate": rate,
        "avg_latency_ms": sum(latencies) / len(latencies),
        "p95_latency_ms": sorted(latencies)[int(0.95 * len(latencies))],
    }


async def main() -> None:  # noqa: D103
    parser = argparse.ArgumentParser()
    parser.add_argument("-n", "--total", type=int, default=DEFAULT_TOTAL)
    parser.add_argument("-r", "--rate", type=int, default=DEFAULT_RATE)
    args = parser.parse_args()

    results = await _run(args.total, args.rate)
    ts = int(time.time())
    out_path = Path(__file__).with_name(f"baseline_{ts}.json")
    out_path.write_text(json.dumps(results, indent=2))
    print(f"[baseline] results saved to {out_path}")
    print(json.dumps(results, indent=2))


if __name__ == "__main__":
    asyncio.run(main()) 