#!/usr/bin/env python3
"""
Simple Infrastructure Test - Minimal Dependencies
================================================

A simplified test that can actually run to provide real infrastructure analysis.
"""

import time
import json
import sys
from pathlib import Path

def test_basic_imports():
    """Test basic imports without complex dependencies."""
    print("🔍 Testing Basic Infrastructure Imports...")
    results = {}
    
    # Test 1: Core infrastructure
    try:
        import person_suit
        results["person_suit_core"] = "SUCCESS"
        print("  ✅ person_suit core: SUCCESS")
    except Exception as e:
        results["person_suit_core"] = f"FAILED: {e}"
        print(f"  ❌ person_suit core: FAILED - {e}")
    
    # Test 2: Message bus
    try:
        from person_suit.core.infrastructure.hybrid_message_bus import HybridMessageBus
        results["message_bus"] = "SUCCESS"
        print("  ✅ HybridMessageBus: SUCCESS")
    except Exception as e:
        results["message_bus"] = f"FAILED: {e}"
        print(f"  ❌ HybridMessageBus: FAILED - {e}")
    
    # Test 3: ACF system
    try:
        from person_suit.core.adaptivity.acf import ACFManager
        results["acf_manager"] = "SUCCESS"
        print("  ✅ ACFManager: SUCCESS")
    except Exception as e:
        results["acf_manager"] = f"FAILED: {e}"
        print(f"  ❌ ACFManager: FAILED - {e}")
    
    # Test 4: Actor system
    try:
        from person_suit.core.actors.actor_system import ActorSystem
        results["actor_system"] = "SUCCESS"
        print("  ✅ ActorSystem: SUCCESS")
    except Exception as e:
        results["actor_system"] = f"FAILED: {e}"
        print(f"  ❌ ActorSystem: FAILED - {e}")
    
    # Test 5: Choreography engine
    try:
        from person_suit.core.actors.choreography.engine import ChoreographyEngine
        results["choreography_engine"] = "SUCCESS"
        print("  ✅ ChoreographyEngine: SUCCESS")
    except Exception as e:
        results["choreography_engine"] = f"FAILED: {e}"
        print(f"  ❌ ChoreographyEngine: FAILED - {e}")
    
    return results

def analyze_codebase_structure():
    """Analyze the codebase structure for architectural compliance."""
    print("\n🏗️ Analyzing Codebase Structure...")
    
    analysis = {
        "total_python_files": 0,
        "core_modules": 0,
        "meta_system_modules": 0,
        "test_files": 0,
        "documentation_files": 0,
        "architectural_compliance": {}
    }
    
    # Count files by category
    person_suit_dir = Path("person_suit")
    if person_suit_dir.exists():
        for py_file in person_suit_dir.rglob("*.py"):
            analysis["total_python_files"] += 1
            
            if "/core/" in str(py_file):
                analysis["core_modules"] += 1
            elif "/meta_systems/" in str(py_file):
                analysis["meta_system_modules"] += 1
    
    # Count test files
    tests_dir = Path("tests")
    if tests_dir.exists():
        analysis["test_files"] = len(list(tests_dir.rglob("*.py")))
    
    # Count documentation files
    docs_dir = Path("docs")
    if docs_dir.exists():
        analysis["documentation_files"] = len(list(docs_dir.rglob("*.md")))
    
    print(f"  📊 Total Python files: {analysis['total_python_files']}")
    print(f"  🔧 Core modules: {analysis['core_modules']}")
    print(f"  🎭 Meta-system modules: {analysis['meta_system_modules']}")
    print(f"  🧪 Test files: {analysis['test_files']}")
    print(f"  📚 Documentation files: {analysis['documentation_files']}")
    
    return analysis

def check_architectural_principles():
    """Check adherence to architectural principles."""
    print("\n🏛️ Checking Architectural Principles...")
    
    principles = {
        "effect_systems": False,
        "choreographed_interactions": False,
        "capability_based_security": False,
        "adaptive_computational_fidelity": False,
        "unified_context_propagation": False
    }
    
    # Check for Effect Systems
    effect_files = list(Path("person_suit/core/effects").glob("*.py")) if Path("person_suit/core/effects").exists() else []
    if effect_files:
        principles["effect_systems"] = True
        print("  ✅ Effect Systems: IMPLEMENTED")
    else:
        print("  ❌ Effect Systems: NOT FOUND")
    
    # Check for Choreography
    choreo_files = list(Path("person_suit/core/actors/choreography").glob("*.py")) if Path("person_suit/core/actors/choreography").exists() else []
    if choreo_files:
        principles["choreographed_interactions"] = True
        print("  ✅ Choreographed Interactions: IMPLEMENTED")
    else:
        print("  ❌ Choreographed Interactions: NOT FOUND")
    
    # Check for Capability-Based Security
    security_files = list(Path("person_suit/core/security").glob("*.py")) if Path("person_suit/core/security").exists() else []
    if security_files:
        principles["capability_based_security"] = True
        print("  ✅ Capability-Based Security: IMPLEMENTED")
    else:
        print("  ❌ Capability-Based Security: NOT FOUND")
    
    # Check for ACF
    acf_files = list(Path("person_suit/core/adaptivity").glob("*.py")) if Path("person_suit/core/adaptivity").exists() else []
    if acf_files:
        principles["adaptive_computational_fidelity"] = True
        print("  ✅ Adaptive Computational Fidelity: IMPLEMENTED")
    else:
        print("  ❌ Adaptive Computational Fidelity: NOT FOUND")
    
    # Check for Unified Context
    context_files = list(Path("person_suit/shared/context").glob("*.py")) if Path("person_suit/shared/context").exists() else []
    if context_files:
        principles["unified_context_propagation"] = True
        print("  ✅ Unified Context Propagation: IMPLEMENTED")
    else:
        print("  ❌ Unified Context Propagation: NOT FOUND")
    
    return principles

def assess_caw_paradigm_implementation():
    """Assess CAW paradigm implementation."""
    print("\n🌊 Assessing CAW Paradigm Implementation...")
    
    caw_components = {
        "wave_particle_duality": False,
        "context_sensitivity": False,
        "adaptive_processing": False,
        "message_based_communication": False
    }
    
    # Check for Wave-Particle Duality
    wave_files = list(Path("person_suit/core/infrastructure/dual_wave").glob("*.py")) if Path("person_suit/core/infrastructure/dual_wave").exists() else []
    if wave_files:
        caw_components["wave_particle_duality"] = True
        print("  ✅ Wave-Particle Duality: IMPLEMENTED")
    else:
        print("  ❌ Wave-Particle Duality: NOT FOUND")
    
    # Check for Context Sensitivity
    if Path("person_suit/shared/context/unified.py").exists():
        caw_components["context_sensitivity"] = True
        print("  ✅ Context Sensitivity: IMPLEMENTED")
    else:
        print("  ❌ Context Sensitivity: NOT FOUND")
    
    # Check for Adaptive Processing
    if Path("person_suit/core/adaptivity/acf.py").exists():
        caw_components["adaptive_processing"] = True
        print("  ✅ Adaptive Processing: IMPLEMENTED")
    else:
        print("  ❌ Adaptive Processing: NOT FOUND")
    
    # Check for Message-Based Communication
    if Path("person_suit/core/infrastructure/hybrid_message_bus.py").exists():
        caw_components["message_based_communication"] = True
        print("  ✅ Message-Based Communication: IMPLEMENTED")
    else:
        print("  ❌ Message-Based Communication: NOT FOUND")
    
    return caw_components

def analyze_scalability_indicators():
    """Analyze indicators of system scalability."""
    print("\n📈 Analyzing Scalability Indicators...")
    
    scalability = {
        "deployment_profiles": False,
        "resource_management": False,
        "distributed_coordination": False,
        "performance_monitoring": False
    }
    
    # Check for Deployment Profiles
    if Path("person_suit/core/infrastructure/deployment_profiles.py").exists():
        scalability["deployment_profiles"] = True
        print("  ✅ Deployment Profiles: IMPLEMENTED")
    else:
        print("  ❌ Deployment Profiles: NOT FOUND")
    
    # Check for Resource Management
    resource_files = list(Path("person_suit/core/resources").glob("*.py")) if Path("person_suit/core/resources").exists() else []
    if resource_files:
        scalability["resource_management"] = True
        print("  ✅ Resource Management: IMPLEMENTED")
    else:
        print("  ❌ Resource Management: NOT FOUND")
    
    # Check for Distributed Coordination
    if Path("person_suit/core/actors/choreography").exists():
        scalability["distributed_coordination"] = True
        print("  ✅ Distributed Coordination: IMPLEMENTED")
    else:
        print("  ❌ Distributed Coordination: NOT FOUND")
    
    # Check for Performance Monitoring
    monitoring_files = list(Path("person_suit/core/infrastructure/monitoring").glob("*.py")) if Path("person_suit/core/infrastructure/monitoring").exists() else []
    if monitoring_files:
        scalability["performance_monitoring"] = True
        print("  ✅ Performance Monitoring: IMPLEMENTED")
    else:
        print("  ❌ Performance Monitoring: NOT FOUND")
    
    return scalability

def calculate_overall_score(results):
    """Calculate overall infrastructure score."""
    scores = {
        "import_success": 0,
        "architectural_compliance": 0,
        "caw_implementation": 0,
        "scalability": 0
    }
    
    # Import success score (25 points)
    import_results = results.get("imports", {})
    successful_imports = len([v for v in import_results.values() if v == "SUCCESS"])
    total_imports = len(import_results)
    if total_imports > 0:
        scores["import_success"] = (successful_imports / total_imports) * 25
    
    # Architectural compliance score (25 points)
    principles = results.get("architectural_principles", {})
    implemented_principles = len([v for v in principles.values() if v])
    total_principles = len(principles)
    if total_principles > 0:
        scores["architectural_compliance"] = (implemented_principles / total_principles) * 25
    
    # CAW implementation score (25 points)
    caw_components = results.get("caw_paradigm", {})
    implemented_caw = len([v for v in caw_components.values() if v])
    total_caw = len(caw_components)
    if total_caw > 0:
        scores["caw_implementation"] = (implemented_caw / total_caw) * 25
    
    # Scalability score (25 points)
    scalability = results.get("scalability", {})
    implemented_scalability = len([v for v in scalability.values() if v])
    total_scalability = len(scalability)
    if total_scalability > 0:
        scores["scalability"] = (implemented_scalability / total_scalability) * 25
    
    overall_score = sum(scores.values())
    
    return scores, overall_score

def main():
    """Run the simple infrastructure test."""
    print("🚀 Simple Infrastructure Test & Analysis")
    print("=" * 60)
    
    start_time = time.time()
    
    # Collect all results
    results = {
        "timestamp": time.time(),
        "test_duration": 0,
        "imports": {},
        "codebase_structure": {},
        "architectural_principles": {},
        "caw_paradigm": {},
        "scalability": {},
        "scores": {},
        "overall_score": 0
    }
    
    try:
        # Run tests
        results["imports"] = test_basic_imports()
        results["codebase_structure"] = analyze_codebase_structure()
        results["architectural_principles"] = check_architectural_principles()
        results["caw_paradigm"] = assess_caw_paradigm_implementation()
        results["scalability"] = analyze_scalability_indicators()
        
        # Calculate scores
        scores, overall_score = calculate_overall_score(results)
        results["scores"] = scores
        results["overall_score"] = overall_score
        
        # Calculate test duration
        results["test_duration"] = time.time() - start_time
        
        # Print summary
        print("\n" + "=" * 60)
        print("📋 INFRASTRUCTURE ANALYSIS SUMMARY")
        print("=" * 60)
        print(f"🎯 Overall Score: {overall_score:.1f}/100")
        print(f"   📦 Import Success: {scores['import_success']:.1f}/25")
        print(f"   🏛️ Architectural Compliance: {scores['architectural_compliance']:.1f}/25")
        print(f"   🌊 CAW Implementation: {scores['caw_implementation']:.1f}/25")
        print(f"   📈 Scalability: {scores['scalability']:.1f}/25")
        print(f"⏱️ Test Duration: {results['test_duration']:.2f} seconds")
        
        # Assessment
        if overall_score >= 80:
            print("\n🎉 EXCELLENT: Infrastructure is well-implemented and ready for production")
        elif overall_score >= 60:
            print("\n✅ GOOD: Infrastructure is solid with minor areas for improvement")
        elif overall_score >= 40:
            print("\n⚠️ FAIR: Infrastructure has significant gaps requiring attention")
        else:
            print("\n❌ POOR: Infrastructure has critical issues requiring immediate action")
        
        # Save results
        results_file = f"simple_infrastructure_test_results.json"
        with open(results_file, 'w') as f:
            json.dump(results, f, indent=2, default=str)
        print(f"\n📄 Results saved to {results_file}")
        
        return results
        
    except Exception as e:
        print(f"\n💥 Test failed: {e}")
        return None

if __name__ == "__main__":
    results = main()
    exit_code = 0 if results and results["overall_score"] >= 60 else 1
    sys.exit(exit_code)
