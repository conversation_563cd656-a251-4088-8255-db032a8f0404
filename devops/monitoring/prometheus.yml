global:
  scrape_interval: 15s
  evaluation_interval: 15s

rule_files:
  - "person_suit_rules.yml"

scrape_configs:
  # Person Suit main application metrics
  - job_name: 'person-suit'
    static_configs:
      - targets: ['person-suit:8000']
    metrics_path: '/metrics'
    scrape_interval: 5s
    scrape_timeout: 10s

  # Prometheus self-monitoring
  - job_name: 'prometheus'
    static_configs:
      - targets: ['localhost:9090']

  # System metrics (if node_exporter is added)
  - job_name: 'node'
    static_configs:
      - targets: ['node-exporter:9100']
    scrape_interval: 10s

  # Redis metrics (if redis_exporter is added)
  - job_name: 'redis'
    static_configs:
      - targets: ['redis-exporter:9121']
    scrape_interval: 10s

  # PostgreSQL metrics (if postgres_exporter is added)
  - job_name: 'postgres'
    static_configs:
      - targets: ['postgres-exporter:9187']
    scrape_interval: 10s

# Alerting configuration
alerting:
  alertmanagers:
    - static_configs:
        - targets:
          # - alertmanager:9093