version: "3.8"

services:
  person-suit:
    build: 
      context: ../../
      dockerfile: Dockerfile
      target: production
    command: python -m person_suit
    environment:
      - PS_METRICS=1
      - PS_METRICS_PORT=8000
      - PS_DEPLOYMENT_PROFILE=server
      - PS_LOG_LEVEL=INFO
      # Database connections (optional - system can run without them)
      # - PS_POSTGRES_URL=*************************************************************/person_suit
      - PS_REDIS_URL=redis://redis:6379
      # - PS_NEO4J_URL=bolt://neo4j:7687  # Not yet implemented
      # - PS_ARANGODB_URL=http://arangodb:8529  # Not yet implemented
      # Redpanda for event streaming (correct env var)
      - PS_REDPANDA_BROKERS=redpanda:9092
      - PS_PROVENANCE_SINK=redpanda
    ports:
      - "8000:8000"  # metrics endpoint
    volumes:
      - person_suit_data:/app/data
      - person_suit_logs:/app/logs
    healthcheck:
      test: ["CMD", "wget", "--no-verbose", "--tries=1", "--spider", "http://localhost:8000/metrics"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s
    restart: unless-stopped

  prometheus:
    image: prom/prometheus:latest
    volumes:
      - ./prometheus.yml:/etc/prometheus/prometheus.yml:ro
      - ./person_suit_rules.yml:/etc/prometheus/person_suit_rules.yml:ro
      - prometheus_data:/prometheus
    ports:
      - "9090:9090"
    command:
      - '--config.file=/etc/prometheus/prometheus.yml'
      - '--storage.tsdb.path=/prometheus'
      - '--web.console.libraries=/etc/prometheus/console_libraries'
      - '--web.console.templates=/etc/prometheus/consoles'
      - '--storage.tsdb.retention.time=200h'
      - '--web.enable-lifecycle'
    restart: unless-stopped

  grafana:
    image: grafana/grafana:latest
    environment:
      - GF_SECURITY_ADMIN_PASSWORD=admin
      - GF_SECURITY_ALLOW_EMBEDDING=true
      - GF_USERS_ALLOW_SIGN_UP=false
    ports:
      - "3000:3000"
    volumes:
      - grafana_data:/var/lib/grafana
      - ./grafana/dashboards:/etc/grafana/provisioning/dashboards
      - ./grafana/datasources:/etc/grafana/provisioning/datasources
      - ../../docs/monitoring:/var/lib/grafana/dashboards:ro
    depends_on:
      - prometheus
    restart: unless-stopped

  # Redis for caching and session storage
  redis:
    image: redis:7-alpine
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    command: redis-server --appendonly yes
    restart: unless-stopped

  # PostgreSQL for persistent storage (optional)
  postgres:
    image: postgres:15-alpine
    environment:
      - POSTGRES_DB=person_suit
      - POSTGRES_USER=person_suit
      - POSTGRES_PASSWORD=changeme_in_production
    ports:
      - "5432:5432"
    volumes:
      - postgres_data:/var/lib/postgresql/data
    restart: unless-stopped

  # Redpanda for event streaming (Kafka-compatible)
  redpanda:
    image: redpandadata/redpanda:latest
    command:
      - redpanda
      - start
      - --smp
      - '1'
      - --memory
      - '1G'
      - --overprovisioned
      - --node-id
      - '0'
      - --kafka-addr
      - PLAINTEXT://0.0.0.0:29092,OUTSIDE://0.0.0.0:9092
      - --advertise-kafka-addr
      - PLAINTEXT://redpanda:29092,OUTSIDE://localhost:9092
    ports:
      - "9092:9092"
      - "29092:29092"
    volumes:
      - redpanda_data:/var/lib/redpanda/data
    restart: unless-stopped

volumes:
  person_suit_data:
    driver: local
  person_suit_logs:
    driver: local
  prometheus_data:
    driver: local
  grafana_data:
    driver: local
  redis_data:
    driver: local
  postgres_data:
    driver: local
  redpanda_data:
    driver: local

networks:
  default:
    name: person_suit_network