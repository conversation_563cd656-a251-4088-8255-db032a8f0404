# Person Suit Prometheus Recording Rules
# These rules pre-calculate common aggregations for dashboard performance

groups:
  - name: person_suit_aggregations
    interval: 30s
    rules:
      # Message bus throughput aggregations
      - record: instance:messages_processed:rate5m
        expr: rate(messages_processed_total[5m])
      
      - record: instance:messages_processed:rate1m
        expr: rate(messages_processed_total[1m])
      
      # Effect execution success rate
      - record: instance:effect_success_rate:5m
        expr: |
          rate(effect_executions_total[5m]) / 
          (rate(effect_attempts_total[5m]) > 0) or vector(1)
      
      # Security denial rate by channel
      - record: channel:security_denials:rate5m
        expr: rate(security_denials_total[5m])
      
      # Provenance loss rate
      - record: instance:provenance_loss:rate5m
        expr: rate(provenance_loss_total[5m])
      
      # Actor restart frequency
      - record: actor:restart_frequency:1h
        expr: increase(actor_restarts_total[1h])
      
      # Choreography workflow completion rate
      - record: choreography:completion_rate:5m
        expr: |
          rate(choreography_workflows_started_total[5m]) > 0
      
      # Effect execution latency percentiles
      - record: effect:execution_latency:p50
        expr: histogram_quantile(0.5, rate(effect_execution_latency_seconds_bucket[5m]))
      
      - record: effect:execution_latency:p95
        expr: histogram_quantile(0.95, rate(effect_execution_latency_seconds_bucket[5m]))
      
      - record: effect:execution_latency:p99
        expr: histogram_quantile(0.99, rate(effect_execution_latency_seconds_bucket[5m])) 