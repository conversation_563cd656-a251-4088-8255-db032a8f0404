#!/usr/bin/env python3
"""
Entry Point Cleanup Script for Bootstrap Unification
====================================================

Systematically removes unauthorized entry points from person_suit/ directory
to fix Gate 3 validation failures. Only person_suit/main.py and 
person_suit/__main__.py should have entry points.
"""

import os
import re
import subprocess
from pathlib import Path
from typing import List, Tu<PERSON>

def find_entry_points() -> List[str]:
    """Find all entry points in person_suit directory."""
    try:
        result = subprocess.run([
            'grep', '-r', 'if __name__ == "__main__":', 'person_suit/',
            '--include=*.py'
        ], capture_output=True, text=True, cwd='.')
        
        if result.returncode == 0:
            lines = result.stdout.strip().split('\n')
            # Filter out allowed entry points
            forbidden = []
            for line in lines:
                if 'person_suit/main.py' not in line and 'person_suit/__main__.py' not in line:
                    # Extract just the file path
                    file_path = line.split(':')[0]
                    forbidden.append(file_path)
            return forbidden
        return []
    except Exception as e:
        print(f"Error finding entry points: {e}")
        return []

def remove_entry_point(file_path: str) -> bool:
    """Remove entry point from a specific file."""
    try:
        path = Path(file_path)
        if not path.exists():
            print(f"⚠️ File not found: {file_path}")
            return False
        
        content = path.read_text(encoding='utf-8')
        original_content = content
        
        # Pattern to match the entire if __name__ == "__main__": block
        # This pattern captures the block and everything indented under it
        pattern = r'\n\s*if\s+__name__\s*==\s*["\']__main__["\']\s*:\s*\n(?:(?:\s{4,}.*\n)*)'
        
        # Remove the entry point block
        new_content = re.sub(pattern, '\n', content, flags=re.MULTILINE)
        
        # Also handle cases where the block is at the end of file
        pattern_eof = r'\s*if\s+__name__\s*==\s*["\']__main__["\']\s*:\s*\n(?:(?:\s{4,}.*\n?)*)\s*$'
        new_content = re.sub(pattern_eof, '', new_content, flags=re.MULTILINE)
        
        if new_content != original_content:
            # Clean up any trailing whitespace
            new_content = new_content.rstrip() + '\n' if new_content.strip() else ''
            path.write_text(new_content, encoding='utf-8')
            print(f"✅ Removed entry point from: {file_path}")
            return True
        else:
            print(f"⚠️ No entry point pattern found in: {file_path}")
            return False
            
    except Exception as e:
        print(f"❌ Error processing {file_path}: {e}")
        return False

def categorize_files(file_paths: List[str]) -> Tuple[List[str], List[str], List[str]]:
    """Categorize files by type for appropriate handling."""
    core_files = []
    test_files = []
    utility_files = []
    
    for file_path in file_paths:
        if any(pattern in file_path for pattern in ['/test_', '/tests/', '_test.py']):
            test_files.append(file_path)
        elif any(pattern in file_path for pattern in ['/core/', '/infrastructure/', '/shared/']):
            core_files.append(file_path)
        else:
            utility_files.append(file_path)
    
    return core_files, test_files, utility_files

def main():
    """Main cleanup function."""
    print("🧹 Bootstrap Unification: Entry Point Cleanup")
    print("=" * 50)
    
    # Find all entry points
    entry_points = find_entry_points()
    
    if not entry_points:
        print("✅ No unauthorized entry points found!")
        return
    
    print(f"Found {len(entry_points)} unauthorized entry points")
    
    # Categorize files
    core_files, test_files, utility_files = categorize_files(entry_points)
    
    print(f"\nCategories:")
    print(f"  Core files: {len(core_files)}")
    print(f"  Test files: {len(test_files)}")
    print(f"  Utility files: {len(utility_files)}")
    
    removed_count = 0
    
    # Process core files first (highest priority)
    if core_files:
        print(f"\n📁 Processing {len(core_files)} core files...")
        for file_path in core_files:
            if remove_entry_point(file_path):
                removed_count += 1
    
    # Process utility files
    if utility_files:
        print(f"\n📁 Processing {len(utility_files)} utility files...")
        for file_path in utility_files:
            if remove_entry_point(file_path):
                removed_count += 1
    
    # Process test files (may need special handling)
    if test_files:
        print(f"\n📁 Processing {len(test_files)} test files...")
        for file_path in test_files:
            if remove_entry_point(file_path):
                removed_count += 1
    
    print(f"\n✨ Cleanup complete! Removed entry points from {removed_count} files.")
    
    # Verify cleanup
    remaining = find_entry_points()
    if remaining:
        print(f"\n⚠️ {len(remaining)} entry points still remain:")
        for entry in remaining[:5]:
            print(f"  - {entry}")
        if len(remaining) > 5:
            print(f"  ... and {len(remaining) - 5} more")
    else:
        print("\n🎉 All unauthorized entry points removed!")

if __name__ == "__main__":
    main()
