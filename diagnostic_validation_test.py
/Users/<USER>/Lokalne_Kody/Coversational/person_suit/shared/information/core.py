from __future__ import annotations
# -*- coding: utf-8 -*-

# Message-based services initialization
from person_suit.core.infrastructure.message_based_imports import ensure_services_initialized

async def _ensure_message_services():
    """Ensure message-based services are initialized."""
    if not await ensure_services_initialized():
        raise RuntimeError("Failed to initialize message-based services")


"""
File: person_suit/core/application/interfaces/events_interface.py
Purpose: Core interfaces for the event management system.

NOTE: This file was moved from core/application/ during de-flattening.

Defines standard event structures, topics, data types, and the main
IEventManager interface.

# TODO: Refactoring Needed
# (List of TODOs from original file...)
"""

import sys
import uuid
from dataclasses import MISSING, dataclass, field
from datetime import datetime, timezone
from enum import Enum, auto
from typing import (
    Any,
    Callable,
    Coroutine,
    Dict,
    List,
    Optional,
    Protocol,
    TypeAlias,
    TypeVar,
    Union,
)
from uuid import UUID, uuid4

from pydantic import BaseModel, Field, field_validator

from person_suit.core.infrastructure.message_based_imports import (
    UnifiedContext as UnifiedContext,
)
from person_suit.shared.information.core import Infom, InformationFactory
from person_suit.shared.information.sources import InformationSource
from person_suit.shared.information.translator import InformationTranslator

_information_translator = InformationTranslator(
    source=InformationSource(source_id="events_interface", source_type="system")
)

if sys.version_info >= (3, 11):
    pass  # pylint: disable=no-name-in-module
else:
    pass

# --- Forward References & Placeholders ---
Capability = TypeVar("Capability")
Effect = TypeVar("Effect")
CryptoMetadata: TypeAlias = Dict[str, Any]
TraceContext: TypeAlias = Dict[str, str]

# --- Event Topics ---


class EventTopic(Enum):
    """Standard event topics for inter-component communication."""

    SYSTEM_INITIALIZED = auto()
    SYSTEM_UPDATED = auto()
    SYSTEM_STARTUP = auto()
    SYSTEM_SHUTDOWN = auto()
    SYSTEM_ERROR = auto()
    SYSTEM_HEALTH_CHANGE = auto()
    SYSTEM_STATE_CHANGE = auto()
    CONFIGURATION_LOADED = auto()
    CONFIG_CHANGED = auto()
    SERVICE_REGISTERED = auto()
    SERVICE_UNREGISTERED = auto()
    RESOURCE_THRESHOLD_EXCEEDED = auto()
    THROTTLING_ACTIVATED = auto()
    THROTTLING_DEACTIVATED = auto()
    ALERT_TRIGGERED = auto()
    SECURITY_EVENT = auto()
    LOG_MESSAGE = auto()
    PERFORMANCE_METRIC = auto()
    INPUT_RECEIVED = auto()
    USER_INTERACTION = auto()
    GENERIC_EVENT = auto()
    CUSTOM_EVENT = auto()
    MEMORY = "pc.memory"
    MEMORY_OPERATION_REQUESTED = "pc.memory.operation.requested"
    MEMORY_OPERATION_COMPLETED = "pc.memory.operation.completed"
    MEMORY_OPERATION_FAILED = "pc.memory.operation.failed"
    MEMORY_STATE_CHANGED = "pc.memory.state.changed"
    SEM = "pc.folded_mind.sem"
    SEM_STATE_CHANGED = "pc.folded_mind.sem.state.changed"
    SEM_COGNITIVE_EVENT = "pc.folded_mind.sem.cognitive.event"
    SEM_EMOTIONAL_EVENT = "pc.folded_mind.sem.emotional.event"
    NEUROCHEMICAL = "pc.folded_mind.sem.neurochemical"
    NEUROCHEMICAL_LEVEL_CHANGED = "pc.folded_mind.sem.neurochemical.level.changed"
    NEUROCHEMICAL_STATE_EFFECT = "pc.folded_mind.sem.neurochemical.state.effect"
    SYNC = "pc.folded_mind.sync"
    SYNC_STATE_UPDATE = "pc.folded_mind.sync.state.update"
    SYNC_REQUESTED = "pc.folded_mind.sync.requested"
    ANALYSIS_REQUESTED = "an.analysis.requested"
    ANALYSIS_COMPLETED = "an.analysis.completed"
    PATTERN_DETECTED = "an.pattern.detected"
    PREDICTION_REQUESTED = "pr.prediction.requested"
    PREDICTION_GENERATED = "pr.prediction.generated"
    OUTPUT_SENT = "sio.output.sent"
    EXTERNAL_SYSTEM_EVENT = "sio.external.event"
    CONSISTENCY_CHECK_REQUESTED = "sync.consistency.check.requested"
    CONSISTENCY_INCONSISTENCY_DETECTED = "sync.consistency.inconsistency.detected"
    CONSISTENCY_REPAIRED = "sync.consistency.repaired"
    CONSISTENCY_REPAIR_FAILED = "sync.consistency.repair.failed"
    STATE_UPDATED = "sync.state.updated"
    EMOTION_FEEL_REQUESTED = "pc.emotion.feel.requested"
    EMOTION_GET_STATE_REQUESTED = "pc.emotion.get_state.requested"
    EMOTION_STATE_REPORTED = "pc.emotion.state.reported"
    EMOTION_APPRAISAL_COMPLETED = "pc.emotion.appraisal.completed"
    EXPRESSION_REQUESTED = "pc.expression.requested"
    EXPRESSION_PERFORMED = "pc.expression.performed"
    DISPLAYED_EMOTION_INFO = "pc.expression.displayed_info"
    SOCIAL_CONTEXT_UPDATE = "pc.social.context.update"
    PHYSIOLOGICAL_STATE_UPDATE = "pc.physiology.state.update"
    PERCEPTION_RESULT_PROCESSED = "app.perception.result.processed"

    def __str__(self) -> str:
        return str(self.value) if isinstance(self.value, str) else self.name


# --- Base Event Data ---


@dataclass(kw_only=True)
class BaseEventData:
    """Base class for all event data payloads..."""

    source_component: Optional[str] = None
    timestamp: datetime = field(default_factory=lambda: datetime.now(timezone.utc))
    originator_capability: Optional[Any] = field(default=None, repr=False)
    required_capabilities: Optional[List[Any]] = field(default=None, repr=False)
    crypto_metadata: Optional[CryptoMetadata] = field(default=None, repr=False)
    correlation_id: Optional[str] = None
    trace_context: Optional[TraceContext] = None
    choreography_id: Optional[str] = None
    associated_effects: Optional[List[Any]] = field(default=None, repr=False)
    is_differentiable: bool = False
    payload_encoding: str = "application/json"
    custom_metadata: Dict[str, Any] = field(default_factory=dict)


@dataclass
class OptionalEventData(BaseEventData):
    """Base class with optional fields for event data payloads."""

    source_component: Optional[str] = None
    correlation_id: Optional[str] = None
    extra_data: Dict[str, Any] = field(default_factory=dict)
    timestamp: datetime = field(default_factory=datetime.now)


@dataclass(kw_only=True)
class TargetedEventData(BaseEventData):
    """Base class for events that target a specific component."""

    target_component: str


@dataclass(kw_only=True)
class ThrottlingEventData(BaseEventData):
    """Data structure for throttling events."""

    target_component: str
    reason: str
    source_component: Optional[str] = None
    correlation_id: Optional[str] = None
    throttle_level: Optional[float] = None
    duration_seconds: Optional[float] = None
    extra_data: Dict[str, Any] = field(default_factory=dict)
    details: Dict[str, Any] = field(default_factory=dict)
    timestamp: datetime = field(default_factory=datetime.now)


# --- Domain Specific Event Data ---


@dataclass(kw_only=True)
class MemoryEventData(BaseEventData):
    """Data structure for memory-related events."""

    operation_type: str
    details: Dict[str, Any] = field(default_factory=dict)


# --- Standard / Generic Event Data Types ---


@dataclass
class GenericEventData(BaseEventData):
    """General purpose event data."""

    event_type: Optional[str] = None
    payload: Optional[Any] = None
    details: Dict[str, Any] = field(default_factory=dict)


@dataclass
class ResourceEventData(BaseEventData):
    """Data structure for resource monitoring events."""

    resource_type: Optional[str] = None
    usage_level: Optional[float] = None
    threshold: Optional[float] = None
    units: Optional[str] = None
    details: Dict[str, Any] = field(default_factory=dict)


@dataclass
class AlertEventData(BaseEventData):
    """Data structure for system alerts."""

    alert_type: Optional[str] = None
    severity: Optional[str] = None
    message: Optional[str] = None
    details: Dict[str, Any] = field(default_factory=dict)


@dataclass(kw_only=True)
class ErrorEventData(BaseEventData):
    """Data structure for error events."""

    source_component: str
    error_message: str
    exception_type: Optional[str] = None
    traceback: Optional[str] = None
    error_id: Optional[str] = None
    severity: Optional[str] = None
    details: Dict[str, Any] = field(default_factory=dict)


@dataclass(kw_only=True)
class ConfigChangedEventData(BaseEventData):
    """Data structure for configuration change events."""

    section: str
    key: Optional[str] = None
    old_value: Optional[Any] = None
    new_value: Optional[Any] = None
    details: Dict[str, Any] = field(default_factory=dict)


@dataclass(kw_only=True)
class EmotionalStateEventData(BaseEventData):
    """Data structure for reporting emotional state."""

    active_emotions: Dict[str, int]
    top_emotions: List[Dict[str, Any]]
    dominant_emotion: Optional[Dict[str, Any]]
    mood: Dict[str, Any]
    request_id: Optional[str] = None
    details: Dict[str, Any] = field(default_factory=dict)


@dataclass(kw_only=True)
class ExpressionRequestEventData(BaseEventData):
    """Data structure for requesting an emotional expression."""

    expression_type: str
    content: str
    emotion: str
    intensity: int
    context: Optional[Dict[str, Any]] = None
    priority: Optional[int] = None
    details: Dict[str, Any] = field(default_factory=dict)


@dataclass(kw_only=True)
class ExpressionPerformedEventData(BaseEventData):
    """Data structure for reporting that an expression was performed."""

    displayed_emotion: Dict[str, Any]
    actual_modality: Optional[str] = None
    duration_seconds: Optional[float] = None
    success: bool = True
    details: Dict[str, Any] = field(default_factory=dict)


@dataclass
class PhysiologicalStateData(BaseEventData):
    """Data structure reporting simulated physiological state changes."""

    heart_rate: Optional[float] = None
    skin_conductance: Optional[float] = None
    respiration_rate: Optional[float] = None
    arousal_level_from_physiology: Optional[float] = None
    stress_indicator: Optional[float] = None
    details: Dict[str, Any] = field(default_factory=dict)


@dataclass(kw_only=True)
class EmotionFeelRequestData(BaseEventData):
    """Data structure for requesting an emotion to be felt."""

    emotion: str
    intensity: int
    source: Optional[str] = None
    context: Optional[Dict[str, Any]] = None
    social_context_id: Optional[str] = None
    details: Dict[str, Any] = field(default_factory=dict)


@dataclass
class PerceptionEventData(BaseEventData):
    """Data structure for perception input events."""

    content: Any
    content_type: str
    sensor_id: Optional[str] = None
    metadata: Dict[str, Any] = field(default_factory=dict)
    details: Dict[str, Any] = field(default_factory=dict)


@dataclass
class SocialContextUpdateData(BaseEventData):
    """Data structure for social context update events."""

    context_id: Optional[str] = None
    context_type: Optional[str] = None
    participants: List[str] = field(default_factory=list)
    update_details: Dict[str, Any] = field(default_factory=dict)
    details: Dict[str, Any] = field(default_factory=dict)


@dataclass
class DisplayedEmotionInfoData(BaseEventData):
    """Data structure carrying the detailed DisplayedEmotion generated by the EmotionManager."""

    displayed_emotion: Dict[str, Any]


@dataclass
class SecurityEventData(BaseEventData):
    """Data structure for security-related events."""

    event_type: str
    severity: int
    details: Dict[str, Any] = field(default_factory=dict)


@dataclass(kw_only=True)
class BottleneckEventData(BaseEventData):
    """Data structure for resource or system bottleneck events.

    Captures details about detected bottlenecks, including type, impacted components,
    analysis context, and severity. Used for monitoring, diagnostics, and adaptive
    response in resource and performance management subsystems.

    Args:
        bottleneck_type: The type/category of bottleneck (e.g., 'CPU', 'Memory', 'I/O').
        impacted_components: List of component names affected by the bottleneck.
        analysis_details: Additional analysis context or metrics (e.g., stack traces, stats).
        severity: Severity level of the bottleneck (e.g., 'critical', 'warning').
        resource_type: The resource involved (e.g., 'CPU', 'Memory', 'Disk').
        metric_value: The measured value that triggered the bottleneck event.
        threshold: The threshold value for the resource/metric.
        details: Additional details or context for the event.

    Example:
        event_data = BottleneckEventData(
            source_component="ResourceMonitor",
            bottleneck_type="CPU",
            impacted_components=["InferenceEngine", "MemoryManager"],
            analysis_details={"cpu_usage": 98.5, "duration": 12.3},
            severity="critical",
            resource_type="CPU",
            metric_value=98.5,
            threshold=95.0,
            details={"note": "Sustained high CPU usage detected."}
        )
    """

    bottleneck_type: str
    impacted_components: List[str] = field(default_factory=list)
    analysis_details: Dict[str, Any] = field(default_factory=dict)
    severity: Optional[str] = None
    resource_type: Optional[str] = None
    metric_value: Optional[float] = None
    threshold: Optional[float] = None
    details: Dict[str, Any] = field(default_factory=dict)


# --- Base Event Wrapper ---


@dataclass(frozen=True)
class BaseEvent:
    """Base wrapper class for all events..."""

    topic: EventTopic
    data: BaseEventData
    context: UnifiedContext
    event_id: UUID = field(default_factory=uuid4)

    @property
    def timestamp(self) -> datetime:
        return self.data.timestamp

    @property
    def source_component(self) -> Optional[str]:
        return self.data.source_component

    @property
    def correlation_id(self) -> Optional[str]:
        return self.data.correlation_id


# --- Event Priority Enum ---
class EventPriority(Enum):
    """Priority levels for event processing."""

    LOW = auto()
    NORMAL = auto()
    HIGH = auto()
    CRITICAL = auto()


# --- Event Manager Interface ---

EventType = TypeVar("EventType", bound=BaseEvent)
EventCallback = Callable[[EventType], Coroutine[Any, Any, None]]


@dataclass
class EventOptimizationConfig:
    """Configuration for event manager optimization."""

    batch_size: int = 1
    enable_m3: bool = False
    priority_queuing: bool = False


class IEventManager(Protocol[EventType]):
    """Interface for the event management system."""

    def subscribe(
        self, event_topic: EventTopic, callback: EventCallback[EventType]
    ) -> None:
        """Subscribe a callback function..."""
        ...

    async def publish(self, event: EventType) -> None:
        """Publish an event..."""
        ...

    def configure_optimization(self, config: EventOptimizationConfig) -> None:
        """Configure optimization settings..."""
        ...

    def unsubscribe(
        self, event_topic: EventTopic, callback: EventCallback[EventType]
    ) -> None:
        """Unsubscribe a callback..."""
        ...


# --- Enhanced Event Wrapper (Redefinition - Remove this) ---
# @dataclass(frozen=True)
# class BaseEvent:
#     ...

# --- Configuration for Event Bus (Redefinition - Remove this) ---
# @dataclass
# class EventOptimizationConfig:
#     ...

# --- Interfaces for Pluggable Components (Redefinition - Remove this) ---
# EventHandler = Callable[[BaseEvent], Awaitable[None]]
# EventFilter = Callable[[BaseEvent], bool]

# --- Example Concrete Event Definition ---


@dataclass
class SystemStatusData(BaseEventData):
    """Data for system status update events."""

    status: str
    message: Optional[str] = None
    component_name: Optional[str] = None


# Example Usage:
# status_data = SystemStatusData(source_component="core_bootstrap", status="STARTING")
# system_event = BaseEvent(topic=EventTopic.SYSTEM, data=status_data)
# print(system_event.event_type) # Output: SystemStatusData

# Keep a single translator instance
_information_translator = InformationTranslator(
    source=InformationSource(source_id="events_interface", source_type="system")
)


class EventData(BaseModel):
    """
    The "what" of an event, containing the core information payload.
    This class now standardizes all incoming data into an Infom object.
    """
    source: str
    infom: Infom = Field(description="The unified information payload of the event.")

    @field_validator('infom', mode='before')
    @classmethod
    def _validate_and_convert_to_infom(cls, v: Any) -> Infom:
        """
        Pydantic validator to automatically convert any input data into an Infom object.
        This is the core of the new unified information flow.
        """
        if isinstance(v, Infom):
            return v
        # Use the translator for any other data type (dict, other objects)
        return _information_translator.translate(v, to_type=Infom)

    class Config:
        arbitrary_types_allowed = True


class BaseEvent(BaseModel):
    """
    Base class for all events, defining the common structure (the "envelope").
    It ensures that every event has a standardized data payload.
    """
    event_id: str = Field(default_factory=lambda: str(uuid.uuid4()))
    timestamp: datetime = Field(default_factory=lambda: datetime.now(timezone.utc))
    data: EventData

    @field_validator('data', mode='before')
    @classmethod
    def _validate_and_wrap_data(cls, v: Any) -> EventData:
        """
        Ensures the payload is always a compliant EventData object,
        converting Infom or other types as needed.
        """
        if isinstance(v, EventData):
            return v

        # If raw Infom is passed, wrap it in EventData
        if isinstance(v, Infom):
            return EventData(source="unknown_wrapped", infom=v)

        # If a dictionary or other object is passed, let EventData's validator handle it
        if isinstance(v, dict) and 'source' in v and ('infom' in v or 'data' in v):
             # This handles the case where a dict representing EventData is passed
            infom_payload = v.get('infom', v.get('data'))
            return EventData(source=v['source'], infom=infom_payload)

        # Fallback for simple data types that need to be translated
        return EventData(source="unknown_translated", infom=v)


class Event(BaseEvent):
    """
A generic, concrete event class that can be used for most purposes.
Inherits the validation and structure from BaseEvent.
"""
    pass


# Example of a more specific event type
class SystemEvent(BaseEvent):
    """An event specifically related to system operations."""
    pass
---
"""
Core Dual-State Information Carrier (Infom)
===========================================

This module defines the `Infom` class, the universal carrier for information
within the Person Suit system. It embodies the Contextual Adaptive Wave (CAW)
principle of duality.

The `Infom` can exist in two states:
- A `Wave` state, representing a probabilistic distribution of potential information.
- A `Particle` state, representing a single, collapsed, concrete piece of
  information from the canonical `nodes` module.

This structure allows for a flexible and powerful representation of uncertainty
and potentiality, which can be resolved (collapsed) based on context.

Related Files:
- person_suit/shared/information/nodes.py: Defines the concrete node types
  that are carried by the Infom's Particle state.
"""
from __future__ import annotations

import uuid
from typing import TYPE_CHECKING, Any, Dict, Optional, Union
from datetime import datetime, timezone

import numpy as np
from pydantic import BaseModel, Field, field_validator

from .nodes import BaseNode, ConceptNode, DatomNode, InfonNode
from .sources import InformationSource

if TYPE_CHECKING:
    from person_suit.core.context.unified import UnifiedContext

# A type hint for any of the concrete node classes from our canonical schema
CanonicalNode = Union[InfonNode, DatomNode, ConceptNode]

class Particle(BaseModel):
    """
    Represents the 'particle' aspect of an Infom: a collapsed, concrete state.
    This is an actualized piece of information, grounded by a specific context,
    and it must contain one of the canonical node types.
    """
    data: CanonicalNode = Field(..., description="The concrete data payload, which must be a canonical node type.")
    context_id: uuid.UUID = Field(..., description="The ID of the UnifiedContext that triggered the collapse.")
    timestamp: float = Field(..., description="The timestamp of when the collapse occurred.")

    class Config:
        arbitrary_types_allowed = True

class Wave(BaseModel):
    """
    Represents the 'wave' aspect of an Infom: a superposition of potential states.
    """
    potentiality: float = Field(..., ge=0.0, le=1.0, description="The overall probability that this wave will collapse into any particle. A measure of certainty.")
    distribution: Dict[str, float] = Field(..., description="A probability distribution over potential outcomes. The keys can be identifiers for potential nodes and values are their probabilities.")
    embedding: Optional[np.ndarray] = Field(None, description="An optional vector embedding representing the semantic content of the wave.")

    class Config:
        arbitrary_types_allowed = True

    @field_validator('distribution')
    @classmethod
    def probabilities_must_sum_to_one(cls, v: Dict[str, float]) -> Dict[str, float]:
        """Ensures the probabilities in the distribution sum to approximately 1.0."""
        if not np.isclose(sum(v.values()), 1.0):
            raise ValueError("Probabilities in the distribution must sum to 1.0")
        return v

class Infom(BaseModel):
    """
    The core dual-state information unit.
    An Infom can have both wave and particle aspects simultaneously,
    embodying true quantum duality principles from CAW.
    """
    id: uuid.UUID = Field(default_factory=uuid.uuid4)
    wave: Optional[Wave] = None
    particle: Optional[Particle] = None

    @field_validator('particle')
    @classmethod
    def ensure_at_least_one_state(cls, v, info):
        """Ensures that at least one state (wave or particle) is present."""
        wave_value = info.data.get('wave') if info.data else None
        if v is None and wave_value is None:
            raise ValueError("An Infom must have at least one state (wave or particle).")
        return v

    def is_wave(self) -> bool:
        """Returns True if the Infom has a wave state."""
        return self.wave is not None

    def is_particle(self) -> bool:
        """Returns True if the Infom has a particle state."""
        return self.particle is not None
    
    def is_dual_state(self) -> bool:
        """Returns True if the Infom has both wave and particle states simultaneously."""
        return self.wave is not None and self.particle is not None

    def collapse(self, context: 'UnifiedContext', choice_override: Optional[str] = None) -> Infom:
        """
        Collapses a wave-state Infom into a particle-state Infom.
        If the Infom already has a particle state, it preserves it.

        The choice of which particle to collapse into is determined by the
        probability distribution in the wave state. This can be influenced by
        the provided context or overridden.

        Args:
            context: The UnifiedContext driving the collapse.
            choice_override: Optionally force a specific outcome by its key
                             in the distribution.

        Returns:
            A new Infom instance with a particle state (and optionally wave state preserved).
        """
        if self.is_particle() and not self.is_wave():
            # Already a pure particle, return copy
            return self.copy(deep=True)

        if not self.wave:
            raise ValueError("Cannot collapse an Infom with no wave state.")

        # In a real implementation, the context (e.g., its fidelity settings)
        # would influence the choice. Here we use a simple probabilistic choice.
        if choice_override:
            if choice_override not in self.wave.distribution:
                raise ValueError(f"Choice '{choice_override}' not in wave distribution.")
            chosen_key = choice_override
        else:
            outcomes = list(self.wave.distribution.keys())
            probabilities = list(self.wave.distribution.values())
            chosen_key = np.random.choice(outcomes, p=probabilities)

        # This is a placeholder. In a real scenario, you would fetch or create
        # the full CanonicalNode object based on the chosen_key.
        # For now, we create a placeholder node.
        # TODO: Replace this with a proper node retrieval/creation mechanism.
        placeholder_data = ConceptNode(
            name=f"collapsed_concept_{chosen_key}", 
            timestamp=context.timestamp
        )

        # Create new Infom with particle state, optionally preserving wave state
        return Infom(
            wave=self.wave if context.acf_setting.preserve_wave_on_collapse else None,
            particle=Particle(
                data=placeholder_data,
                context_id=context.id,
                timestamp=context.timestamp
            )
        )

class InformationFactory:
    """A factory for creating Infom objects from various data sources."""

    @staticmethod
    def from_dict(data: Dict[str, Any], source: 'InformationSource') -> 'Infom':
        """
        Creates an Infom object from a dictionary.

        This is a simple implementation that creates a particle-state Infom.
        A more advanced version would handle wave-state creation.
        """
        now = datetime.now(timezone.utc)
        particle_data = DatomNode(
            entity_id=data.get("entity_id", uuid.uuid4()),
            attribute=data.get("attribute", "default_attribute"),
            value=data.get("value", data),
            timestamp=data.get("timestamp", now.timestamp()),
            metadata={'source': source.source_id}
        )
        return Infom(
            particle=Particle(
                data=particle_data,
                context_id=uuid.uuid4(), # Placeholder context
                timestamp=now.timestamp()
            )
        )
---
#!/usr/bin/env python3
"""
Diagnostic Validation Test
Validates specific assumptions about Person Suit system failures
"""

import asyncio
import importlib.util
import logging
import sys
import time
import uuid
from pathlib import Path

# Set up logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger('diagnostic_validation')

class DiagnosticValidator:
    """Validates specific failure assumptions with targeted diagnostics"""
    
    def __init__(self):
        self.assumptions = []
        self.validation_results = {}
    
    async def run_diagnostics(self):
        """Run all diagnostic validations"""
        logger.info("🔬 Starting Diagnostic Validation Tests")
        logger.info("=" * 70)
        
        # Test each assumption
        await self._validate_dependency_issues()
        await self._validate_security_authorization()
        await self._validate_message_type_system()
        await self._validate_effects_authentication()
        await self._validate_bootstrap_resilience()
        await self._validate_unified_information_flow()
        await self._validate_dual_state_propagation()
        
        # Generate diagnostic report
        self._generate_diagnostic_report()
    
    async def _validate_dependency_issues(self):
        """ASSUMPTION 1: Missing aiohttp dependency causing actor failures"""
        logger.info("\n📋 ASSUMPTION 1: Missing aiohttp dependency")
        
        try:
            # Check if aiohttp is available
            aiohttp_available = importlib.util.find_spec('aiohttp') is not None
            logger.info(f"   aiohttp module available: {aiohttp_available}")
            
            if not aiohttp_available:
                logger.error("   ❌ VALIDATED: aiohttp dependency missing")
                self.validation_results['dependency_missing'] = True
                
                # Check requirements.txt
                req_path = Path('requirements.txt')
                if req_path.exists():
                    requirements = req_path.read_text()
                    has_aiohttp = 'aiohttp' in requirements
                    logger.info(f"   aiohttp in requirements.txt: {has_aiohttp}")
                    if not has_aiohttp:
                        logger.error("   ❌ aiohttp not listed in requirements.txt")
                else:
                    logger.warning("   ⚠️ requirements.txt not found")
            else:
                logger.info("   ✅ aiohttp dependency available")
                self.validation_results['dependency_missing'] = False
                
        except Exception as e:
            logger.error(f"   ❌ Dependency check failed: {e}")
            self.validation_results['dependency_missing'] = 'error'
    
    async def _validate_security_authorization(self):
        """ASSUMPTION 2: Security manager blocking legitimate operations"""
        logger.info("\n📋 ASSUMPTION 2: Security manager over-authorization")
        
        try:
            # Try to import and inspect security manager
            from person_suit.security import get_security_manager, SecurityLevel
            
            # Create security manager and test default behavior
            security_manager = get_security_manager()
            logger.info("   Security manager created successfully")
            
            # Test authorization for basic operations using the correct method
            test_channels = [
                "effects.state.read",
                "effects.state.write",
                "effects.database.read",
                "system.bootstrap"
            ]
            
            authorized_count = 0
            for channel in test_channels:
                try:
                    # Use the correct authorize_effect method with proper context
                    from person_suit.core.context.unified import UnifiedContext
                    from person_suit.core.security_types import Capability, CapabilityScope
                    
                    # Create a context with basic capabilities
                    test_context = UnifiedContext.create_default("security_test")
                    test_context.current_capability = Capability(
                        issuer="diagnostic_test",
                        subject="test_subject",
                        permissions={"effects.state.read", "effects.state.write", "effects.database.read", "system.bootstrap"},
                        scope=CapabilityScope(scope_type="system", resource_id="*")
                    )
                    
                    # Test authorization using the actual method
                    authorized = await security_manager.authorize_effect(
                        channel=channel, 
                        payload={}, 
                        context=test_context, 
                        security_level_override=SecurityLevel.MINIMAL
                    )
                    logger.info(f"   Authorization for {channel}: {authorized}")
                    if authorized:
                        authorized_count += 1
                        
                except Exception as e:
                    logger.warning(f"   Authorization test failed for {channel}: {e}", exc_info=True)
            
            # Validate assumption
            if authorized_count == 0:
                logger.error("   ❌ VALIDATED: Security manager blocks all operations")
                self.validation_results['security_blocking'] = True
            elif authorized_count < len(test_channels) / 2:
                logger.warning("   ⚠️ PARTIAL: Security manager blocks most operations")
                self.validation_results['security_blocking'] = 'partial'
            else:
                logger.info("   ✅ Security manager allows reasonable operations")
                self.validation_results['security_blocking'] = False
                
        except Exception as e:
            logger.error(f"   ❌ Security validation failed: {e}", exc_info=True)
            self.validation_results['security_blocking'] = 'error'
    
    async def _validate_message_type_system(self):
        """ASSUMPTION 3: Message type system has string vs enum bugs"""
        logger.info("\n📋 ASSUMPTION 3: Message type system bugs")
        
        try:
            # Try to reproduce the string vs enum error
            from person_suit.core.infrastructure.message_based_core import DecoupledMessage
            
            # Test creating a message with string type (the failing case)
            try:
                from person_suit.core.infrastructure.message_based_core import MessageType
                legacy_msg = DecoupledMessage(
                    message_type=MessageType.REQUEST,  # Use proper enum
                    source_service="diagnostic",
                    target_service="test",
                    operation="test_operation",
                    data={"test": "data"}
                )
                
                # Try to convert to hybrid message (where error occurs)
                hybrid_msg = legacy_msg.to_hybrid_message()
                logger.info("   ✅ Message type conversion succeeded")
                self.validation_results['message_type_bug'] = False
                
            except AttributeError as e:
                if "'str' object has no attribute 'name'" in str(e):
                    logger.error("   ❌ VALIDATED: String vs enum bug in message types")
                    self.validation_results['message_type_bug'] = True
                else:
                    logger.error(f"   ❌ Different message type error: {e}")
                    self.validation_results['message_type_bug'] = 'different_error'
            
        except Exception as e:
            logger.error(f"   ❌ Message type validation failed: {e}")
            self.validation_results['message_type_bug'] = 'error'
    
    async def _validate_effects_authentication(self):
        """ASSUMPTION 4: Effects system not properly authenticated"""
        logger.info("\n📋 ASSUMPTION 4: Effects authentication issues")
        
        try:
            # Try to initialize effects system and check authentication setup
            from person_suit.effects import MessageBasedEffectsAPI
            
            # Check if effects API has security manager configured
            api = MessageBasedEffectsAPI()
            
            # CRITICAL: Initialize the API to ensure security manager is loaded
            await api._ensure_initialized()
            
            has_security = hasattr(api, 'security_manager') and api.security_manager is not None
            logger.info(f"   Effects API has security manager: {has_security}")
            
            if hasattr(api, 'enhanced_security') and api.enhanced_security is not None:
                logger.info("   Effects API has enhanced security configured")
                
                # Try a simple operation to see auth behavior
                try:
                    # Create proper UnifiedContext instead of plain dict
                    from person_suit.core.context.unified import UnifiedContext
                    test_context = UnifiedContext.create_default("diagnostic_test")
                    
                    result = await api.execute_effect(
                        "effects.state.read",
                        {"key": "test"},
                        test_context
                    )
                    logger.info("   ✅ Effects operation succeeded")
                    self.validation_results['effects_auth_issue'] = False
                    
                except Exception as e:
                    if "authorization FAILED" in str(e) or "Authorization FAILED" in str(e):
                        logger.error("   ❌ VALIDATED: Effects authentication failing")
                        self.validation_results['effects_auth_issue'] = True
                    else:
                        logger.error(f"   ❌ Different effects error: {e}")
                        self.validation_results['effects_auth_issue'] = 'different_error'
            else:
                logger.warning("   ⚠️ Effects API security configuration unclear")
                self.validation_results['effects_auth_issue'] = 'unclear'
                
        except Exception as e:
            logger.error(f"   ❌ Effects authentication validation failed: {e}")
            self.validation_results['effects_auth_issue'] = 'error'
    
    async def _validate_bootstrap_resilience(self):
        """ASSUMPTION 5: Bootstrap not handling partial failures gracefully"""
        logger.info("\n📋 ASSUMPTION 5: Bootstrap resilience issues")
        
        try:
            # Check bootstrap behavior with intentional failures
            from person_suit.main import PersonSuitResilientBootstrap
            
            # Try to start system and observe failure handling
            person_suit = PersonSuitResilientBootstrap()
            
            logger.info("   Testing bootstrap failure handling...")
            
            try:
                await person_suit.initialize()
                logger.info("   ✅ Bootstrap succeeded completely")
                self.validation_results['bootstrap_resilience'] = False
                
            except Exception as e:
                error_msg = str(e)
                logger.error(f"   Bootstrap failed with: {error_msg}")
                
                # Check if failure was graceful or catastrophic
                if "CRITICAL" in error_msg or "system cannot start" in error_msg:
                    logger.error("   ❌ VALIDATED: Bootstrap fails catastrophically")
                    self.validation_results['bootstrap_resilience'] = True
                else:
                    logger.warning("   ⚠️ Bootstrap fails but may be graceful")
                    self.validation_results['bootstrap_resilience'] = 'partial'
            
            # Clean up
            try:
                await person_suit.shutdown()
            except Exception:
                pass  # Ignore shutdown errors during test
                
        except Exception as e:
            logger.error(f"   ❌ Bootstrap resilience validation failed: {e}", exc_info=True)
            self.validation_results['bootstrap_resilience'] = 'error'

    async def _validate_unified_information_flow(self):
        """ASSUMPTION 6: Events are not carrying unified Infom objects"""
        logger.info("\n📋 ASSUMPTION 6: Events carry unified Infom data")
        
        try:
            # Try to create an event with non-Infom data and see if it converts
            from person_suit.core.application.interfaces.events_interface import BaseEvent
            from person_suit.shared.information.core import Infom
            from person_suit.shared.information.nodes import DatomNode

            # Create an event with legacy dictionary data
            legacy_data = {
                "entity_id": str(uuid.uuid4()),
                "attribute": "legacy_attribute",
                "value": "test_data", 
                "timestamp": 12345.67
            }
            event = BaseEvent(
                data=legacy_data,
                source="diagnostic_test"
            )

            # Check if the data was converted to an Infom object
            if isinstance(event.data.infom, Infom):
                logger.info("   ✅ Event data successfully converted to Infom")
                
                # Check if the underlying data is a DatomNode
                if isinstance(event.data.infom.particle.data, DatomNode):
                    logger.info("   ✅ Infom contains a valid DatomNode")
                    self.validation_results['unified_information_flow'] = False
                else:
                    logger.error("   ❌ Infom does not contain a DatomNode")
                    self.validation_results['unified_information_flow'] = 'wrong_node_type'
            else:
                logger.error("   ❌ VALIDATED: Event data was not converted to Infom")
                self.validation_results['unified_information_flow'] = True
                
        except Exception as e:
            logger.error(f"   ❌ Unified information flow validation failed: {e}", exc_info=True)
            self.validation_results['unified_information_flow'] = 'error'

    async def _validate_dual_state_propagation(self):
        """ASSUMPTION 7: Dual-state (wave/particle) not propagated"""
        logger.info("\n📋 ASSUMPTION 7: Dual-state (wave/particle) propagation")
        
        try:
            # 1. Set up a mock listener on the message bus
            from person_suit.core.application.interfaces.events_interface import BaseEvent
            from person_suit.core.infrastructure.hybrid_message_bus import get_message_bus
            
            received_event = None
            
            async def listener_handler(event: BaseEvent):
                nonlocal received_event
                received_event = event
                logger.info(f"   Listener received event: {event.event_id}")

            bus = await get_message_bus()
            
            # Subscribe the listener to a specific event channel
            channel = "dual_state_test_channel"
            bus.subscribe(channel, listener_handler)
            logger.info(f"   Test listener subscribed to channel '{channel}'")

            # 2. Create a complex, dual-state Infom object
            from person_suit.shared.information.core import Infom, Wave, Particle
            from person_suit.shared.information.nodes import DatomNode
            
            particle_data = DatomNode(
                entity_id=uuid.uuid4(),
                attribute="test_attribute",
                value="This is a concrete fact.",
                timestamp=123.456
            )
            wave_data = {
                "potential_meaning_1": 0.8,
                "potential_meaning_2": 0.2,
            }
            infom = Infom(
                wave=Wave(potentiality=0.9, distribution=wave_data),
                particle=Particle(data=particle_data, context_id=uuid.uuid4(), timestamp=123.456)
            )

            # 3. Publish an event with this Infom
            from person_suit.core.context.unified import UnifiedContext
            from person_suit.core.application.interfaces.events_interface import Event, EventData
            
            event_to_publish = Event(
                data=EventData(source="dual_state_test", infom=infom)
            )
            
            # Send via message bus
            from person_suit.core.infrastructure.hybrid_message import HybridMessage
            hybrid_message = HybridMessage(
                channel=channel,
                payload=event_to_publish.model_dump(),
                context=UnifiedContext.create_default("dual_state_test")
            )
            await bus.send(hybrid_message)
            
            # 4. Wait for the listener to receive the event
            await asyncio.sleep(0.1) # Allow time for processing
            
            # 5. Validate the received event
            if received_event:
                logger.info("   ✅ Listener received an event")
                
                # Re-create the event object from the received payload
                from pydantic import ValidationError
                try:
                    received_event_obj = Event.model_validate(received_event.payload)
                    
                    if received_event_obj.data.infom.is_dual_state():
                        logger.info("   ✅ Received event has dual-state Infom")
                        self.validation_results['dual_state_propagation'] = False
                    else:
                        logger.error("   ❌ Received event Infom is not dual-state")
                        self.validation_results['dual_state_propagation'] = 'state_lost'
                        
                except ValidationError as e:
                    logger.error(f"   ❌ Received event payload failed validation: {e}")
                    self.validation_results['dual_state_propagation'] = 'validation_error'
                    
            else:
                logger.error("   ❌ VALIDATED: Listener did not receive the event")
                self.validation_results['dual_state_propagation'] = True

        except Exception as e:
            logger.error(f"   ❌ Dual-state propagation validation failed with an exception: {e}", exc_info=True)
            self.validation_results['dual_state_propagation'] = 'error'

    def _generate_diagnostic_report(self):
        """Generate a summary report of the diagnostic results"""
        logger.info("\n" + "=" * 70)
        logger.info("🎯 DIAGNOSTIC VALIDATION RESULTS")
        logger.info("=" * 70)

        validated = [k for k, v in self.validation_results.items() if v is True]
        partial = [k for k, v in self.validation_results.items() if v == 'partial']
        rejected = [k for k, v in self.validation_results.items() if v is False]
        errors = [k for k, v in self.validation_results.items() if isinstance(v, str) and v.endswith('error')]

        logger.info(f"\n✅ VALIDATED ASSUMPTIONS ({len(validated)}):")
        for assumption in validated:
            logger.info(f"   • {assumption}")

        logger.info(f"\n⚠️ PARTIAL ASSUMPTIONS ({len(partial)}):")
        for assumption in partial:
            logger.info(f"   • {assumption}")

        logger.info(f"\n❌ REJECTED ASSUMPTIONS ({len(rejected)}):")
        for assumption in rejected:
            logger.info(f"   • {assumption}")

        logger.info(f"\n🔧 ERROR ASSUMPTIONS ({len(errors)}):")
        for assumption in errors:
            logger.info(f"   • {assumption}")

        logger.info("\n🎯 PRIORITY FIX RECOMMENDATIONS:")
        if 'security_blocking' in validated:
            logger.info("   1. CRITICAL: Security manager is blocking all operations.")
        elif 'security_blocking' in partial:
            logger.info("   2. CRITICAL: Fix security manager over-authorization")
        if 'unified_information_flow' in validated or 'unified_information_flow' in errors:
            logger.info("   CRITICAL: The information flow unification has failed. Events are not carrying Infom objects.")
        if 'dual_state_propagation' in validated or 'dual_state_propagation' in errors:
            logger.info("   CRITICAL: An error occurred during the dual-state propagation test.")

async def main():
    """Main entry point for the diagnostic script"""
    validator = DiagnosticValidator()
    await validator.run_diagnostics()

if __name__ == "__main__":
    asyncio.run(main()) 