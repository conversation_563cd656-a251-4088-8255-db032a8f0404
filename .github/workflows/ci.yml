name: CI/CD Pipeline

on:
  push:
    branches: [ main, develop ]
  pull_request:
    branches: [ main, develop ]

env:
  PYTHON_VERSION: "3.11"

jobs:
  # ============================================================================
  # Architectural Compliance and Code Quality
  # ============================================================================
  lint-and-compliance:
    name: Lint & Architectural Compliance
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v4
      
      - name: Set up Python
        uses: actions/setup-python@v4
        with:
          python-version: ${{ env.PYTHON_VERSION }}
          
      - name: Cache dependencies
        uses: actions/cache@v3
        with:
          path: ~/.cache/pip
          key: ${{ runner.os }}-pip-${{ hashFiles('requirements/*.txt') }}
          
      - name: Install dependencies
        run: |
          python -m pip install --upgrade pip
          pip install -r requirements/core.txt
          pip install -r requirements/dev.txt
          
      - name: Install package in development mode
        run: pip install -e .
        
      - name: Run Ruff linting
        run: ruff check person_suit/ tests/ --output-format=github
        
      - name: Run Ruff formatting check
        run: ruff format --check person_suit/ tests/
        
      - name: Check PS-NO-DIRECT-IO architectural compliance
        run: python tools/ruff_plugins/ps_no_direct_io.py person_suit/services/ person_suit/meta_systems/
        
      - name: Check for legacy imports
        run: ./scripts/check_no_legacy_imports.sh
        
      - name: Type checking with mypy
        run: mypy person_suit/ --ignore-missing-imports
        continue-on-error: true  # Allow mypy to fail for now

  # ============================================================================
  # Core Infrastructure Tests
  # ============================================================================
  test-core:
    name: Core Infrastructure Tests
    runs-on: ubuntu-latest
    strategy:
      matrix:
        python-version: ["3.8", "3.9", "3.10", "3.11"]
    steps:
      - uses: actions/checkout@v4
      
      - name: Set up Python ${{ matrix.python-version }}
        uses: actions/setup-python@v4
        with:
          python-version: ${{ matrix.python-version }}
          
      - name: Cache dependencies
        uses: actions/cache@v3
        with:
          path: ~/.cache/pip
          key: ${{ runner.os }}-${{ matrix.python-version }}-pip-${{ hashFiles('requirements/*.txt') }}
          
      - name: Install dependencies
        run: |
          python -m pip install --upgrade pip
          pip install -r requirements/core.txt
          pip install -r requirements/dev.txt
          
      - name: Install package
        run: pip install -e .
        
      - name: Run core infrastructure tests
        run: make test_core
        
      - name: Run services layer tests
        run: make test_services
        
      - name: Run fast integration tests
        run: make test_integration_fast

  # ============================================================================
  # Security and Vulnerability Scanning
  # ============================================================================
  security-scan:
    name: Security Scanning
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v4
      
      - name: Set up Python
        uses: actions/setup-python@v4
        with:
          python-version: ${{ env.PYTHON_VERSION }}
          
      - name: Install dependencies
        run: |
          python -m pip install --upgrade pip
          pip install safety bandit semgrep
          
      - name: Run safety check for dependencies
        run: safety check --json || true
        
      - name: Run bandit security linter
        run: bandit -r person_suit/ -f json || true
        
      - name: Run semgrep security analysis
        run: semgrep --config=auto person_suit/ || true

  # ============================================================================
  # Performance and Load Testing
  # ============================================================================
  performance-tests:
    name: Performance Tests
    runs-on: ubuntu-latest
    if: github.event_name == 'push' || github.event.pull_request.base.ref == 'main'
    steps:
      - uses: actions/checkout@v4
      
      - name: Set up Python
        uses: actions/setup-python@v4
        with:
          python-version: ${{ env.PYTHON_VERSION }}
          
      - name: Install dependencies
        run: |
          python -m pip install --upgrade pip
          pip install -r requirements/core.txt
          pip install -r requirements/dev.txt
          
      - name: Install package
        run: pip install -e .
        
      - name: Run hybrid bus diagnostics
        run: python scripts/diagnostics/hybrid_bus_diagnostics.py
        
      - name: Run ACF performance tests
        run: pytest tests/performance/ -v || true
        
      - name: Run load tests
        run: make test_load || true

  # ============================================================================
  # Integration Tests with Docker
  # ============================================================================
  integration-tests:
    name: Integration Tests
    runs-on: ubuntu-latest
    if: github.event_name == 'push'
    services:
      redis:
        image: redis:7-alpine
        ports:
          - 6379:6379
      postgres:
        image: postgres:15-alpine
        env:
          POSTGRES_PASSWORD: testpass
          POSTGRES_DB: person_suit_test
        ports:
          - 5432:5432
        options: >-
          --health-cmd pg_isready
          --health-interval 10s
          --health-timeout 5s
          --health-retries 5
    steps:
      - uses: actions/checkout@v4
      
      - name: Set up Python
        uses: actions/setup-python@v4
        with:
          python-version: ${{ env.PYTHON_VERSION }}
          
      - name: Install dependencies
        run: |
          python -m pip install --upgrade pip
          pip install -r requirements/core.txt
          pip install -r requirements/dev.txt
          
      - name: Install package
        run: pip install -e .
        
      - name: Run full integration tests
        run: make test_integration_full
        env:
          REDIS_URL: redis://localhost:6379
          DATABASE_URL: postgresql://postgres:testpass@localhost:5432/person_suit_test

  # ============================================================================
  # Coverage Analysis
  # ============================================================================
  coverage:
    name: Coverage Analysis
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v4
      
      - name: Set up Python
        uses: actions/setup-python@v4
        with:
          python-version: ${{ env.PYTHON_VERSION }}
          
      - name: Install dependencies
        run: |
          python -m pip install --upgrade pip
          pip install -r requirements/core.txt
          pip install -r requirements/dev.txt
          pip install pytest-cov
          
      - name: Install package
        run: pip install -e .
        
      - name: Run tests with coverage
        run: make coverage_full
        
      - name: Upload coverage to Codecov
        uses: codecov/codecov-action@v3
        with:
          file: ./coverage.xml
          fail_ci_if_error: false

  # ============================================================================
  # Build and Package
  # ============================================================================
  build:
    name: Build Package
    runs-on: ubuntu-latest
    if: github.event_name == 'push'
    steps:
      - uses: actions/checkout@v4
      
      - name: Set up Python
        uses: actions/setup-python@v4
        with:
          python-version: ${{ env.PYTHON_VERSION }}
          
      - name: Install build dependencies
        run: |
          python -m pip install --upgrade pip
          pip install build twine
          
      - name: Build package
        run: python -m build
        
      - name: Check package
        run: twine check dist/*
        
      - name: Upload build artifacts
        uses: actions/upload-artifact@v3
        with:
          name: dist-packages
          path: dist/

  # ============================================================================
  # Docker Build and Test
  # ============================================================================
  docker:
    name: Docker Build
    runs-on: ubuntu-latest
    if: github.event_name == 'push'
    steps:
      - uses: actions/checkout@v4
      
      - name: Set up Docker Buildx
        uses: docker/setup-buildx-action@v2
        
      - name: Build Docker image
        uses: docker/build-push-action@v4
        with:
          context: .
          file: ./Dockerfile
          push: false
          tags: person-suit:test
          cache-from: type=gha
          cache-to: type=gha,mode=max
          
      - name: Test Docker image
        run: |
          docker run --rm person-suit:test python -c "import person_suit; print('✅ Package imports successfully')"

  # ============================================================================
  # System Verification
  # ============================================================================
  verify-system:
    name: System Verification
    runs-on: ubuntu-latest
    needs: [lint-and-compliance, test-core]
    steps:
      - uses: actions/checkout@v4
      
      - name: Set up Python
        uses: actions/setup-python@v4
        with:
          python-version: ${{ env.PYTHON_VERSION }}
          
      - name: Install dependencies
        run: |
          python -m pip install --upgrade pip
          pip install -r requirements/core.txt
          
      - name: Install package
        run: pip install -e .
        
      - name: Verify core infrastructure
        run: make verify_core_infrastructure
        
      - name: Verify core services
        run: make verify_core_services
        
      - name: Verify meta-systems (optional)
        run: make verify_meta_systems || true
        
      - name: Complete system verification
        run: make verify