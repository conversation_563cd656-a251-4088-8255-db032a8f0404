name: Pre-commit guard

on:
  pull_request:
  push:
    branches: [ main ]

jobs:
  pre-commit:
    runs-on: ubuntu-latest
    steps:
      - name: Checkout repository
        uses: actions/checkout@v4

      - name: Set up Python
        uses: actions/setup-python@v5
        with:
          python-version: '3.11'

      - name: Install pre-commit and project deps
        run: |
          python -m pip install --upgrade pip
          pip install pre-commit

      - name: Run pre-commit on all files
        run: |
          pre-commit run --all-files 