#!/usr/bin/env python3
"""
Minimal bootstrap test to isolate import issues.
"""

import asyncio
import logging
import sys
import time

# Set up basic logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

async def minimal_bootstrap():
    """Minimal bootstrap that avoids problematic imports."""
    logger.info("🚀 Starting minimal bootstrap test...")
    
    try:
        # Test 1: Basic system setup
        logger.info("Phase 1: Testing system setup...")
        from person_suit.core.infrastructure.system_setup import install_optional_torch_stub
        install_optional_torch_stub()
        logger.info("✅ System setup OK")
        
        # Test 2: Config without circular imports
        logger.info("Phase 2: Testing config...")
        from person_suit.core.config.loader import load_config
        config = load_config()
        logger.info(f"✅ Config loaded: {len(config)} keys")
        
        # Test 3: Constants
        logger.info("Phase 3: Testing constants...")
        from person_suit.core.constants.fixed_point_scale import PRIO_CRITICAL
        logger.info(f"✅ Constants OK: PRIO_CRITICAL={PRIO_CRITICAL}")
        
        # Test 4: Message infrastructure (without bus)
        logger.info("Phase 4: Testing message infrastructure...")
        from person_suit.core.infrastructure.hybrid_message import HybridMessage, MessageType
        test_msg = HybridMessage(
            channel="test.channel",
            payload={"test": "data"},
            message_type=MessageType.EVENT
        )
        logger.info(f"✅ Message infrastructure OK: {test_msg.message_id}")
        
        logger.info("✅ Minimal bootstrap completed successfully!")
        return True
        
    except Exception as e:
        logger.error(f"❌ Minimal bootstrap failed: {e}", exc_info=True)
        return False

async def main():
    """Main entry point."""
    success = await minimal_bootstrap()
    if success:
        logger.info("🎉 All tests passed!")
        return 0
    else:
        logger.error("💥 Tests failed!")
        return 1

if __name__ == "__main__":
    sys.exit(asyncio.run(main()))
