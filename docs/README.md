# PersonSuit Documentation

> **File Purpose**: This README provides an overview of the documentation for the PersonSuit project, including architecture, implementation, and advanced concepts.
>
> **Last Updated**: 27.04.2025
>
> **Related Documents**:
> - [CAW_CORE_PRINCIPLES_AND_FOUNDATIONS.md](./CAW_CORE_PRINCIPLES_AND_FOUNDATIONS.md) - Comprehensive CAW v0.3 Principles and Foundations
> - [IMPLEMENTATION_GUIDE.md](../IMPLEMENTATION_GUIDE.md) - Official Implementation Sequence & Priorities
> - [architecture/ARCHITECTURE_OVERVIEW.md](./architecture/ARCHITECTURE_OVERVIEW.md) - System Architecture Overview

## Table of Contents

1. [Overview](#overview)
2. [Architecture](#architecture)
3. [Implementation Status & Guide](#implementation-status--guide)
4. [Core Concepts (CAW v0.3)](#core-concepts-caw-v03)
5. [Advanced Mathematical Structures](#advanced-mathematical-structures)

## Overview

This directory serves as the entry point for documentation related to the PersonSuit project. It covers the system's architecture, implementation status, core concepts (primarily the Contextual Adaptive Wave paradigm v0.3), and advanced mathematical underpinnings.

**Key Documentation Entry Points:**

- **Architecture:** [architecture/ARCHITECTURE_OVERVIEW.md](./architecture/ARCHITECTURE_OVERVIEW.md)
- **Implementation Sequence & Priorities:** [IMPLEMENTATION_GUIDE.md](../IMPLEMENTATION_GUIDE.md) (Single Source of Truth for sequence)
- **Core CAW Principles & Foundations (v0.3):** [CAW_CORE_PRINCIPLES_AND_FOUNDATIONS.md](./CAW_CORE_PRINCIPLES_AND_FOUNDATIONS.md)

## Architecture

The [architecture](./architecture/) directory details the system's design. Start with the [ARCHITECTURE_OVERVIEW.md](./architecture/ARCHITECTURE_OVERVIEW.md) for a high-level view of the meta-systems (Persona Core, Analyst, Predictor), the Folded_Mind cognitive model, memory architecture, and how they integrate under the CAW paradigm.

## Implementation Status & Guide

The primary document outlining the **official implementation sequence, phases, and current priorities** is:

- **[IMPLEMENTATION_GUIDE.md](../IMPLEMENTATION_GUIDE.md)** (Single Source of Truth)

For detailed status updates on specific components, refer to:

- [IMPLEMENTATION_STATUS.md](../IMPLEMENTATION_STATUS.md) (Focuses on PC-2-NG Memory Consolidation)
- [IMPLEMENTATION_PROGRESS.md](../IMPLEMENTATION_PROGRESS.md) (Focuses on AN-1, AN-2, EC-1)

The [implementation](./implementation/) directory contains further details, including guides and status reports. Conceptual deep dives related to the core principles are now integrated into the [CAW_CORE_PRINCIPLES_AND_FOUNDATIONS.md](./CAW_CORE_PRINCIPLES_AND_FOUNDATIONS.md) document.

## Core Concepts (CAW v0.3)

The PersonSuit project is built upon the **Contextual Adaptive Wave (CAW) paradigm (v0.3)**. This unified framework integrates advanced computational concepts to handle complexity, uncertainty, and resource constraints.

The 13 core principles, along with detailed conceptual deep dives, are now consolidated in [CAW_CORE_PRINCIPLES_AND_FOUNDATIONS.md](./CAW_CORE_PRINCIPLES_AND_FOUNDATIONS.md):

1.  **Dual Wave-Particle Information**: The fundamental substrate.
2.  **Contextual Computation**: The fundamental operational mode.
3.  **Adaptive Computational Fidelity (ACF)**: The fundamental regulatory mechanism.
4.  **Concurrent Reactive Entities (CAW Actors)**: CAW's realization of concurrency.
5.  **Coordinated Interaction Protocols (CAW Choreographies)**: CAW's mechanism for coherent multi-entity behavior.
6.  **Fine-Grained Access Control (CAW Capabilities)**: CAW's intrinsic security model.
7.  **Explicit Effect Management (CAW Effects)**: Operational consequences made explicit.
8.  **Pervasive Differentiable Optimization**: Integrated gradient-based learning.
9.  **Inherent Probabilistic Reasoning**: Embracing uncertainty.
10. **Integrated Formal Verification**: Built-in correctness assurance.
11. **Physics-Inspired Dynamics**: Computational metaphors from physics.
12. **Symmetry and Conservation**: Invariance principles.
13. **Conceptual Spacetime**: The substrate for information evolution.

## Advanced Mathematical Structures

Beyond standard vectors, PersonSuit leverages advanced mathematical structures, detailed in [advanced/mathematics/ADVANCED_MATHEMATICAL_STRUCTURES.md](./advanced/mathematics/ADVANCED_MATHEMATICAL_STRUCTURES.md):

- Tensors
- Symmetric Transformations
- Hypergraphs
- Topological Spaces
- (Implicitly) Geometric Algebra, Category Theory (mentioned in CAW Principles)

## Choreography Quick Links

* **Tutorial:** [docs/choreographies/GETTING_STARTED.md](choreographies/GETTING_STARTED.md)
* **CLI Reference:** `python -m person_suit.choreography.cli --help`
* **Grafana Dashboard JSON:** [docs/monitoring/choreography_dashboard.json](monitoring/choreography_dashboard.json)
