{"uid": "choreography-dashboard", "title": "Choreography Overview", "schemaVersion": 36, "version": 1, "time": {"from": "now-15m", "to": "now"}, "refresh": "30s", "panels": [{"type": "timeseries", "title": "Step Throughput (msg/s)", "gridPos": {"x": 0, "y": 0, "w": 12, "h": 8}, "targets": [{"expr": "rate(choreography_step_executions_total[1m])", "legendFormat": "{{choreography_id}} {{step_name}}", "refId": "A"}]}, {"type": "heatmap", "title": "Step Latency (ms)", "gridPos": {"x": 12, "y": 0, "w": 12, "h": 8}, "targets": [{"expr": "histogram_quantile(0.95, sum by (le, choreography_id, step_name)(rate(choreography_step_latency_seconds_bucket[2m])))", "format": "heatmap", "legendFormat": "{{choreography_id}} {{step_name}}", "refId": "B"}], "heatmap": {"colorScale": "sqrt", "dataFormat": "tsbuckets"}}, {"type": "timeseries", "title": "Workflow Latency p95 (s)", "gridPos": {"x": 0, "y": 8, "w": 12, "h": 8}, "targets": [{"expr": "histogram_quantile(0.95, sum by (le, choreography_id)(rate(choreography_workflow_latency_seconds_bucket[2m])))", "legendFormat": "{{choreography_id}}", "refId": "C"}]}, {"type": "stat", "title": "Active Workflows", "gridPos": {"x": 12, "y": 8, "w": 3, "h": 4}, "targets": [{"expr": "count_over_time(sys_workflow_started[5m]) - count_over_time(sys_workflow_finished[5m])", "refId": "D"}]}, {"type": "stat", "title": "Workflows Started (5m)", "gridPos": {"x": 15, "y": 8, "w": 4, "h": 4}, "targets": [{"expr": "increase(choreography_workflows_started_total[5m])", "refId": "E"}]}]}