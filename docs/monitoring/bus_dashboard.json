{"uid": "bus-security-provenance-dashboard", "title": "Bus & Security Overview", "schemaVersion": 36, "version": 1, "time": {"from": "now-15m", "to": "now"}, "refresh": "30s", "panels": [{"type": "stat", "title": "Provenance Loss (5m)", "gridPos": {"x": 0, "y": 0, "w": 6, "h": 4}, "targets": [{"expr": "increase(provenance_loss_total[5m])", "refId": "A"}], "options": {"colorMode": "value", "graphMode": "none", "justifyMode": "center"}}, {"type": "stat", "title": "Security Denials (5m)", "gridPos": {"x": 6, "y": 0, "w": 6, "h": 4}, "targets": [{"expr": "increase(security_denials_total[5m])", "refId": "B"}], "options": {"colorMode": "value", "graphMode": "none", "justifyMode": "center"}}, {"type": "timeseries", "title": "Provenance Loss Rate (msg/s)", "gridPos": {"x": 0, "y": 4, "w": 12, "h": 8}, "targets": [{"expr": "rate(provenance_loss_total[1m])", "legendFormat": "loss_rate", "refId": "C"}]}, {"type": "timeseries", "title": "Security Denials Rate (msg/s)", "gridPos": {"x": 0, "y": 12, "w": 12, "h": 8}, "targets": [{"expr": "rate(security_denials_total[1m])", "legendFormat": "denials_rate", "refId": "D"}]}]}