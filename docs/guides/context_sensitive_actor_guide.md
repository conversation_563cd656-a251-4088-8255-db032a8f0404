# Context-Sensitive Actor Guide

> **File Purpose**: This guide provides detailed instructions and best practices for implementing context-sensitive actors using the CAW paradigm.
>
> **Last Updated**: 2023-07-15
>
> **Related Documents**:
> - [CAW Paradigm Overview](../future/unified_paradigm/v0.3/overview.md)
> - [Actor System Documentation](../implementation/actor_system.md)
> - [Wave Function Guide](../guides/wave_function_guide.md)

## Table of Contents

1. [Introduction](#introduction)
2. [Understanding Context in CAW](#understanding-context-in-caw)
3. [Implementing a DecoupledActor](#implementing-a-cawactor)
4. [Context-Sensitive Behavior Patterns](#context-sensitive-behavior-patterns)
5. [Helper Methods for Context Sensitivity](#helper-methods-for-context-sensitivity)
6. [Advanced Techniques](#advanced-techniques)
7. [Best Practices](#best-practices)
8. [Examples](#examples)
9. [Troubleshooting](#troubleshooting)

## Introduction

Context-sensitive actors are a core component of the Contextual Adaptive Wave (CAW) paradigm. They enable adaptive behavior based on the current context, allowing for more flexible and responsive systems. This guide will help you implement effective context-sensitive actors using the `DecoupledActor` class and related components.

## Understanding Context in CAW

In the CAW paradigm, a context is represented by the `Context` class, which has the following key attributes:

- **domain**: The domain or category of the context (e.g., "critical", "background", "query")
- **priority**: The priority level of the context (e.g., "urgent", "normal", "background")
- **constraints**: A list of constraints or requirements for the context (e.g., "real-time", "low-memory", "high-throughput")
- **properties**: Additional properties specific to the context

Contexts influence how information is interpreted and processed. The same message can be processed differently depending on the context, allowing for adaptive behavior.

## Implementing a DecoupledActor

To implement a context-sensitive actor, follow these steps:

1. **Subclass DecoupledActor**:
   ```python
   from person_suit.core.infrastructure.actors.caw_actor import DecoupledActor
   
   class MyContextSensitiveActor(DecoupledActor):
       def __init__(self):
           super().__init__()
           # Initialize actor-specific state
   ```

2. **Implement _default_process_in_context**:
   ```python
   async def _default_process_in_context(
       self,
       message: Any,
       processing_context: Context,
       actor_context: ActorContext,
       sender_token: Optional[Any] = None
   ) -> Optional[Any]:
       # Process the message based on the context
       if processing_context.domain == "critical":
           return await self._handle_critical_message(message)
       elif processing_context.domain == "background":
           return await self._handle_background_message(message)
       else:
           return await self._handle_default_message(message)
   ```

3. **Register Wave Functions** (optional but recommended):
   ```python
   def __init__(self):
       super().__init__()
       
       # Register wave functions for message types
       self.create_simple_wave_function(
           message_type=TextMessage,
           domain_match_amplitude=1.0,
           priority_match_amplitude=0.8,
           default_amplitude=0.5
       )
   ```

4. **Implement Context-Specific Handlers**:
   ```python
   async def _handle_critical_message(self, message: Any) -> Any:
       # Handle message in critical context
       # Prioritize speed and reliability
       
   async def _handle_background_message(self, message: Any) -> Any:
       # Handle message in background context
       # Prioritize efficiency and throughput
   ```

## Context-Sensitive Behavior Patterns

Here are common patterns for implementing context-sensitive behavior:

### 1. Context-Based Dispatch

Route messages to different handlers based on the context:

```python
async def _default_process_in_context(self, message, processing_context, actor_context, sender_token):
    # Use the select_handler_for_context helper method
    handler = self.select_handler_for_context(
        processing_context,
        {
            "critical": self._handle_critical,
            "background": self._handle_background,
            "default": self._handle_default
        }
    )
    
    if handler:
        return await handler(message)
    else:
        return None
```

### 2. Context-Sensitive State Management

Store and retrieve state that is specific to the current context:

```python
# Store context-specific state
self.set_context_sensitive_state("last_message", message.content, processing_context)

# Retrieve context-specific state
last_message = self.get_context_sensitive_state("last_message", processing_context)
```

### 3. Behavior Adaptation

Adapt behavior parameters based on the context:

```python
# Define default behavior
behavior = {
    "max_retries": 3,
    "timeout": 60,
    "batch_size": 1
}

# Adapt behavior based on context
adapted_behavior = self.adapt_behavior_to_context(behavior, processing_context)

# Use adapted behavior
max_retries = adapted_behavior["max_retries"]
timeout = adapted_behavior["timeout"]
```

### 4. Context-Sensitive Responses

Create responses that are tailored to the current context:

```python
# Create a context-sensitive response
response = self.create_context_sensitive_response(message, processing_context)

# Add message-specific information
response["message_type"] = type(message).__name__
response["content"] = message.content

return response
```

## Helper Methods for Context Sensitivity

The `DecoupledActor` class provides several helper methods for implementing context-sensitive behavior:

### Context Matching

```python
# Check if a context matches specific criteria
is_critical = self.is_context_match(
    processing_context,
    domain="critical",
    priority="urgent",
    constraints=["real-time"]
)
```

### Context Property Access

```python
# Get a property from a context with a default value
timeout = self.get_context_property(processing_context, "timeout", default=60)
```

### Handler Selection

```python
# Select a handler based on the context domain
handler = self.select_handler_for_context(
    processing_context,
    {
        "critical": self._handle_critical,
        "background": self._handle_background,
        "default": self._handle_default
    }
)
```

### Action Prioritization

```python
# Prioritize actions based on the context
actions = self.prioritize_actions(
    processing_context,
    [
        {"domain": "critical", "priority": "urgent", "action": "notify"},
        {"domain": "background", "priority": "low", "action": "log"},
        {"domain": "query", "priority": "normal", "action": "respond"}
    ]
)
```

### Context-Sensitive Response Creation

```python
# Create a context-sensitive response
response = self.create_context_sensitive_response(message, processing_context)
```

### Context-Sensitive State Management

```python
# Set context-sensitive state
self.set_context_sensitive_state("last_message", message.content, processing_context)

# Get context-sensitive state
last_message = self.get_context_sensitive_state("last_message", processing_context)
```

### Behavior Adaptation

```python
# Adapt behavior based on context
adapted_behavior = self.adapt_behavior_to_context(
    {
        "max_retries": 3,
        "timeout": 60,
        "batch_size": 1
    },
    processing_context
)
```

### Wave Function Creation

```python
# Create a simple wave function for a message type
self.create_simple_wave_function(
    message_type=TextMessage,
    domain_match_amplitude=1.0,
    priority_match_amplitude=0.8,
    default_amplitude=0.5
)
```

## Advanced Techniques

### 1. Custom Wave Functions

Create custom wave functions for more sophisticated context matching:

```python
def custom_amplitude_func(message, context):
    # Custom amplitude calculation based on message and context
    if hasattr(message, 'importance') and callable(message.importance):
        importance = message.importance()
        if importance == "high" and context.priority == "urgent":
            return 1.0
        elif importance == "medium" and context.priority == "normal":
            return 0.8
        else:
            return 0.5
    return 0.5

def custom_phase_func(message, context):
    # Custom phase calculation based on message and context
    if hasattr(message, 'category') and callable(message.category):
        category = message.category()
        if category == context.domain:
            return 0.0
        else:
            return math.pi / 2
    return math.pi / 4

# Create and register the wave function
wave_function = WaveFunction(
    amplitude_func=custom_amplitude_func,
    phase_func=custom_phase_func
)
self.register_wave_function(CustomMessage, wave_function)
```

### 2. Context Switching

Dynamically switch contexts based on message content or other factors:

```python
async def _default_process_in_context(self, message, processing_context, actor_context, sender_token):
    # Check if we need to switch context based on message content
    if hasattr(message, 'is_urgent') and callable(message.is_urgent) and message.is_urgent():
        # Get the critical context
        critical_context = actor_context.context_registry.get_context("critical")
        if critical_context:
            # Process the message in the critical context instead
            return await self.process_in_context(message, critical_context, actor_context, sender_token)
    
    # Process normally in the current context
    # ...
```

### 3. Context-Sensitive Effect Tracking

Use the `context_effects` decorator to track effects in different contexts:

```python
from person_suit.core.infrastructure.effects.context_effects import context_effects
from person_suit.core.infrastructure.effects.core import EffectType

@context_effects([EffectType.STATE_CHANGE, EffectType.MESSAGING])
async def _handle_critical_message(self, message, context):
    # Handle message in critical context
    # The effects will be tracked with context information
```

### 4. Context-Based Capability Checks

Perform capability checks based on the current context:

```python
async def _default_process_in_context(self, message, processing_context, actor_context, sender_token):
    # Check if the sender has the required capabilities for this context
    if processing_context.domain == "critical":
        # Critical context requires higher capabilities
        if not actor_context.has_capability(sender_token, "critical_operations"):
            return {"error": "Insufficient capabilities for critical operations"}
    
    # Process normally
    # ...
```

## Best Practices

1. **Use Helper Methods**: Leverage the helper methods provided by `DecoupledActor` to simplify context-sensitive behavior.

2. **Register Wave Functions**: Register wave functions for message types to enable better context determination.

3. **Implement Context-Specific Handlers**: Create separate handler methods for different contexts to keep your code organized.

4. **Use Context Properties**: Store context-specific information in context properties for easy access.

5. **Track Effects**: Use the `context_effects` decorator to track effects in different contexts.

6. **Handle Context Switching**: Be prepared to handle context switches during message processing.

7. **Provide Fallbacks**: Always provide fallback behavior for unknown contexts.

8. **Document Context Requirements**: Document the contexts that your actor supports and their requirements.

9. **Test with Different Contexts**: Test your actor with different contexts to ensure it behaves correctly.

10. **Use Context-Sensitive State**: Use context-sensitive state management to store state that is specific to a context.

## Examples

See the [context_sensitive_actor.py](../../person_suit/core/infrastructure/actors/examples/context_sensitive_actor.py) example for a complete implementation of a context-sensitive actor.

## Troubleshooting

### Common Issues

1. **Context Not Available**: If the context is not available, check if the actor system is correctly configured to use `DecoupledActorContext`.

2. **Wave Functions Not Working**: Ensure that wave functions are correctly registered and that the message types match.

3. **Context-Sensitive State Not Working**: Check if the state dictionaries (`_state` and `_context_state`) are properly initialized.

4. **Helper Methods Not Available**: Ensure that your actor is a subclass of `DecoupledActor`.

5. **Context Switching Not Working**: Check if the context registry is correctly configured and that the contexts are registered.

### Debugging Tips

1. **Enable Debug Logging**: Enable debug logging to see detailed information about context determination and message processing.

2. **Inspect Context Properties**: Inspect the properties of the context to ensure they are set correctly.

3. **Check Wave Function Evaluation**: Add logging to wave functions to see how they are evaluated for different messages and contexts.

4. **Test with Simple Contexts**: Start with simple contexts and gradually add complexity to isolate issues.

5. **Use the Context Registry**: Use the context registry to manage and retrieve contexts instead of creating them on the fly.

---

By following this guide, you should be able to implement effective context-sensitive actors using the CAW paradigm. Remember that context sensitivity is a powerful tool for creating adaptive and responsive systems, but it requires careful design and implementation to be effective.
