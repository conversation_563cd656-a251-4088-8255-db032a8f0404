# CAW Paradigm Implementation Guide

> **Status:** `v1.0 - Initial Draft`
> **Last Updated:** `YYYY-MM-DD`
> **Related Documents:**
> - `../../../IMPLEMENTATION_GUIDE.md`
> - `../../architecture/DESIGN_PHILOSOPHY.md`
> - `../../architecture/UNIVERSAL_ARCHITECTURAL_PRINCIPLES.md`

## 1. Purpose: From Theory to Practice

This guide provides developers with concrete, actionable guidance on how to implement the advanced concepts of Contextual Adaptive Wave (CAW) programming within the Person Suit codebase. Where the main `IMPLEMENTATION_GUIDE.md` defines *what* to build, this document explains *how* to build it in a way that is true to the CAW paradigm.

## 2. Implementing Duality (Wave-Particle)

The core CAW principle of duality posits that information exists as both a definite "particle" and a potential "wave."

-   **Particle State (`HybridMessage.particle_state`):** Represents concrete, factual, and deterministic information. This is the "data" as it is typically understood. Use this for commands, direct observations, and established facts.
-   **Wave State (`HybridMessage.wave_state`):** Represents potential, possibility, and probabilistic context. Use this for predictions, subjective interpretations, and contexts where multiple futures are possible. This is where you would store probability distributions, belief models, or potential emotional resonances.

**Implementation Rule (The Wave-Particle Message Rule):** When designing a new message or service, ask: "Does this information have an element of uncertainty, potential, or subjectivity?" If so, it must have a `wave_state`. A message with only a `particle_state` is asserting an undeniable, objective fact.

**Example: Dream Consolidation (`FM-WPD`)**
-   **Particle:** The direct sensory input recorded during the day (e.g., "heard the sound of rain").
-   **Wave:** The emotional resonance of that sound (e.g., a probability distribution across `calm`, `melancholy`, `cozy`), the potential connections to other memories (a list of related memory IDs with confidence scores), or the symbolic meaning (a vector representing "cleansing" or "gloom").

## 3. Implementing Adaptive Computational Fidelity (ACF)

ACF is the system's ability to trade computational resources for quality of output based on context.

-   **Fidelity is a Number:** Fidelity is represented as a scaled integer (from 0 to 1,000,000) within the `ACFMetadata` of a message. Higher numbers mean higher fidelity.
-   **Context is King:** The `UnifiedContext` of a message determines the required fidelity. A high-priority context (e.g., a direct user command) requires high fidelity. A background task (e.g., log analysis) can tolerate low fidelity.
-   **Services Must Adapt:** Services and actors that receive a message **must** inspect its `acf_metadata.fidelity` and adjust their behavior accordingly.

**Implementation Rule (The Context-Driven Adaptation Rule):**
1.  **Check Fidelity:** At the start of any resource-intensive function, check the fidelity from the message's context.
2.  **Branch Logic:** Create explicit `if/else` branches based on the fidelity level.
    -   **High Fidelity (> 750,000):** Use the most precise algorithms, largest models, and deepest search settings.
    -   **Medium Fidelity (250,000 - 750,000):** Use standard, balanced algorithms.
    -   **Low Fidelity (< 250,000):** Use faster, approximate algorithms, smaller models, or return cached/default results.

**Example: Memory Performance Optimization (`PC-MPO`)**
-   **High Fidelity:** Perform an exhaustive vector search across the entire memory database.
-   **Low Fidelity:** Perform a search only on a smaller, in-memory cache of recent memories (e.g., using an Approximate Nearest Neighbor index).

## 4. Implementing Differential Context Propagation

The system must adapt to changes in context without full re-computation.

**Implementation Rule (The Differential Context Propagation Rule):**
-   **Listen for Deltas:** The `HybridMessageBus` automatically emits messages on the `sys.context.delta` channel whenever a context changes.
-   **Design for Incremental Updates:** Actors and services should subscribe to these delta messages. When a delta is received, the actor should only update the part of its internal state that is affected by the change. It should **not** re-load its entire state.

**Example: AlertManager**
-   An `AlertManager` actor is responsible for notifying the user if their sentiment is negative for a prolonged period.
-   Instead of polling the user's sentiment every second, it subscribes to `sys.context.delta` for the user's context.
-   When it receives a delta message containing a change to the `sentiment` field, it updates its internal state and checks if the alert condition is met. This is far more efficient than constant polling.
