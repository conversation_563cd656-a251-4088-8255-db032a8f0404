# Context-Aware Programming Guide

## Introduction

Context-Aware Programming is a paradigm that enables components to adapt their behavior based on the current context. In the Person Suit system, this is implemented through the Contextual Adaptive Wave (CAW) paradigm, which combines contextual awareness with wave-particle duality to create a flexible and adaptive system.

This guide provides an overview of context-aware programming in Person Suit, focusing on the context-aware capability system, context-aware effect system, and their integration.

## Understanding Contexts

A context represents an operational environment or situation in which the system operates. In Person Suit, contexts are represented by the `Context` class:

```python
@dataclass
class Context:
    domain: str
    priority: str
    constraints: List[str]
```

- **domain**: The domain of the context (e.g., "standard", "high_security", "emergency")
- **priority**: The priority of the context (e.g., "normal", "high", "critical")
- **constraints**: Constraints on the context (e.g., "strict", "relaxed", "emergency")

Contexts are used to adapt the behavior of various components, including capability verification, effect tracking, and message processing.

### Common Context Types

Person Suit defines several common context types:

- **Standard Context**: The default operational context
  ```python
  standard_context = Context(
      domain="standard",
      priority="normal",
      constraints=[]
  )
  ```

- **High Security Context**: A context with stricter security requirements
  ```python
  high_security_context = Context(
      domain="high_security",
      priority="high",
      constraints=["strict"]
  )
  ```

- **Low Security Context**: A context with relaxed security requirements
  ```python
  low_security_context = Context(
      domain="low_security",
      priority="low",
      constraints=["relaxed"]
  )
  ```

- **Emergency Context**: A context for emergency situations
  ```python
  emergency_context = Context(
      domain="emergency",
      priority="critical",
      constraints=["emergency"]
  )
  ```

### Creating Custom Contexts

You can create custom contexts for specific operational environments:

```python
# Create a custom context for a maintenance operation
maintenance_context = Context(
    domain="maintenance",
    priority="high",
    constraints=["system_maintenance", "limited_access"]
)

# Create a custom context for a debugging session
debug_context = Context(
    domain="debug",
    priority="normal",
    constraints=["verbose_logging", "extended_diagnostics"]
)
```

## Context-Aware Capability-Based Security

The context-aware capability system enables capability tokens to adapt their behavior based on the current context.

### Creating Context-Aware Capability Tokens

```python
from person_suit.core.infrastructure.security.capabilities import (
    CapabilityScope, Permission, Context,
    create_context_aware_capability
)

# Create a context-aware capability token
token = create_context_aware_capability(
    scope=CapabilityScope.USER,
    operations=[Permission.READ],
    subject="user123",
    context_operations={
        "emergency": [Permission.READ, Permission.WRITE, Permission.EXECUTE],
        "low_security": [Permission.READ, Permission.WRITE]
    },
    context_restrictions={
        "high_security": {
            "target_resources": ["resource1"]
        },
        "emergency": {
            "target_resources": ["*"]
        }
    },
    context_rules={
        "emergency": {
            "expiration_grace_period": 3600.0
        }
    },
    restrictions={
        "target_resources": ["resource1", "resource2"]
    },
    expires_at=time.time() + 3600,
    metadata={}
)
```

### Verifying Context-Aware Capability Tokens

```python
from person_suit.core.infrastructure.security.capabilities import (
    CapabilityScope, Permission, Context,
    verify_context_aware_capability
)

# Create a context
context = Context(
    domain="high_security",
    priority="high",
    constraints=["strict"]
)

# Verify the token in the context
result = verify_context_aware_capability(
    token=token,
    required_permission=Permission.READ,
    resource_id="resource1",
    required_scope=CapabilityScope.USER,
    context=context
)

if result:
    print("Token is valid in the high security context")
else:
    print("Token is not valid in the high security context")
```

### Using the Context-Aware Security Decorator

```python
from person_suit.core.infrastructure.security.capabilities import (
    CapabilityScope, Permission
)
from person_suit.core.infrastructure.integration import (
    with_context_aware_security
)

@with_context_aware_security(
    scope=CapabilityScope.USER,
    operations=[Permission.READ],
    resource_id="resource1",
    track_chain=True,
    track_transitions=True
)
async def read_resource(resource_id, capability=None, context=None):
    # This function will only be called if the capability token
    # grants the READ permission for the specified resource
    # in the given context
    return f"Reading resource: {resource_id}"
```

## Context-Aware Effect System

The context-aware effect system enables effects to be tracked and analyzed differently based on the current context.

### Tracking Context-Aware Effects

```python
from person_suit.core.infrastructure.effects import (
    EffectType, get_contextual_effect_tracker
)
from person_suit.core.infrastructure.wave.core import Context

# Create a context
context = Context(
    domain="standard",
    priority="normal",
    constraints=[]
)

# Get the effect tracker
tracker = get_contextual_effect_tracker()

# Track an effect in the context
effect = tracker.track_effect_in_context(
    effect_type=EffectType.IO,
    source="my_function",
    context=context
)
```

### Using the Context-Aware Effect Decorator

```python
from person_suit.core.infrastructure.effects import (
    EffectType, context_effects
)

@context_effects(
    effect_types=[EffectType.IO, EffectType.DATABASE],
    track_chain=True,
    track_transitions=True,
    context_param="ctx"
)
async def process_data(data, ctx=None):
    # This function will track IO and DATABASE effects
    # in the context provided by the ctx parameter
    # ...
    return result
```

### Analyzing Context-Aware Effects

```python
from person_suit.core.infrastructure.effects import (
    EffectType, get_contextual_effect_tracker,
    EffectAnalysisType
)
from person_suit.core.infrastructure.wave.core import Context

# Create a context
context = Context(
    domain="standard",
    priority="normal",
    constraints=[]
)

# Get the effect tracker
tracker = get_contextual_effect_tracker()

# Analyze effect flow
flow_result = tracker.analyze_effects(
    analysis_type=EffectAnalysisType.FLOW,
    context=context
)
print(f"Flow analysis: {flow_result.data}")

# Analyze effect dependencies
dependency_result = tracker.analyze_effects(
    analysis_type=EffectAnalysisType.DEPENDENCY,
    context=context
)
print(f"Dependency analysis: {dependency_result.data}")
```

## Context-Aware Integration

The context-aware integration module provides integration between the context-aware capability system and the context-aware effect system.

### Analyzing Security Effects

```python
from person_suit.core.infrastructure.integration import (
    analyze_security_effects
)
from person_suit.core.infrastructure.wave.core import Context

# Create a context
context = Context(
    domain="standard",
    priority="normal",
    constraints=[]
)

# Analyze security effects in the context
results = analyze_security_effects(context)

# Print analysis results
print(f"Total effects: {results['total_effects']}")
print(f"Successful effects: {results['successful_effects']}")
print(f"Failed effects: {results['failed_effects']}")
print(f"Success rate: {results['success_rate']:.2f}")
print(f"Average confidence: {results['average_confidence']:.2f}")
```

### Generating Security Dashboards

```python
from person_suit.core.infrastructure.integration import (
    generate_security_dashboard
)
from person_suit.core.infrastructure.wave.core import Context

# Create a context
context = Context(
    domain="standard",
    priority="normal",
    constraints=[]
)

# Generate a security dashboard for the context
dashboard_path = generate_security_dashboard(context)
print(f"Dashboard generated at: {dashboard_path}")
```

## Context-Aware Actors

Person Suit provides context-aware actors that can adapt their behavior based on the current context.

### Creating Context-Aware Actors

```python
from person_suit.core.infrastructure.actors import (
    DecoupledActor, DecoupledActorContext
)
from person_suit.core.infrastructure.wave.core import (
    WaveFunction, Information
)

class MyActor(DecoupledActor):
    async def process_in_context(self, message, context):
        # Context-sensitive behavior
        if context.domain == "standard":
            return await self.handle_standard(message, context)
        elif context.domain == "high_security":
            return await self.handle_high_security(message, context)
        elif context.domain == "emergency":
            return await self.handle_emergency(message, context)
        else:
            return await self.handle_default(message, context)

    async def handle_standard(self, message, context):
        # Standard context behavior
        # ...
        return result

    async def handle_high_security(self, message, context):
        # High security context behavior
        # ...
        return result

    async def handle_emergency(self, message, context):
        # Emergency context behavior
        # ...
        return result

    async def handle_default(self, message, context):
        # Default behavior for unknown contexts
        # ...
        return result

    def create_message_wave_function(self, message):
        # Create a wave function for the message
        # This is used to determine the best context for processing
        if message.type == "command":
            return CommandWaveFunction(message)
        elif message.type == "query":
            return QueryWaveFunction(message)
        else:
            return DefaultWaveFunction(message)
```

### Using Context-Aware Actors

```python
from person_suit.core.infrastructure.actors import (
    ActorSystem, DecoupledActor
)
from person_suit.core.infrastructure.wave.core import Context

# Create an actor system
system = ActorSystem()

# Create a context-aware actor
actor_ref = await system.create_actor(
    MyActor,
    name="my_actor"
)

# Create a context
context = Context(
    domain="high_security",
    priority="high",
    constraints=["strict"]
)

# Send a message to the actor with a context
result = await system.ask(
    actor_ref,
    "Hello, world!",
    context=context
)
```

## Context-Aware Choreography

Person Suit provides context-aware choreographies that can adapt their behavior based on the current context.

### Creating Context-Aware Choreographies

```python
# <!-- OLD IMPORT: from person_suit.core.infrastructure.choreography import (
#     Choreography, ChoreographyStep, ChoreographyParticipant
# )
# from person_suit.core.infrastructure.wave.core import Context

# Define a context-aware choreography
# class MyChoreography(Choreography):
#     def __init__(self, name, participants):
#         super().__init__(name, participants)
#         self.context_steps = {
#             "standard": [
#                 ChoreographyStep(
#                     name="step1",
#                     sender="participant1",
#                     receiver="participant2",
#                     message_type="command",
#                     message_content={"action": "process"}
#                 ),
#                 ChoreographyStep(
#                     name="step2",
#                     sender="participant2",
#                     receiver="participant3",
#                     message_type="result",
#                     message_content={"status": "success"}
#                 )
#             ],
#             "high_security": [
#                 ChoreographyStep(
#                     name="step1",
#                     sender="participant1",
#                     receiver="participant2",
#                     message_type="command",
#                     message_content={"action": "process", "security_level": "high"}
#                 ),
#                 ChoreographyStep(
#                     name="step2",
#                     sender="participant2",
#                     receiver="security_validator",
#                     message_type="validate",
#                     message_content={"action": "validate"}
#                 ),
#                 ChoreographyStep(
#                     name="step3",
#                     sender="security_validator",
#                     receiver="participant2",
#                     message_type="validation_result",
#                     message_content={"status": "valid"}
#                 ),
#                 ChoreographyStep(
#                     name="step4",
#                     sender="participant2",
#                     receiver="participant3",
#                     message_type="result",
#                     message_content={"status": "success", "security_level": "high"}
#                 )
#             ]
#         }
# 
#     def get_steps_for_context(self, context):
#         # Get the steps for the given context
#         if context.domain in self.context_steps:
#             return self.context_steps[context.domain]
#         else:
#             return self.context_steps["standard"]
```

### Using Context-Aware Choreographies

```python
# <!-- OLD IMPORT: from person_suit.core.infrastructure.choreography import (
#     ChoreographyEngine, ChoreographyParticipant
# )
# from person_suit.core.infrastructure.wave.core import Context

# Create a choreography engine
# engine = ChoreographyEngine()

# Create participants
# participant1 = ChoreographyParticipant(name="participant1", actor_ref=actor_ref1)
# participant2 = ChoreographyParticipant(name="participant2", actor_ref=actor_ref2)
# participant3 = ChoreographyParticipant(name="participant3", actor_ref=actor_ref3)
# security_validator = ChoreographyParticipant(name="security_validator", actor_ref=validator_ref)

# Create a choreography
# choreography = MyChoreography(
#     name="my_choreography",
#     participants=[participant1, participant2, participant3, security_validator]
# )

# Create a context
# context = Context(
#     domain="high_security",
#     priority="high",
#     constraints=["strict"]
# )

# Start the choreography with a context
# instance_id = await engine.start_choreography(
#     choreography=choreography,
#     context=context
# )
```

## Best Practices

### 1. Define Clear Contexts

Create well-defined contexts with meaningful domains, priorities, and constraints:

```python
# Standard context
standard_context = Context(
    domain="standard",
    priority="normal",
    constraints=[]
)

# High security context
high_security_context = Context(
    domain="high_security",
    priority="high",
    constraints=["strict", "audit_all_operations"]
)

# Emergency context
emergency_context = Context(
    domain="emergency",
    priority="critical",
    constraints=["emergency", "bypass_non_critical_checks"]
)
```

### 2. Use Context-Specific Security Policies

Define different security policies for different contexts:

```python
# Create a context-aware capability token
token = create_context_aware_capability(
    scope=CapabilityScope.USER,
    operations=[Permission.READ],  # Standard context: READ only
    subject="user123",
    context_operations={
        "emergency": [Permission.READ, Permission.WRITE, Permission.EXECUTE],  # Emergency context: more permissions
        "low_security": [Permission.READ, Permission.WRITE]  # Low security context: READ and WRITE
    },
    context_restrictions={
        "high_security": {
            "target_resources": ["resource1"]  # High security context: restricted resources
        },
        "emergency": {
            "target_resources": ["*"]  # Emergency context: all resources
        }
    },
    restrictions={
        "target_resources": ["resource1", "resource2"]  # Standard context: limited resources
    }
)
```

### 3. Track Context-Specific Effects

Track effects differently in different contexts:

```python
@context_effects(
    effect_types=[EffectType.IO, EffectType.DATABASE],
    track_chain=True,
    track_transitions=True
)
async def process_data(data, context=None):
    # This function will track IO and DATABASE effects
    # in the context provided by the context parameter
    # ...
    return result
```

### 4. Implement Context-Sensitive Behavior

Implement different behavior for different contexts:

```python
async def process_request(request, context=None):
    if context and context.domain == "high_security":
        # High security context behavior
        return await process_request_high_security(request)
    elif context and context.domain == "emergency":
        # Emergency context behavior
        return await process_request_emergency(request)
    else:
        # Standard context behavior
        return await process_request_standard(request)
```

### 5. Use the Combined Decorator

Use the `with_context_aware_security` decorator to combine security verification and effect tracking:

```python
@with_context_aware_security(
    scope=CapabilityScope.USER,
    operations=[Permission.READ],
    resource_id="resource1",
    track_chain=True,
    track_transitions=True
)
async def read_resource(resource_id, capability=None, context=None):
    # This function will only be called if the capability token
    # grants the READ permission for the specified resource
    # in the given context
    return f"Reading resource: {resource_id}"
```

### 6. Analyze Effects Regularly

Regularly analyze effects to identify patterns and potential issues:

```python
# Analyze security effects
results = analyze_security_effects()

# Check success rate
if results["success_rate"] < 0.9:
    print("Warning: Low security success rate!")

# Check average confidence
if results["average_confidence"] < 0.8:
    print("Warning: Low security confidence!")
```

### 7. Use Dashboards

Use dashboards to visualize and analyze effects:

```python
# Generate a security dashboard
dashboard_path = generate_security_dashboard()
print(f"Dashboard generated at: {dashboard_path}")
```

### 8. Implement Context Transitions

Implement smooth transitions between contexts:

```python
async def transition_to_emergency(current_context):
    # Create an emergency context
    emergency_context = Context(
        domain="emergency",
        priority="critical",
        constraints=["emergency"]
    )

    # Notify all components of the context transition
    await notify_context_transition(current_context, emergency_context)

    # Return the new context
    return emergency_context
```

### 9. Test Context-Sensitive Behavior

Test behavior in different contexts:

```python
# Test in standard context
result_standard = await process_request(request, standard_context)

# Test in high security context
result_high_security = await process_request(request, high_security_context)

# Test in emergency context
result_emergency = await process_request(request, emergency_context)

# Compare results
assert result_standard != result_high_security
assert result_standard != result_emergency
assert result_high_security != result_emergency
```

### 10. Document Context-Sensitive Behavior

Document how behavior changes in different contexts:

```python
def process_request(request, context=None):
    """
    Process a request.

    Args:
        request: The request to process
        context: The context in which to process the request

    Returns:
        The processed request

    Context-Sensitive Behavior:
        - Standard context: Normal processing with standard validation
        - High security context: Enhanced validation and restricted access
        - Emergency context: Minimal validation and expanded access
    """
    # ...
```

## Conclusion

Context-Aware Programming is a powerful paradigm that enables components to adapt their behavior based on the current context. By leveraging the CAW paradigm, Person Suit provides a flexible and adaptive system that can respond to changing operational environments.

This guide has provided an overview of context-aware programming in Person Suit, focusing on the context-aware capability system, context-aware effect system, and their integration. By following the best practices outlined in this guide, you can create context-aware components that adapt their behavior based on the current context, enabling more flexible and nuanced system behavior.
