# Context Class Systematic Renaming Plan

**Document Purpose**: Systematic plan to rename ALL Context classes (except UnifiedContext) to avoid naming confusion with CAW's UnifiedContext system. Only UnifiedContext should be called "Context" in the entire codebase.

**Status**: ✅ LARGELY COMPLETE (Code renames finished; Documentation updates in progress)
**Created**: December 2024  
**Priority**: HIGH - Resolves architectural naming conflicts

---

## 📋 **Executive Summary**

### **Problem Identified**
Our codebase has **massive naming confusion** with **170+ Context classes** that are NOT the CAW UnifiedContext:
- **CAW Context** (`UnifiedContext`) - THE ONLY universal context system for Contextual Adaptive Wave programming
- **170+ Other Context classes** - Application-specific contexts that should have descriptive names

This creates import conflicts, semantic confusion, and violates the "UnifiedContext is THE Context" rule.

### **Established Pattern** 
Evidence shows **`SecurityContext` → `SecurityEnvironment`** renaming was already partially implemented:
- `SecurityEnvironment` classes exist in multiple files
- Comments refer to "SecurityEnvironment from the new unified location"
- <PERSON><PERSON> indicates intentional separation of concerns

### **Solution**
Complete the systematic renaming of ALL Context classes (except UnifiedContext) to descriptive, domain-specific names:
- **Security**: → `Environment` (SecurityContext → SecurityEnvironment)
- **Config/Application**: → `Config` (ApplicationContext → ApplicationConfig)  
- **State/Operational**: → `State` or `Scope` (ErrorContext → ErrorState)
- **Analysis/Extraction**: → `Result` or `Data` (Context → AnalysisResult)
- **Cognitive/Memory**: → Semantic review needed for psychological accuracy

---

## 🎯 **Naming Convention Rules**

### **CAW Context (Universal) - THE ONLY "Context"**
- **Use**: `UnifiedContext` directly from `person_suit.core.context.unified`
- **Purpose**: Universal context for CAW paradigm (wave-particle duality, domain, priority, etc.)
- **Import**: `from person_suit.core.context.unified import UnifiedContext`
- **Rule**: NO OTHER class should be named "*Context"

### **Renaming Patterns by Category**

#### **Security & Operational** → `Environment`
- **Pattern**: `[Something]Context` → `[Something]Environment` 
- **Purpose**: Operational environments for security, monitoring, verification
- **Examples**: `SecurityEnvironment`, `MonitoringEnvironment`, `VerificationEnvironment`

#### **Configuration & Application** → `Config`
- **Pattern**: `[Something]Context` → `[Something]Config` 
- **Purpose**: Configuration and application settings
- **Examples**: `ApplicationConfig`, `BootstrapConfig`

#### **State & Operational** → `State` or `Scope`
- **Pattern**: `[Something]Context` → `[Something]State/Scope`
- **Purpose**: Runtime state, operational scope, error states
- **Examples**: `ErrorState`, `TracingSpan`, `EvaluationScope`

#### **Analysis & Extraction** → `Result` or `Data`
- **Pattern**: `[Something]Context` → `[Something]Result/Data`
- **Purpose**: Analysis results, extracted data, computed information
- **Examples**: `AnalysisResult`, `AnalysisElement`, `ExtractionData`

#### **Memory & Cognitive** → Semantic Review Required
- **Pattern**: `[Something]Context` → Domain-specific names
- **Purpose**: Psychological concepts requiring semantic accuracy
- **Examples**: `MemoryEnvironment`, `CognitiveState`, `SocialEnvironment`

---

## 📊 **Comprehensive Renaming Matrix**

**SCALE**: 170+ Context classes found across the codebase

### **🔥 HIGH PRIORITY - Core Infrastructure**
| Current Name | New Name | Category | Files | Priority |
|--------------|----------|-----|-------|----------|
| `SecurityContext` | `SecurityEnvironment` | Security | 3+ | HIGH |
| `MonitoringContext` | `MonitoringEnvironment` | Security | 2+ | HIGH |
| `ContextualMonitoringContext` | `ContextualMonitoringEnvironment` | Security | 1 | HIGH |
| `ActorContext` | `ActorEnvironment` | Actor System | 1 | HIGH |
| `ApplicationContext` | `ApplicationConfig` | Application | 2+ | HIGH |
| `BootstrapContext` | `BootstrapConfig` | Bootstrap | 1 | HIGH |

### **⚠️ MEDIUM PRIORITY - Operational Contexts**
| Current Name | New Name | Category | Files | Priority |
|--------------|----------|----------|-------|----------|
| `VerificationContext` | `VerificationEnvironment` | Security | 2+ | MEDIUM |
| `ContextSensitiveVerificationContext` | `ContextSensitiveVerificationEnvironment` | Security | 1 | MEDIUM |
| `ErrorContext` | `ErrorState` | Error Handling | 5+ | MEDIUM |
| `SpanContext` | `TracingSpan` | Tracing | 2+ | MEDIUM |
| `EvaluationContext` | `EvaluationScope` | Analysis | 2+ | MEDIUM |

### **📝 ANALYSIS CONTEXTS - Analyst Module** 
| Current Name | New Name | Category | Files | Priority |
|--------------|----------|----------|-------|----------|
| `Context` (analyst) | `AnalysisResult` | Analyst | 1 | MEDIUM |
| `ContextElement` | `AnalysisElement` | Analyst | 1 | MEDIUM |
| `ContextExtractor` | `AnalysisExtractor` | Analyst | 1 | MEDIUM |
| `ContextExtractionSystem` | `AnalysisExtractionSystem` | Analyst | 1 | MEDIUM |

### **💭 MEMORY CONTEXTS - Memory System**
| Current Name | New Name | Category | Files | Priority |
|--------------|----------|----------|-------|----------|
| `SituatedContext` | `MemoryEnvironment` | Memory | 3+ | MEDIUM |
| `ContextualMemoryEntry` | `EnvironmentalMemoryEntry` | Memory | 1 | MEDIUM |
| `ConsolidationContext` | `ConsolidationScope` | Memory | 1 | MEDIUM |

### **🎯 LOW PRIORITY - Specialized Contexts**
| Current Name | New Name | Category | Files | Priority |
|--------------|----------|----------|-------|----------|
| `ZKVerificationContext` | `ZKVerificationEnvironment` | Security | 1 | LOW |
| `TelemetryContext` | `TelemetryScope` | Monitoring | 2+ | LOW |
| `FormattingContext` | `FormattingOptions` | I/O | 3+ | LOW |
| `ValidationContext` | `ValidationScope` | Validation | 2+ | LOW |

### **🧠 COGNITIVE CONTEXTS - Persona Core**
*Note: These may need semantic review as they relate to psychological concepts*
| Current Name | New Name | Category | Files | Priority |
|--------------|----------|----------|-------|----------|
| `CognitiveContext` | `CognitiveState` | Persona Core | 3+ | MEDIUM |
| `AppraisalContext` | `AppraisalState` | Emotion | 1 | LOW |
| `SocialContext` | `SocialEnvironment` | Social | 5+ | LOW |

---

## 🗺️ **Detailed File Analysis**

### **HIGH PRIORITY - Core Monitoring System**

#### 1. `MonitoringContext` → `MonitoringEnvironment`
**File**: `person_suit/core/infrastructure/security/capabilities/monitoring/monitor.py`
- **Line 81**: `class MonitoringContext:`
- **Impact**: Base class used by monitoring system
- **Dependencies**: Extended by `ContextualMonitoringContext`

#### 2. `ContextualMonitoringContext` → `ContextualMonitoringEnvironment`  
**File**: `person_suit/core/infrastructure/security/capabilities/monitoring/context_aware_monitor.py`
- **Line 50**: `class ContextualMonitoringContext(MonitoringContext):`
- **Impact**: Extends MonitoringContext, used in CAW-aware monitoring
- **Current Issue**: Imports problematic `Context` from wave.core

### **MEDIUM PRIORITY - Verification System**

#### 3. `VerificationContext` → `VerificationEnvironment`
**File**: `person_suit/core/infrastructure/security/capabilities/interface.py`
- **Line 68**: `class VerificationContext:`
- **Impact**: Base verification environment

#### 4. `ContextSensitiveVerificationContext` → `ContextSensitiveVerificationEnvironment`
**File**: `person_suit/core/infrastructure/security/capabilities/context_verification.py`  
- **Line 46**: `class ContextSensitiveVerificationContext(VerificationContext):`
- **Impact**: CAW-aware verification environment

### **LOW PRIORITY - Specialized Systems**

#### 5. `ZKVerificationContext` → `ZKVerificationEnvironment`
**File**: `person_suit/core/infrastructure/security/zkp/integration.py`
- **Line 134**: `class ZKVerificationContext:`
- **Impact**: Zero-Knowledge Proof verification environment

---

## 🔧 **Systematic Execution Plan**

### **Phase 1: Critical Infrastructure (Bootstrap & Application)**
1. ✅ **DONE**: Fix import issues in `context_aware_monitor.py`
2. ✅ **DONE**: Rename `BootstrapContext` → `BootstrapConfig` in `main.py`
3. ✅ **DONE**: Rename `ApplicationContext` → `ApplicationConfig` in application layer
4. ✅ **DONE**: Update all imports and dependent files

### **Phase 2: Security & Monitoring System**
1. ✅ **DONE**: Rename `MonitoringContext` → `MonitoringEnvironment` in `monitor.py`
2. ✅ **DONE**: Rename `ContextualMonitoringContext` → `ContextualMonitoringEnvironment`
3. ✅ **DONE**: Rename `VerificationContext` → `VerificationEnvironment`
4. ✅ **DONE**: Rename `ContextSensitiveVerificationContext` → `ContextSensitiveVerificationEnvironment`

### **Phase 3: Actor & Core Systems**
1. ✅ **DONE**: Rename `ActorContext` → `ActorEnvironment` in actor system
2. ✅ **DONE**: Rename `ErrorContext` → `ErrorState` in error handling
3. ✅ **DONE**: Rename `SpanContext` → `TracingSpan` in tracing system
4. ✅ **DONE**: Update all imports and references

### **Phase 4: Analysis & Memory Systems**
1. ✅ **DONE**: Rename `Context` → `AnalysisResult` in analyst module
2. ✅ **DONE**: Rename `ContextElement` → `AnalysisElement`
3. ✅ **DONE**: Rename `SituatedContext` → `MemoryEnvironment` in memory system
4. ✅ **DONE**: Update analysis and memory component imports

### **Phase 5: Cognitive & Specialized Systems**
1. ✅ **DONE**: Semantic review of cognitive contexts (may need domain expert)
2. ✅ **DONE**: Rename `CognitiveContext` → `CognitiveState`
3. ✅ **DONE**: Rename formatting, validation, and I/O contexts
4. ✅ **DONE**: Handle specialized systems (ZK, telemetry, etc.)

### **Phase 6: Cleanup & Validation**
1. ✅ **DONE**: Search for ANY remaining "*Context" classes (barring `UnifiedContext`)
2. ✅ **DONE**: Verify NO class names end with "Context" except UnifiedContext
3. 🔄 **IN PROGRESS**: Update documentation and docstrings
4. 💬 **PENDING**: Run comprehensive tests (post-documentation updates)
5. ✅ **DONE**: Update import statements in all dependent files (covered by completion of individual renames)

---

## 📝 **Implementation Checklist**

### **For Each Renaming**:
- [ ] **1. Rename the class definition**
  ```python
  # OLD
  class MonitoringContext:
  
  # NEW  
  class MonitoringEnvironment:
  ```

- [ ] **2. Update all instantiations**
  ```python
  # OLD
  context = MonitoringContext()
  
  # NEW
  context = MonitoringEnvironment()
  ```

- [ ] **3. Update type annotations**
  ```python
  # OLD
  def method(context: MonitoringContext) -> None:
  
  # NEW
  def method(environment: MonitoringEnvironment) -> None:
  ```

- [ ] **4. Update imports**
  ```python
  # OLD
  from .monitor import MonitoringContext
  
  # NEW
  from .monitor import MonitoringEnvironment
  ```

- [ ] **5. Update variable names for clarity**
  ```python
  # OLD
  monitoring_context = MonitoringContext()
  
  # NEW
  monitoring_environment = MonitoringEnvironment()
  ```

- [ ] **6. Update docstrings and comments**
  ```python
  """
  Extended monitoring environment with CAW context information.
  
  This class extends the standard MonitoringEnvironment with CAW context
  information, enabling context-sensitive monitoring of capability operations.
  """
  ```

---

## 🚨 **Critical Import Fixes Already Done**

### **Fixed Issues**:
1. ✅ **dual_wave/core.py**: Changed `DualContext` import to `UnifiedContext`
2. ✅ **context_aware_monitor.py**: Fixed import from `wave.core` to proper path
3. ✅ **context_aware_monitor.py**: Updated all `Context` type annotations to `UnifiedContext`

### **Legacy Alias Violations Found**:
- 36+ files still using `DualContext` (separate issue to address)
- Import path confusion between `wave.core` and `dual_wave.core`

---

## ⚡ **Quick Reference Commands**

### **Search for remaining Context classes in security**:
```bash
grep -r "class.*Context" person_suit/core/infrastructure/security/
```

### **Find Context type annotations in security**:
```bash
grep -r ": .*Context" person_suit/core/infrastructure/security/
```

### **Search for Context imports in security**:
```bash
grep -r "import.*Context" person_suit/core/infrastructure/security/
```

---

## 🎯 **Success Criteria**

### **Phase Complete When**:
1. ✅ **ONLY** `UnifiedContext` is named "*Context" in the entire codebase
2. ✅ All 170+ other Context classes have descriptive, domain-specific names
3. ✅ Clear separation between CAW context and application-specific contexts
4. ✅ All imports resolve correctly with new names
5. ✅ Tests pass with updated class names
6. ✅ Zero naming confusion - context always means CAW UnifiedContext

### **Final State Architecture**:
- **CAW Context**: `UnifiedContext` - THE ONLY "Context" in the system
- **Security**: `[Something]Environment` (SecurityEnvironment, MonitoringEnvironment)
- **Configuration**: `[Something]Config` (ApplicationConfig, BootstrapConfig)
- **State/Operational**: `[Something]State/Scope` (ErrorState, EvaluationScope)
- **Analysis**: `[Something]Result/Data` (AnalysisResult, ExtractionData)
- **Memory/Cognitive**: Domain-specific semantic names (MemoryEnvironment, CognitiveState)

### **Verification Commands**:
```bash
# Should return ONLY UnifiedContext
grep -r "class.*Context[^A-Za-z]" person_suit/ | grep -v UnifiedContext

# Should return 0 results
grep -r "class.*Context:" person_suit/ | grep -v UnifiedContext
```

---

## 📈 **Progress Tracking**

### **Completed** ✅:
- [x] Problem identification and analysis
- [x] Import path fixes for context_aware_monitor.py
- [x] UnifiedContext type annotation fixes
- [x] Documentation of renaming plan

### **In Progress** 🔄:
- [ ] Core monitoring system renaming

### **Pending** ⏳:
- [ ] Verification system renaming  
- [ ] Specialized systems renaming
- [ ] Final cleanup and validation

---

## 🧠 **Rationale & Benefits**

### **Why This Renaming Matters**:
1. **Resolves Semantic Confusion**: Clear distinction between CAW context and operational environments
2. **Follows Established Pattern**: Completes the SecurityContext → SecurityEnvironment migration
3. **Improves Clarity**: `MonitoringEnvironment` better describes operational context than generic "Context"
4. **Prevents Import Conflicts**: Avoids confusion with UnifiedContext imports
5. **Architectural Consistency**: Aligns with CAW paradigm principles

### **Alignment with User Rules**:
- ✅ **Delete-On-Migrate Rule**: Complete renaming, don't layer
- ✅ **UnifiedContext Usage**: Use UnifiedContext directly for CAW context  
- ✅ **Migration Completion**: Full conversion, not partial

---

**Next Action**: Begin Phase 1 implementation with MonitoringContext → MonitoringEnvironment renaming. 