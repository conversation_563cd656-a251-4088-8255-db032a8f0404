# CAW-Aligned Native Provenance System Implementation Plan

*Status: Draft - Created 2025-06-23*

## Executive Summary

This document outlines the implementation plan for a native, CAW-aligned provenance system that operates without external dependencies (Kafka/Redpanda) while maintaining compatibility with them as optional export targets. The system embodies CAW principles through dual trace representation, context-aware storage, adaptive fidelity, and capability-controlled access.

## 1. Design Philosophy

### 1.1 Core Principles

1. **Dual Trace Representation**
   - **Wave Log**: High-volume probabilistic summaries for cheap global reasoning
   - **Particle Log**: Exact, immutable records for authoritative replay

2. **Context-Aware Storage**
   - Every record embeds full `UnifiedContext` snapshot + delta hash
   - Differential propagation stores only deltas ≤ 1KB (satisfies Differential Context Propagation Rule)

3. **Adaptive Computational Fidelity (ACF)**
   - Writer actor chooses wave vs particle ratio based on load & importance
   - High priority/security → particle log
   - Bulk telemetry under pressure → wave log + sampling

4. **Capability-Aware Access**
   - Biscuit tokens on each record
   - Append denied without `capability.provenance.append`

5. **Native First, External Optional**
   - Core system uses persistent segment files (no Docker required)
   - Optional plugins for Redpanda/Kafka export
   - Optional replication via rsync/ZeroMQ

## 2. Architecture

```
┌─────────────────────────────────────┐
│  ProvenanceMiddleware               │
│  • Intercepts all bus messages      │
│  • Extracts context & capabilities  │
└─────────────────────────────────────┘
                │
                ▼
┌─────────────────────────────────────┐
│  ProvenanceWriterActor (supervised) │
│  • Bounded in-memory queue          │
│  • ACF-aware batching               │
│  • Back-pressure handling           │
└─────────────────────────────────────┘
                │
        ┌───────┴───────┐
        ▼               ▼
┌──────────────┐ ┌──────────────┐
│ Particle Log │ │  Wave Log    │
│ • Segments   │ │ • Sketches   │
│ • Immutable  │ │ • Summaries  │
└──────────────┘ └──────────────┘
        │               │
        └───────┬───────┘
                ▼
┌─────────────────────────────────────┐
│  ProvenanceQueryAPI                 │
│  • Filter by context/time/capability│
│  • Stream DualTrace results         │
└─────────────────────────────────────┘
```

## 3. Data Schemas

### 3.1 Particle Record (FlatBuffers)

```flatbuffers
table ParticleRecord {
  timestamp_nanos: uint64;
  message_id: string;
  message_type: MessageType;
  channel: string;
  capability_token_hash: [ubyte];  // 32 bytes
  context_delta_hash: [ubyte];     // 32 bytes
  payload_sha256: [ubyte];         // 32 bytes
  wave_particle_ratio: uint32;     // 0-1,000,000 scale
  context_delta: [ubyte];          // Optional compressed delta
}
```

### 3.2 Wave Summary (Per Segment)

- Count-min sketch on (channel, message_type)
- Bloom filter on capability_token_hash
- HyperLogLog on source_actor for cardinality
- Time-bucketed aggregates for ACF metrics

### 3.3 Segment Format

```
Header (256 bytes):
  - Magic bytes: "PSPV" (PersonSuit ProVenance)
  - Version: uint16
  - Compression: enum (none, zstd, lz4)
  - Context schema hash: 32 bytes
  - Segment UUID: 16 bytes
  - Previous segment hash: 32 bytes
  - Record count: uint32
  - CRC32: uint32

Body:
  - Compressed ParticleRecord array
  - Wave summary at end
```

## 4. Implementation Modules

### 4.1 Core Components

```python
# person_suit/core/provenance/backends/native/
├── __init__.py
├── writer_actor.py      # ProvenanceWriterActor
├── storage_engine.py    # Segment file management
├── particle_log.py      # Exact record storage
├── wave_log.py          # Probabilistic summaries
├── query_api.py         # Async query interface
├── schemas.py           # FlatBuffer schemas
└── utils.py             # Compression, hashing
```

### 4.2 Integration Points

```python
# person_suit/core/infrastructure/middleware/provenance.py
class ProvenanceProcessor:
    async def initialize(self, bus: HybridMessageBus) -> None:
        backend = self._select_backend()  # native, redpanda, etc.
        if backend == "native":
            self.backend = NativeProvenanceBackend()
        # ... existing code for other backends
```

## 5. Sprint Breakdown

### Sprint 1: Foundations (5 days)

**Day 1-2: Segment Format & Storage Engine**
- Implement segment file format with header/body structure
- Create `StorageEngine` class for rotation, indexing, cleanup
- Unit tests for segment write/read/validate

**Day 3-4: ProvenanceWriterActor**
- Supervised actor with bounded queue
- ACF-aware flushing logic
- Back-pressure handling when queue full

**Day 5: Integration & Testing**
- Wire `PS_PROVENANCE_SINK=native` environment variable
- Integration test: 10k records, verify flush & recovery
- Counter-evidence: kill writer mid-flush, verify no data loss

### Sprint 2: Context & Duality (6 days)

**Day 1-2: Context Delta Computation**
- Implement efficient context diff algorithm
- Store only deltas ≤ 1KB (full context if larger)
- Lazy reconstruction on query

**Day 3-4: Wave Log Implementation**
- Count-min sketch for channel/type frequencies
- Bloom filter for capability audit trails
- HyperLogLog for actor cardinality

**Day 5-6: ACF Integration**
- Wire load metrics to writer's decision logic
- Implement wave_particle_ratio policy
- Benchmark: differential propagation ≤ 2× single actor cost

### Sprint 3: Query, Security & Production (5 days)

**Day 1-2: Query API**
- Async streaming API with filters
- Parallel segment scanning
- Context reconstruction from deltas

**Day 3: Security Integration**
- Biscuit token validation on append
- Capability filtering in queries
- Audit log for denied operations

**Day 4: Production Hardening**
- Metrics: flush latency, queue depth, segment count
- Graceful degradation on corrupted segments
- Optional export plugins (Redpanda, S3)

**Day 5: Counter-Evidence & Documentation**
- Load test: 50k msgs @ 1k/s, loss < 0.5%
- Penetration tests: malformed tokens, replay attacks
- Architecture & operation guides

## 6. Configuration

```yaml
# config/provenance.yaml
provenance:
  backend: native  # or redpanda, pulsar
  
  native:
    data_dir: ./data/provenance
    segment_size_mb: 10
    segment_duration_seconds: 60
    flush_size_threshold: 100
    flush_time_threshold_ms: 5000
    compression: zstd
    compression_level: 3
    
    # ACF thresholds (integer scale)
    wave_threshold_load: 600000      # 0.6 load
    particle_sample_rate: 100000     # 0.1 under high load
    
    # Retention
    max_segments: 1000
    max_age_days: 30
    
    # Export plugins (optional)
    export:
      redpanda:
        enabled: false
        brokers: localhost:9092
        topic: person_suit.provenance.export
```

## 7. Production Metrics

```python
# Prometheus metrics
provenance_particle_records_total
provenance_wave_records_total
provenance_segments_total
provenance_flush_duration_seconds
provenance_query_duration_seconds
provenance_context_delta_bytes
provenance_capability_denials_total
provenance_segment_corruptions_total
```

## 8. Migration Path

1. **Phase 1**: Deploy native backend alongside existing Redpanda
   - Shadow write to both backends
   - Compare query results for consistency

2. **Phase 2**: Switch primary to native
   - Redpanda becomes optional export target
   - Monitor metrics for regression

3. **Phase 3**: Production validation
   - Run for 30 days with full load
   - Verify counter-evidence probes pass

## 9. Counter-Evidence Test Suite

```python
# scripts/counter_evidence/provenance_native.py

async def test_no_data_loss():
    """Send 100k messages, verify all queryable"""
    
async def test_crash_recovery():
    """Kill writer mid-batch, restart, verify continuity"""
    
async def test_capability_enforcement():
    """Attempt writes without capability, verify denial"""
    
async def test_differential_propagation():
    """Mutate context by 1KB, verify ≤ 2× propagation cost"""
    
async def test_acf_degradation():
    """Simulate high load, verify wave/particle ratio adapts"""
```

## 10. Success Criteria

1. **Performance**
   - Throughput: >10k records/sec on M3 Max
   - Query latency: <100ms for 1M records
   - Storage efficiency: <100 bytes/record compressed

2. **Reliability**
   - Zero data loss across 100k messages
   - MTTR < 2s for writer actor
   - Graceful handling of corrupted segments

3. **CAW Alignment**
   - Dual trace representation working
   - Context deltas propagating efficiently
   - ACF adapting to load conditions
   - Capability enforcement active

## 11. Future Enhancements

1. **Replication**: Ship segments via rsync/NATS
2. **Columnar Storage**: Migrate to Parquet for analytics
3. **Time Travel**: Point-in-time system state reconstruction
4. **Federated Queries**: Cross-node provenance correlation

## 12. Conclusion

This native provenance system provides Person Suit with a zero-dependency, CAW-aligned audit trail that scales from development laptops to production clusters. By implementing dual trace representation and adaptive fidelity, we achieve both comprehensive tracking and efficient resource usage. The optional export plugins ensure compatibility with existing infrastructure while removing hard dependencies on external systems. 