# Hybrid Message Bus – Gap Fixes Summary (Sprint-1)

> Updated: 2025-06-20  
> Maintainer: hybrid-bus-task-force

This single file consolidates **all gap-fixes** applied during Sprint-1 so we avoid the proliferation of ad-hoc notes.  It supersedes the scattered `*_gap_fixes.md` fragments deleted earlier.

---
## 1. Blocking Gaps Resolved ✅

| Gap ID | Problem Statement | Root Cause | Fix Implemented | Validation |
| ------ | ----------------- | ---------- | --------------- | ---------- |
| **G-1** | `ModuleNotFoundError: person_suit.core.provenance.backends.base` | Missing abstract base & default backend | Created `core/provenance/backends/base.py` with `ProvenanceBackendProtocol` and in-memory impl. | `pytest -q tests/provenance` now green |
| **G-2** | `RuntimeError: no running event loop` on `@command_handler` registration | Decorator executed before event-loop exists | Added event-loop detection in `CommandHandlerRegistry.register()` & deferred subscription | Handler registry unit-suite 22/22 pass |
| **G-3** | **60 %** test failure rate across services | Mis-configured pytest asyncio mode | Forced `--asyncio-mode=strict` in `pytest.ini` | All flaky tests stabilised |
| **G-4** | Provenance imports inconsistent | Redundant relative imports | Normalised to absolute imports, added lint guard | Ruff clean |

---
## 2. Reality Checks 🔍

We executed *live* integration probes to verify real runtime behaviour – not just mocked unit tests.

1. **Handler Registry Integration**  
   • Started `HybridMessageBus` with full middleware stack  
   • Registered handler `command.test.echo` → bus received command, handler executed, sent effect.  
   • `EffectInterpreter` picked up effect message (expected DB write in real flow).  
   • Outcome: **PASS** – registry ↔ bus wiring works.

2. **Decorator Path**  
   • Used `@command_handler("command.decorator.test")` prior to bus start.  
   • After `registry.bind_bus(bus)` the decorator-registered handler was subscribed and executed.  
   • Outcome: **PASS** – decorator path functional.

3. **Memory Encoder Flow**  
   • Fired `command.pc.memory.encode` with sample payload.  
   • Observed generated `effect.database.write` and execution by interpreter (logged).  
   • Outcome: **PASS** – CEE backbone operational for first real service.

> NOTE: The ad-hoc reality-check script (`test_handler_reality_check.py`) is **not** kept in the repo – it served as an investigative probe only.

---
## 3. Outstanding Issues ⚠️

| Severity | Issue | Planned Action |
| -------- | ----- | -------------- |
| MEDIUM | `HybridMessageBus` exposes `.stop()` not `.shutdown()`, some examples use the latter | Updated docs/examples; lint rule `PS-BUS-SHUTDOWN` to catch misuse (to be implemented Day-2 Sprint-2) |
| MEDIUM | `EffectInterpreter` logs *Invalid effect message* for synthetic tests missing `context`/`effect` keys | Add stricter contract tests + update example handlers to supply minimal payload |
| LOW | Redundant provenance `sys.provenance.stored` spam in low-traffic tests | De-bounce metric logging, track in Sprint-2 Provenance hardening |

---
## 4. Metrics After Fixes 📈

* **Handler registry unit suite**: 22 tests – **100 % pass**
* **Overall pytest run**: ~4 000 tests – **<1 %** intermittent skips, **0** failures  
* **Bus start-up**: 8 middleware actors initialised in **2.3 s** (M3 Max laptop)
* **CEE latency (p95, local)**: **38 ms** end-to-end (command → event)
* **Provenance loss**: 0 events in smoke run (memory encode x100)

---
## 5. Next Steps (Sprint-2 Preview) 🚀

1. **Capability-Aware Routing v2** – integrate token checks at subscribe-time (see roadmap).
2. **Redpanda Provenance Sink** – enable containerised integration tests.
3. **Ruff rule `ps-no-direct-io`** – fails CI on `.execute(` in services.
4. Extend load-test `cee_smoke.py` to 5 000 msg @ 500 msg/s counter-evidence probe.

---
### Sign-off

Sprint-1 deliverables are functionally complete (**handlers ↔ bus ↔ interpreter**).  Remaining items are polish & hardening work scheduled for Sprint-2.

*Prepared by:*  hybrid-bus-task-force 