# Actor-Bus Architecture Relationship

## Executive Summary

The HybridMessageBus is **NOT being replaced** - it remains the central nervous system of Person Suit. Actors complement the bus by providing supervised, restartable business logic containers. The bus handles all routing, prioritization, and ACF adaptation, while actors focus purely on business logic and return declarative Effects.

## Architectural Overview

### The HybridMessageBus Role
- **Central Communication Backbone**: All inter-component communication flows through the bus
- **Channel-Based Routing**: Fast topic-based routing with wildcards
- **Priority Management**: Integer-based priority queuing (0-1,000,000 scale)
- **ACF Adaptation**: Adjusts message fidelity based on system load
- **Capability-Based Security**: Filters subscriptions based on capabilities
- **Provenance Tracking**: Records all message flows for audit

### The Actor System Role
- **Business Logic Containers**: Actors encapsulate stateful business logic
- **Supervision Hierarchy**: Automatic restart and failure handling
- **Effect-Based I/O**: All I/O operations return Effects, not perform them
- **Lifecycle Management**: Proper startup/shutdown sequences
- **Context Propagation**: CAW context flows through actor messages

### The Bridge Pattern
The `BusActorBridge` connects these two worlds:
```python
# Bus publishes a message
bus.send(HybridMessage(channel="health.check.request"))

# Bridge subscribes and routes to actor
bridge.subscribe("health.check.*", health_monitor_actor)

# Actor receives and returns Effect
effect = CheckHealth(component="memory", check_id="mem_001")

# Effect interpreter executes and publishes result
bus.send(HybridMessage(channel="health.check.complete", payload=result))
```

## Canonical Actor Standards

### 1. Base Actor (Simple Pattern)
Used for foundation services that need basic message handling:
```python
class HealthMonitorActor(Actor):
    async def receive(self, message: StandardActorMessage) -> Optional[Effect]:
        # Handle message, return Effect
        return CheckHealth(...)
```

### 2. DecoupledActor (CAW-Aware Pattern)
Used for components that need context composition and ACF:
```python
class PersonaCoreActor(DecoupledActor):
    async def receive_with_context(
        self, 
        message: StandardActorMessage,
        context: UnifiedContext,
        fidelity: int
    ) -> None:
        # Business logic with context and fidelity awareness
```

## Monitoring Coverage Analysis

### Current Foundation Actors Coverage

| Thread-Based Component | Actor Replacement | Functionality Covered | Status |
|------------------------|-------------------|----------------------|--------|
| HealthMonitor | HealthMonitorActor | ✅ Periodic health checks<br/>✅ Component registration<br/>✅ Health aggregation | Complete |
| ResourceOptimizationMetricsCollector | MetricsCollectorActor | ✅ Metric collection<br/>✅ Multiple collectors<br/>✅ Periodic scheduling | Complete |
| RuntimeMonitor | RuntimeVerificationActor | ✅ Rule registration<br/>✅ Event buffering<br/>✅ Batch verification | Complete |
| EnergyHarvester/Scheduler | EnergyManagementActor | ✅ Energy source management<br/>✅ Task scheduling<br/>✅ Resource allocation | Complete |
| Dashboard threads | DashboardActor | ✅ Dashboard registration<br/>✅ Periodic updates<br/>✅ Data push | Complete |

### Key Architectural Improvements

1. **No Blocking Operations**: All actors use `asyncio`, no `time.sleep()`
2. **Declarative Effects**: Actors return Effect descriptions, not perform I/O
3. **Supervision**: All actors can be restarted on failure
4. **Message-Based**: All communication through bus channels
5. **Context Propagation**: CAW context flows through all operations

## Integration Points

### 1. Bootstrap Integration
```python
# Main bootstrap creates actors under supervision
supervisor_ref = await actor_system.create_actor(
    FoundationSupervisorActor,
    name="foundation_supervisor"
)

# Create child actors
health_ref = await actor_system.create_actor(
    HealthMonitorActor,
    parent=supervisor_ref
)

# Bridge connects bus channels to actors
bridge = await create_bus_actor_bridge(bus, {
    "health.check.*": health_ref,
    "metrics.collect.*": metrics_ref,
})
```

### 2. Effect Flow
```
Bus Message → Actor → Effect → Interpreter → Bus Event
```

### 3. Supervision Hierarchy
```
FoundationSupervisorActor
├── HealthMonitorActor
├── MetricsCollectorActor
├── RuntimeVerificationActor
├── EnergyManagementActor
└── DashboardActor
```

## Design Decisions

### Why Not Replace the Bus?
1. **Proven Infrastructure**: The bus is battle-tested and optimized
2. **Channel Routing**: Efficient wildcard-based routing
3. **ACF Integration**: Load-based adaptation built-in
4. **Backward Compatibility**: Existing components continue to work

### Why Add Actors?
1. **Supervision**: Automatic failure recovery
2. **State Management**: Actors naturally encapsulate state
3. **Scalability**: Actor model scales horizontally
4. **Clean Separation**: Business logic isolated from infrastructure

### Why the Bridge?
1. **Protocol Translation**: HybridMessage ↔ StandardActorMessage
2. **Decoupling**: Actors don't know about bus internals
3. **Flexibility**: Can route same channel to multiple actors

## Migration Strategy

### Phase 1: Foundation (Current)
- ✅ Create foundation actors
- ✅ Integrate with ActorSystem
- ✅ Bridge bus to actors
- 🟡 Remove thread-based components

### Phase 2: Meta-Systems
- Convert PersonaCore to actors
- Convert Analyst to actors
- Convert Predictor to actors
- Maintain bus for inter-system communication

### Phase 3: Full Integration
- All long-running services as actors
- Bus handles all routing
- Complete supervision coverage
- Zero threads in core

## Conclusion

The Actor-Bus architecture is a **complementary system**, not a replacement:
- **Bus**: Infrastructure for routing, priority, security, and adaptation
- **Actors**: Business logic containers with supervision and effects
- **Bridge**: Seamless integration between the two worlds

This design achieves the best of both worlds: the proven infrastructure of the HybridMessageBus with the supervision and state management benefits of the Actor model, all while maintaining the CAW principles of declarative effects and context propagation. 