# Canonical Actor Standards

## Executive Summary

The Person Suit codebase currently has **multiple competing actor implementations** that need consolidation. The canonical standard should be based on `person_suit/core/actors/base.py` with `DecoupledActor` from `caw_actor.py` for CAW-aware components.

## Current Actor Implementations

### 1. **Base Actor** (`core/actors/base.py`) ✅ CANONICAL
- Standard implementation for all actors
- Provides lifecycle management (pre_start, post_stop, etc.)
- Message handling with mailbox queue
- Supervision support via ActorSystem
- Used by: Foundation actors, service wrappers, system actors

### 2. **DecoupledActor** (`core/actors/caw_actor.py`) ✅ CANONICAL for CAW
- Extends base Actor with CAW awareness
- Adds UnifiedContext composition
- Integrates ACF (Adaptive Computational Fidelity)
- Simplified `receive_with_context` hook
- Used by: Meta-system actors (PersonaCore, Analyst, Predictor)

### 3. **Enhanced DecoupledActor** (`core/actors/enhanced_actors.py`) ❌ DUPLICATE
- Completely different implementation (doesn't extend base Actor!)
- Has its own message type (DecoupledMessage vs StandardActorMessage)
- Implements own lifecycle and supervision
- **Should be deprecated** - violates single actor standard

### 4. **Enhanced CAW Actors** (`core/actors/enhanced_caw_actors.py`) ❌ DUPLICATE
- Another duplicate DecoupledActor implementation
- Also doesn't extend base Actor
- **Should be deprecated**

## Inconsistencies Found

1. **Multiple DecoupledActor Classes**
   - `caw_actor.py`: Extends Actor (correct)
   - `enhanced_actors.py`: Standalone implementation (incorrect)
   - `enhanced_caw_actors.py`: Another standalone (incorrect)

2. **Different Message Types**
   - Canonical: `StandardActorMessage` from base.py
   - Enhanced: `DecoupledMessage` (incompatible!)

3. **Incompatible Supervision**
   - Canonical: ActorSystem handles supervision
   - Enhanced: Actors handle their own supervision

## Recommended Actions

### 1. Immediate Deprecation
```python
# Mark these files for deletion:
- person_suit/core/actors/enhanced_actors.py
- person_suit/core/actors/enhanced_caw_actors.py
- person_suit/core/actors/legacy/
```

### 2. Canonical Pattern Usage

#### For Simple Actors:
```python
from person_suit.core.actors.base import Actor, StandardActorMessage

class MyServiceActor(Actor):
    async def receive(self, message: StandardActorMessage) -> None:
        # Handle message, return Effects
        if message.type == "health_check":
            return CheckHealth(...)
```

#### For CAW-Aware Actors:
```python
from person_suit.core.actors.caw_actor import DecoupledActor
from person_suit.core.context.unified import UnifiedContext

class MyCAWActor(DecoupledActor):
    async def receive_with_context(
        self,
        message: StandardActorMessage,
        context: UnifiedContext,
        fidelity: int
    ) -> None:
        # Business logic with context and fidelity
```

### 3. Migration Path

1. **Update all actors to use canonical base classes**
2. **Convert DecoupledMessage to StandardActorMessage**
3. **Remove custom supervision - use ActorSystem**
4. **Delete duplicate implementations**

## Actor-Bus Integration Standard

### Message Flow
1. **HybridMessageBus** receives external messages
2. **BusActorBridge** subscribes to channels and routes to actors
3. **Actors** process messages and return Effects
4. **EffectInterpreter** executes Effects
5. **Events** published back to bus

### Key Principles
- Actors **never** perform I/O directly
- All side effects through Effects
- Bus handles routing, not actors
- ActorSystem handles supervision, not actors
- One message type: StandardActorMessage

## Monitoring Coverage Verification

The foundation actors **do** cover all monitoring functionality:

| Function | Thread Version | Actor Version | Coverage |
|----------|----------------|---------------|----------|
| Health Checks | HealthMonitor.check_health() | HealthMonitorActor + CheckHealth effect | ✅ |
| Metrics Collection | MetricsCollector.collect() | MetricsCollectorActor + CollectMetrics effect | ✅ |
| Anomaly Detection | AnomalyDetector.detect() | RuntimeVerificationActor + DetectAnomaly effect | ✅ |
| Dashboard Updates | Dashboard.update() | DashboardActor + UpdateDashboard effect | ✅ |
| Resource Monitoring | ResourceMonitor.monitor() | MetricsCollectorActor + MonitorResources effect | ✅ |
| Energy Management | EnergyScheduler.schedule() | EnergyManagementActor + HarvestEnergy effect | ✅ |

## Conclusion

1. **Use only canonical implementations** from base.py and caw_actor.py
2. **Delete all duplicate actor implementations**
3. **HybridMessageBus remains the backbone** - actors complement it
4. **All monitoring functionality is covered** by foundation actors
5. **Enforce single message type** (StandardActorMessage) across all actors 