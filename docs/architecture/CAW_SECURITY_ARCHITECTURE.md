# CAW Security Architecture for Universal Deployment

## Overview

This document outlines the security architecture for the Person Suit system, designed to scale from nanobots (1KB RAM) to quantum computers while maintaining the CAW (Contextual Adaptive Wave Programming) principles. The security model itself exhibits wave-particle duality and adapts to deployment constraints.

## Core Security Principles

### 1. Adaptive Security Fidelity

Security measures scale with available resources and threat models:

- **High Fidelity (Servers/Quantum)**: Full cryptographic proofs, formal verification, complete audit trails
- **Medium Fidelity (Edge/Drones)**: Session-based crypto, MAC codes, sampled auditing
- **Low Fidelity (Nanobots)**: Pre-shared keys, channel-based trust, no auditing

### 2. Wave-Particle Security Duality

Security tokens and capabilities exhibit dual nature:

- **Wave State**: Probabilistic trust, potential access rights, speculative authorization
- **Particle State**: Deterministic proofs, specific permissions, measured access
- **Superposition**: Capabilities that resolve upon observation/use

### 3. Effect-Based Security

Control what effects messages can cause rather than what operations they can perform:

- Effect quotas (e.g., "max 1MB memory writes per second")
- Effect chains tracking causality
- Effect capabilities as first-class tokens

## Security Models by Deployment Profile

### Nanobots (1KB RAM, <1MHz CPU)

```python
security_profile = {
    "crypto": "none",  # Pre-shared keys only
    "auth": "channel_based",  # Subscribe = authorize
    "verification": "trust_on_first_use",
    "overhead": "near_zero",
    "trust_model": "physical_proximity"
}
```

**Key Features:**
- No public key cryptography (too expensive)
- Simple XOR or lightweight stream ciphers if needed
- Authorization through physical deployment
- Trust established at manufacturing time

### Drones/Edge Devices (1GB RAM, Real-time)

```python
security_profile = {
    "crypto": "chacha20_poly1305",  # Fast AEAD
    "signatures": "ed25519",  # If needed
    "auth": "session_capabilities",
    "verification": "periodic_mac",
    "overhead": "5-10%",
    "trust_model": "group_attestation"
}
```

**Key Features:**
- Lightweight authenticated encryption
- Group signatures for swarm scenarios
- Time-bounded capabilities
- Real-time constraints prioritized

### Servers (64GB RAM, Full Compute)

```python
security_profile = {
    "crypto": "aes256_gcm",
    "signatures": "ecdsa_p384",
    "auth": "full_capability_tokens",
    "verification": "every_message",
    "overhead": "acceptable",
    "trust_model": "zero_trust"
}
```

**Key Features:**
- Industry-standard cryptography
- Complete message authentication
- Full audit trails
- Hardware security module support

### Quantum-Ready Environments

```python
security_profile = {
    "crypto": "kyber1024_dilithium5",  # Post-quantum
    "signatures": "sphincs+",
    "auth": "quantum_resistant_tokens",
    "verification": "formal_proofs",
    "overhead": "significant",
    "trust_model": "cryptographic_proof"
}
```

**Key Features:**
- Post-quantum algorithms
- Larger key sizes
- Hybrid classical/quantum schemes
- Future-proof token formats

## Channel-Based Security

### Channel Security Definitions

```python
CHANNEL_SECURITY = {
    # Critical system channels
    "sys.acf.adjust": {
        "min_capability": "system_admin",
        "crypto_required": True,
        "audit": "always",
        "effect_limit": "system_wide"
    },
    
    # Memory operations
    "pc.memory.write": {
        "min_capability": "memory_write",
        "crypto_required": "adaptive",  # Based on content
        "audit": "sampled",
        "effect_limit": "1MB/second"
    },
    
    # Public channels
    "sys.health.check": {
        "min_capability": None,  # Public
        "crypto_required": False,
        "audit": False,
        "effect_limit": "read_only"
    },
    
    # Folded mind channels
    "pc.folded_mind.integrate": {
        "min_capability": "cognition",
        "crypto_required": "adaptive",
        "audit": "adaptive",
        "effect_limit": "cognitive_state"
    }
}
```

### Channel Subscription Authorization

```python
def authorize_subscription(channel: str, subscriber: str, capability: Token) -> bool:
    """Authorize channel subscription based on deployment profile."""
    
    if deployment.profile == "nanobot":
        # Pre-authorized at deployment
        return channel in deployment.allowed_channels
        
    elif deployment.profile == "edge":
        # Capability-based with caching
        return verify_capability_cached(capability, channel)
        
    else:  # Server/quantum
        # Full cryptographic verification
        return verify_capability_crypto(capability, channel)
```

## Capability Token Design

### Adaptive Capability Structure

```python
@dataclass
class AdaptiveCapability:
    """Security capability that adapts to deployment environment."""
    
    # Core fields (always present)
    rights: List[str]  # What can be done
    channels: List[str]  # Where it can be done
    expires: float  # When it expires
    
    # Adaptive fields (present based on deployment)
    signature: Optional[bytes] = None  # For crypto-capable devices
    mac: Optional[bytes] = None  # For lightweight auth
    issuer: Optional[str] = None  # For audit trails
    
    # CAW fields
    wave_particle_ratio: float = 0.5  # Deterministic vs probabilistic
    confidence: float = 1.0  # Trust level
    context_bound: bool = True  # Only valid in issuing context
    
    # Effect limits
    effect_quotas: Dict[str, Any] = field(default_factory=dict)
    
    def verify(self, deployment_profile: str) -> bool:
        """Verify capability based on deployment profile."""
        if deployment_profile == "nanobot":
            return self.expires > time.time()  # Just check expiry
        elif deployment_profile == "edge":
            return verify_mac(self.mac) and self.expires > time.time()
        else:
            return verify_signature(self.signature) and self.expires > time.time()
```

### Capability Propagation

```python
def propagate_capability(parent_cap: AdaptiveCapability, 
                        attenuation: Dict[str, Any]) -> AdaptiveCapability:
    """Create attenuated child capability."""
    child = AdaptiveCapability(
        rights=[r for r in parent_cap.rights if r in attenuation.get("rights", [])],
        channels=[c for c in parent_cap.channels if c in attenuation.get("channels", [])],
        expires=min(parent_cap.expires, attenuation.get("expires", float('inf'))),
        wave_particle_ratio=parent_cap.wave_particle_ratio * 0.9,  # Slightly more deterministic
        confidence=parent_cap.confidence * attenuation.get("confidence_factor", 0.95)
    )
    
    # Reduce effect quotas
    for effect, quota in parent_cap.effect_quotas.items():
        child.effect_quotas[effect] = quota * 0.8
    
    return child
```

## Zero-Overhead Security Strategies

### 1. Compile-Time Security

```python
# Security checks that can be eliminated at compile time
if DEPLOYMENT_TRUSTED:
    # These become no-ops in trusted environments
    verify_capability = lambda cap: True
    check_effect_quota = lambda effect, amount: True
else:
    # Full security in untrusted environments
    verify_capability = full_capability_verification
    check_effect_quota = full_quota_checking
```

### 2. Trust Domains

```python
TRUST_DOMAINS = {
    "internal": {  # Within same process/device
        "verification": "none",
        "crypto": "none",
        "overhead": "zero"
    },
    "local": {  # Same physical device
        "verification": "lightweight",
        "crypto": "optional",
        "overhead": "minimal"
    },
    "remote": {  # Network communication
        "verification": "full",
        "crypto": "required",
        "overhead": "acceptable"
    }
}
```

### 3. Security Effect Budgets

```python
SecurityEffects = {
    "verify_signature": {
        "latency_ms": 5,
        "cpu_cycles": 100000,
        "memory_bytes": 4096
    },
    "verify_mac": {
        "latency_ms": 0.5,
        "cpu_cycles": 5000,
        "memory_bytes": 256
    },
    "check_capability": {
        "latency_ms": 0.1,
        "cpu_cycles": 1000,
        "memory_bytes": 128
    }
}
```

## ACF-Driven Security

### Security Fidelity Mapping

```python
def get_security_level(acf_fidelity: float) -> SecurityLevel:
    """Map ACF fidelity to security level."""
    if acf_fidelity >= 0.9:
        return SecurityLevel.MILITARY_GRADE  # Full crypto, formal verification
    elif acf_fidelity >= 0.7:
        return SecurityLevel.ENTERPRISE  # Standard crypto, audit trails
    elif acf_fidelity >= 0.5:
        return SecurityLevel.BASIC  # Lightweight crypto, sampled audit
    elif acf_fidelity >= 0.3:
        return SecurityLevel.MINIMAL  # MAC only, no audit
    else:
        return SecurityLevel.TRUST_BASED  # Channel authorization only
```

### Dynamic Security Adaptation

```python
def adapt_security(msg: HybridMessage, system_load: float) -> SecurityConfig:
    """Adapt security based on message and system state."""
    
    base_security = get_security_level(msg.acf_metadata.fidelity)
    
    # Reduce security under extreme load
    if system_load > 0.9 and msg.priority < 0.7:
        base_security = downgrade_security(base_security)
    
    # Enhance security for critical channels
    if msg.channel.startswith("sys.") and msg.priority > 0.8:
        base_security = upgrade_security(base_security)
    
    return SecurityConfig(
        level=base_security,
        crypto_enabled=base_security >= SecurityLevel.BASIC,
        audit_enabled=base_security >= SecurityLevel.ENTERPRISE,
        effect_tracking=base_security >= SecurityLevel.MINIMAL
    )
```

## Quantum-Ready Architecture

### Crypto Agility Layer

```python
class CryptoProvider(ABC):
    """Abstract crypto provider for algorithm agility."""
    
    @abstractmethod
    def encrypt(self, data: bytes, key: bytes) -> bytes:
        pass
    
    @abstractmethod
    def sign(self, data: bytes, key: bytes) -> bytes:
        pass
    
    @abstractmethod
    def verify(self, data: bytes, signature: bytes, key: bytes) -> bool:
        pass

# Implementations
class ClassicalCrypto(CryptoProvider):
    """Current classical algorithms."""
    pass

class PostQuantumCrypto(CryptoProvider):
    """Quantum-resistant algorithms."""
    pass

class HybridCrypto(CryptoProvider):
    """Both classical and post-quantum in parallel."""
    pass

# Runtime selection
def get_crypto_provider(deployment: DeploymentProfile) -> CryptoProvider:
    if deployment.quantum_threat_model:
        return HybridCrypto()  # Use both during transition
    elif deployment.constraints.cpu_mhz < 100:
        return NoCrypto()  # Too constrained
    else:
        return ClassicalCrypto()  # Standard crypto
```

### Future-Proof Token Format

```python
# Designed to accommodate larger post-quantum signatures
TOKEN_FORMAT = {
    "version": 2,  # Version for upgrades
    "algorithm": "variable",  # Algorithm identifier
    "key_size": "variable",  # Support up to 8KB keys
    "signature_size": "variable",  # Support up to 16KB signatures
    "metadata": {
        "quantum_resistant": False,
        "hybrid_mode": False,
        "algorithm_oid": ""
    }
}
```

## Implementation Priorities

### Phase 1: Channel-Based Authorization (Zero Overhead)
- Implement channel security definitions
- Add subscription authorization checks
- Deploy with trusted channel lists

### Phase 2: Adaptive Capabilities (Low Overhead)
- Design capability token format
- Implement lightweight verification
- Add effect quota tracking

### Phase 3: Cryptographic Security (Medium Overhead)
- Add crypto provider abstraction
- Implement classical algorithms
- Enable for high-fidelity messages only

### Phase 4: Quantum Readiness (Future)
- Add post-quantum providers
- Implement hybrid schemes
- Gradual rollout based on threat model

## Security Metrics

### Performance Impact Targets

```python
SECURITY_OVERHEAD_TARGETS = {
    "nanobot": "< 1% CPU, < 100 bytes RAM",
    "edge": "< 5% CPU, < 1KB RAM",
    "server": "< 10% CPU, < 1MB RAM",
    "quantum": "< 20% CPU, < 10MB RAM"  # Post-quantum is expensive
}
```

### Security Effectiveness Metrics

```python
SECURITY_METRICS = {
    "unauthorized_access_prevented": Counter,
    "effect_quota_violations_blocked": Counter,
    "crypto_verifications_performed": Counter,
    "average_verification_latency_ms": Histogram,
    "security_downgrades_due_to_load": Counter
}
```

## Open Questions for Implementation

1. **Capability Storage**: Where do we store capabilities in memory-constrained environments?
2. **Key Distribution**: How do we distribute keys to nanobots during manufacturing?
3. **Revocation**: How do we revoke capabilities in disconnected environments?
4. **Attestation**: How do edge devices prove their deployment profile?
5. **Audit Storage**: Where do we store audit logs in constrained environments?

## Conclusion

The CAW security architecture achieves military-grade security when resources allow while scaling down to near-zero overhead for constrained deployments. By making security itself subject to Adaptive Computational Fidelity, we ensure the system remains universal while providing appropriate protection for each deployment scenario.

The key insight is that security, like computation in CAW, exhibits wave-particle duality - sometimes we need cryptographic proof (particle), sometimes probabilistic trust (wave) suffices. The system adapts its security posture based on context, resources, and threat model, ensuring both protection and performance across the entire deployment spectrum from nanobots to quantum computers. 