# HybridMessageBus Singleton Pattern

## Overview

The HybridMessageBus is implemented as a hard singleton to ensure:
1. **True singleton behavior** - Only one instance can exist in the entire system
2. **CAW alignment** - Separation of pure operations (getting reference) from effects (starting bus)
3. **Consistent initialization** - All consumers get the same properly initialized instance
4. **Thread safety** - Singleton access is guaranteed to be thread-safe

## Key Design Decisions

### Synchronous Factory Function

The `get_message_bus()` factory function is **synchronous**, not async:

```python
def get_message_bus(profile: Optional[DeploymentProfile] = None) -> HybridMessageBus:
    """Return the global HybridMessageBus singleton."""
```

This aligns with CAW principles:
- **Getting a reference is pure** - No side effects, no I/O
- **Starting the bus is an effect** - Must be done explicitly via `await bus.start()`

### Runtime Enforcement

Direct instantiation is **forbidden** and will raise a `RuntimeError`:

```python
# ❌ This will raise RuntimeError
bus = HybridMessageBus()

# ✅ This is the only way to get the bus
bus = get_message_bus()
```

The enforcement mechanism uses a class-level flag:

```python
class HybridMessageBus(BusKernel):
    _creating_singleton = False  # Tracks if we're creating via factory
    
    def __init__(self, profile: Optional[DeploymentProfile] = None):
        if not HybridMessageBus._creating_singleton:
            raise RuntimeError(
                "Direct instantiation of HybridMessageBus is forbidden. "
                "Use get_message_bus() factory function instead."
            )
```

### Lifecycle Management

The bus lifecycle is explicitly managed:

```python
# Get reference (pure)
bus = get_message_bus()

# Start the bus (effect)
if not bus.is_running():
    await bus.start()

# Use the bus
await bus.send(message)

# Stop the bus (effect)
await stop_message_bus()
```

## Migration Guide

### Before (Async Factory with Autostart)

```python
# Old pattern - mixed concerns
bus = await get_message_bus()  # Got reference AND started it
await bus.send(message)
```

### After (Sync Factory, Explicit Start)

```python
# New pattern - separated concerns
bus = get_message_bus()         # Pure - just get reference
if not bus.is_running():
    await bus.start()           # Effect - explicitly start
await bus.send(message)
```

### Migration Script

A migration script was created to update all code:
```bash
python scripts/maintenance/fix_get_message_bus_calls.py
```

This script successfully migrated 105 files from the old pattern to the new one.

## Architectural Enforcement

### Ruff Plugin: PS005

A custom Ruff plugin (`ps_no_direct_bus_construction.py`) enforces the singleton pattern:

- **Error Code**: PS005
- **Message**: "Direct HybridMessageBus construction detected. Use get_message_bus() factory instead."
- **Whitelisted Files**: 
  - `person_suit/core/infrastructure/hybrid_message_bus.py` (where it's defined)
  - Test files that test the enforcement itself

### Running the Check

The architectural lint check can be run via:
```bash
python tools/ruff_plugins/ps_no_direct_bus_construction.py person_suit/
```

Or integrate it into your CI pipeline.

## Best Practices

### 1. Always Use the Factory

```python
from person_suit.core.infrastructure.hybrid_message_bus import get_message_bus

# Good
bus = get_message_bus()

# Bad - will raise RuntimeError
from person_suit.core.infrastructure.hybrid_message_bus import HybridMessageBus
bus = HybridMessageBus()
```

### 2. Check Running State

```python
bus = get_message_bus()
if not bus.is_running():
    await bus.start()
```

### 3. Handle Lifecycle in Bootstrap

The `CanonicalBootstrap` handles bus lifecycle:

```python
class CanonicalBootstrap:
    async def _initialize_core_infrastructure(self):
        self.message_bus = get_message_bus(self.profile)
        await self.message_bus.start()
```

### 4. Test Isolation

Tests should use the global singleton but ensure clean state:

```python
@pytest.fixture
async def clean_bus():
    bus = get_message_bus()
    if bus.is_running():
        await bus.stop()
    yield bus
    if bus.is_running():
        await bus.stop()
```

## Common Pitfalls

### 1. Forgetting to Start the Bus

```python
# ❌ Wrong - bus not started
bus = get_message_bus()
await bus.send(message)  # Will hang!

# ✅ Correct
bus = get_message_bus()
if not bus.is_running():
    await bus.start()
await bus.send(message)
```

### 2. Multiple Start Calls

```python
# ✅ Safe - start() is idempotent
bus = get_message_bus()
await bus.start()
await bus.start()  # No-op if already running
```

### 3. Import Confusion

```python
# ❌ Don't import the class directly
from person_suit.core.infrastructure.hybrid_message_bus import HybridMessageBus

# ✅ Import the factory function
from person_suit.core.infrastructure.hybrid_message_bus import get_message_bus
```

## Future Considerations

1. **Thread-Local Storage**: Currently uses global module state. May need thread-local storage for multi-threaded applications.
2. **Multiple Profiles**: Currently supports only one global bus. May need named instances for different deployment profiles.
3. **Async Context Manager**: Consider adding async context manager support for automatic lifecycle management.

## Related Documentation

- [CAW Principles](../CAW_CORE_PRINCIPLES_AND_FOUNDATIONS.md)
- [Bootstrap Unification Roadmap](../../roadmaps/bootstrap_unification_roadmap.md)
- [Architecture Overview](ARCHITECTURE_OVERVIEW.md)

## Thread Safety

The singleton implementation uses proper thread safety mechanisms:

### Implementation Details

```python
_bus: Optional[HybridMessageBus] = None
_bus_lock = threading.Lock()  # Thread safety for singleton creation

def get_message_bus(profile: Optional[DeploymentProfile] = None) -> HybridMessageBus:
    """Thread-safe singleton factory using double-checked locking pattern."""
    global _bus
    
    # Fast path: if bus exists, return it (no lock needed)
    if _bus is not None:
        return _bus
    
    # Slow path: acquire lock and create bus if needed
    with _bus_lock:
        # Double-check pattern: another thread might have created it
        if _bus is None:
            HybridMessageBus._creating_singleton = True
            try:
                _bus = HybridMessageBus(profile)
                _assert_singleton_invariant()
            finally:
                HybridMessageBus._creating_singleton = False
    
    return _bus
```

### Thread Safety Features

1. **Double-Checked Locking**: Optimizes performance by checking the instance twice - once without lock (fast path) and once with lock (safe path)
2. **Thread Lock**: Uses `threading.Lock()` to ensure only one thread can create the singleton
3. **Flag Protection**: The `_creating_singleton` flag prevents race conditions during instantiation
4. **Atomic Operations**: The `stop_message_bus()` function also uses the lock for thread-safe cleanup

### Thread Safety Tests

Comprehensive thread safety tests have been implemented in `tests/core/infrastructure/test_bus_thread_safety.py`:

- **Concurrent Access**: Multiple threads calling `get_message_bus()` simultaneously
- **High Concurrency**: 1000 calls from 50 worker threads  
- **Race Conditions**: Artificial delays to expose potential race conditions
- **Memory Consistency**: All threads see the same singleton state
- **Mixed Async/Sync**: Thread pool operations mixed with async coroutines

All tests pass successfully, confirming thread-safe singleton behavior. 