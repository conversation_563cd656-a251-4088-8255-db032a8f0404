# Guiding Principles for the PersonSuit Security Architecture

## 1. Introduction: From Reactive to Predictive Security

This document codifies the foundational architectural principles for the PersonSuit security system. It serves as the guiding constitution for all development, ensuring every component, from a simple event handler to a complex learning model, contributes to a holistic, intelligent, and resilient security fabric.

Our philosophy is a departure from traditional, reactive security models. We are not building a system that merely responds to static rules; we are architecting a **predictive, self-tuning, and context-aware security ecosystem** that learns from the system it protects. These principles are the laws that govern that ecosystem. Adherence is not optional.

## 2. The Five Core Principles

### I. The Principle of Absolute Decoupling
**"Components shall not know each other. They shall only know of contracts and the bus."**

- **Law**: Direct communication between security components (via imports and method calls) is strictly forbidden. All interactions must be mediated asynchronously through the `HybridMessageBus`.
- **Implementation**: A component (e.g., `SecurityManager`) publishes an `Event` to a topic. It has zero knowledge of whether zero, one, or many components are listening. Subscribers (e.g., `SecurityAlertHandler`) listen to topics and react to `Events`. They have zero knowledge of who published the event.
- **Litmus Test**: If you need to add `import ApplicationLayerComponent` to a `CoreLayerComponent`, your design is flawed. The relationship must be inverted, with both components depending on an abstraction (the event contract) in the `shared` layer.

### II. The Principle of Contextual Supremacy
**"An event without context is noise. An action without context is reckless."**

- **Law**: Every security-relevant event published and every action taken **must** be accompanied by the `UnifiedContext` of the operation. There are no exceptions.
- **Implementation**: The `Event` base model, which wraps all payloads, must contain a non-optional `context: UnifiedContext` field. When a security component receives an event, its first action should be to inspect the context. The context dictates the priority, severity, and appropriate response.
- **Litmus Test**: If you are about to write `if event.payload.some_value > X:`, you must first ask, "How does the `context` modify the meaning of `X`?". An anomaly that is critical in a user-facing, high-priority context might be informational in a background maintenance context.

### III. The Principle of Capability as Sole Authority
**"Possession of a capability is the only valid form of authority. All else is assumption."**

- **Law**: A component's ability to perform a sensitive action (e.g., publish a critical alert, access PII, modify a security profile) must be explicitly granted by a `Capability` token present in its `UnifiedContext`.
- **Implementation**: Before performing a privileged operation, a component must check `CapabilityManager.has(context, "domain:subdomain:action")`. If the check fails, the action is denied. This applies to both publishing (a component needs the capability to publish) and subscribing (the message bus should verify a subscriber has the capability to receive).
- **Litmus Test**: If you find yourself checking `if user.role == 'admin'`, you are violating this principle. The check must be `if CapabilityManager.has(context, "users:delete")`. Roles are merely a way to *assign* capabilities; they are not the authority itself.

### IV. The Principle of Differentiable by Design
**"A static defense is a predictable defense. All parameters should be learnable."**

- **Law**: Security components should not be designed with static, hard-coded thresholds or parameters. They should be designed to be *learnable*. Parameters should be treated as differentiable tensors (`torch.nn.Parameter` or equivalent).
- **Implementation**: Instead of `if cpu_usage > 0.9:`, the `0.9` should be a learnable parameter. Feedback loops (e.g., an operator confirming a threat was real) must be used to generate a loss signal, which is then backpropagated to tune these parameters automatically.
- **Litmus Test**: If your component relies on a complex set of rules in a config file, ask yourself: "Could an ML model learn these rules more effectively from data?" If the answer is yes, the design must incorporate a differentiable component.

### V. The Principle of CAW Duality
**"See not just the event, but the potential from which it emerged."**

- **Law**: Security information must be treated as having two states: the **Wave** (potential, probabilistic, patterns over time) and the **Particle** (a concrete, discrete event). Our architecture must process both.
- **Implementation**:
    - **Wave Processing**: Specialized `Analyst` or `Detector` components subscribe to raw telemetry streams (CPU, network, API calls). They don't look for single events, but for subtle correlations and anomalous patterns over time. The output is a "threat potential" vector—a wave.
    - **Wave Collapse**: A `Manager` component receives this wave. It applies a threshold (which should be learnable, per Principle IV). If the potential is high enough, it "collapses the wave" and emits a discrete, actionable `SecurityEvent`—a particle.
- **Litmus Test**: Is your system only capable of reacting to single, atomic failure events? If so, it is blind to the subtle, distributed patterns that precede catastrophic failure. You must build components that can integrate information over time to see the developing "wave".

## 3. Architectural Integrity Checklist

Before committing any code to the security subsystem, verify it against these principles. A "no" answer indicates an architectural violation that must be corrected.

1.  **Decoupling**: Does this change introduce any direct import from a higher layer to a lower layer (e.g., `application` into `core`)? (Y/N)
2.  **Context**: Does every event published and action taken carry and respect the `UnifiedContext`? (Y/N)
3.  **Capabilities**: Is every privileged action explicitly gated by a `CapabilityManager` check? (Y/N)
4.  **Differentiability**: Are new parameters or thresholds designed to be learnable, or are they static and brittle? (Learnable/Static)
5.  **Duality**: Does this component contribute to processing "waves" (patterns) or just "particles" (discrete events)? Does it understand the difference? (Y/N) 