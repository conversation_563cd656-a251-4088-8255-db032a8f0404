# Command→Effect→Event Flow Guide

> **Sprint 1 Deliverable**: Documentation with Mermaid diagram and copy-paste ready code snippets

This guide explains the Command→Effect→Event (CEE) flow in the Person Suit message-based architecture. The CEE pattern enables pure business logic separation, testability, and distributed processing.

## Table of Contents

1. [Overview](#overview)
2. [Architecture Flow](#architecture-flow)
3. [Implementation Guide](#implementation-guide)
4. [Code Examples](#code-examples)
5. [Testing](#testing)
6. [Best Practices](#best-practices)

## Overview

The Command → Effect → Event (CEE) pattern is the cornerstone of Person Suit's architecture, implementing the principle: "Components shall not act. They shall only declare intent. The system choreographs the fulfillment of that intent."

## Architecture

```mermaid
graph TD
    A[Component] --sends--> B(Bus: COMMAND)
    B --routes to--> C{Command Handler}
    C --returns--> D(Bus: EFFECT)
    D --routes to--> E[Effect Interpreter]
    E --performs I/O--> F(External System)
    E --publishes--> G(Bus: EVENT)
    G --notifies--> H((Subscribers))
```

## Core Components

### 1. Command Handlers
Pure business logic functions that receive commands and return effect descriptions.

```python
from person_suit.core.handlers.registry import command_handler
from person_suit.core.infrastructure.hybrid_message import HybridMessage, MessageType

@command_handler("command.pc.memory.encode", priority=0)
async def handle_memory_encode(cmd: HybridMessage) -> HybridMessage:
    """Convert command to effect - no I/O performed here!"""
    effect = WriteDatabaseEffect(
        table="memories",
        data=cmd.payload
    )
    
    return HybridMessage(
        message_type=MessageType.EFFECT.name,
        channel="effect.database.write",
        payload={
            "effect": effect.to_dict(),
            "context": cmd.context,
        },
        correlation_id=cmd.correlation_id or cmd.message_id,
    )
```

### 2. Effect Interpreter
The ONLY component that performs actual I/O and side effects.

```python
# Automatically subscribes to effect.* messages
# Validates capabilities
# Executes effects based on ACF strategy
# Publishes completion events
```

### 3. Event Publishers
Effects result in events that notify the system of completion.

## Message Flow Example

1. **Command**: `command.pc.memory.encode`
   ```json
   {
     "message_type": "COMMAND",
     "channel": "command.pc.memory.encode",
     "payload": {
       "content": "User said hello",
       "importance": 0.8
     }
   }
   ```

2. **Effect**: `effect.database.write`
   ```json
   {
     "message_type": "EFFECT",
     "channel": "effect.database.write",
     "payload": {
       "effect": {
         "table": "memories",
         "data": {...}
       },
       "context": {...}
     }
   }
   ```

3. **Event**: `event.pc.memory.encoded`
   ```json
   {
     "message_type": "EVENT",
     "channel": "event.pc.memory.encoded",
     "payload": {
       "memory_id": "123",
       "content": "User said hello"
     }
   }
   ```

## Testing CEE Flows

### ⚠️ Critical Requirements

**All three components MUST be initialized for CEE tests:**

1. **HybridMessageBus** - Routes messages
2. **CommandHandlerRegistry** - Handles commands
3. **EffectInterpreter** - Executes effects

Without the EffectInterpreter, effects are published but never executed!

### Correct Test Setup

```python
@pytest.fixture
async def cee_system():
    """Complete CEE system fixture"""
    # 1. Create and start bus
    bus = HybridMessageBus()
    await bus.start()
    
    # 2. Register command handlers
    registry = get_command_handler_registry()
    registry.register("command.pc.memory.encode", handle_memory_encode)
    await registry.bind_bus(bus)
    
    # 3. Initialize effect interpreter (CRITICAL!)
    interpreter = EffectInterpreter(message_bus=bus)
    await interpreter.initialize()
    
    yield bus, registry, interpreter
    
    # Cleanup
    await interpreter.shutdown()
    await bus.stop()

@pytest.mark.asyncio
async def test_cee_flow(cee_system):
    bus, _, _ = cee_system
    
    # Subscribe to completion events
    events = []
    await bus.subscribe(
        "event.effect.completed",
        lambda msg: events.append(msg)
    )
    
    # Send command with proper context
    context = UnifiedContext(
        domain="test",
        capabilities=["database:memories:write"]
    )
    
    await bus.send(HybridMessage(
        message_type=MessageType.COMMAND.name,
        channel="command.pc.memory.encode",
        payload={"content": "Test", "importance": 0.8},
        context=context.to_dict()
    ))
    
    # Wait for completion
    await asyncio.sleep(0.5)
    assert len(events) > 0
```

## Common Issues

### Test Timeouts
- **Symptom**: Test waits forever for events
- **Cause**: Missing EffectInterpreter initialization
- **Fix**: Ensure all three CEE components are initialized

### Missing Events
- **Symptom**: No completion events received
- **Cause**: Missing capabilities in context
- **Fix**: Include required capabilities in UnifiedContext

### Handler Not Called
- **Symptom**: Command sent but handler never executes
- **Cause**: Handler not registered with bus
- **Fix**: Call `registry.bind_bus(bus)` after registering handlers

## Best Practices

1. **Pure Handlers**: Command handlers must be pure functions - no I/O
2. **Declarative Effects**: Effects describe what should happen, not how
3. **Context Propagation**: Always include context for capability checks
4. **Correlation IDs**: Maintain correlation IDs for request tracing
5. **Test Isolation**: Clear handler registry between tests

## Metrics and Monitoring

Track these key metrics:
- Command processing rate
- Effect execution latency
- Event publication rate
- Effect success/failure ratio
- Capability denial rate

## Next Steps

- [Writing Command Handlers](writing_command_handlers.md)
- [Creating Custom Effects](creating_custom_effects.md)
- [Security and Capabilities](security_capabilities.md)

## Implementation Guide

### Step 1: Define Service with Pure Business Logic

```python
from person_suit.core.effects.database import WriteDatabaseEffect
from person_suit.core.context.unified import UnifiedContext
from person_suit.core.constants.fixed_point_scale import float_to_bucket

class MemoryEncoderService:
    """Pure business logic service - no I/O operations."""
    
    def __init__(self) -> None:
        self.name = "MemoryEncoderService"
    
    def register_encode_command(
        self,
        content: str,
        importance: float,
        context: Optional[UnifiedContext] = None
    ) -> WriteDatabaseEffect:
        """Pure function that returns an Effect instead of doing I/O."""
        if not content:
            raise ValueError("Content cannot be empty")
        
        # Convert float importance to fixed-point bucket
        importance_bucket = float_to_bucket(importance)
        
        # Return Effect - no I/O performed here
        return WriteDatabaseEffect(
            table="memories",
            data={
                "content": content,
                "importance": importance_bucket,
                "timestamp": None,  # Will be set by interpreter
                "context_domain": context.domain if context else "unknown"
            },
            operation="insert"
        )
```

### Step 2: Create Command Handler

```python
from person_suit.core.handlers.registry import command_handler
from person_suit.core.infrastructure.hybrid_message import HybridMessage, MessageType

@command_handler("command.pc.memory.encode")
async def handle_memory_encode_command(message: HybridMessage) -> HybridMessage:
    """Command handler that processes commands and returns effects."""
    service = get_memory_encoder_service()
    
    # Extract data from command
    content = message.payload.get("content", "")
    importance = message.payload.get("importance", 0.5)
    
    # Extract context
    context = None
    if message.context:
        if isinstance(message.context, dict):
            context = UnifiedContext.from_dict(message.context)
        elif isinstance(message.context, UnifiedContext):
            context = message.context
    
    # Call pure business logic
    effect = service.register_encode_command(
        content=content,
        importance=importance,
        context=context
    )
    
    # Return effect message
    return HybridMessage(
        message_type=MessageType.EFFECT.name,
        channel="effect.database.write",
        payload={"effect": effect.to_dict()},
        correlation_id=message.correlation_id,
        context=message.context
    )
```

### Step 3: Register Handler with Message Bus

```python
from person_suit.core.handlers.registry import get_command_handler_registry
from person_suit.core.infrastructure.hybrid_message_bus import get_message_bus

async def setup_cee_flow():
    """Set up the complete CEE flow."""
    # Get message bus and registry
    bus = await get_message_bus()
    registry = get_command_handler_registry()
    
    # Bind registry to bus (auto-subscribes all handlers)
    await registry.bind_bus(bus)
    
    # Handlers are now active and will process commands
    print("✅ CEE flow active - handlers registered")
```

## Code Examples

### Complete Working Example

```python
"""Complete CEE Flow Example

This example shows a complete working implementation of the CEE pattern
for a memory encoding service.
"""
import asyncio
from person_suit.core.infrastructure.hybrid_message_bus import get_message_bus
from person_suit.core.infrastructure.hybrid_message import HybridMessage, MessageType
from person_suit.core.handlers.registry import get_command_handler_registry

# Import the service to register handlers
import person_suit.services.memory_encoder.service

async def main():
    """Run complete CEE flow example."""
    # 1. Set up message bus and handlers
    bus = await get_message_bus()
    registry = get_command_handler_registry()
    await registry.bind_bus(bus)
    
    # 2. Create and send command
    command = HybridMessage(
        message_type=MessageType.COMMAND.name,
        channel="command.pc.memory.encode",
        payload={
            "content": "This is a test memory to encode",
            "importance": 0.8
        },
        correlation_id="example_001"
    )
    
    # 3. Send command (triggers CEE flow)
    result = await bus.send(command, timeout=5.0)
    
    # 4. Verify result
    if result and result.success:
        print("✅ Command processed successfully")
        print(f"   Correlation ID: {command.correlation_id}")
    else:
        print("❌ Command failed")
    
    # 5. Clean up
    await bus.stop()

if __name__ == "__main__":
    asyncio.run(main())
```

### Sending Commands from Client Code

```python
async def send_memory_encode_command(content: str, importance: float):
    """Send a memory encode command from client code."""
    bus = await get_message_bus()
    
    command = HybridMessage(
        message_type=MessageType.COMMAND.name,
        channel="command.pc.memory.encode",
        payload={
            "content": content,
            "importance": importance
        }
    )
    
    result = await bus.send(command, timeout=10.0)
    return result
```

### Subscribing to Events

```python
async def subscribe_to_memory_events():
    """Subscribe to memory-related events."""
    bus = await get_message_bus()
    
    async def handle_memory_encoded(event: HybridMessage):
        """Handle memory encoded events."""
        print(f"Memory encoded: {event.payload}")
    
    await bus.subscribe(
        "event.pc.memory.encoded",
        handle_memory_encoded,
        subscriber_id="memory_event_listener"
    )
```

## Testing

### Pure Unit Tests

```python
import pytest
from person_suit.services.memory_encoder.service import MemoryEncoderService
from person_suit.core.effects.database import WriteDatabaseEffect

class TestMemoryEncoderServicePure:
    """Test pure business logic without I/O."""
    
    def test_encode_command_basic(self):
        """Test basic encoding command."""
        service = MemoryEncoderService()
        
        effect = service.register_encode_command(
            content="Test content",
            importance=0.7
        )
        
        assert isinstance(effect, WriteDatabaseEffect)
        assert effect.table == "memories"
        assert effect.data["content"] == "Test content"
        assert effect.data["importance"] == 700000  # Fixed-point bucket
```

### Integration Tests

```python
import pytest
from person_suit.core.infrastructure.hybrid_message_bus import get_message_bus
from person_suit.core.infrastructure.hybrid_message import HybridMessage, MessageType

@pytest.mark.asyncio
async def test_cee_flow_integration():
    """Test complete CEE flow integration."""
    bus = await get_message_bus()
    registry = get_command_handler_registry()
    await registry.bind_bus(bus)
    
    # Send command
    command = HybridMessage(
        message_type=MessageType.COMMAND.name,
        channel="command.pc.memory.encode",
        payload={"content": "Integration test", "importance": 0.6}
    )
    
    result = await bus.send(command, timeout=5.0)
    
    assert result is not None
    assert result.success
```

## Best Practices

### 1. Keep Services Pure

```python
# ✅ Good: Pure function that returns Effect
def process_command(data: dict) -> WriteDatabaseEffect:
    return WriteDatabaseEffect(table="data", data=data)

# ❌ Bad: Direct I/O in service
def process_command(data: dict):
    database.save(data)  # Direct I/O!
```

### 2. Use Fixed-Point Arithmetic

```python
from person_suit.core.constants.fixed_point_scale import float_to_bucket, bucket_to_float

# ✅ Good: Convert floats to fixed-point buckets
importance_bucket = float_to_bucket(0.75)  # 750000

# ❌ Bad: Using floats directly
importance = 0.75  # Precision issues in ACF calculations
```

### 3. Always Provide Context

```python
# ✅ Good: Include context in effects
effect = WriteDatabaseEffect(
    table="memories",
    data={
        "content": content,
        "context_domain": context.domain if context else "unknown"
    }
)

# ❌ Bad: No context tracking
effect = WriteDatabaseEffect(table="memories", data={"content": content})
```

### 4. Use Correlation IDs

```python
# ✅ Good: Propagate correlation IDs
return HybridMessage(
    message_type=MessageType.EFFECT.name,
    channel="effect.database.write",
    payload={"effect": effect.to_dict()},
    correlation_id=message.correlation_id  # Propagate for tracing
)
```

### 5. Handle Errors Gracefully

```python
@command_handler("command.example.process")
async def handle_process_command(message: HybridMessage) -> Optional[HybridMessage]:
    """Handle command with proper error handling."""
    try:
        service = get_example_service()
        effect = service.process_command(message.payload)
        
        return HybridMessage(
            message_type=MessageType.EFFECT.name,
            channel="effect.database.write",
            payload={"effect": effect.to_dict()},
            correlation_id=message.correlation_id
        )
    except ValueError as e:
        # Log error and return None (no effect)
        logger.error(f"Invalid command data: {e}")
        return None
    except Exception as e:
        # Log unexpected errors
        logger.error(f"Unexpected error processing command: {e}")
        raise  # Re-raise to trigger error handling
```

## Troubleshooting

### Common Issues

1. **Handler Not Registered**: Ensure the service module is imported so handlers are registered
2. **Effect Not Executed**: Check that EffectInterpreter is running and subscribed to effect channels
3. **Context Lost**: Always propagate context and correlation IDs through the flow
4. **Type Mismatches**: Use proper type hints and validate data at service boundaries

### Debugging Tips

```python
# Enable debug logging
import logging
logging.getLogger("person_suit.core.handlers").setLevel(logging.DEBUG)
logging.getLogger("person_suit.core.effects").setLevel(logging.DEBUG)

# Use correlation IDs for tracing
command = HybridMessage(
    message_type=MessageType.COMMAND.name,
    channel="command.pc.memory.encode",
    payload={"content": "Debug test", "importance": 0.8},
    correlation_id="debug_trace_001"  # Easy to grep in logs
)
```

This completes the CEE flow implementation guide. The pattern provides a solid foundation for scalable, testable, and maintainable message-based architectures. 