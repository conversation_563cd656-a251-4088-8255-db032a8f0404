# Canonical Actor Pattern (CAW-Compliant)

This guide defines the **canonical pattern** every `Actor` subclass in *Person Suit* must follow.  It captures the separation-of-concerns contract between Actors, Effects, the Hybrid Message Bus and the EffectInterpreter.

---
## 1. Signature & Responsibility

```python
from person_suit.core.actors.base import Actor, StandardActorMessage

class MyActor(Actor):
    async def receive(self, message: StandardActorMessage) -> None:  # ← ALWAYS `None`
        ...
```

* `receive()` **MUST return `None`**.  Actors ***never*** perform side-effects directly nor return `Effect` objects.
* The only job of an `Actor` is **decision-making**: assemble intent, pick or compose `Effect` objects, route messages.

---
## 2. Expressing Side-Effects

```python
from person_suit.core.infrastructure._message_bridge import dispatch_effect
from person_suit.core.effects.monitoring_effects import CheckHealth

async def receive(self, message: StandardActorMessage) -> None:
    if message.type == "health.check.request":
        effect = CheckHealth(check_id="core", component="bus", config={})
        await dispatch_effect(effect, message.context)  # ← delegate execution
```

* Create concrete `Effect` instances.
* **Delegate execution** via `await dispatch_effect(effect, message.context)` (or by publishing a `HybridMessage` on `effect.<type>`).  This passes control to the central `EffectInterpreter`.

---
## 3. Composing Multiple Effects

```python
from person_suit.core.effects.base import CompositeEffect

compound = CompositeEffect(effects=[effect1, effect2, ...])
await dispatch_effect(compound, message.context)
```

---
## 4. NEVERs

* ❌ **Do not** `return effect` from `receive()`.
* ❌ **Do not** perform blocking I/O or state mutation inside the actor.
* ❌ **Do not** bypass the bus / interpreter with direct method calls.

---
## 5. Helper Function (optional)

```python
aSYNC def emit(effect: Effect, msg: StandardActorMessage) -> None:
    """One-liner wrapper for dispatching with context."""
    await dispatch_effect(effect, msg.context)
```

---
## 6. Lint Rule PS010 – "NoEffectReturn"

A new Ruff custom rule (`PS010`) forbids `return <Effect>` inside any `receive()` method of a subclass of `Actor`.

---
## 7. Rationale

This pattern enforces CAW's *Declarative-Intent* principle: Actors declare **what** should happen; the *EffectInterpreter* decides **how** (ACF, capability checks, etc.).  It guarantees message-based decoupling and keeps all side-effects centrally observable. 