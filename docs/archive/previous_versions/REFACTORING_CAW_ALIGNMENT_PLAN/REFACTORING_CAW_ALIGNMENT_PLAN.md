# Refactoring Plan: Core Alignment with CAW, CBS, ChP, ES

> **Note**: This document outlines a specific refactoring plan focused on aligning the `core/` directory with CAW and related paradigms. For the official, overarching implementation sequence and priorities for the entire project, please refer to [../IMPLEMENTATION_GUIDE.md](../IMPLEMENTATION_GUIDE.md). This plan may be superseded by [REFACTORING_CAW_ALIGNMENT_PLAN_UPDATED.md](./REFACTORING_CAW_ALIGNMENT_PLAN_UPDATED.md).

**Objective:** Refactor the `person_suit/core/` directory to fully align its components (especially infrastructure) with the Contextual Adaptive Wave (CAW) paradigm, Capability-Based Security (CBS), Choreographic Programming (ChP), and Effect Systems (ES), ensuring architectural coherence and robustness.

## Introduction to CAW Principles

Contextual Adaptive Wave (CAW) programming is a paradigm that combines:

1. **Contextual Awareness**: Components adapt their behavior based on the current context, allowing for dynamic responses to changing environments and requirements.

2. **Wave-Particle Duality**: Information inherently possesses both wave and particle aspects simultaneously, following light behavior in physics. This dual representation treats all information as having both wave-like properties (distributed, interference-capable, probabilistic) and particle-like properties (discrete, localized, concrete) at the same time, enabling rich contextual behavior that adapts based on observation and interaction patterns.

3. **Interference Patterns**: Interactions between wave functions create interference patterns that influence decision-making, allowing for complex emergent behaviors.

4. **Adaptive Behavior**: Components dynamically adjust their behavior based on context and wave interference, creating a system that can respond intelligently to novel situations.

5. **Physics-Inspired Unified Representation**: The dual wave-particle representation provides a unified way to model system behavior following principles from quantum mechanics. This representation inherently captures both deterministic and probabilistic aspects, enabling the system to exhibit wave-like behavior (interference, superposition, entanglement) and particle-like behavior (discrete states, localized interactions) simultaneously, with the manifestation depending on the context and observation patterns.

6. **Information Particle Hierarchy**: All information is composed of a hierarchy of fundamental particles, inspired by the Standard Model of particle physics:
   - **Infons**: The most basic units of information, analogous to quarks.
   - **Datoms**: Composite particles formed from infons, analogous to protons and neutrons.
   - **Concepts**: Stable arrangements of datoms, analogous to atoms.
   - **Relations**: Bonds between concepts, analogous to molecular bonds.

7. **Spacetime Continuum**: Information entities exist and interact within a computational spacetime continuum that provides the environment for information propagation, transformation, and interaction. This continuum follows principles from relativity theory, with information propagation speed limits, reference frame effects, and spacetime curvature based on information density.

CAW integrates with other paradigms in the following ways:

- **With Capability-Based Security (CBS)**: Context-sensitive capability verification and dual wave-particle-based security decisions that adapt based on observation patterns and contextual factors. Security capabilities can be represented as special types of information particles with specific quantum numbers that determine their access properties.

- **With Choreographic Programming (ChP)**: Context-aware choreographies with physics-inspired coordination mechanisms, including entanglement between participants, superposition of potential execution paths, and interference-based routing of choreography steps. Choreographies can be represented as field equations in the spacetime continuum, guiding the propagation and interaction of information particles.

- **With Effect Systems (ES)**: Context-dependent effect tracking with dual representation of effects, enabling both precise tracking of localized effects (particle aspect) and distributed propagation of effect patterns (wave aspect). Effects can be modeled as disturbances in the spacetime continuum that propagate according to wave equations.

- **With Differentiable Programming (DP)**: End-to-end gradient-based optimization of parameters within the physics-inspired model, including wave function parameters, information particle configurations, and spacetime properties. This enables learning optimal representations and behaviors through gradient descent.

- **With Probabilistic Programming (PP)**: Reasoning under uncertainty using the inherent probabilistic nature of the wave-particle duality, with probability distributions emerging naturally from wave function properties. The wave aspect provides a natural representation for uncertainty and probabilistic reasoning.

This refactoring plan aims to align the Person Suit core components (primarily within `core/infrastructure/`) with these principles to create a more flexible, context-aware system that can adapt to different operational contexts while maintaining security and predictability.

## Success Criteria

A component is considered successfully aligned with CAW principles when it:

1. **Contextual Awareness**: Accepts and utilizes context information to adapt its behavior based on the current operational environment.

2. **Dual Wave-Particle Integration**: Represents all information using a unified dual representation that inherently possesses both wave and particle aspects simultaneously, enabling rich contextual behavior that adapts based on observation patterns. The implementation follows physics-inspired principles such as superposition, interference, entanglement, and the uncertainty principle. Information is organized in a hierarchy of particles (infons, datoms, concepts, relations) existing within a spacetime continuum.

3. **Physics-Inspired Information Processing**: Implements mechanisms for wave interference, superposition, entanglement, and probabilistic collapse to influence routing, processing, and decision-making. The system leverages these physics-inspired principles to create emergent behaviors and adaptive responses that go beyond traditional deterministic processing. Information processing follows the laws of the computational physics model, including conservation laws, quantum numbers, and field equations in the spacetime continuum.

4. **Clear Context-Sensitive Interfaces**: Provides well-defined interfaces for context-sensitive operations that other components can use.

5. **Backward Compatibility**: Maintains compatibility with existing code through adapter patterns or interface inheritance.

6. **Comprehensive Testing**: Includes tests that demonstrate context-sensitive behavior and wave function interactions.

7. **Documentation**: Is documented with clear examples of context-sensitive usage and wave function interactions.

8. **Performance Considerations**: Maintains or improves performance compared to the non-CAW implementation.

9. **Security Integration**: Integrates with capability-based security in a context-sensitive manner.

10. **Effect Tracking**: Properly declares and tracks effects in a context-sensitive way.

## Risk Assessment and Mitigation

| Risk | Impact | Likelihood | Mitigation Strategy |
|------|--------|------------|---------------------|
| Breaking changes to public APIs | High | Medium | Maintain backward compatibility layers, comprehensive testing, adapter patterns |
| Performance degradation | High | Medium | Benchmark before and after, optimize critical paths, profile context determination |
| Increased complexity | Medium | High | Clear documentation, examples, abstraction layers, helper methods |
| Incomplete refactoring | Medium | Medium | Prioritize core components, ensure partial implementations still function |
| Resource constraints | Medium | High | Focus on high-priority components first, incremental approach |
| Integration challenges | High | Medium | Clear interfaces, comprehensive testing, dependency management |
| Security vulnerabilities | High | Low | Security review, capability verification, formal verification where possible |

## Timeline Estimates

| Phase | Estimated Duration | Dependencies |
|-------|-------------------|---------------|
| Phase 1: Foundational Infrastructure | 4-6 weeks | None |
| Phase 2: Core CAW Integration | 3-4 weeks | Phase 1 |
| Phase 3: Choreography Implementation | 2-3 weeks | Phase 2 |
| Phase 4: Paradigm Integration (Steps 7-17) | 8-10 weeks | Phases 1-3 |
| Phase 5: Wider Alignment & Review | 3-4 weeks | Phases 1-4 |

Total estimated duration: 20-27 weeks

## Proposed Core Directory Structure (Post-Refactoring)

This structure aims to clearly separate concerns within `core/infrastructure/` while reflecting the integration of the core paradigms. Files listed include their primary intended classes/functions and purposes. `application/` and `deployment/` logic should reside outside the `core` directory.

```
person_suit/core/
├── __init__.py           # Exports core interfaces (e.g., PersonSuitError) and utilities.
├── README.md             # Overview of the core components and their interactions.
├── REFACTORING_CAW_ALIGNMENT_PLAN.md # This plan.
│
├── constants/            # Core constants used across multiple modules.
│   ├── __init__.py
│   ├── error_codes.py    # Defines: ErrorCode enum or constants.
│   ├── event_types.py    # Defines: CoreEventType enum or constants.
│   └── ...               # Other shared constants as needed.
│
├── data_structures/      # Core data structures frequently passed between components.
│   ├── __init__.py
│   ├── base.py           # Defines: Base dataclasses or common structures.
│   └── ...               # Other common, reusable data structures (e.g., IDs).
│
├── toolbox/              # Genuinely generic utility functions and classes supporting core operations.
│   ├── __init__.py
│   ├── async_utils.py    # Functions: safe_gather, cancel_task_group, etc.
│   ├── patterns.py       # Classes: Singleton, AsyncSingleton.
│   ├── validation.py     # Functions: common validation helpers.
│   └── ...               # Other general-purpose utilities.
│
├── memory/               # Core Memory System interfaces and base definitions.
│   ├── __init__.py
│   ├── interfaces.py     # Defines: IMemorySystem, IMemoryStorage, IMemoryRetrieval ABCs, ISensoryMemory, IWorkingMemory etc.
│   ├── base_memory.py    # Defines: Base memory component classes (if common logic exists).
│   └── errors.py         # Defines: MemoryError hierarchy (MemoryNotFound, etc.).
│
├── infrastructure/       # Foundational mechanisms and paradigm implementations.
│   ├── __init__.py       # Exports infrastructure sub-package APIs.
│   │
│   ├── actors/           # Consolidated Actor Model (CAW, ChP aware).
│   │   ├── __init__.py   # Exports the public Actor API.
│   │   ├── actor_system.py # Defines: ActorSystem, get_default_system helpers, Supervisor ABC, DefaultSupervisor, SupervisionStrategy enum.
│   │   ├── actor.py      # Defines: Actor ABC, ActorContext, ActorRef, ActorState enum, ActorPath, ActorMessage, MessageHandler type alias, receive, default_receive decorators.
│   │   ├── caw_actor.py  # Defines: DecoupledActor, DecoupledActorContext, CAW decorators.
│   │   ├── choreographed_actor.py # Defines: ChoreographedActor, ChoreographyParticipant ABC, ChoreographyProjection, ChP decorators.
│   │   ├── actor_registry.py # Defines: ActorRegistry, ActorInfo, registry functions.
│   │   ├── mailbox.py    # Defines: Mailbox base, PriorityMailbox, BoundedMailbox, MailboxStatus enum, create_mailbox factory.
│   │   ├── supervision.py # Defines: OneForOneStrategy, AllForOneStrategy, SupervisorDirective enum.
│   │   ├── messages.py   # Defines: Standard system/actor messages, ChoreographyStep.
│   │   ├── decorators.py # Defines: General actor decorators.
│   │   └── errors.py     # Defines: ActorSystemError, ActorCreationError etc.
│   │
│   ├── security/         # Security-related infrastructure.
│   │   ├── __init__.py
│   │   └── capabilities/ # Capability-Based Security (CBS).
│   │       ├── __init__.py # Exports CBS API.
│   │       ├── types.py  # Defines: Permission enum, CapabilityScope, CapabilityToken.
│   │       ├── system.py # Defines: CapabilitySystem class (verification, revocation).
│   │       ├── generation.py # Defines: generate_capability_token function.
│   │       ├── registry.py # Defines: CapabilityRegistry, CapabilityTemplate.
│   │       ├── interface.py # Defines: ICapabilityVerifier, requires_capability decorator.
│   │       ├── utils.py  # Defines: Helper functions for common capability operations.
│   │       ├── envelope.py # Defines: CapabilityEnvelope dataclass.
│   │       └── errors.py # Defines: CapabilityError hierarchy.
│   │
│   ├── choreography/     # Choreographic Programming (ChP) support.
│   │   ├── __init__.py
│   │   ├── types.py      # Defines: Choreography ABC/dataclass, Participant ABC/dataclass, Message dataclass (if used), ChoreographyContext dataclass. (Renamed from core.py)
│   │   ├── engine/       # Choreography Engine implementation (Phase 3).
│   │   │   ├── __init__.py
│   │   │   ├── base.py       # Defines: ChoreographyEngine ABC.
│   │   │   ├── actor_engine.py # Defines: ActorChoreographyEngine implementation.
│   │   │   ├── loader.py     # Defines: Loads choreography definitions.
│   │   │   └── runtime.py    # Defines: Manages active choreography instances.
│   │   └── errors.py     # Defines: ChoreographyError, ProjectionError, etc.
│   │
│   ├── effects/          # Effect System (ES) implementation.
│   │   ├── __init__.py   # Exports ES API.
│   │   ├── core.py       # Defines: effects decorator, EffectType enum, Effect dataclass, EffectHandlerRegistry, register_handler, handle_effect function. (Merged registry/runtime)
│   │   └── errors.py     # Defines: UnhandledEffectError, etc.
│   │
│   ├── wave/             # Contextual Adaptive Wave (CAW) - Wave components.
│   │   ├── __init__.py
│   │   └── core.py       # Defines: WaveFunction, Information, DualInformation, WaveTransformation, WavePattern, Resonator classes/dataclasses.
│   │
│   ├── contextual/       # Contextual Adaptive Wave (CAW) - Context components.
│   │   ├── __init__.py
│   │   └── core.py       # Defines: Context dataclass, ContextRegistry class, ContextualProcessor class (review role).
│   │
│   ├── caw/              # Contextual Adaptive Wave (CAW) - Integration components.
│   │   ├── __init__.py
│   │   └── core.py       # Defines: CAWProcessor, CAWRouter, ContextAdapter classes (review roles).
│   │
│   └── eventing/         # Optional: Event bus mechanism.
│       ├── __init__.py
│       └── bus.py        # Defines: EventBus implementation.
│       └── events.py     # Defines: Core event types.
```

**Key Changes Rationale:**

* **Core Scope:** `application/` and `deployment/` logic resides outside `core/`.
* **Infrastructure Focus:** Consolidates paradigm implementations (Actors, CBS, ChP, ES, CAW) under `infrastructure/`.
* **Clear Actor Structure:** `infrastructure/actors/` contains essential components.
* **Paradigm Subdirectories:** Explicit locations under `infrastructure/` (e.g., `security/capabilities`, `choreography`, `effects`, `dual_wave`, `contextual`, `caw`).
* **Refined Capabilities Structure:** `security/capabilities/` is modular (`system.py`, `generation.py`, etc.).
* **Memory Interfaces:** `memory/` holds core interfaces/base classes.
* **Utility Separation:** `constants/`, `data_structures/`, `toolbox/` for shared utilities.

## Related Refactoring Plans

This CAW alignment plan is part of a broader refactoring effort. Other related plans might be indexed in `docs/refactoring/REFACTORING_INDEX.md` (if it exists and is up-to-date).

*(Links to specific refactoring plans below might be outdated or point to non-existent files. Check `docs/refactoring/` if available)*

## Circular Import Resolution

Circular imports are a significant issue in the codebase that need to be addressed as part of the refactoring effort. The following circular import issues have been identified:

1. **Infrastructure/Monitoring and Meta_Systems/Persona_Core**:
   - `infrastructure/monitoring/service.py` imports from `meta_systems/persona_core/folded_mind/monitoring/system.py`
   - `meta_systems/persona_core/folded_mind/monitoring/system.py` imports from `infrastructure/monitoring/service.py`
   - **Resolution**: Create a new `infrastructure/monitoring/interfaces.py` module with abstract base classes that both modules can import from

2. **Infrastructure/Effects**:
   - `infrastructure/effects/initialization.py` imports from `infrastructure/effects/types.py`
   - `infrastructure/effects/types.py` imports from `infrastructure/effects/initialization.py`
   - **Resolution**: Restructure the effects module to have a cleaner dependency hierarchy

3. **Infrastructure/Dual_Wave and Infrastructure/Monitoring**:
   - `infrastructure/dual_wave/core.py` imports from `infrastructure/monitoring/telemetry.py`
   - `infrastructure/monitoring/telemetry.py` imports from `infrastructure/dual_wave/core.py`
   - **Resolution**: Create a new `infrastructure/monitoring/wave_telemetry.py` module for shared functionality

See a potential [Circular Import Resolution Plan](./refactoring/CIRCULAR_IMPORT_RESOLUTION_PLAN.md) (if exists) for details.

## Phase 1: Foundational Infrastructure Refinement (Actors, Capabilities, Effects)

1. **Finalize Actor Infrastructure Cleanup:**
    - **Action:** Perform a `grep` search across the entire `person_suit/core/` directory (and potentially the broader project) for any remaining *code imports* (not just comments) of deleted or deprecated modules: `system.py`, `caw_system.py`, `registry.py`, `caw_decorators.py`, `caw_mailbox.py`, `caw_messages.py`, `caw_supervision.py`, `system_actors.py`, `caw_system_actors.py`.
    - **Goal:** Ensure no active code relies on the removed components. Remove or update any found imports.
    - **Action:** Review `person_suit/core/infrastructure/actors/__init__.py` to ensure it exports only the relevant, current API components and accurately reflects the consolidated structure in its docstring.
    - **Status:** In progress. Started the Actor System Refactoring to break down the large `actor_system.py` file (1232 lines) into smaller, more manageable modules. Created a detailed refactoring plan in `docs/refactoring/ACTOR_SYSTEM_REFACTORING.md` that outlines the approach for splitting the file into multiple modules while maintaining the same functionality and ensuring alignment with the CAW paradigm.
    - **Detailed Implementation Plan:**
        1. **Code Import Analysis:**
           - Use `grep -r "import.*system" --include="*.py" person_suit/core/` to find imports of deprecated system modules.
           - Use `grep -r "from.*system import" --include="*.py" person_suit/core/` to find specific imports from deprecated system modules.
           - Repeat similar searches for each deprecated module.
           - Create a comprehensive list of all files that import deprecated modules.
        2. **Import Replacement:**
           - For each file that imports deprecated modules, determine the appropriate replacement imports.
           - Replace imports of `system.py` with imports from `actor_system.py`.
           - Replace imports of `caw_system.py` with imports from `caw_actor.py` and `actor_system.py`.
           - Replace imports of `registry.py` with imports from `actor_registry.py`.
           - Replace imports of other deprecated modules with their consolidated equivalents.
        3. **Code Adaptation:**
           * Update code that uses deprecated classes, functions, or methods to use their replacements.
           * Ensure type hints are updated to reference the new types.
           * Verify that method signatures and behaviors match between old and new implementations.
        4. **API Export Review:**
           * Review `person_suit/core/infrastructure/actors/__init__.py` to identify current exports.
           * Remove exports of deprecated modules and components.
           * Add exports for new consolidated components.
           * Update the module docstring to accurately reflect the new structure.
        5. **Documentation Update:**
           * Update any documentation that references the deprecated modules.
           * Add migration notes for users of the deprecated modules.
           * Create a mapping document showing old components and their new equivalents.
        6. **Testing:**
           * Run existing tests to ensure they still pass with the updated imports.
           * Create new tests for any functionality that might be affected by the changes.
           * Verify that all actor-related functionality works as expected.

2. **Refine Capability System Integration:** ✅
    * **Action:** Implement the TODO for **Parent Actor Supervision Tokens** in `actor_system.py`.
        * **Chosen Approach (Option C - Dynamic Capability):** Supervision rights are granted via capability tokens, aligning with CAW's dynamic nature. The ability to supervise is decoupled from static actor types.
        * **Implementation Steps:**
            1. In `create_actor`, when a parent creates a child, generate a `CapabilityToken` with `SUPERVISE`, `STOP`, `RESTART` permissions for the child resource, granted to the parent `subject_id`. ✅
            2. Implement a mechanism to securely deliver this token to the parent actor instance (e.g., add to `ActorContext`, send via dedicated message). ✅
        * **Status:** Completed (as per `CAW_ALIGNMENT_PROGRESS_REPORT.md`). Implemented Parent Actor Supervision Tokens and enhanced `verify_capability` with crypto signature verification and context sensitivity.
            3. Update the supervision handling logic in `_process_messages`:
                * Check if a failed actor has a parent.
                * If yes, check if the parent possesses the required `SUPERVISE` capability token for the child.
                * If capable, escalate failure notification to the parent. The parent decides the `SupervisorDirective` based on its logic and capabilities.
                * If parent is incapable or non-existent, fall back to the system supervisor.
    * **Action:** Enhance `verify_capability` (in `core/infrastructure/security/capabilities/`) to include placeholder or actual cryptographic signature verification and potentially revocation checks (even if the underlying crypto isn't fully implemented yet, the function signature and logic should account for it).
    * **Action:** Review and standardize the usage of `resource_id` and `scope` (`CapabilityScope`) for common actor operations (create, tell, ask, stop, watch, etc.) to ensure consistency.
    * **Detailed Implementation Plan:**
        1. **Parent Actor Supervision Tokens:**
           * Analyze the current implementation of `create_actor` in `actor_system.py`.
           * Identify the appropriate location to generate the supervision capability token.
           * Implement token generation using the `generate_capability_token` function.
           * Add the token to the parent actor's context or send it via a dedicated message.
           * Update the `_process_messages` method to check for supervision capabilities.
           * Implement the escalation logic for failure notifications.
           * Add fallback to the system supervisor when needed.
           * Add comprehensive logging for supervision-related actions.
        2. **Capability Verification Enhancement:**
           * Analyze the current implementation of `verify_capability` in `core/infrastructure/security/capabilities/`.
           * Design an enhanced verification process that includes signature verification.
           * Implement placeholder or actual cryptographic signature verification.
           * Add support for capability revocation checks.
           * Implement a caching mechanism for frequently verified capabilities.
           * Add comprehensive logging for verification-related actions.
           * Update the function signature and documentation to reflect the enhanced verification process.
        3. **Resource ID and Scope Standardization:** ✅
           * Identify all actor operations that use `resource_id` and `scope`. ✅
           * Document the current usage patterns and inconsistencies. ✅
           * Define a standardized approach for resource identification and scoping. ✅
           * Update all actor operations to use the standardized approach. ✅
           * Create helper functions for common resource ID and scope patterns. ✅
           * Add validation for resource IDs and scopes to ensure consistency. ✅
           * Update documentation to reflect the standardized approach. ✅
        4. **Capability Registry Integration:** ✅
           * Enhance the capability registry to support the new verification process. ✅
           * Add support for capability revocation and expiration. ✅
           * Implement a mechanism for capability delegation. ✅
           * Add support for capability composition (combining multiple capabilities). ✅
           * Create utility functions for common capability operations. ✅
           * Add comprehensive logging for registry-related actions. ✅
        5. **Security Audit and Testing:**
           * Perform a security audit of the capability system.
           * Identify potential vulnerabilities and address them.
           * Create comprehensive tests for the capability system.
           * Test edge cases and failure scenarios.
           * Verify that the capability system works correctly with the actor system.
           * Document security considerations and best practices.

3. **Refine Effect System Usage:** ✅
    * **Action:** Audit `@effects` decorators in `core/infrastructure/actors/` (`actor_system.py`, `actor.py`, `caw_actor.py`, `choreographed_actor.py`). Ensure they accurately reflect the side effects of each decorated function (e.g., `create_actor` likely has `ACTOR_LIFECYCLE` and potentially `MESSAGING` if it needs to notify the parent). Add or correct decorators as needed.
    * **Goal:** Improve the accuracy and completeness of effect declarations.
    * **Status:** Completed (as per `CAW_ALIGNMENT_PROGRESS_REPORT.md`). Refactored effects system, consolidated files, updated imports, added `ACTOR_LIFECYCLE` type.
    * **Detailed Implementation Plan:**
        1. **Effect Audit and Analysis:** ✅
           * Analyze all methods in `actor_system.py`, `actor.py`, `caw_actor.py`, and `choreographed_actor.py`.
           * Create a comprehensive list of all methods and their current effect declarations.
           * Identify methods that are missing effect declarations.
           * Identify methods with incomplete or incorrect effect declarations.
           * Document the actual effects of each method based on code analysis.
        2. **Effect Declaration Enhancement:** ✅
           * Update existing `@effects` decorators to accurately reflect the side effects.
           * Add `@effects` decorators to methods that are missing them.
           * Ensure that all effect types are correctly specified.
           * Add detailed comments explaining the rationale for each effect declaration.
        3. **Effect Type Expansion:** ✅
           * Review the current `EffectType` enum in `core/infrastructure/effects/core.py`.
           * Identify any missing effect types that are needed for the actor system.
           * Add new effect types as needed, with clear documentation.
           * Update existing effect declarations to use the new effect types.
        4. **Effect Tracking Enhancement:** ✅
           * Enhance the effect tracking system to provide more detailed information.
           * Add support for tracking effect chains across multiple method calls.
           * Implement a mechanism for visualizing effect flows.
           * Add support for effect analysis and optimization.
        5. **Effect Documentation:** ✅
           * Create comprehensive documentation for the effect system.
           * Document best practices for effect declaration.
           * Create examples of correct effect usage.
           * Add guidelines for effect analysis and optimization.
        6. **Effect Testing:** ✅
           * Create tests to verify that effect declarations are accurate.
           * Implement tests for effect tracking and analysis.
           * Create tests for effect visualization.
           * Verify that all effect-related functionality works as expected.

## Phase 2: Core CAW Integration

4. **Integrate CAW Context Determination:** ✅
    * **Action:** Replace the placeholder logic in `DecoupledActorContext.determine_best_context` and `DecoupledActor._create_message_wave_function`. This requires integrating with the components defined in `core/infrastructure/dual_wave/`, `core/infrastructure/contextual/`, and `core/infrastructure/caw/`. The logic should involve:
        * Retrieving relevant `Context`s from the `ContextRegistry`.
        * Creating `WaveFunction`s based on incoming messages.
        * Using `Information` objects to `interpret` the message in different contexts.
        * Selecting the best `Context` based on interpretation results (e.g., highest probability).
    * **Action:** Determine the role of `CAWProcessor`, `CAWRouter`, `ContextAdapter` and integrate them where appropriate (likely within `DecoupledActorContext` or `ActorSystem`).
    * **Action:** Ensure `_process_messages` in `actor_system.py` correctly calls the *updated* `determine_best_context` logic.
    * **Status:** Completed (as per this plan's internal status). Enhanced `DecoupledActorContext.determine_best_context` with multi-stage approach using CAW components.
    * **Detailed Implementation Plan:**
        1. **Context Registry Integration:**
           * Analyze the current implementation of `ContextRegistry` in `core/infrastructure/contextual/core.py`.
           * Enhance the `DecoupledActorContext` constructor to accept a `ContextRegistry` parameter.
           * Implement a mechanism to retrieve the global context registry if none is provided.
           * Add methods to register and retrieve contexts from the registry.
           * Implement caching for frequently used contexts to improve performance.
        2. **Wave Function Creation:**
           * Analyze the current implementation of `_create_message_wave_function` in `DecoupledActor`.
           * Implement a multi-step approach to find the most appropriate wave function:
             1. Try to get a wave function registered with the actor.
             2. Try to get a wave function from the CAW registry.
             3. Generate a default wave function based on message attributes.
           * Add support for message type-specific wave functions.
           * Implement wave function composition for complex messages.
           * Add comprehensive logging for wave function creation.
        3. **Context Determination:**
           * Analyze the current implementation of `determine_best_context` in `DecoupledActorContext`.
           * Implement a multi-step approach to determine the best context:
             1. Create an `Information` object with the message and its wave function.
             2. Retrieve relevant contexts from the registry.
             3. Use the `CAWProcessor` to interpret the message in different contexts.
             4. Select the context with the highest probability.
           * Add support for context constraints and priorities.
           * Implement fallback mechanisms for when no suitable context is found.
           * Add comprehensive logging for context determination.
        4. **CAW Component Integration:**
           * Analyze the current implementations of `CAWProcessor`, `CAWRouter`, and `ContextAdapter`.
           * Determine the appropriate roles for each component:
             - `CAWProcessor`: Responsible for interpreting messages in different contexts.
             - `CAWRouter`: Responsible for routing messages based on wave interference patterns.
             - `ContextAdapter`: Responsible for adapting behavior based on context and wave patterns.
           * Enhance the `ActorSystem` constructor to create and initialize these components.
           * Add methods to the `ActorSystem` to access these components.
           * Implement integration points between the actor system and these components.
        5. **Process Messages Update:**
           * Analyze the current implementation of `_process_messages` in `actor_system.py`.
           * Update the method to correctly call the updated `determine_best_context` logic.
           * Add support for context-sensitive message processing.
           * Implement error handling for context determination failures.
           * Add comprehensive logging for message processing.
        6. **Testing and Validation:**
           * Create comprehensive tests for context determination.
           * Test with different message types and contexts.
           * Verify that the correct context is selected for each message.
           * Test error handling and fallback mechanisms.
           * Benchmark performance to ensure efficient context determination.

5. **Refine CAW Actor Behavior:** ✅
    * **Action:** Update the docstrings and potentially add helper methods or examples to `DecoupledActor` and `DecoupledActorContext` to clarify how subclasses should implement `process_in_context` and leverage the `processing_context` to modify their behavior dynamically.
    * **Detailed Implementation Plan:**
        1. **Documentation Enhancement:** ✅
           * Analyze the current docstrings in `DecoupledActor` and `DecoupledActorContext`.
           * Update the docstrings to provide clear guidance on how to implement `process_in_context`.
           * Add examples of different context-sensitive behaviors.
           * Create a comprehensive guide on leveraging the `processing_context`.
           * Document best practices for context-sensitive actor behavior.
        2. **Helper Method Implementation:** ✅
           * Identify common patterns in context-sensitive behavior.
           * Implement helper methods for these common patterns.
           * Add methods for context-based message filtering.
           * Implement utility functions for context-sensitive state management.
           * Create helper methods for context-based decision making.
        3. **Default Implementation:** ✅
           * Implement a default version of `process_in_context` that provides basic context-sensitive behavior.
           * Add support for common context-based adaptations.
           * Implement fallback behavior for unknown contexts.
           * Add comprehensive logging for context-sensitive behavior.
           * Create a mechanism for context-based behavior composition.
        4. **Example Implementation:** ✅
           * Create example implementations of `DecoupledActor` subclasses.
           * Demonstrate different context-sensitive behaviors.
           * Show how to leverage the `processing_context` for dynamic behavior.
           * Provide examples of context-based message handling.
           * Create examples of context-sensitive state management.
        5. **Testing and Validation:** ✅
           * Create comprehensive tests for the helper methods.
           * Test the default implementation with different contexts.
           * Verify that the example implementations work as expected.
           * Test edge cases and error handling.
           * Benchmark performance to ensure efficient context-sensitive behavior.
    * **Status:** Completed (as per this plan's internal status). Enhanced `DecoupledActor` with helper methods, improved docstrings, added examples.

## Phase 3: Choreography Implementation

6. **Implement Choreography Engine:** ✅
    * **Action:** Design and implement a **Choreography Engine** (e.g., `core/infrastructure/choreography/engine_actor.py`).
    * **Action:** Refine `ChoreographedActor` methods.
    * **Action:** Remove `__current_sender_token_hack`.
    * **Status:** Completed (as per this plan's internal status). Implemented `ChoreographyEngineActor`, refined `ChoreographedActor`, removed hack, added examples.

## Phase 4: Paradigm Integration & Alignment

7. **Integrate Differentiable and Differential Components with CAW:** 🔄
    * **Action:** Refactor the `core/infrastructure/differentiable/` and `core/infrastructure/differential/` directories to align with CAW principles and the physics-inspired dual representation model.
    * **Specific Steps:**
        * Update `differentiable/autodiff/` to support context-sensitive differentiation based on CAW contexts. ✅
        * Enhance `differentiable/decorators/` to integrate with CAW decorators. ✅
        * Refactor `differentiable/integration/probabilistic/` to use CAW wave functions for probability distributions.
        * Update `differential/collections/` to support context-sensitive differential dataflow.
        * Enhance `differential/operators/` to integrate with CAW wave transformations.
        * Create integration points between differential dataflow and CAW routing.
        * Implement differentiable operations for the information particle hierarchy (infons, datoms, concepts, relations). ✅
        * Create differentiable spacetime continuum operations. ✅
    * **Status:** In progress (as per `CAW_ALIGNMENT_PROGRESS_REPORT.md`). Consolidated wave modules to `dual_wave/`, implemented differentiable spacetime ops, created examples and integration bridges/decorators.
    * **Detailed Implementation Plan:**
        1. **Analysis and Design:**
           * Analyze the current implementations of differentiable and differential components.
           * Identify key integration points with CAW components and the physics-inspired model.
           * Design a unified approach for context-sensitive differentiation and dataflow.
           * Create a design document outlining the integration strategy.
           * Identify mathematical models for differentiating through spacetime operations.

        2. **Context-Sensitive Autodiff:**
           * Extend the `Tensor` class in `differentiable/autodiff/` to include context information.
           * Implement context-sensitive gradient computation that adapts based on the current context.
           * Add support for wave function-based gradient scaling.
           * Create utility functions for context-dependent automatic differentiation.
           * Implement automatic differentiation for the 2048-dimensional vector space.

        3. **Differentiable Information Particles:**
           * Implement differentiable operations for `Infon` class.
           * Create gradient computation for `Datom` composition and decomposition.
           * Implement differentiable operations for `Concept` formation.
           * Create gradient flow through `Relation` bonds.
           * Implement end-to-end differentiability across the particle hierarchy.
           * Create utility functions for optimizing information particle configurations.

        4. **Differentiable Spacetime:**
           * Implement differentiable operations for the `SpacetimeContinuum` class.
           * Create gradient computation for information propagation through spacetime.
           * Implement differentiable relativistic effects.
           * Create utility functions for optimizing spacetime configurations.
           * Implement gradient flow through field equations.

        5. **CAW-Aware Decorators:**
           * Enhance the `@differentiable` decorator to integrate with CAW decorators.
           * Implement a new `@contextual_differentiable` decorator that adapts differentiation based on context.
           * Add support for wave function-based differentiation control.
           * Create utility functions for applying CAW principles to differentiable functions.
           * Implement decorators for differentiable information particle operations.

        6. **Probabilistic Integration:**
           * Refactor `differentiable/integration/probabilistic/` to use CAW wave functions.
           * Implement context-sensitive probability distributions.
           * Add support for wave interference-based probability calculations.
           * Create utility functions for context-dependent probabilistic programming.
           * Implement probabilistic models for information particle interactions.

        7. **Context-Sensitive Differential Dataflow:**
           * Extend the collection classes in `differential/collections/` to include context information.
           * Implement context-sensitive dataflow operations that adapt based on the current context.
           * Add support for wave function-based dataflow control.
           * Create utility functions for context-dependent differential dataflow.
           * Implement differential dataflow for information particle transformations.

        8. **Wave-Integrated Operators:**
           * Enhance the operators in `differential/operators/` to integrate with CAW wave transformations.
           * Implement context-sensitive operator behavior.
           * Add support for wave interference-based operator selection.
           * Create utility functions for context-dependent operator application.
           * Implement operators for spacetime continuum operations.

        9. **CAW Routing Integration:**
           * Create integration points between differential dataflow and CAW routing.
           * Implement context-sensitive routing based on dataflow patterns.
           * Add support for wave function-based routing decisions.
           * Create utility functions for context-dependent routing.
           * Implement routing based on spacetime topology.

        10. **Examples and Documentation:**
           * Update existing examples to demonstrate context-sensitive differentiation and dataflow.
           * Create new examples showcasing the integration with CAW components.
           * Add comprehensive documentation explaining the integration approach.
           * Create diagrams illustrating the context-sensitive behavior.
           * Develop examples of differentiable information particle operations.
           * Create tutorials on differentiable spacetime operations.

        11. **Testing:**
           * Implement unit tests for context-sensitive differentiation and dataflow.
           * Create integration tests for the CAW integration.
           * Benchmark the performance of context-sensitive operations.
           * Verify correctness of gradient computations in different contexts.
           * Test differentiable operations across the information particle hierarchy.
           * Validate gradient flow through spacetime operations.

    * **Goal:** Enable differentiable and differential components to adapt their behavior based on context and wave interference patterns, with full integration with the physics-inspired dual representation model, including differentiable operations for information particles and spacetime continuum.

8. **Enhance Security Capabilities with CAW:** ✅
    * **Action:** Refactor the `core/infrastructure/security/capabilities/` directory to better integrate with CAW principles.
    * **Specific Steps:**
        * Update `capabilities/monitoring/` to track capability usage in different contexts.
        * Enhance `capabilities/examples/` to demonstrate context-sensitive capability checks.
        * Create a new `capabilities/integration/` directory with CAW-specific capability components.
        * Implement context-sensitive capability verification in `capabilities/system.py`.
        * Add wave function support to capability tokens to enable interference-based security decisions.
    * **Goal:** Enable capability-based security to adapt its behavior based on context and wave interference patterns.
    * **Status:** Completed (as per `CAW_ALIGNMENT_PROGRESS_REPORT.md`). Implemented context-aware monitoring, wave-based tokens/verifier in `core/infrastructure/security/capabilities/integration/caw/`, added examples.

9. **Align Effects System with CAW:** ✅
    * **Action:** Refactor the `core/infrastructure/effects/` directory to better integrate with CAW principles.
    * **Specific Steps:**
        * Update `effects/integration/probabilistic/` to use CAW wave functions for effect probability distributions. ✅
        * Enhance `effects/logging/` to include context information in effect logs. ✅
        * Update `effects/visualization/` to visualize effects in different contexts. ✅
        * Create a new `effects/integration/caw/` directory with CAW-specific effect components. ✅
        * Implement context-sensitive effect handling in `effects/core.py`. ✅
        * Refactor the large `effects/handlers.py` file into a dedicated `handlers/` directory with separate files for each handler. ✅
    * **Goal:** Enable the effects system to track and handle effects differently based on context and wave interference patterns.
    * **Status:** Completed (as per this plan's internal status). Refactored effects system, consolidated files, updated imports, added `ACTOR_LIFECYCLE` type, refactored handlers, enhanced `ContextualEffect`.

10. **Unify Wave and Wave_Particle Components with Physics-Inspired Dual Representation:**
    * **Action:** Refactor the `core/infrastructure/wave/` and `core/infrastructure/wave_particle/` directories to create a unified dual-representation model aligned with CAW principles and physics-inspired wave-particle duality.
    * **Specific Steps:**
        * Implement a true dual-representation approach where all information inherently possesses both wave and particle aspects simultaneously, following light behavior in physics.
        * Create a unified `DualWaveFunction` class that natively supports both wave-like (distributed, interference-capable) and particle-like (discrete, localized) behaviors.
        * Implement physics-inspired principles such as superposition, interference, entanglement, and the uncertainty principle.
        * Ensure the dual representation is applied consistently throughout the entire system and CAW paradigm.
        * Develop context-dependent observation mechanisms that determine whether wave or particle aspects are manifested.
        * Create seamless integration with the CAW paradigm, ensuring all CAW components can work with the dual representation.
        * Implement the spacetime continuum model for information entities.
        * Create the hierarchy of fundamental information particles (infons, datoms, concepts, relations).
    * **Detailed Implementation Plan:** (See [REFACTORING_CAW_ALIGNMENT_PLAN_POINT10_UPDATE.md](./REFACTORING_CAW_ALIGNMENT_PLAN_POINT10_UPDATE.md) for details)
    * **Status:** Completed (as per `CAW_ALIGNMENT_PROGRESS_REPORT.md`). Implemented physics-inspired dual representation, information particle hierarchy, spacetime continuum, observation mechanisms, CAW integration, system-wide application, and testing.

11. **Enhance Choreography Integration with CAW:** ✅
    * **Action:** Refactor the `core/infrastructure/choreography/integration/` directory to better align with CAW principles.
    * **Specific Steps:**
        * Update `choreography/integration/effects/` to track choreography effects in different contexts.
        * Create a new `choreography/integration/caw/` directory with CAW-specific choreography components.
        * Implement context-sensitive choreography execution in `choreography/engine/`.
        * Add wave function support to choreography steps to enable interference-based routing.
        * Enhance `choreography/examples/` to demonstrate context-sensitive choreographies.
    * **Goal:** Enable choreographies to adapt their behavior based on context and wave interference patterns.
    * **Status:** Completed (as per this plan's internal status). Implemented wave-based steps, context-sensitive engine, wave-based effects in `core/infrastructure/choreography/integration/`.

12. **Integrate Ultra_Efficient Components with CAW:** ✅
    * **Action:** Refactor the `core/infrastructure/ultra_efficient/` directory to align with CAW principles.
    * **Specific Steps:**
        * Update `ultra_efficient/energy_harvesting/` to support context-sensitive energy harvesting.
        * Enhance `ultra_efficient/examples/` to demonstrate context-sensitive resource optimization.
        * Create a new `ultra_efficient/integration/` directory with CAW-specific optimization components.
        * Implement wave function-based resource allocation strategies.
        * Add support for context-sensitive power management.
    * **Goal:** Enable ultra-efficient components to adapt their behavior based on context and wave interference patterns.
    * **Status:** Completed (as per `CAW_ALIGNMENT_PROGRESS_REPORT.md`). Implemented wave-based harvesting, context-sensitive budget, wave-based allocation in `core/infrastructure/ultra_efficient/integration/caw/`, added examples.

13. **Align Verification Integration with CAW:** ✅
    * **Action:** Refactor the `core/infrastructure/verification/integration/` directory to better integrate with CAW principles.
    * **Specific Steps:**
        * Update `verification/integration/probabilistic/` to use CAW wave functions for verification probability distributions.
        * Create a new `verification/integration/caw/` directory with CAW-specific verification components.
        * Implement context-sensitive verification in `verification/core.py`.
        * Add wave function support to verification properties to enable interference-based verification.
        * Enhance `verification/examples/` to demonstrate context-sensitive verification.
    * **Goal:** Enable verification components to adapt their behavior based on context and wave interference patterns.
    * **Status:** Completed (as per `CAW_ALIGNMENT_PROGRESS_REPORT.md`). Implemented wave-based properties, context-sensitive checking/proving in `core/infrastructure/verification/integration/caw/`, updated probabilistic verification, added examples.

14. **Integrate Quantum Components with CAW:** ✅
    * **Action:** Refactor the `core/infrastructure/quantum/` directory to align with CAW principles.
    * **Specific Steps:**
        * Update `quantum/hybrid/` to support context-sensitive quantum-classical interactions.
        * Enhance `quantum/examples/` to demonstrate context-sensitive quantum algorithms.
        * Create a new `quantum/integration/caw/` directory with CAW-specific quantum components.
        * Implement wave function-based quantum algorithm selection.
        * Add support for context-sensitive quantum resource allocation.
    * **Detailed Implementation Plan:**
        1. **Analysis and Design:**
           * Analyze the current implementation of quantum components.
           * Identify integration points with CAW components.
           * Design a unified approach for context-sensitive quantum algorithms.
           * Create a design document outlining the integration strategy.
        2. **Hybrid System Enhancement:**
           * Extend the hybrid quantum-classical system to support context-sensitive behavior.
           * Implement context-dependent algorithm selection mechanisms.
           * Add support for wave function-based quantum resource allocation.
           * Create utility functions for context-sensitive quantum operations.
        3. **CAW Integration:**
           * Create a new `quantum/integration/caw/` directory.
           * Implement adapter classes to bridge quantum components with CAW components.
           * Add support for context-sensitive quantum algorithm execution.
           * Create utility functions for quantum-CAW interactions.
        4. **Examples and Documentation:**
           * Update existing examples to demonstrate context-sensitive quantum algorithms.
           * Create new examples showcasing the integration with CAW components.
           * Add comprehensive documentation explaining the quantum-CAW integration.
           * Create diagrams illustrating the context-sensitive quantum behavior.
        5. **Testing:**
           * Implement unit tests for context-sensitive quantum components.
           * Create integration tests for the quantum-CAW integration.
           * Benchmark the performance of context-sensitive quantum algorithms.
           * Verify correctness of quantum operations in different contexts.
    * **Goal:** Enable quantum components to adapt their behavior based on context and wave interference patterns.
    * **Status:** Completed (as per `CAW_ALIGNMENT_PROGRESS_REPORT.md`). Implemented wave-based selection, context-sensitive allocation, wave-based generation in `core/infrastructure/quantum/integration/caw/`, added examples.

15. **Enhance Probabilistic Core with CAW:** ✅
    * **Action:** Refactor the `core/infrastructure/probabilistic/` directory to align with CAW principles.
    * **Specific Steps:**
        * Update core probabilistic models to support context-sensitive probability distributions. ✅
        * Enhance `probabilistic/examples/` to demonstrate context-sensitive probabilistic programming. ✅
        * Create a new `probabilistic/integration/caw/` directory with CAW-specific probabilistic components. ✅
        * Implement wave function-based probability calculations. ✅
        * Add support for context-sensitive inference algorithms. ✅
    * **Detailed Implementation Plan:**
        1. **Analysis and Design:** ✅
           * Analyze the current implementation of probabilistic components. ✅
           * Identify integration points with CAW components. ✅
           * Design a unified approach for context-sensitive probabilistic programming. ✅
           * Create a design document outlining the integration strategy. ✅
        2. **Core Probabilistic Enhancement:** ✅
           * Extend the core probabilistic models to support context-sensitive behavior. ✅
           * Implement context-dependent probability distribution mechanisms. ✅
           * Add support for wave function-based probability calculations. ✅
           * Create utility functions for context-sensitive probabilistic operations. ✅
        3. **CAW Integration:** ✅
           * Create a new `probabilistic/integration/caw/` directory. ✅
           * Implement adapter classes to bridge probabilistic components with CAW components. ✅
           * Add support for context-sensitive inference algorithms. ✅
           * Create utility functions for probabilistic-CAW interactions. ✅
        4. **Examples and Documentation:** ✅
           * Update existing examples to demonstrate context-sensitive probabilistic programming. ✅
           * Create new examples showcasing the integration with CAW components. ✅
           * Add comprehensive documentation explaining the probabilistic-CAW integration. ✅
           * Create diagrams illustrating the context-sensitive probabilistic behavior. ✅
        5. **Testing:** ✅
           * Implement unit tests for context-sensitive probabilistic components. ✅
           * Create integration tests for the probabilistic-CAW integration. ✅
           * Benchmark the performance of context-sensitive inference algorithms. ✅
           * Verify correctness of probabilistic operations in different contexts. ✅
    * **Goal:** Enable probabilistic components to adapt their behavior based on context and wave interference patterns.
    * **Status:** Completed (as per `CAW_ALIGNMENT_PROGRESS_REPORT.md`). Implemented wave distributions, context variables/models/inference in `core/infrastructure/probabilistic/integration/caw/`, added utils, visualizations, examples, tests.

16. **Integrate Pathway Components with CAW:**
    * **Action:** Refactor the `core/infrastructure/pathway/` directory to align with CAW principles.
    * **Specific Steps:**
        * Update pathway models to support context-sensitive cognitive processing.
        * Implement dual-pathway integration with CAW context determination.
        * Create a new `pathway/integration/caw/` directory with CAW-specific pathway components.
        * Add support for wave function-based pathway selection.
        * Enhance pathway examples to demonstrate context-sensitive cognitive processing.
    * **Detailed Implementation Plan:**
        1. **Analysis and Design:**
           * Analyze the current implementation of pathway components.
           * Identify integration points with CAW components.
           * Design a unified approach for context-sensitive dual-pathway architecture.
           * Create a design document outlining the integration strategy.
        2. **Dual-Pathway Enhancement:**
           * Extend the dual-pathway cognitive architecture to support context-sensitive behavior.
           * Implement context-dependent pathway selection mechanisms.
           * Add support for wave function-based cognitive processing.
           * Create utility functions for context-sensitive pathway operations.
        3. **CAW Integration:**
           * Create a new `pathway/integration/caw/` directory.
           * Implement adapter classes to bridge pathway components with CAW components.
           * Add support for context-sensitive cognitive processing.
           * Create utility functions for pathway-CAW interactions.
        4. **Examples and Documentation:**
           * Create examples demonstrating context-sensitive dual-pathway processing.
           * Add comprehensive documentation explaining the pathway-CAW integration.
           * Create diagrams illustrating the context-sensitive cognitive behavior.
           * Document best practices for implementing context-sensitive cognitive processing.
        5. **Testing:**
           * Implement unit tests for context-sensitive pathway components.
           * Create integration tests for the pathway-CAW integration.
           * Benchmark the performance of context-sensitive cognitive processing.
           * Verify correctness of pathway operations in different contexts.
    * **Goal:** Enable pathway components to adapt their cognitive processing based on context and wave interference patterns.

17. **Enhance Communication Components with CAW:**
    * **Action:** Refactor the `core/infrastructure/communication/` directory to align with CAW principles.
    * **Specific Steps:**
        * Update communication protocols to support context-sensitive message routing.
        * Implement wave function-based communication channel selection.
        * Create a new `communication/integration/caw/` directory with CAW-specific communication components.
        * Add support for context-sensitive message prioritization.
        * Enhance communication examples to demonstrate context-sensitive protocols.
    * **Detailed Implementation Plan:**
        1. **Analysis and Design:**
           * Analyze the current implementation of communication components.
           * Identify integration points with CAW components.
           * Design a unified approach for context-sensitive communication.
           * Create a design document outlining the integration strategy.
        2. **Protocol Enhancement:**
           * Extend the communication protocols to support context-sensitive behavior.
           * Implement context-dependent message routing mechanisms.
           * Add support for wave function-based channel selection.
           * Create utility functions for context-sensitive communication operations.
        3. **CAW Integration:**
           * Create a new `communication/integration/caw/` directory.
           * Implement adapter classes to bridge communication components with CAW components.
           * Add support for context-sensitive message prioritization.
           * Create utility functions for communication-CAW interactions.
        4. **Examples and Documentation:**
           * Create examples demonstrating context-sensitive communication protocols.
           * Add comprehensive documentation explaining the communication-CAW integration.
           * Create diagrams illustrating the context-sensitive message routing.
           * Document best practices for implementing context-sensitive communication.
        5. **Testing:**
           * Implement unit tests for context-sensitive communication components.
           * Create integration tests for the communication-CAW integration.
           * Benchmark the performance of context-sensitive message routing.
           * Verify correctness of communication operations in different contexts.
    * **Goal:** Enable communication components to adapt their behavior based on context and wave interference patterns.

## Phase 5: Wider Core Directory Alignment & Review

18. **Review & Align Other Core Modules:**
    * **Action:** Systematically review modules in `core/memory/`, `core/application/`, `core/data_structures/`, `core/toolbox/`, `core/constants/`, `core/deployment/`.
    * **Goal:** For each module, assess its interaction with the CAW-aligned actor system. Apply CAW, CBS, ES principles where relevant (e.g., Should memory access be context-dependent? Should application services be actors? Do toolbox utilities follow async/error standards?). Refactor as needed.

19. **Code Organization and Directory Structure Refactoring:**
    * **Action:** Refactor the directory structure of `person_suit/core/` (primarily `core/infrastructure/`).
    * **Specific Steps:**
        * Consolidate wave modules into `dual_wave/`. ✅ (Done as part of Step 10)
        * Flatten `deployment/` structure. ✅ (Planning Phase - Assuming this means outside `core/`)
        * Restructure `application/` (outside `core/`).
        * Reorganize `infrastructure/` (ongoing via paradigm subdirs).
        * Remove redundant files.
    * **Detailed Implementation Plan:**
        1. **Wave Modules Consolidation:**
           * Consolidate all wave-related functionality into the `dual_wave` directory. ✅ (Planning Phase)
           * Create a comprehensive migration guide in `dual_wave/migration.md`. ✅ (Planning Phase)
           * Add adapter/compatibility layers in `dual_wave/adapters.py`. ✅ (Planning Phase)
           * Deprecate older modules with warnings.
           * Update documentation to reflect the consolidated structure.
           * Update imports throughout the codebase.

        2. **Deployment Directory Flattening:**
           * Move functionality from top-level files into their corresponding subdirectories. ✅ (Planning Phase)
           * Update the `__init__.py` to re-export the functionality. ✅ (Planning Phase)
           * Remove redundant top-level files. ✅ (Planning Phase)
           * Update imports throughout the codebase. ✅ (Planning Phase)

        3. **Application Module Restructuring:**
           * Create a clear separation between CAW and non-CAW implementations.
           * Move CAW-specific implementations to a `caw` subdirectory.
           * Split large files into smaller, more focused components.
           * Update imports throughout the codebase.

        4. **Infrastructure Directory Organization:**
           * Group related paradigms into higher-level categories.
           * Create a clearer hierarchy for different components.
           * Split very large files into smaller, more focused components.
           * Update imports throughout the codebase.

        5. **Redundant Files Removal:**
           * Identify and remove redundant files.
           * Consolidate similar functionality.
           * Update imports throughout the codebase.
           * Ensure backward compatibility through adapter patterns.

20. **Architectural Compliance Check:**
    * **Action:** Review the entire refactored `core/` structure against principles in `docs/DOCUMENTATION_STANDARDS.md` and `docs/Architecture/ARCHITECTURE_OVERVIEW.md`.
    * **Goal:** Ensure the refactored code adheres to the established high-level architecture and design principles.

21. **Testing:**
    * **Action:** Develop a comprehensive suite of unit and integration tests for the refactored components. Pay special attention to:
        * Correct context creation (`ActorContext` vs `DecoupledActorContext`).
        * Message routing logic in `_process_messages`.
        * Capability token checks (including parent supervision).
        * CAW context determination logic (mocking wave/contextual components initially).
        * Choreography Engine interactions (sending steps, receiving results).

## Execution Approach

* Treat each numbered step as a distinct unit of work.
* Follow incremental development within each step (skeleton -> comments -> code -> tests).
* Commit frequently with clear descriptions linking back to the plan step.
* Address TODOs as they arise or create specific follow-up tasks.

## Implementation Sequence and Prioritization

The implementation should follow this sequence to ensure that the most critical components are refactored first and that dependencies are respected:

### High Priority (Must Complete)

1. **Phase 1: Foundational Infrastructure Refinement**
   * Steps 1-3: Actor Infrastructure, Capability System, Effect System
   * These components form the foundation of the CAW paradigm and must be completed first.

2. **Phase 2: Core CAW Integration**
   * Steps 4-5: CAW Context Determination, CAW Actor Behavior
   * These steps integrate the core CAW components with the actor system.

3. **Wave and Wave_Particle Unification (Step 10)** ✅
   * This is a critical component as it provides the foundation for the CAW paradigm.
   * The unified wave model is required by many other components.
   * Implementation details are provided in the following documents:
     * `docs/implementation_plans/information_particle_hierarchy.md`: Implementation of the information particle hierarchy (infons, datoms, concepts, relations)
     * `docs/implementation_plans/spacetime_continuum.md`: Implementation of the spacetime continuum model
     * `docs/implementation_plans/differentiable_integration.md`: Integration with differentiable programming

### Medium Priority (Should Complete)

4. **Differentiable and Differential Integration (Step 7)** ✅
   - These components are important for advanced functionality and are required for optimizing the physics-inspired model parameters.
   - This step should be implemented early as it's needed for the information particle hierarchy and spacetime continuum.
   - Implementation completed with differentiable particles, gradient tape, and optimization utilities.

5. **Phase 3: Choreography Implementation (Step 6)**
   * The choreography engine is important for complex interactions but can be implemented after the core CAW components.

6. **Security Capabilities Enhancement (Step 8)**
   * Enhancing security capabilities with CAW principles is important for secure operations.

7. **Effects System Alignment (Step 9)**
   * Aligning the effects system with CAW principles improves tracking and handling of side effects.

### Lower Priority (Nice to Have)

8. **Choreography Integration Enhancement (Step 11)**
   * Further enhancing the choreography integration with CAW principles.

9. **Ultra_Efficient Components Integration (Step 12)**
   * Integrating ultra-efficient components with CAW principles for resource optimization.

10. **Verification Integration Alignment (Step 13)**
    * Aligning verification integration with CAW principles for formal verification.

11. **Quantum Components Integration (Step 14)**
    * Integrating quantum components with CAW principles for quantum-inspired algorithms.

12. **Probabilistic Core Enhancement (Step 15)**
    * Enhancing probabilistic core components with CAW principles for context-sensitive probability distributions.

13. **Pathway Components Integration (Step 16)**
    * Integrating pathway components with CAW principles for context-sensitive cognitive processing.

14. **Communication Components Enhancement (Step 17)**
    * Enhancing communication components with CAW principles for context-sensitive message routing.

15. **Phase 5: Wider Core Directory Alignment & Review (Steps 18-20)**
    * These steps involve reviewing and aligning other core modules, checking architectural compliance, and comprehensive testing.

### Implementation Dependencies

The following dependencies should be considered when planning the implementation:

* **Physics-Inspired Dual Representation (Step 10)** is a critical foundation for the entire CAW paradigm and a prerequisite for many other steps. This unified dual wave-particle representation that follows light behavior in physics must be implemented first, as it provides the core model for how information is represented and processed throughout the system. This includes:
  * Information particle hierarchy (infons, datoms, concepts, relations) as detailed in `docs/implementation_plans/information_particle_hierarchy.md`
  * Spacetime continuum model as detailed in `docs/implementation_plans/spacetime_continuum.md`
  * Integration with differentiable programming as detailed in `docs/implementation_plans/differentiable_integration.md`

* **Differentiable Components Integration (Step 7)** is needed for optimizing the physics-inspired model parameters and enabling learning within the system. This should be implemented early as it's required for:
  * Gradient-based optimization of particle configurations
  * Differentiable operations in the spacetime continuum
  * End-to-end differentiability across the entire physics-inspired model

* **Core CAW Integration (Phase 2)** is required before implementing any component that depends on context-sensitive behavior.

* **Capability System Integration (Step 2)** is needed for secure operations in the choreography engine and other components.

* **Effect System Refinement (Step 3)** is required for proper tracking of side effects in all components.

* **Probabilistic Core Enhancement (Step 15)** is needed for quantum components and pathway components that rely on probabilistic models.

* **Communication Components Enhancement (Step 17)** may be needed for components that require advanced message routing capabilities.

#### Component Interdependencies

* **Information Particle Hierarchy** (part of Step 10) depends on:
  * Differentiable Components (Step 7) for gradient-based optimization of particle configurations
  * Effect System (Step 3) for tracking particle transformations

* **Spacetime Continuum** (part of Step 10) depends on:
  * Information Particle Hierarchy (part of Step 10) for placing particles in spacetime
  * Differentiable Components (Step 7) for gradient-based optimization of spacetime properties

* **Differentiable Integration** (part of Step 10) depends on:
  * Information Particle Hierarchy (part of Step 10) for making particles differentiable
  * Spacetime Continuum (part of Step 10) for making spacetime operations differentiable
  * Differentiable Components (Step 7) for integration with existing autodiff infrastructure

* **Quantum Components (Step 14)** depend on:
  * Physics-Inspired Dual Representation (Step 10) for the quantum-inspired principles
  * Probabilistic Core (Step 15) for handling uncertainty in quantum operations

* **Pathway Components (Step 16)** depend on:
  * Physics-Inspired Dual Representation (Step 10) for the dual wave-particle representation
  * Probabilistic Core (Step 15) for probabilistic reasoning
  * Differentiable Components (Step 7) for gradient-based learning

* **Communication Components (Step 17)** depend on:
  * Physics-Inspired Dual Representation (Step 10) for the dual wave-particle representation
  * Security Capabilities (Step 8) for secure communication

By following this sequence and respecting these dependencies, the refactoring can be completed in a systematic and efficient manner, ensuring that the most critical components are implemented first and that dependencies are properly handled.

## Documentation Updates

As part of the refactoring process, the following documentation updates will be required:

1. **API Reference Documentation**:
   - Update all docstrings to reflect the new CAW-aligned interfaces and behaviors
   - Document context-sensitive parameters and return values
   - Provide examples of context-sensitive usage
   - Document dual wave-particle representation integration points
   - Include detailed explanations of physics-inspired principles (superposition, interference, entanglement, uncertainty)
   - Add diagrams illustrating the dual nature of information in the system

2. **Developer Guides**:
   - Create a "Migrating to CAW" guide for existing code
   - Update the "Actor Programming Guide" to include CAW concepts
   - Create a "Context-Sensitive Programming" guide
   - Update the "Security Guide" to include context-sensitive capability verification
   - Create a "Physics-Inspired Dual Representation" guide explaining how to work with the dual wave-particle nature of information
   - Develop a "Quantum-Inspired Programming Patterns" guide showing how to leverage superposition, interference, and entanglement

3. **Architecture Documentation**:
   - Update the architecture diagrams to reflect the CAW integration
   - Document the dual wave-particle representation and context determination flow
   - Update the component interaction diagrams to show physics-inspired interactions
   - Document the context-sensitive message routing with wave interference patterns
   - Create detailed diagrams showing how information maintains both wave and particle aspects throughout the system
   - Develop architectural patterns for leveraging superposition, entanglement, and interference in system design

4. **Examples and Tutorials**:
   - Create example applications demonstrating CAW principles with dual wave-particle representation
   - Update existing tutorials to use CAW-aligned components with physics-inspired patterns
   - Create a tutorial on creating custom contexts and dual wave-particle functions
   - Provide examples of context-sensitive debugging with wave and particle visualization
   - Develop interactive tutorials showing how information behaves as both wave and particle
   - Create code examples demonstrating superposition, interference, entanglement, and uncertainty principles
   - Build sample applications that leverage the dual nature for adaptive behavior

## Backward Compatibility Strategy

To ensure a smooth transition to the CAW-aligned architecture, the following backward compatibility strategies will be implemented:

1. **Adapter Pattern**:
   - Create adapter classes that implement the old interfaces but use the new CAW-aligned components internally
   - Example: `LegacyActor` adapter that wraps a `DecoupledActor` but exposes the old `process_message` method

2. **Default Contexts**:
   - Provide default contexts that mimic the behavior of the old components
   - Example: `LegacyContext` that always selects the traditional behavior

3. **Gradual Migration Path**:
   - Allow mixed usage of old and new components during the transition period
   - Provide clear migration guides and examples
   - Implement automatic context detection for legacy components

4. **Deprecation Warnings**:
   - Add deprecation warnings to old components that will be removed in future versions
   - Provide clear migration instructions in the warning messages
   - Set a timeline for the removal of deprecated components

5. **Compatibility Layer**:
   - Create a compatibility layer that automatically adapts between old and new interfaces
   - Example: `CompatibilityActorSystem` that can work with both `Actor` and `DecoupledActor` instances

## Concrete Examples

### Example: CAW-Aligned Actor Behavior

#### Before Refactoring

```python
class MyActor(Actor):
    async def process_message(self, message):
        if message.type == "command":
            return await self.handle_command(message.content)
        elif message.type == "query":
            return await self.handle_query(message.content)
        else:
            return await self.handle_default(message)
```

#### After Refactoring

```python
class MyActor(DecoupledActor):
    async def process_in_context(self, message, context):
        # Context-sensitive behavior
        if context.name == "command_context":
            return await self.handle_command(message.content, context)
        elif context.name == "query_context":
            return await self.handle_query(message.content, context)
        else:
            return await self.handle_default(message, context)

    # Wave function for message interpretation
    def create_message_wave_function(self, message):
        if message.type == "command":
            return CommandWaveFunction(message)
        elif message.type == "query":
            return QueryWaveFunction(message)
        else:
            return DefaultWaveFunction(message)
```

### Example: CAW-Aligned Capability Verification

#### Before Refactoring

```python
def verify_capability(capability_token, resource_id, action):
    # Simple verification based on token attributes
    if not capability_token:
        return False

    if capability_token.resource_id != resource_id:
        return False

    if action not in capability_token.actions:
        return False

    return True
```

#### After Refactoring

```python
async def verify_capability(capability_token, resource_id, action, context=None):
    # Context-sensitive verification
    if not capability_token:
        return False

    # Create wave function for verification
    verification_wave = VerificationWaveFunction(capability_token, resource_id, action)

    # If context is provided, use it for context-sensitive verification
    if context:
        # Interpret the verification in the given context
        information = Information(verification_wave)
        result = await context.interpret(information)

        # If the probability is high enough, consider it verified
        if result.probability > 0.8:
            return True

    # Fallback to traditional verification
    if capability_token.resource_id != resource_id:
        return False

    if action not in capability_token.actions:
        return False

    # Verify cryptographic signature
    if not verify_signature(capability_token):
        return False

    # Check for revocation
    if is_revoked(capability_token):
        return False

    return True
```

### Example: CAW-Aligned Effect Declaration

#### Before Refactoring

```python
@effects(EffectType.ACTOR_LIFECYCLE)
def create_actor(actor_class, *args, **kwargs):
    # Actor creation logic
    actor = actor_class(*args, **kwargs)
    # Register actor
    return actor
```

#### After Refactoring

```python
@effects(EffectType.ACTOR_LIFECYCLE, EffectType.MESSAGING, context_sensitive=True)
async def create_actor(actor_class, *args, context=None, **kwargs):
    # Context-sensitive actor creation
    if context:
        # Create a wave function for actor creation
        creation_wave = ActorCreationWaveFunction(actor_class, args, kwargs)

        # Interpret the creation in the given context
        information = Information(creation_wave)
        result = await context.interpret(information)

        # Use the context to determine the best actor configuration
        kwargs.update(result.configuration)

    # Actor creation logic
    actor = actor_class(*args, **kwargs)

    # Register actor with context information
    if context:
        register_actor_with_context(actor, context)
    else:
        register_actor(actor)

    return actor
```
