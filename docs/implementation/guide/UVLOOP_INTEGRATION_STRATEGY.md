# Person Suit: `uvloop` Integration Strategy

**Status:** Adopted | **Date:** 2025-06-21

## 1. Executive Summary

This document outlines the strategy for integrating `uvloop`, a high-performance, drop-in replacement for Python's default `asyncio` event loop, across the entire Person Suit system. The primary goal is to significantly boost the performance of our I/O-bound architecture, resulting in higher message throughput, lower latency, and greater scalability. This integration is a critical step in meeting the performance and resilience goals defined by our architectural principles.

## 2. Why `uvloop`?

`uvloop` is built on `libuv`, the same library that powers Node.js, and is implemented in Cython. This provides a substantial performance increase (often 2-4x or more) over the standard Python-based event loop. For our specific architecture, this translates to:

- **Higher Message Bus Throughput:** The `HybridMessageBus`, as the system's central nervous system, will process commands, events, and effects much faster.
- **More Efficient Effect Interpreter:** All real-world I/O (database, network, file) is handled by the `EffectInterpreter`. `uvloop` directly accelerates these operations, allowing the system to handle more concurrent effects.
- **Improved Actor & Choreography Scalability:** The performance gains reduce the overhead of scheduling the many concurrent tasks that constitute our actor and choreography systems, allowing for more complex workflows and greater scale on the same hardware.

## 3. Implementation Strategy

### 3.1. Installation Point

The `uvloop.install()` command must be executed once, at the very beginning of the application's lifecycle. To ensure this, it will be added to all designated entry points.

The installation will be guarded to prevent errors on unsupported platforms, primarily Windows.

```python
# Example for an entry point file
import platform
import asyncio

def install_event_loop_policy():
    """Install uvloop as the default event loop policy on compatible systems."""
    if platform.system() != "Windows":
        try:
            import uvloop
            uvloop.install()
            print("uvloop installed as the default event loop.")
        except ImportError:
            print("uvloop not found, using default asyncio event loop.")
    else:
        print("Windows detected, using default asyncio event loop.")

# Call this at the top of the main script
install_event_loop_policy()

# ... rest of the application startup code
```

This call will be placed in:
- `person_suit/main.py`
- `person_suit/__main__.py`
- Any other script intended to run the full application.

### 3.2. Verification

To verify that `uvloop` is active, use the following CLI command:

```bash
python -c "import asyncio; print(asyncio.get_event_loop_policy())"
```

- **Expected output with `uvloop`:** `<uvloop.LoopPolicy ...>`
- **Expected output without `uvloop`:** `<asyncio.DefaultEventLoopPolicy ...>`

A dedicated verification script will be added to `scripts/diagnostics/` for use in CI/CD pipelines.

### 3.3. Testing Strategy

To ensure robust compatibility, our `pytest` suite should be capable of running against both the default `asyncio` loop and `uvloop`.

- **Default:** By default, tests will run using the standard `asyncio` loop.
- **`--with-uvloop` flag:** A custom `pytest` command-line flag will be added. When present, a `conftest.py` fixture will call `uvloop.install()` before any tests are run.

This dual-mode testing ensures that our code does not develop an accidental, hard dependency on `uvloop`-specific behaviors and remains portable.

### 3.4. Code Usage Clarification

This is a critical point for all developers: **You do not need to change your `asyncio` code.**

- **DO** continue to write standard `asyncio` code: `import asyncio`, `asyncio.sleep()`, `asyncio.gather()`, etc.
- **DO NOT** import `uvloop` directly in business logic or library code.

The `uvloop.install()` call at startup is the *only* piece of integration required. It seamlessly and transparently replaces the underlying mechanism without altering the high-level `asyncio` API.

## 4. Action Plan

1.  [ ] **Dependency:** Confirm `uvloop` is listed in `requirements/core.txt`. (Already done).
2.  [ ] **Create `install_event_loop_policy` function:** Implement the platform-aware installation function in a shared utility module, e.g., `person_suit/core/infrastructure/system_setup.py`.
3.  [ ] **Update Entry Points:** Import and call the new setup function in `person_suit/main.py` and `person_suit/__main__.py`.
4.  [ ] **Create Verification Script:** Add `scripts/diagnostics/verify_uvloop.py`.
5.  [ ] **Update Test Suite:** Add the `--with-uvloop` flag and corresponding fixture in `tests/conftest.py`.
6.  [ ] **Update CI Pipeline:** Add a separate pipeline job that runs the test suite with the `--with-uvloop` flag.
7.  [ ] **Documentation:** Update the developer setup guide (`docs/guides/developer_setup.md`) to mention the new dependency and testing flag.
8.  [ ] **Merge & Deploy:** Announce the change to the development team. 