graph TD
subgraph BusCreators [Direct HybridMessageBus() Calls]
    Binary_file_tests_lint___pycache___test_bare_subscription_checker_cpython_313_pytest_8_3_5_pyc_matches["Binary file tests/lint/__pycache__/test_bare_subscription_checker.cpython-313-pytest-8.3.5.pyc matches"]
    person_suit_core_infrastructure_hybrid_message_bus_py_class_HybridMessageBus_BusKernel__["person_suit/core/infrastructure/hybrid_message_bus.py:class HybridMessageBus(BusKernel):"]
    scripts_load_tests_acf_stress_test_py_________self_bus___HybridMessageBus__["scripts/load_tests/acf_stress_test.py:        self.bus = HybridMessageBus()"]
    scripts_load_tests_caw_principles_simple_test_py_________self_bus___HybridMessageBus__["scripts/load_tests/caw_principles_simple_test.py:        self.bus = HybridMessageBus()"]
    scripts_load_tests_caw_principles_stress_test_py_________self_bus___HybridMessageBus__["scripts/load_tests/caw_principles_stress_test.py:        self.bus = HybridMessageBus()"]
    scripts_load_tests_extreme_acf_test_py_________self_bus___HybridMessageBus__["scripts/load_tests/extreme_acf_test.py:        self.bus = HybridMessageBus()"]
    scripts_load_tests_minimal_cee_stress_test_py_____bus___HybridMessageBus__["scripts/load_tests/minimal_cee_stress_test.py:    bus = HybridMessageBus()"]
    scripts_load_tests_simple_acf_test_py_________self_bus___HybridMessageBus__["scripts/load_tests/simple_acf_test.py:        self.bus = HybridMessageBus()"]
    scripts_test_cee_simple_py_____bus___HybridMessageBus__["scripts/test_cee_simple.py:    bus = HybridMessageBus()"]
    scripts_test_native_provenance_py_____bus___HybridMessageBus__["scripts/test_native_provenance.py:    bus = HybridMessageBus()"]
    scripts_test_ps005_rule_py________Test_that_direct_HybridMessageBus___calls_are_detected____["scripts/test_ps005_rule.py:    """Test that direct HybridMessageBus() calls are detected.""""]
    scripts_test_ps005_rule_py________Test_that_module_HybridMessageBus___is_detected____["scripts/test_ps005_rule.py:    """Test that module.HybridMessageBus() is detected.""""]
    scripts_test_ps005_rule_py_bus___bus_module_HybridMessageBus__["scripts/test_ps005_rule.py:bus = bus_module.HybridMessageBus()"]
    scripts_test_ps005_rule_py_bus___HybridMessageBus__["scripts/test_ps005_rule.py:bus = HybridMessageBus()"]
    tests_bus_test_bus_autosubscribe_py_____bus___HybridMessageBus__["tests/bus/test_bus_autosubscribe.py:    bus = HybridMessageBus()"]
    tests_choreography_test_step_metric_py_____bus___HybridMessageBus__["tests/choreography/test_step_metric.py:    bus = HybridMessageBus()"]
    tests_core_infrastructure_test_bus_thread_safety_py_________________bus___HybridMessageBus__["tests/core/infrastructure/test_bus_thread_safety.py:                bus = HybridMessageBus()"]
    tests_handlers_test_handler_reality_check_py_________bus___HybridMessageBus__["tests/handlers/test_handler_reality_check.py:        bus = HybridMessageBus()"]
    tests_integration_test_cee_flow_py_____bus_instance___HybridMessageBus__["tests/integration/test_cee_flow.py:    bus_instance = HybridMessageBus()"]
    tests_lint_test_bare_subscription_checker_py_bus___HybridMessageBus__["tests/lint/test_bare_subscription_checker.py:bus = HybridMessageBus()"]
    tests_performance_test_acf_priority_adaptation_py_____bus___HybridMessageBus__["tests/performance/test_acf_priority_adaptation.py:    bus = HybridMessageBus()"]
    tests_performance_test_hybrid_bus_throughput_py_____bus___HybridMessageBus__["tests/performance/test_hybrid_bus_throughput.py:    bus = HybridMessageBus()"]
    tests_provenance_test_redpanda_sink_py_____bus___HybridMessageBus__["tests/provenance/test_redpanda_sink.py:    bus = HybridMessageBus()"]
    tests_security_test_routing_denial_py_____bus___HybridMessageBus__["tests/security/test_routing_denial.py:    bus = HybridMessageBus()"]
end
subgraph Bootstraps
    person_suit_core_actors___init___py_class_ActorSystemBootstrap_["person_suit/core/actors/__init__.py:class ActorSystemBootstrap:"]
    person_suit_core_bootstrap_effects_py_class__EffectsBootstrapFeature_____noqa__D101___simple_data_container["person_suit/core/bootstrap/effects.py:class _EffectsBootstrapFeature:  # noqa: D101 – simple data container"]
    person_suit_core_infrastructure_bootstrap_py_class_SystemBootstrap_____noqa__D101___legacy_class_retained_for_compatibility["person_suit/core/infrastructure/bootstrap.py:class SystemBootstrap:  # noqa: D101 – legacy class retained for compatibility"]
    person_suit_main_py_class_CanonicalBootstrap_["person_suit/main.py:class CanonicalBootstrap:"]
end
subgraph Entrypoints
    _Users_przemyslawzajac_Lokalne_Kody_Coversational_diagnostic_validation_test_py_if___name_________main____["/Users/<USER>/Lokalne_Kody/Coversational/diagnostic_validation_test.py:if __name__ == "__main__":"]
    _Users_przemyslawzajac_Lokalne_Kody_Coversational_person_suit_shared_information_core_py["/Users/<USER>/Lokalne_Kody/Coversational/person_suit/shared/information/core.py"]
    Binary_file_person_suit_meta_systems_persona_core_folded_mind__ruff_cache_0_11_6_10776158372106444667_matches["Binary file person_suit/meta_systems/persona_core/folded_mind/.ruff_cache/0.11.6/10776158372106444667 matches"]
    Binary_file_person_suit_meta_systems_persona_core_folded_mind__ruff_cache_0_11_6_13011302693533012005_matches["Binary file person_suit/meta_systems/persona_core/folded_mind/.ruff_cache/0.11.6/13011302693533012005 matches"]
    Binary_file_person_suit_meta_systems_persona_core_folded_mind__ruff_cache_0_11_6_1425758283337358432_matches["Binary file person_suit/meta_systems/persona_core/folded_mind/.ruff_cache/0.11.6/1425758283337358432 matches"]
    Binary_file_person_suit_meta_systems_persona_core_folded_mind__ruff_cache_0_11_6_14560324934471790518_matches["Binary file person_suit/meta_systems/persona_core/folded_mind/.ruff_cache/0.11.6/14560324934471790518 matches"]
    Binary_file_person_suit_meta_systems_persona_core_folded_mind__ruff_cache_0_11_6_15014403304535495537_matches["Binary file person_suit/meta_systems/persona_core/folded_mind/.ruff_cache/0.11.6/15014403304535495537 matches"]
    Binary_file_person_suit_meta_systems_persona_core_folded_mind__ruff_cache_0_11_6_15424288424656541273_matches["Binary file person_suit/meta_systems/persona_core/folded_mind/.ruff_cache/0.11.6/15424288424656541273 matches"]
    Binary_file_person_suit_meta_systems_persona_core_folded_mind__ruff_cache_0_11_6_1643290606633524984_matches["Binary file person_suit/meta_systems/persona_core/folded_mind/.ruff_cache/0.11.6/1643290606633524984 matches"]
    Binary_file_person_suit_meta_systems_persona_core_folded_mind__ruff_cache_0_11_6_16469802721816680300_matches["Binary file person_suit/meta_systems/persona_core/folded_mind/.ruff_cache/0.11.6/16469802721816680300 matches"]
    Binary_file_person_suit_meta_systems_persona_core_folded_mind__ruff_cache_0_11_6_16678384086304382882_matches["Binary file person_suit/meta_systems/persona_core/folded_mind/.ruff_cache/0.11.6/16678384086304382882 matches"]
    Binary_file_person_suit_meta_systems_persona_core_folded_mind__ruff_cache_0_11_6_16761975299367165275_matches["Binary file person_suit/meta_systems/persona_core/folded_mind/.ruff_cache/0.11.6/16761975299367165275 matches"]
    Binary_file_person_suit_meta_systems_persona_core_folded_mind__ruff_cache_0_11_6_17382197324664717372_matches["Binary file person_suit/meta_systems/persona_core/folded_mind/.ruff_cache/0.11.6/17382197324664717372 matches"]
    Binary_file_person_suit_meta_systems_persona_core_folded_mind__ruff_cache_0_11_6_17771730831821175986_matches["Binary file person_suit/meta_systems/persona_core/folded_mind/.ruff_cache/0.11.6/17771730831821175986 matches"]
    Binary_file_person_suit_meta_systems_persona_core_folded_mind__ruff_cache_0_11_6_4107262457787004581_matches["Binary file person_suit/meta_systems/persona_core/folded_mind/.ruff_cache/0.11.6/4107262457787004581 matches"]
    Binary_file_person_suit_meta_systems_persona_core_folded_mind__ruff_cache_0_11_6_6272773850761899018_matches["Binary file person_suit/meta_systems/persona_core/folded_mind/.ruff_cache/0.11.6/6272773850761899018 matches"]
    Binary_file_person_suit_meta_systems_persona_core_folded_mind__ruff_cache_0_11_6_7398276292080183661_matches["Binary file person_suit/meta_systems/persona_core/folded_mind/.ruff_cache/0.11.6/7398276292080183661 matches"]
    Binary_file_person_suit_meta_systems_persona_core_folded_mind__ruff_cache_0_11_6_9365034068558861558_matches["Binary file person_suit/meta_systems/persona_core/folded_mind/.ruff_cache/0.11.6/9365034068558861558 matches"]
    Binary_file_person_suit_meta_systems_persona_core_folded_mind__ruff_cache_0_11_6_9847919926671642639_matches["Binary file person_suit/meta_systems/persona_core/folded_mind/.ruff_cache/0.11.6/9847919926671642639 matches"]
    Binary_file_person_suit_meta_systems_persona_core_folded_mind_cam_algorithmic_reasoning__ruff_cache_0_11_6_878553895102874401_matches["Binary file person_suit/meta_systems/persona_core/folded_mind/cam/algorithmic_reasoning/.ruff_cache/0.11.6/878553895102874401 matches"]
    Binary_file_person_suit_meta_systems_persona_core_folded_mind_sem_atomization_test___pycache___setup_test_env_cpython_313_pyc_matches["Binary file person_suit/meta_systems/persona_core/folded_mind/sem/atomization/test/__pycache__/setup_test_env.cpython-313.pyc matches"]
    examples_acf_integration_demo_py_if___name_________main____["examples/acf_integration_demo.py:if __name__ == "__main__":"]
    examples_dashboard_demo_py_if___name_________main____["examples/dashboard_demo.py:if __name__ == "__main__":"]
    examples_developmental_tracking_demo_py_if___name_________main____["examples/developmental_tracking_demo.py:if __name__ == "__main__":"]
    examples_dual_wave_concrete_backends_example_py_if___name_________main____["examples/dual_wave/concrete_backends_example.py:if __name__ == "__main__":"]
    examples_dual_wave_distributed_storage_example_py_if___name_________main____["examples/dual_wave/distributed_storage_example.py:if __name__ == "__main__":"]
    examples_dual_wave_performance_optimization_example_py_if___name_________main____["examples/dual_wave/performance_optimization_example.py:if __name__ == "__main__":"]
    examples_dual_wave_simple_message_bus_test_py_if___name_________main____["examples/dual_wave/simple_message_bus_test.py:if __name__ == "__main__":"]
    examples_dual_wave_tiered_storage_example_py_if___name_________main____["examples/dual_wave/tiered_storage_example.py:if __name__ == "__main__":"]
    examples_effect_system_demo_py_if___name_________main____["examples/effect_system_demo.py:if __name__ == "__main__":"]
    examples_episodic_future_thinking_example_py_if___name_________main____["examples/episodic_future_thinking_example.py:if __name__ == "__main__":"]
    examples_evaluation_demo_py_if___name_________main____["examples/evaluation_demo.py:if __name__ == "__main__":"]
    examples_hypothesis_generation_demo_py_if___name_________main____["examples/hypothesis_generation_demo.py:if __name__ == "__main__":"]
    examples_isolation_async_event_example_py_if___name_________main____["examples/isolation/async_event_example.py:if __name__ == "__main__":"]
    examples_isolation_basic_isolation_example_py_if___name_________main____["examples/isolation/basic_isolation_example.py:if __name__ == "__main__":"]
    examples_isolation_boundary_extensions_py_if___name_________main____["examples/isolation/boundary_extensions.py:if __name__ == "__main__":"]
    examples_memory_consolidation_demo_py_if___name_________main____["examples/memory_consolidation_demo.py:if __name__ == "__main__":"]
    examples_memory_integrity_demo_py_if___name_________main____["examples/memory_integrity_demo.py:if __name__ == "__main__":"]
    examples_memory_optimization_example_py_if___name_________main____["examples/memory_optimization_example.py:if __name__ == "__main__":"]
    examples_path_integration_demo_py_if___name_________main____["examples/path_integration_demo.py:if __name__ == "__main__":"]
    %% ... {len(ent_nodes)-40} more entrypoints clipped ...
end
classDef bus fill:#ffcccc,stroke:#333;
classDef boot fill:#ccffcc,stroke:#333;
classDef entry fill:#ccccff,stroke:#333;
class Binary_file_tests_lint___pycache___test_bare_subscription_checker_cpython_313_pytest_8_3_5_pyc_matches bus;
class person_suit_core_infrastructure_hybrid_message_bus_py_class_HybridMessageBus_BusKernel__ bus;
class scripts_load_tests_acf_stress_test_py_________self_bus___HybridMessageBus__ bus;
class scripts_load_tests_caw_principles_simple_test_py_________self_bus___HybridMessageBus__ bus;
class scripts_load_tests_caw_principles_stress_test_py_________self_bus___HybridMessageBus__ bus;
class scripts_load_tests_extreme_acf_test_py_________self_bus___HybridMessageBus__ bus;
class scripts_load_tests_minimal_cee_stress_test_py_____bus___HybridMessageBus__ bus;
class scripts_load_tests_simple_acf_test_py_________self_bus___HybridMessageBus__ bus;
class scripts_test_cee_simple_py_____bus___HybridMessageBus__ bus;
class scripts_test_native_provenance_py_____bus___HybridMessageBus__ bus;
class scripts_test_ps005_rule_py________Test_that_direct_HybridMessageBus___calls_are_detected____ bus;
class scripts_test_ps005_rule_py________Test_that_module_HybridMessageBus___is_detected____ bus;
class scripts_test_ps005_rule_py_bus___bus_module_HybridMessageBus__ bus;
class scripts_test_ps005_rule_py_bus___HybridMessageBus__ bus;
class tests_bus_test_bus_autosubscribe_py_____bus___HybridMessageBus__ bus;
class tests_choreography_test_step_metric_py_____bus___HybridMessageBus__ bus;
class tests_core_infrastructure_test_bus_thread_safety_py_________________bus___HybridMessageBus__ bus;
class tests_handlers_test_handler_reality_check_py_________bus___HybridMessageBus__ bus;
class tests_integration_test_cee_flow_py_____bus_instance___HybridMessageBus__ bus;
class tests_lint_test_bare_subscription_checker_py_bus___HybridMessageBus__ bus;
class tests_performance_test_acf_priority_adaptation_py_____bus___HybridMessageBus__ bus;
class tests_performance_test_hybrid_bus_throughput_py_____bus___HybridMessageBus__ bus;
class tests_provenance_test_redpanda_sink_py_____bus___HybridMessageBus__ bus;
class tests_security_test_routing_denial_py_____bus___HybridMessageBus__ bus;
class person_suit_core_actors___init___py_class_ActorSystemBootstrap_ boot;
class person_suit_core_bootstrap_effects_py_class__EffectsBootstrapFeature_____noqa__D101___simple_data_container boot;
class person_suit_core_infrastructure_bootstrap_py_class_SystemBootstrap_____noqa__D101___legacy_class_retained_for_compatibility boot;
class person_suit_main_py_class_CanonicalBootstrap_ boot;
class _Users_przemyslawzajac_Lokalne_Kody_Coversational_diagnostic_validation_test_py_if___name_________main____ entry;
class _Users_przemyslawzajac_Lokalne_Kody_Coversational_person_suit_shared_information_core_py entry;
class Binary_file_person_suit_meta_systems_persona_core_folded_mind__ruff_cache_0_11_6_10776158372106444667_matches entry;
class Binary_file_person_suit_meta_systems_persona_core_folded_mind__ruff_cache_0_11_6_13011302693533012005_matches entry;
class Binary_file_person_suit_meta_systems_persona_core_folded_mind__ruff_cache_0_11_6_1425758283337358432_matches entry;
class Binary_file_person_suit_meta_systems_persona_core_folded_mind__ruff_cache_0_11_6_14560324934471790518_matches entry;
class Binary_file_person_suit_meta_systems_persona_core_folded_mind__ruff_cache_0_11_6_15014403304535495537_matches entry;
class Binary_file_person_suit_meta_systems_persona_core_folded_mind__ruff_cache_0_11_6_15424288424656541273_matches entry;
class Binary_file_person_suit_meta_systems_persona_core_folded_mind__ruff_cache_0_11_6_1643290606633524984_matches entry;
class Binary_file_person_suit_meta_systems_persona_core_folded_mind__ruff_cache_0_11_6_16469802721816680300_matches entry;
class Binary_file_person_suit_meta_systems_persona_core_folded_mind__ruff_cache_0_11_6_16678384086304382882_matches entry;
class Binary_file_person_suit_meta_systems_persona_core_folded_mind__ruff_cache_0_11_6_16761975299367165275_matches entry;
class Binary_file_person_suit_meta_systems_persona_core_folded_mind__ruff_cache_0_11_6_17382197324664717372_matches entry;
class Binary_file_person_suit_meta_systems_persona_core_folded_mind__ruff_cache_0_11_6_17771730831821175986_matches entry;
class Binary_file_person_suit_meta_systems_persona_core_folded_mind__ruff_cache_0_11_6_4107262457787004581_matches entry;
class Binary_file_person_suit_meta_systems_persona_core_folded_mind__ruff_cache_0_11_6_6272773850761899018_matches entry;
class Binary_file_person_suit_meta_systems_persona_core_folded_mind__ruff_cache_0_11_6_7398276292080183661_matches entry;
class Binary_file_person_suit_meta_systems_persona_core_folded_mind__ruff_cache_0_11_6_9365034068558861558_matches entry;
class Binary_file_person_suit_meta_systems_persona_core_folded_mind__ruff_cache_0_11_6_9847919926671642639_matches entry;
class Binary_file_person_suit_meta_systems_persona_core_folded_mind_cam_algorithmic_reasoning__ruff_cache_0_11_6_878553895102874401_matches entry;
class Binary_file_person_suit_meta_systems_persona_core_folded_mind_sem_atomization_test___pycache___setup_test_env_cpython_313_pyc_matches entry;
class examples_acf_integration_demo_py_if___name_________main____ entry;
class examples_dashboard_demo_py_if___name_________main____ entry;
class examples_developmental_tracking_demo_py_if___name_________main____ entry;
class examples_dual_wave_concrete_backends_example_py_if___name_________main____ entry;
class examples_dual_wave_distributed_storage_example_py_if___name_________main____ entry;
class examples_dual_wave_performance_optimization_example_py_if___name_________main____ entry;
class examples_dual_wave_simple_message_bus_test_py_if___name_________main____ entry;
class examples_dual_wave_tiered_storage_example_py_if___name_________main____ entry;
class examples_effect_system_demo_py_if___name_________main____ entry;
class examples_episodic_future_thinking_example_py_if___name_________main____ entry;
class examples_evaluation_demo_py_if___name_________main____ entry;
class examples_hypothesis_generation_demo_py_if___name_________main____ entry;
class examples_isolation_async_event_example_py_if___name_________main____ entry;
class examples_isolation_basic_isolation_example_py_if___name_________main____ entry;
class examples_isolation_boundary_extensions_py_if___name_________main____ entry;
class examples_memory_consolidation_demo_py_if___name_________main____ entry;
class examples_memory_integrity_demo_py_if___name_________main____ entry;
class examples_memory_optimization_example_py_if___name_________main____ entry;
class examples_path_integration_demo_py_if___name_________main____ entry;