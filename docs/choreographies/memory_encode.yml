id: memory_encode_v1
description: Linear memory encoding flow Gateway -> Security -> Interpreter -> MemoryBus
steps:
  - name: gateway.accept
    channel: gateway.requests.memory_encode
    message_type: COMMAND
    next_step: security.check
  - name: security.check
    channel: secure.cmd
    message_type: COMMAND
    requires_capability: WRITE
    next_step: interpreter.dispatch
  - name: interpreter.dispatch
    channel: effects.io.execute
    message_type: COMMAND
    next_step: pc.memory.encode
  - name: pc.memory.encode
    channel: pc.memory.encode
    message_type: COMMAND
    requires_capability: memory_write 