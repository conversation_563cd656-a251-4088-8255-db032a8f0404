id: "user_registration_v1"
description: "Handles the end-to-end user registration flow."

steps:
  - name: "entry.receive_request"
    channel: "command.api.users.register"
    message_type: "COMMAND"
    next_step: "core.validate_input"

  - name: "core.validate_input"
    channel: "command.validation.user.schema"
    message_type: "COMMAND"
    next_step: "core.create_user_record"

  - name: "core.create_user_record"
    channel: "command.db.users.create"
    message_type: "COMMAND"
    requires_capability: "db:users:create"
    next_step: "notify.send_welcome_email"

  - name: "notify.send_welcome_email"
    channel: "command.notification.email.send"
    message_type: "COMMAND"
    next_step: "exit.publish_success_event"
    
  - name: "exit.publish_success_event"
    channel: "event.api.users.registration_complete"
    message_type: "EVENT"
    # This is the terminal step, so it has no `next_step`. 