# Building & Running Your First Choreography

This quick-start walks you from **zero** to a fully-running workflow that you
can observe in metrics — all in under five minutes.

> Prerequisites
> -------------
> • Python ≥3.10 with virtual-env activated (`.venv` recommended)  
> • `prometheus-client` already installed *(comes from our requirements)*  
> • The base infrastructure running via `scripts/start_person_suit.py`

---

## 1. Start the message bus
```bash
source .venv/bin/activate          # if not already active
python scripts/start_person_suit.py # metrics exposed on :8000
```
Leave this terminal open – it is the heart of the system.

---

## 2. Validate the sample choreography
```bash
python -m person_suit.choreography.cli compile \
       docs/choreographies/memory_encode.yml
```
Expected JSON summary:
```json
{
  "id": "memory_encode_v1",
  "steps": [
    "gateway.accept",
    "security.check",
    "interpreter.dispatch",
    "pc.memory.encode"
  ]
}
```

---

## 3. Install it on the running bus
```bash
python -m person_suit.choreography.cli install \
       docs/choreographies/memory_encode.yml
# ➜ "Installed choreography 'memory_encode_v1' with 4 steps"
```

---

## 4. Start a workflow instance
```bash
python -m person_suit.choreography.cli start \
       docs/choreographies/memory_encode.yml
# ➜ "Started workflow <uuid> for choreography 'memory_encode_v1'"
```
The command returns **immediately**; the workflow executes asynchronously.

---

## 5. Observe runtime state
### List active workflows
```bash
python -m person_suit.choreography.cli list workflows
# ["<uuid>"]
```

### Inspect details
```bash
python -m person_suit.choreography.cli inspect <uuid>
```
You'll see JSON containing `status": "finished"` and latency statistics once the
terminal step (`pc.memory.encode`) fires.

---

## 6. Metrics peek (optional)
Even without Prometheus you can watch counters grow:
```bash
curl http://localhost:8000/metrics | grep choreography_step_executions_total
```
Re-run **step 4** a few times and the numbers will increment.

---

🎉  That's it – you've compiled, installed and executed a declarative
choreography!

Next experiments
----------------
1. **Modify** `memory_encode.yml` – add a new intermediate step, re-install, re-start.
2. **Create** a fresh spec: copy the template below, tweak channel names.

```yaml
id: my_first_flow
steps:
  - name: entry
    channel: gateway.requests.hello
    next_step: log
  - name: log
    channel: sys.monitor.alert
```
3. Run `install` + `start` again and check counters.

If you hit issues, consult the CLI help:
```bash
python -m person_suit.choreography.cli --help
``` 