# WaveTrace Gaps Analysis

## Executive Summary

WaveTrace is currently at **85% completeness** for basic provenance needs but lacks critical features for full distributed tracing and CAW-aligned observability. This analysis identifies specific gaps and provides actionable solutions.

## Current Coverage ✅

### What's Working
1. **Basic Message Recording**
   - Message envelopes captured with metadata
   - Handler execution results tracked
   - Success/failure status recorded
   - Latency measurements available

2. **Native Backend Storage**
   - Zero-dependency segment files
   - CRC32 integrity checks
   - Compression support (zstd)
   - Rotation and cleanup policies

3. **Prometheus Export**
   - `WaveTraceExporterActor` operational
   - Latency histograms by channel
   - Success/failure counters
   - Grafana dashboard available

4. **Environment Configuration**
   - `PS_PROVENANCE_SINK` variable supported
   - Native, memory, and Redpanda backends available
   - Configuration via YAML files

## Critical Gaps ❌

### 1. Missing Span Context Propagation
**Problem**: No trace_id/span_id propagation across message boundaries
```python
# Current: Messages lack tracing context
msg = HybridMessage(channel="command.pc.memory.encode", ...)
# Missing: trace_id, span_id, parent_span_id fields
```

**Impact**: Cannot correlate related messages in distributed flows

### 2. No Actor Mailbox Metrics
**Problem**: Actor queue depths and processing delays invisible
```python
# Missing in WaveTrace records:
- actor_mailbox_depth
- actor_processing_delay_ms
- actor_retry_count
```

**Impact**: Cannot diagnose actor bottlenecks or cascade failures

### 3. Wave-Particle Branch Tracking Missing
**Problem**: No recording of computational path decisions
```python
# Not captured:
- wave_processing_branch_taken: bool
- particle_processing_branch_taken: bool
- fidelity_adaptation_reason: str
```

**Impact**: Cannot verify CAW duality principles in production

### 4. Choreography Flow Markers Absent
**Problem**: No `choreo_id` or step markers in traces
```python
# Missing choreography context:
- choreography_id
- choreography_step
- participant_role
```

**Impact**: Cannot trace end-to-end choreographic flows

### 5. Differential Context Deltas Not Recorded
**Problem**: Context changes not captured in provenance
```python
# sys.context.delta events not linked to WaveTrace spans
```

**Impact**: Cannot replay context evolution or debug propagation issues

### 6. No Capability Decision Audit
**Problem**: Security authorization decisions not in trace
```python
# Missing security context:
- capability_check_result
- denied_capabilities
- security_level_at_execution
```

**Impact**: Cannot audit security decisions post-mortem

### 7. Effect Execution Traces Incomplete
**Problem**: Effect interpreter decisions not recorded
```python
# Not captured:
- effect_strategy_selected (WAVE/PARTICLE)
- effect_batch_size
- effect_queue_time_ms
```

**Impact**: Cannot optimize effect execution strategies

### 8. Resource Usage Attribution Missing
**Problem**: CPU/memory usage not attributed to spans
```python
# Missing resource metrics:
- cpu_time_ms
- memory_allocated_bytes
- io_operations_count
```

**Impact**: Cannot identify resource-intensive operations

## Implementation Plan

### Phase 1: Core Span Context (Sprint 2b Extension)
```python
# Extend HybridMessage with tracing fields
@dataclass
class HybridMessage:
    # Existing fields...
    
    # New tracing context
    trace_id: Optional[str] = None
    span_id: Optional[str] = None
    parent_span_id: Optional[str] = None
    trace_flags: int = 0
    trace_state: Dict[str, str] = field(default_factory=dict)
```

### Phase 2: Actor Integration (Sprint 3 Addition)
```python
# Add to DecoupledActor base class
async def record_mailbox_depth(self):
    """Record actor mailbox metrics to WaveTrace."""
    if self._mailbox:
        await self.bus.publish(HybridMessage(
            channel="sys.actor.metrics",
            payload={
                "actor_path": str(self.path),
                "mailbox_depth": len(self._mailbox),
                "trace_id": self.current_trace_id,
            }
        ))
```

### Phase 3: Enhanced Provenance Schema
```python
# Extend ProvenanceRecord with trace fields
@dataclass
class EnhancedProvenanceRecord:
    # Core fields
    timestamp: float
    channel: str
    message_id: str
    
    # Trace context
    trace_id: str
    span_id: str
    parent_span_id: Optional[str]
    
    # CAW-specific
    wave_particle_ratio: float
    fidelity_actual: int
    computational_branch: str  # "WAVE" | "PARTICLE" | "HYBRID"
    
    # Actor context
    actor_path: Optional[str]
    actor_mailbox_depth: Optional[int]
    
    # Choreography
    choreography_id: Optional[str]
    choreography_step: Optional[int]
    
    # Security
    capabilities_used: List[str]
    authorization_result: str
    
    # Performance
    queue_time_ms: float
    processing_time_ms: float
    total_latency_ms: float
    
    # Resources
    cpu_time_ms: Optional[float]
    memory_bytes: Optional[int]
```

## Success Metrics

1. **Trace Completeness**: 100% of messages have trace_id and span_id
2. **Actor Visibility**: All actor mailbox depths recorded when > 0
3. **CAW Branch Coverage**: 100% of adapted messages show branch taken
4. **Choreography Tracking**: All choreographed flows have end-to-end traces
5. **Security Audit**: 100% of capability checks recorded
6. **Resource Attribution**: >90% of spans have resource metrics

## Counter-Evidence Tests

### Test 1: Trace Continuity
```python
async def test_trace_continuity():
    """Verify trace_id propagates through entire flow."""
    # Send command with trace_id
    # Verify effect has same trace_id with new span_id
    # Verify event has same trace_id with child span_id
```

### Test 2: Actor Metrics Under Load
```python
async def test_actor_metrics_recording():
    """Verify mailbox depths recorded during overload."""
    # Send 10k messages rapidly
    # Query WaveTrace for actor metrics
    # Verify mailbox_depth > 0 recorded
```

### Test 3: CAW Branch Verification
```python
async def test_wave_particle_branch_recording():
    """Verify computational branches are tracked."""
    # Send high-priority message (particle branch expected)
    # Send low-priority bulk (wave branch expected)
    # Verify WaveTrace shows correct branches
```

## Integration with Existing Systems

### 1. OpenTelemetry Bridge
```python
class WaveTraceOTLPExporter:
    """Export WaveTrace spans to OpenTelemetry collectors."""
    
    async def export_batch(self, spans: List[EnhancedProvenanceRecord]):
        # Convert to OTLP format
        # Send to configured endpoint
```

### 2. Grafana Tempo Integration
- Configure Tempo data source
- Link from message latency to full trace view
- Enable trace-to-metrics correlation

### 3. Jaeger Compatibility
- Export trace format compatible with Jaeger
- Enable distributed trace visualization
- Support trace comparison features

## Conclusion

WaveTrace has a solid foundation but needs these enhancements to provide full observability for CAW-based systems. The implementation can be done incrementally without breaking existing functionality, with each phase providing immediate value.

The most critical gaps are:
1. Trace context propagation (blocks all distributed tracing)
2. Actor mailbox visibility (blocks performance debugging)
3. CAW branch tracking (blocks principle verification)

These should be prioritized in the next sprint cycle. 