# Monitoring System Architecture Redesign

Unified Communication Architecture Note: 
**Note:** This redesign now incorporates our unified message-based decoupled architecture, which governs imports, connections, orchestration, and overall system choreography. See [docs/architecture/message_based_architecture.md](../architecture/message_based_architecture.md) for a comprehensive explanation.

## Overview

This document outlines a comprehensive redesign of the Person Suit monitoring system architecture to eliminate circular dependencies, improve maintainability, and align with the CAW paradigm. The redesign focuses on proper separation of concerns, dependency inversion, and clean interfaces.

## Current Architecture Issues

The current monitoring system architecture suffers from several issues:

1. **Circular Dependencies**:
   - `infrastructure/monitoring/service.py` ↔ `meta_systems/persona_core/folded_mind/monitoring/system.py`
   - `infrastructure/monitoring/__init__.py` ↔ Various implementation modules
   - `infrastructure/dual_wave/core.py` ↔ `infrastructure/monitoring/telemetry.py`

2. **Tight Coupling**:
   - Components directly import concrete implementations rather than interfaces
   - Bidirectional dependencies between components
   - Excessive re-exporting in `__init__.py` files

3. **Global State**:
   - Heavy use of singletons and global variables
   - Direct access to global state across module boundaries
   - Complex initialization order dependencies

4. **Monolithic Components**:
   - Large, multi-responsibility classes
   - Insufficient separation of concerns
   - Unclear component boundaries

## Target Architecture

The redesigned monitoring system will follow these architectural principles:

1. **Layered Architecture**:
   - **Interface Layer**: Pure abstract interfaces with no dependencies on implementations
   - **Core Layer**: Core implementation components that depend only on interfaces
   - **Integration Layer**: Adapters for integrating with other systems
   - **Utility Layer**: Shared utilities and helpers

2. **Dependency Inversion**:
   - All components depend on interfaces, not concrete implementations
   - Dependencies flow in one direction (from higher to lower layers)
   - No circular dependencies between components

3. **Clean Module Structure**:
   - Clear separation of concerns between modules
   - Minimal and explicit public APIs
   - Controlled visibility of implementation details

4. **Dependency Injection**:
   - No direct instantiation of dependencies
   - Dependencies provided through constructors or factory methods
   - Centralized dependency management

## Module Structure

The redesigned monitoring system will have the following module structure:

```
person_suit/core/infrastructure/monitoring/
├── interfaces/                  # Interface definitions only
│   ├── __init__.py             # Exports all interfaces
│   ├── alert.py                # Alert-related interfaces
│   ├── metric.py               # Metric-related interfaces
│   ├── service.py              # Service-related interfaces
│   ├── storage.py              # Storage-related interfaces
│   ├── system.py               # System monitoring interfaces
│   └── telemetry.py            # Telemetry-related interfaces
├── core/                        # Core implementation components
│   ├── __init__.py             # Minimal exports
│   ├── alert/                  # Alert implementation
│   │   ├── __init__.py
│   │   ├── manager.py          # Alert manager implementation
│   │   └── threshold.py        # Alert threshold implementation
│   ├── metric/                 # Metric implementation
│   │   ├── __init__.py
│   │   ├── collector.py        # Metric collector implementation
│   │   ├── registry.py         # Metric registry implementation
│   │   └── types.py            # Metric type implementations
│   ├── service/                # Service implementation
│   │   ├── __init__.py
│   │   └── monitoring.py       # Monitoring service implementation
│   ├── storage/                # Storage implementation
│   │   ├── __init__.py
│   │   ├── memory.py           # In-memory storage implementation
│   │   └── persistent.py       # Persistent storage implementation
│   └── system/                 # System monitoring implementation
│       ├── __init__.py
│       └── monitor.py          # System monitor implementation
├── integration/                 # Integration with other systems
│   ├── __init__.py
│   ├── dual_wave/              # Integration with dual wave system
│   │   ├── __init__.py
│   │   └── adapter.py          # Dual wave adapter
│   ├── effects/                # Integration with effects system
│   │   ├── __init__.py
│   │   └── adapter.py          # Effects adapter
│   └── meta_systems/           # Integration with meta systems
│       ├── __init__.py
│       └── adapter.py          # Meta systems adapter
├── utils/                       # Shared utilities
│   ├── __init__.py
│   ├── async_helpers.py        # Async utility functions
│   ├── constants.py            # Shared constants
│   └── validation.py           # Validation utilities
├── __init__.py                  # Minimal public API
├── factory.py                   # Factory functions for creating components
└── di.py                        # Dependency injection container
```

## Interface Definitions

The interface layer will define clean, abstract interfaces with no dependencies on implementations:

### Alert Interfaces

```python
# interfaces/alert.py
from abc import ABC, abstractmethod
from enum import Enum
from typing import Dict, List, Any, Optional, Callable, Union
from datetime import datetime
from dataclasses import dataclass

class AlertSeverity(Enum):
    """Alert severity levels."""
    INFO = 0
    WARNING = 1
    ERROR = 2
    CRITICAL = 3

@dataclass(frozen=True)
class AlertThresholdData:
    """Data structure representing threshold configuration."""
    metric_name: str
    operator: str
    threshold_value: Union[int, float]
    severity: AlertSeverity = AlertSeverity.WARNING
    duration_seconds: Optional[int] = None
    sensitivity: float = 1.0
    cooldown_seconds: int = 300
    description_template: str = ""

@dataclass(frozen=True)
class AlertData:
    """Data structure representing a generated alert."""
    id: str
    metric_name: str
    value: Union[int, float]
    threshold: AlertThresholdData
    timestamp: datetime
    status: str
    severity: AlertSeverity
    description: str
    acknowledged_at: Optional[datetime] = None
    resolved_at: Optional[datetime] = None
    acknowledged_by: Optional[str] = None

# Type alias for alert handlers
AlertHandlerCallable = Callable[[AlertData], None]

class AlertManagerInterface(ABC):
    """Interface for managing alert thresholds and processing alerts."""

    @abstractmethod
    def add_threshold(self, threshold: AlertThresholdData) -> None:
        """Add a threshold configuration."""
        pass

    @abstractmethod
    def remove_threshold(self, metric_name: str, threshold_value: Union[int, float], operator: str) -> bool:
        """Remove a threshold configuration."""
        pass

    @abstractmethod
    def register_alert_handler(self, handler: AlertHandlerCallable) -> None:
        """Register a callback for generated alerts."""
        pass

    @abstractmethod
    def evaluate_metric(self, metric_name: str, value: Union[int, float]) -> List[str]:
        """Evaluate a metric value against thresholds, potentially triggering alerts."""
        pass

    # Additional methods omitted for brevity
```

### Metric Interfaces

```python
# interfaces/metric.py
from abc import ABC, abstractmethod
from enum import Enum, auto
from typing import Dict, List, Optional, Any, Union, TypeVar, Generic

# Type variables
T = TypeVar('T')
V = TypeVar('V', int, float)

class MetricType(Enum):
    """Defines the types of metrics that can be collected."""
    COUNTER = auto()
    GAUGE = auto()
    HISTOGRAM = auto()
    TIMER = auto()
    METER = auto()
    SUMMARY = auto()

class MetricUnit(Enum):
    """Defines the units for metrics."""
    # Time units
    NANOSECONDS = "ns"
    MICROSECONDS = "µs"
    MILLISECONDS = "ms"
    SECONDS = "s"
    MINUTES = "min"
    HOURS = "h"

    # Data size units
    BYTES = "B"
    KILOBYTES = "KB"
    MEGABYTES = "MB"
    GIGABYTES = "GB"

    # Rate units
    PER_SECOND = "/s"
    PER_MINUTE = "/min"
    PER_HOUR = "/h"

    # Other units
    COUNT = "count"
    PERCENT = "%"
    NONE = ""

class MetricValueInterface(ABC):
    """Interface for metric values."""

    @abstractmethod
    def to_dict(self) -> Dict[str, Any]:
        """Convert the metric value to a dictionary representation."""
        pass

class MetricInterface(Generic[V], ABC):
    """Interface for metrics."""

    @abstractmethod
    def get_value(self) -> MetricValueInterface:
        """Get the current value of the metric."""
        pass

    @abstractmethod
    def get_type(self) -> MetricType:
        """Get the type of the metric."""
        pass

    @abstractmethod
    def to_dict(self) -> Dict[str, Any]:
        """Convert the metric to a dictionary representation."""
        pass

class MetricCollectorInterface(ABC):
    """Interface for metric collectors."""

    @abstractmethod
    async def initialize(self) -> None:
        """Initialize the metric collector."""
        pass

    @abstractmethod
    async def collect_system_metrics(self) -> Dict[str, Any]:
        """Collect system metrics."""
        pass

    @abstractmethod
    async def shutdown(self) -> None:
        """Shutdown the metric collector."""
        pass

class MetricRegistryInterface(ABC):
    """Interface for metric registries."""

    @abstractmethod
    def register(self, metric: MetricInterface) -> None:
        """Register a metric."""
        pass

    @abstractmethod
    def unregister(self, name: str, namespace: str = "") -> bool:
        """Unregister a metric."""
        pass

    @abstractmethod
    def get(self, name: str, namespace: str = "") -> Optional[MetricInterface]:
        """Get a metric by name."""
        pass

    # Additional methods omitted for brevity
```

### System Interfaces

```python
# interfaces/system.py
from abc import ABC, abstractmethod
from typing import Dict, List, Optional, Any

class SystemMetricsInterface(ABC):
    """Interface for system metrics data structure."""

    @property
    @abstractmethod
    def timestamp(self) -> float:
        """Get the timestamp when the metrics were collected."""
        pass

    @property
    @abstractmethod
    def cpu_usage(self) -> float:
        """Get the CPU usage percentage."""
        pass

    @property
    @abstractmethod
    def memory_usage(self) -> int:
        """Get the memory usage in bytes."""
        pass

    @property
    @abstractmethod
    def computational_load(self) -> float:
        """Get the load on the computational pathway."""
        pass

    @property
    @abstractmethod
    def subjective_load(self) -> float:
        """Get the load on the subjective pathway."""
        pass

    @property
    @abstractmethod
    def active_processes(self) -> int:
        """Get the number of active processes."""
        pass

    @property
    @abstractmethod
    def queue_sizes(self) -> Dict[str, int]:
        """Get the sizes of processing queues."""
        pass

    @property
    @abstractmethod
    def processing_rates(self) -> Dict[str, float]:
        """Get the processing rates."""
        pass

    @property
    @abstractmethod
    def error_counts(self) -> Dict[str, int]:
        """Get the error counts."""
        pass

    @property
    @abstractmethod
    def latencies(self) -> Dict[str, float]:
        """Get the latencies."""
        pass

    @property
    @abstractmethod
    def custom_metrics(self) -> Dict[str, Any]:
        """Get the custom metrics."""
        pass

class SystemMonitorInterface(ABC):
    """Interface for system monitor components."""

    @abstractmethod
    def initialize(self) -> bool:
        """Initialize the system monitor."""
        pass

    @abstractmethod
    def collect_metrics(self) -> SystemMetricsInterface:
        """Collect system metrics."""
        pass

    @abstractmethod
    def get_latest_metrics(self) -> Optional[SystemMetricsInterface]:
        """Get the latest system metrics."""
        pass

    @abstractmethod
    def get_metrics_history(self, count: int = 10) -> List[SystemMetricsInterface]:
        """Get the history of system metrics."""
        pass

    @abstractmethod
    def check_system_health(self) -> Dict[str, Any]:
        """Check the health of the system."""
        pass

    @abstractmethod
    def add_custom_metric(self, name: str, value: Any, description: Optional[str] = None) -> None:
        """Add a custom metric to the monitoring system."""
        pass
```

### Service Interfaces

```python
# interfaces/service.py
from abc import ABC, abstractmethod
from typing import Dict, Any, Optional

class MonitoringServiceInterface(ABC):
    """Interface for the core monitoring service."""

    @abstractmethod
    async def initialize(self) -> None:
        """Initialize the monitoring service and its components."""
        pass

    @abstractmethod
    async def start(self) -> None:
        """Start the background monitoring processes."""
        pass

    @abstractmethod
    async def stop(self) -> None:
        """Stop the background monitoring processes gracefully."""
        pass

    @abstractmethod
    async def record_request(self, service_name: str, response_time_ms: float, success: bool = True) -> None:
        """Record metrics for a service request."""
        pass

    @abstractmethod
    async def get_system_metrics(self) -> Dict[str, Any]:
        """Get current system-level metrics."""
        pass

    @abstractmethod
    async def get_service_metrics(self, service_name: str) -> Optional[Dict[str, Any]]:
        """Get current metrics for a specific service."""
        pass

    @abstractmethod
    async def get_all_service_metrics(self) -> Dict[str, Dict[str, Any]]:
        """Get current metrics for all monitored services."""
        pass

    @abstractmethod
    async def get_m3_optimization_info(self) -> Dict[str, Any]:
        """Get information related to M3 hardware optimizations, if applicable."""
        pass

    @abstractmethod
    async def get_uptime_seconds(self) -> float:
        """Get the uptime of the monitoring service itself."""
        pass

    @abstractmethod
    async def get_performance_state(self) -> str:
        """Get a general indicator of the system's performance state."""
        pass

    @property
    @abstractmethod
    def is_monitoring(self) -> bool:
        """Check if the monitoring service is currently active."""
        pass
```

## Implementation Strategy

The implementation of the redesigned monitoring system will follow these steps:

1. **Create Interface Layer**:
   - Define all interfaces in the `interfaces/` package
   - Ensure interfaces have no dependencies on implementations
   - Create comprehensive documentation for all interfaces

2. **Implement Core Components**:
   - Implement core components in the `core/` package
   - Each component should depend only on interfaces
   - Use dependency injection for all dependencies

3. **Create Integration Adapters**:
   - Implement adapters for integrating with other systems
   - Adapters should translate between monitoring interfaces and external systems
   - Use the Adapter pattern to maintain clean boundaries

4. **Implement Factory and DI Container**:
   - Create factory functions for instantiating components
   - Implement a dependency injection container for managing dependencies
   - Provide a clean public API for creating and configuring the monitoring system

5. **Update Public API**:
   - Simplify the `__init__.py` file to expose only the public API
   - Provide factory functions for creating common configurations
   - Document the public API comprehensively

## Dependency Management

The redesigned monitoring system will use dependency injection to manage dependencies:

```python
# di.py
from typing import Dict, Any, Type, TypeVar, Optional, cast

T = TypeVar('T')

class DependencyContainer:
    """Container for managing dependencies."""

    def __init__(self):
        self._instances: Dict[Type, Any] = {}
        self._factories: Dict[Type, Any] = {}

    def register_instance(self, interface_type: Type[T], instance: T) -> None:
        """Register an instance for an interface type."""
        self._instances[interface_type] = instance

    def register_factory(self, interface_type: Type[T], factory: Any) -> None:
        """Register a factory function for an interface type."""
        self._factories[interface_type] = factory

    def resolve(self, interface_type: Type[T], **kwargs) -> T:
        """Resolve an instance for an interface type."""
        # Check if we have a registered instance
        if interface_type in self._instances:
            return cast(T, self._instances[interface_type])

        # Check if we have a registered factory
        if interface_type in self._factories:
            factory = self._factories[interface_type]
            instance = factory(**kwargs)
            self._instances[interface_type] = instance
            return cast(T, instance)

        # If we can't resolve the dependency, raise an error
        raise ValueError(f"No registration found for {interface_type.__name__}")

# Global container instance
_container: Optional[DependencyContainer] = None

def get_container() -> DependencyContainer:
    """Get the global dependency container instance."""
    global _container
    if _container is None:
        _container = DependencyContainer()
    return _container

def register_instance(interface_type: Type[T], instance: T) -> None:
    """Register an instance for an interface type."""
    get_container().register_instance(interface_type, instance)

def register_factory(interface_type: Type[T], factory: Any) -> None:
    """Register a factory function for an interface type."""
    get_container().register_factory(interface_type, factory)

def resolve(interface_type: Type[T], **kwargs) -> T:
    """Resolve an instance for an interface type."""
    return get_container().resolve(interface_type, **kwargs)
```

## Factory Functions

The factory module will provide functions for creating common configurations:

```python
# factory.py
from typing import Dict, Any, Optional

from .interfaces.service import MonitoringServiceInterface
from .interfaces.alert import AlertManagerInterface
from .interfaces.metric import MetricCollectorInterface, MetricRegistryInterface
from .interfaces.storage import MetricStorageInterface
from .interfaces.system import SystemMonitorInterface

from .core.service.monitoring import MonitoringService
from .core.alert.manager import AlertManager
from .core.metric.collector import MetricCollector
from .core.metric.registry import MetricRegistry
from .core.storage.memory import InMemoryMetricStorage
from .core.system.monitor import SystemMonitor

from .di import register_instance, register_factory, resolve

def create_monitoring_service(config: Optional[Dict[str, Any]] = None) -> MonitoringServiceInterface:
    """Create a monitoring service with default dependencies."""
    # Register dependencies if not already registered
    try:
        resolve(AlertManagerInterface)
    except ValueError:
        register_instance(AlertManagerInterface, AlertManager())

    try:
        resolve(MetricCollectorInterface)
    except ValueError:
        register_instance(MetricCollectorInterface, MetricCollector())

    try:
        resolve(MetricRegistryInterface)
    except ValueError:
        register_instance(MetricRegistryInterface, MetricRegistry())

    try:
        resolve(MetricStorageInterface)
    except ValueError:
        register_instance(MetricStorageInterface, InMemoryMetricStorage())

    try:
        resolve(SystemMonitorInterface)
    except ValueError:
        register_instance(SystemMonitorInterface, SystemMonitor())

    # Create and register the monitoring service
    service = MonitoringService(
        alert_manager=resolve(AlertManagerInterface),
        metric_collector=resolve(MetricCollectorInterface),
        metric_storage=resolve(MetricStorageInterface),
        system_monitor=resolve(SystemMonitorInterface),
        config=config or {}
    )
    register_instance(MonitoringServiceInterface, service)

    return service
```

## Public API

The `__init__.py` file will provide a clean public API:

```python
"""
Person Suit - Monitoring System

This module provides comprehensive monitoring capabilities for the Person Suit framework,
including metrics collection, performance analysis, and anomaly detection.
"""

# Re-export interfaces
from .interfaces.service import MonitoringServiceInterface
from .interfaces.alert import AlertManagerInterface, AlertSeverity, AlertThresholdData, AlertData
from .interfaces.metric import MetricCollectorInterface, MetricType, MetricUnit
from .interfaces.storage import MetricStorageInterface
from .interfaces.system import SystemMonitorInterface, SystemMetricsInterface

# Re-export factory functions
from .factory import create_monitoring_service

# Re-export dependency injection functions
from .di import register_instance, register_factory, resolve

# Define public API
__all__ = [
    # Interfaces
    'MonitoringServiceInterface',
    'AlertManagerInterface',
    'AlertSeverity',
    'AlertThresholdData',
    'AlertData',
    'MetricCollectorInterface',
    'MetricType',
    'MetricUnit',
    'MetricStorageInterface',
    'SystemMonitorInterface',
    'SystemMetricsInterface',

    # Factory functions
    'create_monitoring_service',

    # Dependency injection
    'register_instance',
    'register_factory',
    'resolve',
]
```

## Migration Strategy

Migrating from the current architecture to the redesigned architecture will be a gradual process:

1. **Create New Structure**:
   - Create the new directory structure
   - Define interfaces in the `interfaces/` package
   - Implement core components in the `core/` package

2. **Implement Adapters**:
   - Create adapters for existing implementations
   - Ensure backward compatibility with existing code

3. **Update Consumers**:
   - Update consumers to use the new interfaces
   - Inject dependencies instead of directly instantiating

4. **Deprecate Old API**:
   - Mark old API as deprecated
   - Provide migration guidance for consumers

5. **Remove Old Implementation**:
   - Once all consumers have migrated, remove the old implementation

## Testing Strategy

The redesigned monitoring system will be thoroughly tested:

1. **Unit Tests**:
   - Test each component in isolation
   - Mock dependencies using interfaces
   - Verify behavior against interface contracts

2. **Integration Tests**:
   - Test components together
   - Verify interactions between components
   - Test with real dependencies

3. **System Tests**:
   - Test the entire monitoring system
   - Verify end-to-end functionality
   - Test with real-world scenarios

4. **Performance Tests**:
   - Test performance characteristics
   - Verify scalability
   - Test with high load

## Conclusion

The redesigned monitoring system architecture addresses the issues with the current architecture by:

1. **Eliminating Circular Dependencies**:
   - Clean separation of interfaces and implementations
   - One-way dependency flow
   - No direct imports of concrete implementations

2. **Reducing Coupling**:
   - Dependency injection for all dependencies
   - Clear component boundaries
   - Explicit public API

3. **Eliminating Global State**:
   - No direct access to global state
   - Proper dependency management
   - Clean initialization process

4. **Improving Modularity**:
   - Clear separation of concerns
   - Smaller, focused components
   - Explicit component boundaries

The redesigned architecture will be more maintainable, testable, and extensible, while also aligning with the CAW paradigm.

## Alignment with CAW Schema and Design Documents

The redesigned monitoring system architecture aligns with the CAW schema and design documents in the following ways:

### Dual Wave-Particle Representation

The monitoring system will support the dual wave-particle representation as defined in the CAW schema:

- **Wave Aspect**: The monitoring system will track wave-related metrics such as vector dimensions, wave function properties, and interference patterns through the `DualWaveTelemetry` interface.
- **Particle Aspect**: The monitoring system will track particle-related metrics such as hypergraph node/edge counts, structural properties, and state transitions.

### Context-Aware Monitoring

The monitoring system will be context-aware, as defined in the `Context_Management_Design.md` document:

- Metrics collection and evaluation will be influenced by the current context
- Alerts and thresholds will be context-sensitive
- Resource monitoring will adapt based on context parameters

### Effect-Based Monitoring

The monitoring system will integrate with the Effect system as defined in the `Event_Effect_Log_Service_Design.md` document:

- Monitor effect application and outcomes
- Track effect-related metrics such as success rates, latencies, and error counts
- Integrate with the Event/Effect Log Service for historical analysis

### Capability-Based Security

The monitoring system will implement capability-based security as defined in the `Capability_Management_Design.md` document:

- Access to monitoring data will be controlled through capabilities
- Monitoring operations will require appropriate capabilities
- Capability delegation will be tracked and monitored

### Integration with Central State Actor

The monitoring system will integrate with the Central State Actor as defined in the `Central_State_Actor_Design.md` document:

- Monitor state transitions and updates
- Track state-related metrics such as update frequencies, sizes, and latencies
- Provide feedback to the State Actor for adaptive behavior

## References

1. [Dependency Inversion Principle](https://en.wikipedia.org/wiki/Dependency_inversion_principle)
2. [Clean Architecture](https://blog.cleancoder.com/uncle-bob/2012/08/13/the-clean-architecture.html)
3. [Python Dependency Injection](https://python-dependency-injector.ets-labs.org/)
4. [CAW Paradigm Documentation](../future/unified_paradigm/v0.3/README.md)
5. [Monitoring Circular Import Resolution](./MONITORING_CIRCULAR_IMPORT_RESOLUTION.md)
6. [Circular Import Resolution Plan](./CIRCULAR_IMPORT_RESOLUTION_PLAN.md)
7. [CAW Python Schema](../../schemas/caw_python_schema.py)
8. [DualInformation Implementation Design](../design/DualInformation_Implementation_Design.md)
9. [ParticleState Implementation Design](../design/ParticleState_Implementation_Design.md)
10. [WaveState Implementation Design](../design/WaveState_Implementation_Design.md)
11. [Central State Actor Design](../design/Central_State_Actor_Design.md)
12. [Context Management Design](../design/Context_Management_Design.md)
13. [Capability Management Design](../design/Capability_Management_Design.md)
14. [Event Effect Log Service Design](../design/Event_Effect_Log_Service_Design.md)
