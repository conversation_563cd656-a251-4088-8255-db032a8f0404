# Metrics Consolidation and Enhancement Plan

## 1. Current State & Problem Statement

The Person Suit codebase currently utilizes multiple, distinct, and sometimes overlapping systems for metrics collection and namespacing. This fragmentation makes it difficult to:
- Get a unified view of system health and performance.
- Reliably aggregate and correlate metrics from different parts of the system.
- Implement consistent telemetry and monitoring backends.
- Maintain and evolve the metrics infrastructure efficiently.

Identified Metrics Systems (Pre-Consolidation):
1.  **Core Standard**: `person_suit/core/infrastructure/monitoring/metrics.py` - This is the target system for unified metric object definitions and registration patterns.
2.  **SEM Legacy**: `person_suit/meta_systems/persona_core/folded_mind/sem/metrics_framework.py` - Used by various SEM components. (Targeted by Phase 1)
3.  **Persona Core Internal Legacy**: `person_suit/meta_systems/persona_core/core/monitoring/` (and its submodules like `metrics/`) - A shared metrics system within `persona_core` but older than the core infrastructure standard. (Targeted by Phase 2)
4.  **Folded Mind Monitoring - CognitiveStateMonitor**: `person_suit/meta_systems/persona_core/folded_mind/monitoring/metrics.py` - A localized metrics system for the `CognitiveStateMonitor`. (Targeted by Phase 3)
5.  **CAM Tool Framework Telemetry**: `person_suit/meta_systems/persona_core/folded_mind/cam/tool_framework/telemetry/` - A specialized telemetry system for CAM tools. (Targeted by Phase 4 review)
6.  **SOMA `@telemetry`**: Found in SOMA components, using a decorator from `person_suit.shared.utils.telemetry` and direct dictionary updates. (Targeted by Phase 4 review)
7.  **Other Specialized Collectors**: e.g., in `folded_mind/sem/memory/knowledge/metrics/collection.py` and `folded_mind/sem/logging/metrics.py`. (Targeted by Phase 4 review)


## 2. Goal

- Consolidate all general-purpose metrics collection and namespacing to use the system defined in `person_suit/core/infrastructure/monitoring/metrics.py`.
- Ensure that specialized telemetry systems can feed their data into the main `HybridMessageBus` for unified telemetry aggregation.
- Remove redundant legacy metrics systems and associated code.
- Establish clear patterns for how new components should implement metrics and telemetry.

## 3. Scope & Phases

This refactoring will be done in multiple phases:

### 3.1. Phase 1: SEM Consolidation (Subjective-Experiential Mind)
- **Status**: [COMPLETED]
- **Description**: Refactor components in `person_suit/meta_systems/persona_core/folded_mind/sem/` that were using the SEM-local `metrics_framework.py` or the Persona Core internal legacy system to use `person_suit.core.infrastructure.monitoring.metrics.py`.
- **Files Refactored**:
    - [x] `associative.py` (Completed)
    - [x] `intuition.py` (Completed)
    - [x] `pattern.py` (Completed)
    - [x] `value.py` (Completed)
    - [x] `weighting.py` (Completed)
    - [x] `meaning.py` (Completed)
    - [x] `analogical_reasoning/structure_mapping.py` (Completed)
    - [x] `wave_particle/emotional_integration.py` (Completed)
- **Post-Refactoring**:
    - [x] Deleted `person_suit/meta_systems/persona_core/folded_mind/sem/metrics_framework.py`.

### 3.2. Phase 2: Persona Core Internal Metrics (`person_suit/meta_systems/persona_core/core/monitoring/`)
- **Status**: [IN PROGRESS]
- **Description**: Identify all users of the legacy metrics system defined in `person_suit/meta_systems/persona_core/core/monitoring/` (and its submodules like `metrics/`) and refactor them to use `person_suit.core.infrastructure.monitoring.metrics.py`.
- **Key Files/Modules Using This Legacy System (Initial List - to be expanded/verified):**
    - [x] `person_suit/meta_systems/persona_core/core/optimization/resource_monitor.py`
    - [x] `person_suit/meta_systems/persona_core/folded_mind/monitoring/system.py` (SystemMonitor in Folded Mind)
    - [x] `person_suit/meta_systems/persona_core/folded_mind/cam/analytical.py`
    - [x] `person_suit/meta_systems/persona_core/folded_mind/cam/mathematical.py`
    - [x] `person_suit/meta_systems/persona_core/folded_mind/cam/logical.py`
    - [i] `person_suit/meta_systems/persona_core/intelligence_enhancement/monitor.py` (Uses core `MetricsCollector` base; deeper integration review pending)
    - [ ] Other files importing from `person_suit.meta_systems.persona_core.core.monitoring`
- **Post-Refactoring**: Delete the `person_suit/meta_systems/persona_core/core/monitoring/` directory and its contents.

### 3.3. Phase 3: Folded Mind Cognitive State Monitoring Metrics (`person_suit/meta_systems/persona_core/folded_mind/monitoring/metrics.py`)
- **Status**: [TO DO]
- **Description**: Analyze the metrics system in `person_suit/meta_systems/persona_core/folded_mind/monitoring/metrics.py` (used by `CognitiveStateMonitor` in `monitor.py`).
- **Action**:
    - Determine if its `MetricValue` and collection logic can be fully refactored to align with `core.infrastructure.monitoring.metrics.py`.
    - If a full refactor is feasible, implement it.
    - If it's too specialized, document the reasons and define a clear strategy for how its collected metrics will be published to the `HybridMessageBus` (e.g., via `SYS_TELEMETRY` or a dedicated channel) for central aggregation.
- **Files to Analyze/Refactor**:
    - [ ] `person_suit/meta_systems/persona_core/folded_mind/monitoring/metrics.py`
    - [ ] `person_suit/meta_systems/persona_core/folded_mind/monitoring/monitor.py`

### 3.4. Phase 4: Review and Integration of Other Specialized Telemetry/Metrics Systems
- **Status**: [TO DO]
- **Description**: Review other identified specialized telemetry and metrics systems. The primary goal is not necessarily to force them all onto the core *registry* pattern if their local collection is justified, but to ensure their outputs can be integrated into a unified telemetry stream via the `HybridMessageBus`.
- **Systems/Components for Review**:
    - [ ] **CAM Tool Framework Telemetry**: `person_suit/meta_systems/persona_core/folded_mind/cam/tool_framework/telemetry/`
        - Action: Define how its `TelemetryManager` will publish aggregated/key metrics to the message bus.
    - [ ] **SOMA `@telemetry` and direct updates**: SOMA components (e.g., `homeostatic_driver.py`, `grounding_interface.py`, `somatic_monitor.py`, `integration.py`, `somatic_pathway.py`).
        - Action: Investigate the `person_suit.shared.utils.telemetry` decorator. Determine how to capture this telemetry and route it to the message bus.
    - [ ] **Specialized SEM Collectors**:
        - `person_suit/meta_systems/persona_core/folded_mind/sem/memory/knowledge/metrics/collection.py`
        - `person_suit/meta_systems/persona_core/folded_mind/sem/logging/metrics.py`
        - Action: Assess their outputs and define a strategy for message bus integration.

## 4. Detailed Progress Tracking

### 4.1. Phase 1: SEM Consolidation
- `associative.py`: [x]
- `intuition.py`: [x]
- `pattern.py`: [x]
- `value.py`: [x]
- `weighting.py`: [x]
- `meaning.py`: [x]
- `analogical_reasoning/structure_mapping.py`: [x]
- `wave_particle/emotional_integration.py`: [x]
**Phase 1 Complete.**

### 4.2. Phase 2: Persona Core Internal Metrics (`person_suit/meta_systems/persona_core/core/monitoring/`)
- `person_suit/meta_systems/persona_core/core/optimization/resource_monitor.py`: [x]
- `person_suit/meta_systems/persona_core/folded_mind/monitoring/system.py`: [x] (Note: `AlertLevel` import still pending resolution)
- `person_suit/meta_systems/persona_core/folded_mind/cam/analytical.py`: [x]
- `person_suit/meta_systems/persona_core/folded_mind/cam/mathematical.py`: [x]
- `person_suit/meta_systems/persona_core/folded_mind/cam/logical.py`: [x]
- `person_suit/meta_systems/persona_core/core/application/handlers/action_handler.py`: [?] (Not Found / To Be Verified)
- `person_suit/meta_systems/persona_core/intelligence_enhancement/monitor.py`: [i] (Uses core `MetricsCollector` base; deeper integration review pending)
- `person_suit/meta_systems/persona_core/folded_mind/arbitration/monitoring.py`: [i] (Self-contained metrics; integration review pending)
- `person_suit/meta_systems/persona_core/folded_mind/monitoring_framework.py`: [i] (Facade; underlying impl is Phase 3 target)
- Any other files importing from `person_suit.meta_systems.persona_core.core.monitoring` (to be identified by search)

### 4.3. Phase 3: Folded Mind Cognitive State Monitoring Metrics
- `person_suit/meta_systems/persona_core/folded_mind/monitoring/metrics.py`: [ ]
- `person_suit/meta_systems/persona_core/folded_mind/monitoring/monitor.py`: [ ]

### 4.4. Phase 4: Review and Integration of Other Specialized Telemetry/Metrics Systems
- CAM Tool Framework Telemetry: [ ] (Review pending)
- SOMA `@telemetry`: [ ] (Review pending)
- SEM Knowledge Metrics: [ ] (Review pending)
- SEM Logging Metrics: [ ] (Review pending)

## 5. Key Decisions & Considerations
- **Unified Metric Types**: Strive to use metric types (Counter, Gauge, Histogram) from `core.infrastructure.monitoring.metrics` where possible.
- **Namespacing**: Adopt the `get_core_metric_namespace_str()` approach for consistent string-based namespaces.
- **Telemetry Bus Integration**: For systems not using the central registry, ensure they can publish to `HybridMessageBus` (e.g., `SYS_TELEMETRY`).
- **Stubbing**: Continue using the `MONITORING_AVAILABLE` and stub classes pattern during refactoring to allow components to function if the core metrics system isn't fully available (e.g., in certain test environments or during transitional phases), but the stubs should mirror the target API.
- **`MetricValue` Object**: The legacy `persona_core.core.monitoring` system used a `MetricValue` wrapper object. The new `core.infrastructure.monitoring.metrics` system expects raw float/int values for `gauge.set()`, `counter.increment()`, `histogram.record()`. This conversion must be handled during refactoring.

This plan is now more comprehensive. 