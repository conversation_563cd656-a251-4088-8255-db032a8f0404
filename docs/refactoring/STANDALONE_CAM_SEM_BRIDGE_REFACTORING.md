# Standalone CAM SEM Bridge Refactoring Plan

Unified Communication Architecture Note: 
**Note:** This refactoring has been updated to leverage our unified message-based decoupled architecture. All import, connection, orchestration, and choreography concerns now follow the approach described in [docs/architecture/message_based_architecture.md](../architecture/message_based_architecture.md).

## Overview

This document outlines the plan for refactoring the large example file `person_suit/meta_systems/persona_core/folded_mind/integration/examples/standalone_cam_sem_bridge_example.py` (originally 3310 lines) into smaller, more manageable modules. The goal is to improve readability, maintainability, and potential reusability of the components within the example structure.

## Current State

The `standalone_cam_sem_bridge_example.py` file contains all the code for a comprehensive CAM-SEM bridge demonstration, including:
- Core type definitions (Enums)
- Metrics tracking (`TranslationMetrics`)
- Configuration (`BridgeConfig`)
- Custom exceptions
- Translator infrastructure (Base class, Registry, Evaluator)
- Specific translator implementations (Logical Proposition, Knowledge Graph, Pattern, Metaphor) with numerous helper methods
- The main `ComputationalExperientialBridge` class
- Example data creation functions
- Example execution functions (`run_*`) and `main`

This monolithic structure makes the code difficult to navigate and understand.

## Target State

The functionality will be broken down into the following structure:

```
person_suit/meta_systems/persona_core/folded_mind/integration/examples/cam_sem_bridge/
├── __init__.py
├── standalone_cam_sem_bridge_example.py  # Main execution script (much smaller)
├── bridge.py                             # ComputationalExperientialBridge class
├── config.py                             # BridgeConfig dataclass
├── definitions.py                        # TranslationDirection, StructureType enums
├── exceptions.py                         # Custom translation exceptions
├── metrics.py                            # TranslationMetrics dataclass
├── translation_framework.py              # Translator base, TranslatorRegistry, ConfidenceEvaluator
├── translators/
│   ├── __init__.py
│   ├── base_translator.py                # (Optional: Move Translator base class here)
│   ├── logical_proposition_translator.py # LogicalPropositionTranslator class + helpers
│   ├── knowledge_graph_translator.py     # KnowledgeGraphTranslator class + helpers
│   ├── pattern_translator.py             # PatternTranslator class + helpers
│   └── metaphor_translator.py            # MetaphorTranslator class + helpers
└── utils/                                # (Optional: For example_data, print functions)
    ├── __init__.py
    ├── example_data.py                   # create_..._example functions
    └── printing.py                       # print_structure, print_metrics functions
```

## Migration Strategy

1.  **Create Directory Structure**: Create the `cam_sem_bridge` directory and the `translators` and `utils` subdirectories.
2.  **Extract Simple Components**:
    *   Move `TranslationDirection`, `StructureType` to `definitions.py`.
    *   Move `TranslationError` and subclasses to `exceptions.py`.
    *   Move `TranslationMetrics` to `metrics.py`.
    *   Move `BridgeConfig` to `config.py`.
3.  **Extract Translator Framework**:
    *   Move `Translator` base class, `TranslatorRegistry`, `ConfidenceEvaluator` to `translation_framework.py`.
4.  **Extract Specific Translators**:
    *   For each translator (`LogicalPropositionTranslator`, `KnowledgeGraphTranslator`, `PatternTranslator`, `MetaphorTranslator`):
        *   Create the corresponding file in the `translators/` directory (e.g., `logical_proposition_translator.py`).
        *   Move the class definition and all its private helper methods (`_translate_*`, `_map_*`, `_determine_*`, etc.) to the new file.
        *   Update imports within the translator files as needed (e.g., for types, exceptions).
5.  **Extract Utilities**:
    *   Move `create_*_example` functions to `utils/example_data.py`.
    *   Move `print_structure`, `print_metrics` to `utils/printing.py`.
6.  **Refactor Bridge Class**:
    *   Move the `ComputationalExperientialBridge` class to `bridge.py`.
    *   Update its implementation to import components from the new modules (`config`, `definitions`, `exceptions`, `metrics`, `translation_framework`, `translators`).
7.  **Refactor Main Example Script**:
    *   Keep `standalone_cam_sem_bridge_example.py` as the main entry point.
    *   Remove all extracted code.
    *   Import necessary components (e.g., `ComputationalExperientialBridge` from `bridge`, `run_*` functions, `main` function).
    *   Update the `run_*` functions and `main` to use the imported components and utilities (`example_data`, `printing`).
8.  **Update `__init__.py` Files**: Add necessary imports to the `__init__.py` files for easier access if required, following project conventions.
9.  **Testing**: Adapt existing tests or create new unit tests for the extracted modules (especially translators and framework components). Ensure the main example script still runs correctly.
10. **Documentation**: Update this document and `REFACTORING_INDEX.md` with progress.

## Status

📝 **Planned** - Initial plan drafted. Implementation to follow.

## Dependencies

- None explicitly blocking, but follows Circular Import Resolution in Phase 1 of the main refactoring sequence.

## Risks

- Breaking changes if imports are not handled correctly.
- Difficulty in separating tightly coupled helper methods within translators.
- Ensuring test coverage for newly created modules.

## Mitigation

- Implement step-by-step, testing frequently.
- Carefully trace dependencies when moving code.
- Add unit tests for extracted logic.

## References

1. [Person Suit Coding Standards](../New/coding_standards.md)
2. [CAW Paradigm Documentation](../future/unified_paradigm/v0.3/README.md)
3. [Refactoring CAW Alignment Plan](../REFACTORING_CAW_ALIGNMENT_PLAN.md)
4. [Refactoring Index](./REFACTORING_INDEX.md)
5. [Meta Systems Dependencies](./META_SYSTEMS_DEPENDENCIES.md)
