# Monitoring Interface Organization

Unified Communication Architecture Note: 
**Note:** In this document, import and interface organization issues are now addressed using our unified message-based decoupled architecture approach. For more details on the overall strategy, please refer to [docs/architecture/message_based_architecture.md](../architecture/message_based_architecture.md).

This document outlines the organization of interfaces for the Person Suit monitoring system. It maps existing interfaces to their new locations in the `person_suit/core/infrastructure/monitoring/interfaces/` directory.

## Interface Organization

The interfaces will be organized into the following files:

### 1. `interfaces/alert.py`

Alert-related interfaces:

- `AlertSeverity` (enum)
- `AlertThresholdData` (dataclass)
- `AlertData` (dataclass)
- `AlertHandlerCallable` (type)
- `AlertManagerInterface` (ABC)

### 2. `interfaces/metric.py`

Metric-related interfaces:

- `MetricType` (enum)
- `MetricUnit` (enum)
- `MetricPointData` (dataclass)
- `MetricCollectorInterface` (ABC)
- `MetricStorageInterface` (ABC)

### 3. `interfaces/service.py`

Service-related interfaces:

- `MonitoringServiceInterface` (ABC)
- `MonitoringConfigInterface` (ABC)

### 4. `interfaces/system.py`

System monitoring interfaces:

- `SystemMetricsInterface` (dataclass)
- `SystemMonitorInterface` (ABC)

### 5. `interfaces/telemetry.py`

Telemetry-related interfaces:

- `TelemetryDataInterface` (dataclass)
- `TelemetryCollectorInterface` (ABC)
- `TelemetryProcessorInterface` (ABC)

### 6. `interfaces/anomaly.py`

Anomaly detection interfaces:

- `AnomalyType` (enum)
- `AnomalyData` (dataclass)
- `AnomalyDetectorInterface` (ABC)

### 7. `interfaces/visualization.py`

Visualization-related interfaces:

- `VisualizationType` (enum)
- `VisualizationData` (dataclass)
- `VisualizationProviderInterface` (ABC)
- `DashboardInterface` (ABC)

### 8. `interfaces/health.py`

Health-related interfaces:

- `HealthStatus` (enum)
- `HealthIndicatorInterface` (ABC)
- `HealthCheckInterface` (ABC)
- `HealthRegistryInterface` (ABC)

## Interface Migration Map

This section maps existing interfaces to their new locations:

| Current Location | Interface | New Location |
|------------------|-----------|--------------|
| `interfaces.py` | `AlertSeverity` | `interfaces/alert.py` |
| `interfaces.py` | `AlertThresholdData` | `interfaces/alert.py` |
| `interfaces.py` | `AlertData` | `interfaces/alert.py` |
| `interfaces.py` | `AlertHandlerCallable` | `interfaces/alert.py` |
| `interfaces.py` | `AlertManagerInterface` | `interfaces/alert.py` |
| `interfaces.py` | `MetricStorageInterface` | `interfaces/metric.py` |
| `interfaces.py` | `MonitoringServiceInterface` | `interfaces/service.py` |
| `interfaces.py` | `AnomalyDetectorInterface` | `interfaces/anomaly.py` |
| `metric_interfaces.py` | `MetricType` | `interfaces/metric.py` |
| `metric_interfaces.py` | `MetricUnit` | `interfaces/metric.py` |
| `metric_interfaces.py` | `MetricCollectorInterface` | `interfaces/metric.py` |
| `system_interfaces.py` | `SystemMetricsInterface` | `interfaces/system.py` |
| `system_interfaces.py` | `SystemMonitorInterface` | `interfaces/system.py` |

## Implementation Plan

1. Create or update each interface file in the `interfaces/` directory
2. Move interfaces from their current locations to the new files
3. Update imports in all files that use the interfaces
4. Ensure backward compatibility by re-exporting interfaces from their old locations

## Backward Compatibility

To ensure backward compatibility, the old interface files (`interfaces.py`, `metric_interfaces.py`, `system_interfaces.py`) will re-export the interfaces from their new locations. This will allow existing code to continue working without changes.

For example, `interfaces.py` will contain:

```python
"""
Person Suit - Monitoring Interfaces (Legacy)

This module re-exports interfaces from their new locations for backward compatibility.
New code should import interfaces directly from the interfaces/ directory.
"""

import warnings

# Re-export interfaces from their new locations
from .interfaces.alert import (
    AlertSeverity,
    AlertThresholdData,
    AlertData,
    AlertHandlerCallable,
    AlertManagerInterface,
)

from .interfaces.metric import (
    MetricStorageInterface,
)

from .interfaces.service import (
    MonitoringServiceInterface,
)

from .interfaces.anomaly import (
    AnomalyDetectorInterface,
)

# Emit deprecation warnings
warnings.warn(
    "Importing from 'person_suit.core.infrastructure.monitoring.interfaces' is deprecated. "
    "Import from 'person_suit.core.infrastructure.monitoring.interfaces.*' instead.",
    DeprecationWarning,
    stacklevel=2
)
```

Similar re-export files will be created for `metric_interfaces.py` and `system_interfaces.py`.
