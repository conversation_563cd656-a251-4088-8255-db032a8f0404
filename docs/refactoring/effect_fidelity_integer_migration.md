# Effect System Fidelity & Priority Integer Migration

## Current Status

### ✅ Priorities - Already Using Integer Buckets
The effect system is correctly using integer priority buckets (0-1,000,000):
- `EffectInterpreter` uses `PRIO_HIGH`, `PRIO_LOW` constants from `fixed_point_scale.py`
- Context priority is checked as integer: `context.priority >= PRIO_HIGH`
- Priority handling is consistent with the architecture

### ❌ Fidelity - Still Using Float Values (0.0-1.0)
The effect system is using float values for fidelity instead of integer buckets:

#### Files Using Float Fidelity:
1. **base.py**:
   - `self.min_fidelity = 0.3` (line 142)
   - `self.min_fidelity = 1.0` (line 169, 303)

2. **database.py**:
   - `self.min_fidelity = 1.0` (lines 130, 181, 229)

3. **event.py**:
   - `self.min_fidelity = 0.2` (line 75)
   - `self.min_fidelity = 1.0` (lines 133, 183)
   - `self.min_fidelity = 0.3` (line 233)

4. **io_effects.py**:
   - `self.min_fidelity = 1.0` (lines 126, 181, 284)

5. **network.py**:
   - `self.min_fidelity = 1.0` (lines 81, 146, 200)

6. **computation.py**:
   - `self.min_fidelity = 1.0` (lines 159, 341)

### ✅ Handler Conversion - Properly Implemented
The handlers are correctly converting between float and integer representations:
- `database.py` handler uses `_fidelity_ratio()` to convert integer buckets to float ratios
- Uses `bucket_or_ratio_to_ratio` from `fixed_point_scale.py`

## Required Changes

### 1. Update Base Effect Class
```python
# person_suit/core/effects/base.py
from person_suit.core.constants.fixed_point_scale import (
    SCALE, FIDELITY_MED, FIDELITY_MIN, FIDELITY_MAX
)

class Effect(ABC):
    def __init__(self, ...):
        # ACF metadata - use integer buckets
        self.can_degrade = True
        self.min_fidelity = int(0.3 * SCALE)  # 300,000
        self.cache_ttl_seconds = 300
```

### 2. Update Specific Effect Classes
Replace all float fidelity values with integer buckets:
- `0.2` → `int(0.2 * SCALE)` or `FIDELITY_LOW` (200,000)
- `0.3` → `int(0.3 * SCALE)` (300,000)
- `1.0` → `SCALE` or `FIDELITY_MAX` (1,000,000)

### 3. Update Effect Creation Methods
Effects that mark themselves as non-degradable should use:
```python
self.can_degrade = False
self.min_fidelity = SCALE  # Instead of 1.0
```

## Migration Strategy

1. **Import Constants**: Add imports for `SCALE`, `FIDELITY_*` constants to all effect files
2. **Replace Float Values**: Systematically replace float fidelity with integer buckets
3. **Update Documentation**: Document that fidelity is now an integer bucket (0-1,000,000)
4. **Add Conversion Helper**: Consider adding a helper method to Effect base class:
   ```python
   def set_min_fidelity_ratio(self, ratio: float) -> None:
       """Set minimum fidelity from a 0-1 float ratio."""
       self.min_fidelity = int(ratio * SCALE)
   ```

## Benefits

1. **Consistency**: Aligns with priority system already using integer buckets
2. **Performance**: Integer comparisons are faster than float comparisons
3. **Precision**: Avoids floating-point precision issues
4. **Architecture Alignment**: Consistent with CAW principles and fixed-point arithmetic approach

## Testing Considerations

1. Verify fidelity comparisons still work correctly
2. Test ACF degradation logic with integer buckets
3. Ensure handlers properly convert to ratios when needed
4. Check serialization/deserialization of integer fidelity values

## Conclusion

The effect system is halfway there - priorities use integer buckets correctly, but fidelity still uses floats. This inconsistency should be resolved by migrating all fidelity values to use integer buckets (0-1,000,000) consistent with the SCALE constant used throughout the system. 