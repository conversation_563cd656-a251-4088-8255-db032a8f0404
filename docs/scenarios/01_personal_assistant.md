---
id: 01
title: Personal Assistant
status: "🔄 IN_PROGRESS"
last_updated: 2025-06-05
tags: [personal, productivity, scheduling, assistant]
test_reference: tests/scenarios/test_01_personal_assistant.py
---

# Scenario 01: Personal Assistant

> **User Profile**: <PERSON>, 35, Marketing Manager  
> **Primary Use Case**: Daily task management, scheduling, and productivity support  
> **Deployment**: Server (full features) or Mobile (reduced features)  
> **Complexity**: Moderate (PC + AN coordination)

## 📋 Overview

Sarah uses <PERSON> <PERSON> as her AI personal assistant throughout the workday. The system helps her manage tasks, schedule meetings, track deadlines, and maintain work-life balance. The assistant learns her patterns and preferences over time, providing increasingly personalized support.

## 🎯 Key Features

- Task and calendar management
- Meeting preparation and summaries
- Email prioritization suggestions
- Deadline tracking and reminders
- Work pattern analysis
- Stress level monitoring
- Proactive suggestions

## 📊 Detailed Message Flows

### Morning Check-in Flow

```mermaid
sequenceDiagram
    participant U as User (Sarah)
    participant A as Discord Adapter
    participant MB as Message Bus
    participant PC as Persona Core
    participant AN as Analyst
    participant MEM as Memory
    
    U->>A: "Good morning! What's on my schedule?"
    A->>MB: channel: pc.folded_mind.soma.sense
    MB->>PC: Process greeting (priority: 0.7)
    PC->>MB: channel: pc.memory.retrieve
    MB->>MEM: Get today's schedule
    MEM-->>PC: Schedule data
    PC->>MB: channel: an.pattern.detect
    MB->>AN: Analyze daily patterns
    AN->>MB: channel: pc.memory.search
    MB->>MEM: Find forgotten items pattern
    MEM-->>AN: Historical data
    AN->>PC: Suggest forgotten items
    PC->>A: Formatted response
    A->>U: "Good morning Sarah! You have 4 meetings today..."
```

### Task Management Flow

**User Input**: "Add a task to finish the marketing report by Friday"

```yaml
Message Flow:
1. Input Processing:
   - Channel: pc.folded_mind.soma.sense
   - Priority: 0.6
   - Wave Ratio: 0.3 (concrete task)

2. Entity Extraction:
   - Channel: an.entity.track
   - Entities: {task: "marketing report", deadline: "Friday"}
   - Priority: 0.7

3. Context Analysis:
   - Channel: an.context.extract
   - Context: {domain: "work", urgency: "medium"}
   
4. Memory Storage:
   - Channel: pc.memory.encode
   - Type: Episodic + Procedural
   - Wave Ratio: 0.5
   - ACF: Can degrade to simple storage if loaded

5. Pattern Analysis:
   - Channel: an.pattern.detect
   - Pattern: Task addition behavior
   - Updates user profile

6. Prediction:
   - Channel: pr.predict.behavior
   - Predicts: Reminder needs, completion likelihood
   
7. Response Generation:
   - Channel: pc.folded_mind.integrate
   - Integrates: Task stored + context + predictions
   - Generates confirmation with insights
```

### Stress Detection & Support Flow

**System Initiated**: Detecting stress patterns in communication

```yaml
Continuous Monitoring:
1. Message Analysis:
   - Channel: an.pattern.linguistic
   - Monitors: Word choice, response time, typos
   - Priority: 0.3 (background)

2. Pattern Detection:
   - Channel: an.pattern.detect
   - Pattern: Stress indicators rising
   - Threshold: 3 indicators in 30 minutes

3. Prediction:
   - Channel: pr.predict.behavior
   - Predicts: Burnout risk, productivity impact

4. Whisper Activation:
   - Channel: pc.influence (via Whisper)
   - Priority: 0.2 (subtle)
   - Effect: Adjust tone to calming

5. Proactive Support:
   - Channel: pc.folded_mind.sem.intuit
   - Wave Ratio: 0.9 (highly intuitive)
   - Generates: "You seem busy today. Would you like me to help prioritize?"

6. Memory Update:
   - Channel: pc.memory.encode
   - Stores: Stress event + intervention + outcome
   - Used for: Future pattern learning
```

### Meeting Preparation Flow

**Trigger**: 15 minutes before scheduled meeting

```yaml
Automated Flow:
1. Schedule Check:
   - Channel: sys.schedule.check
   - Identifies: Upcoming meeting

2. Context Retrieval:
   - Channel: pc.memory.search
   - Query: Meeting participants, topic, history
   - Wave Ratio: 0.6 (fuzzy matching)

3. Relevant Info Gathering:
   - Channel: an.context.extract
   - Extracts: Related emails, documents, action items

4. Preparation:
   - Channel: pc.folded_mind.cam.analyze
   - Wave Ratio: 0.1 (logical analysis)
   - Creates: Meeting brief, talking points

5. Notification:
   - Channel: pc.emotion.process
   - Adjusts: Tone based on meeting importance
   - Priority: 0.8 (time-sensitive)

6. Delivery:
   - "Your meeting with the design team starts in 15 minutes. 
    I've prepared a brief with the 3 open action items..."
```

## 🔧 System Behaviors

### Memory Management
- **Working Memory**: Current day's tasks and active projects
- **Long-term Memory**: Meeting history, task patterns, preferences
- **Consolidation**: Nightly pattern analysis and learning

### Adaptation Strategies
- **High Load**: Reduces pattern analysis depth, focuses on critical tasks
- **Low Battery (Mobile)**: Disables proactive suggestions, command-only mode
- **Network Issues**: Operates on cached data, queues syncs

### Entity Tracking
```yaml
Tracked Entities:
- People: Colleagues, meeting participants
- Projects: Active projects with deadlines
- Tasks: Individual tasks with status
- Patterns: Work habits, stress indicators
- Preferences: Communication style, reminder timing
```

### Learning & Personalization
1. **Communication Style**: Adapts formality based on time of day
2. **Reminder Timing**: Learns optimal notification windows
3. **Task Estimation**: Improves time estimates based on history
4. **Stress Management**: Recognizes personal stress indicators

## 📈 ACF (Adaptive Computational Fidelity) Settings

### Server Deployment
```yaml
Default Fidelity: 0.9
Degradation Triggers:
  - CPU > 80%: Reduce to 0.7
  - Memory > 90%: Reduce to 0.5
  - Queue > 1000: Drop non-critical analysis

Channel Priorities:
  - pc.memory.retrieve: 0.8 (high)
  - an.pattern.detect: 0.5 (medium)
  - pr.predict.behavior: 0.3 (low, can skip)
```

### Mobile Deployment
```yaml
Default Fidelity: 0.6
Degradation Triggers:
  - Battery < 20%: Reduce to 0.3
  - Memory > 80%: Disable predictions
  - Thermal warning: Command-only mode

Feature Restrictions:
  - No continuous monitoring
  - Simplified pattern detection
  - Local-only predictions
```

## 🧪 Test Scenarios

### Integration Tests
1. **Morning Routine**: Verify schedule retrieval + pattern analysis
2. **Task Overload**: Test behavior with 50+ tasks
3. **Stress Response**: Validate stress detection accuracy
4. **Memory Recall**: Test fuzzy search for past events

### Load Tests
1. **Rapid Commands**: 10 commands in 30 seconds
2. **Background Analysis**: Monitor resource usage during quiet periods
3. **Context Switching**: Multiple project contexts in succession

### Edge Cases
1. **Conflicting Tasks**: Double-booked meetings
2. **Ambiguous Deadlines**: "next Friday" vs "this Friday"
3. **Memory Gaps**: Referencing unknown entities
4. **Stress False Positives**: Excitement vs stress

## 📝 Implementation Notes

### Critical Channels
- `pc.memory.*`: Must maintain high availability
- `an.entity.track`: Consistency crucial for context
- `pc.folded_mind.integrate`: Quality affects all responses

### Performance Considerations
- Cache frequently accessed memories
- Batch pattern analysis during low-activity periods
- Pre-compute common responses
- Use incremental learning for patterns

### Security & Privacy
- Encrypt all stored tasks and calendar data
- Audit trail for data access
- User control over learning/forgetting
- Separate work/personal contexts

## 🚀 Success Metrics

### User Experience
- Response time < 200ms for queries
- 95% task reminder accuracy
- Stress detection precision > 80%
- User satisfaction > 4.5/5

### System Performance
- Memory usage < 500MB (mobile)
- CPU usage < 20% idle
- Message queue depth < 100
- Cache hit rate > 70%

### Business Value
- 30% reduction in missed deadlines
- 25% improvement in meeting preparation
- 20% decrease in stress-related productivity loss
- 15% time saved on routine tasks

---

**Next**: See [Therapeutic Companion](02_therapeutic_companion.md) for emotion-focused scenarios 