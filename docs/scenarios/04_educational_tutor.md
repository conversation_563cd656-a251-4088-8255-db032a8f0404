---
id: 04
title: Educational Tutor
status: "🔄 IN_PROGRESS"
last_updated: 2025-06-05
deployment: [server, mobile]
tags: [education, tutoring, adaptive-learning]
test_reference: tests/scenarios/test_04_educational_tutor.py
---

# Scenario 04: Educational Tutor

> **User Profile**: <PERSON>, 16, high-school student preparing for STEM exams.  
> **Primary Need**: A personalised tutor that explains difficult concepts, tracks progress, and adapts teaching style to Emma's learning pace.  
> **Environment**: <PERSON>'s smartphone (evening study sessions) plus a school server for heavier model training and analytics.

---

## 📋 Narrative

Emma struggles with quadratic equations and physics vectors.  Traditional classroom lectures move too quickly, and online videos feel generic.  She installs the Person Suit "Study-Sensei" app on her phone.

Study-<PERSON><PERSON> greets her every evening: *"Ready for tonight's study sprint? Yesterday we tackled factoring; today we'll master completing the square."*  When Emma solves practice problems, the app silently monitors error patterns, hinting only after two incorrect tries.  If <PERSON> seems frustrated (long pauses, repeated mistakes) the tutor switches to visual explanations, drawing animated parabolas and real-world analogies (basketball arcs, satellite dishes).

Progress stars and mini-games keep motivation high.  Once a week, Study-<PERSON><PERSON> emails Emma's teacher a concise analytics report — strengths, weaknesses, predicted exam score — helping plan classroom interventions without violating privacy.

---

## 🎯 Behaviour & Needs

1. **Adaptive Explanation Depth** – adjust complexity based on mastery signals.  
2. **Multi-Modal Instruction** – text, animation, voice, interactive widgets.  
3. **Motivation Mechanics** – gamified streaks, badges, study-break reminders.  
4. **Teacher Insights** – share de-identified metrics; respect student privacy.  
5. **Offline Mode** – basic quizzes when internet is unavailable.

---

## 🔧 Technical Notes (High-Level)

- Uses `pr.predict.learning_curve` channel to forecast mastery timeline.  
- Mobile deployment caches lightweight models; heavy-weight retraining on school server overnight.  
- ACF lowers animation frame-rate on low battery but preserves hint generation accuracy.  
- Context tags (`UnifiedContext`) include subject, current topic, frustration level. 