---
id: 15
title: Autonomous Delivery Robot
status: "🔄 IN_PROGRESS"
last_updated: 2025-06-05
deployment: [edge]
tags: [robotics, delivery, logistics]
---

# Scenario 15: Autonomous Delivery Robot

> **Device Profile**: Sidewalk robot (Edge Nvidia Jetson-class, 8 GB RAM) delivering parcels within a 5-km radius.  
> **Mission**: Safely navigate pedestrian paths, avoid obstacles, and delight customers with friendly interaction.  
> **Connectivity**: 5G link to regional hub; intermittent in urban canyons.

---

## 📋 Narrative

The robot — nicknamed "Parcel<PERSON><PERSON><PERSON>" — rolls out of a micro-fulfilment centre at dawn, cooler compartment full of breakfast orders.  It greets passers-by with LED eye-expressions, politely yielding to strollers and dogs.

Halfway to Customer No. 3, a construction zone blocks the usual route.  ParcelPenguin's LIDAR picks up orange cones; it requests alternate waypoints from the hub.  The hub detects multiple robots in vicinity and dynamically reallocates sidewalks to prevent clustering.

Upon arrival, the robot texts the customer, *"Your smoothie has arrived. Please tap unlock in the app."*  The compartment opens; temperature log records 4 °C.  A toddler waves — the robot wiggles an antenna and says, *"Have a sunny day!"*  Customer satisfaction scores soar.

---

## 🎯 Behaviour & Needs

1. **Real-Time Obstacle Avoidance** – sub-50 ms LIDAR loop.  
2. **Adaptive Routing** – switch paths when blocked, considering battery.  
3. **User Interaction** – multimodal (text, LED, voice) with personality variations.  
4. **Fleet Coordination** – avoid sidewalk congestion via message bus choreography.  
5. **Cold-Chain Compliance** – temperature logging, threshold alerts.

---

## 🔧 Technical Notes (High-Level)

- Local PC handles perception; cloud PR predicts foot-traffic heat-maps every 10 min.  
- Channels: `pc.robotics.obstacle`, `an.context.pathfinding`, `pr.predict.pedestrian_density`.  
- ACF: disables voice jokes if battery < 20 %.  
- CAW capabilities: customer's app token required to unlock compartment. 