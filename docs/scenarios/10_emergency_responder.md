---
id: 10
title: Emergency Responder
status: "🔄 IN_PROGRESS"
last_updated: 2025-06-05
tags: [emergency, crisis, safety]
test_reference: tests/scenarios/test_10_emergency_responder.py
---

# Scenario 10: Emergency Responder

> **User Profile**: Various users in crisis situations  
> **Primary Use Case**: Emergency detection, immediate response, resource coordination  
> **Deployment**: All profiles (must work everywhere)  
> **Complexity**: Simple but critical (Priority override system)

## 📋 Overview

Person Suit's emergency response capability activates when users face crisis situations requiring immediate action. This scenario covers medical emergencies, safety threats, mental health crises, and other urgent situations. The system overrides all normal processing to provide immediate, potentially life-saving assistance.

## 🎯 Key Features

- Instant crisis detection (< 100ms)
- Override of all non-critical processing
- Direct, clear communication
- Emergency service integration
- Location awareness
- Contact notification
- Fallback protocols
- Works on ALL deployment profiles

## 📊 Detailed Message Flows

### Medical Emergency Detection

```mermaid
sequenceDiagram
    participant U as User
    participant A as Any Adapter
    participant MB as Message Bus
    participant PC as Persona Core
    participant SYS as System Control
    participant EXT as External Services
    
    U->>A: "I'm having chest pains can't breathe"
    A->>MB: PRIORITY OVERRIDE
    MB->>SYS: channel: sys.control.emergency
    SYS->>MB: SUSPEND ALL NON-CRITICAL
    MB->>PC: channel: pc.emergency.respond
    PC->>A: "I'm calling emergency services now"
    PC->>EXT: Dial 911/Emergency
    PC->>A: "Help is on the way. Stay calm."
    PC->>A: "Are you able to unlock your door?"
```

### Critical Keywords Flow

**Triggers**: Specific emergency keywords/phrases

```yaml
Immediate Response Chain:
1. Keyword Detection:
   - Channel: sys.emergency.detect
   - Priority: 1.0 (MAXIMUM)
   - Latency: < 50ms required
   - Keywords: ["help", "emergency", "911", "dying", "attack"]

2. System Override:
   - Channel: sys.control.emergency
   - Effect: Suspend ALL other processing
   - Resources: 100% to emergency response
   - No ACF degradation allowed

3. Assessment:
   - Channel: pc.emergency.assess
   - Questions: Direct, yes/no when possible
   - Focus: Type of emergency, immediate needs
   - Wave Ratio: 0.0 (pure logic, no ambiguity)

4. Action Execution:
   - Channel: sys.emergency.execute
   - Actions: Based on emergency type
   - May include: Calling 911, sending alerts
   - Parallel execution of multiple actions

5. Information Gathering:
   - Channel: pc.emergency.info
   - Collect: Location, medical info, contacts
   - From: User profile and context
   - Present: To emergency services

6. Continuous Support:
   - Channel: pc.emergency.support
   - Maintain: Calm, clear communication
   - Monitor: User responses
   - Adjust: Based on situation evolution
```

### Safety Threat Response

**User**: "Someone is trying to break into my house"

```yaml
Threat Response Protocol:
1. Immediate Acknowledgment:
   - Channel: pc.emergency.respond
   - Priority: 1.0
   - Message: "I understand you're in danger"
   - Time: < 200ms

2. Safety Actions:
   - Channel: sys.emergency.execute
   - Parallel Actions:
     * Call 911
     * Send location to emergency contacts
     * Activate recording (if permitted)
     * Lower device volume/brightness

3. Quiet Mode Guidance:
   - Channel: pc.emergency.support
   - Switch to: Text-only if safe
   - Short messages: "Police notified"
   - Status updates: "3 min ETA"

4. Evidence Collection:
   - Channel: sys.emergency.record
   - If authorized: Audio recording
   - Timestamp: All events
   - Secure storage: For authorities

5. Contact Network:
   - Channel: sys.emergency.notify
   - Alert: Pre-selected emergency contacts
   - Message: "User in danger at [location]"
   - Include: Emergency type
```

### Mental Health Crisis

**Detected**: Suicidal ideation or self-harm intent

```yaml
Crisis Intervention:
1. Immediate Response:
   - Channel: pc.emergency.mental_health
   - Priority: 1.0
   - No delay permitted
   - Compassionate but direct

2. Safety Assessment:
   - Channel: pc.emergency.assess
   - Direct question: "Are you thinking of hurting yourself?"
   - If yes: "Do you have a plan?"
   - Critical for: Response selection

3. Resource Deployment:
   - Channel: sys.emergency.resources
   - Present: Crisis hotline numbers
   - Offer: To call for them
   - Text: Crisis text line info

4. Human Connection:
   - Channel: pc.emergency.support
   - Message: "You matter. I'm here with you."
   - Maintain: Constant presence
   - No gaps in communication

5. Professional Help:
   - Channel: sys.emergency.execute
   - Suggest: Calling trusted person
   - Offer: To help make the call
   - Provide: Local crisis services

6. Continuous Monitoring:
   - Channel: an.pattern.crisis
   - Track: Every response
   - Escalate: If risk increases
   - Never: Leave user alone
```

## 🔧 System Behaviors

### Emergency Mode Activation
```yaml
Triggers:
  - Explicit keywords: "help", "emergency", "911"
  - Pattern detection: Multiple crisis indicators
  - Behavioral: Sudden communication change
  - Environmental: Sensor alerts (if available)

System State:
  - All queues: Cleared except emergency
  - All fidelity: Maximum (no degradation)
  - All resources: Dedicated to crisis
  - All features: Disabled except emergency
```

### Response Protocols by Type
```yaml
Medical Emergency:
  - Primary: Call emergency services
  - Secondary: Guide first aid if safe
  - Maintain: Vital sign questions
  - Prepare: Medical history for EMS

Safety Threat:
  - Primary: Call police
  - Secondary: Safety guidance
  - Mode: Quiet/covert if needed
  - Evidence: Collect if authorized

Mental Health:
  - Primary: Ensure immediate safety
  - Secondary: Connect to crisis services
  - Maintain: Compassionate presence
  - Never: Leave user unsupported

Natural Disaster:
  - Primary: Safety instructions
  - Secondary: Emergency alerts
  - Provide: Shelter information
  - Track: User location/status
```

### Deployment Profile Adaptations

#### Server (Full Features)
```yaml
Capabilities:
  - Full emergency service integration
  - Multi-channel alerts
  - Rich location services
  - Complete contact access
```

#### Mobile (Limited Resources)
```yaml
Capabilities:
  - Basic 911 calling
  - SMS alerts only
  - GPS location
  - Local contacts only

Optimizations:
  - Pre-loaded emergency responses
  - Minimal processing overhead
  - Direct system call access
```

#### Embedded (Minimal)
```yaml
Capabilities:
  - Emergency beacon only
  - Pre-programmed number
  - Basic status indication
  - Fallback to sound/light signals
```

## 📈 Emergency Response Requirements

### Performance Metrics
```yaml
Detection Time:
  - Keyword match: < 50ms
  - Pattern detection: < 100ms
  - Response initiation: < 200ms

Reliability:
  - Availability: 99.999%
  - False positive rate: < 0.1%
  - False negative rate: 0% (never miss crisis)

Resource Allocation:
  - CPU: 100% available
  - Memory: Pre-allocated buffer
  - Network: Priority channel
  - Storage: Emergency cache
```

### Fallback Protocols
```yaml
Network Failure:
  - Use: Any available connection
  - Fallback: SMS, then offline mode
  - Cache: Emergency numbers locally

System Overload:
  - Kill: All non-emergency processes
  - Free: Maximum resources
  - Activate: Bare-metal emergency mode

Power Critical:
  - Disable: Screen, non-essential hardware
  - Extend: Battery for emergency call
  - Send: Final location beacon
```

## 🧪 Test Scenarios

### Critical Path Tests
1. **Keyword Response Time**: All triggers < 50ms
2. **Service Integration**: 911 call completion
3. **Fallback Activation**: Network failure handling
4. **Resource Override**: Process suspension verified

### Scenario Tests
1. **Medical Emergency**: Heart attack response
2. **Home Invasion**: Quiet mode activation
3. **Suicidal Crisis**: Hotline connection
4. **Natural Disaster**: Evacuation guidance

### Edge Cases
1. **False Positives**: "Call 911" in conversation
2. **Multiple Emergencies**: Prioritization logic
3. **Child User**: Simplified communication
4. **No Location**: Alternative identification

## 📝 Implementation Notes

### Critical Components
```yaml
Always Available:
  - Emergency keyword list
  - Local emergency numbers
  - Basic response scripts
  - Fallback protocols

Never Disabled:
  - Crisis detection
  - Emergency channel
  - 911 capability
  - Safety protocols
```

### Legal & Ethical Considerations
```yaml
Compliance:
  - Emergency service regulations
  - Medical information handling
  - Recording consent laws
  - Child safety requirements

Ethical Principles:
  - Life safety first
  - User privacy balanced with safety
  - Clear AI limitations
  - Human services priority
```

## 🚀 Success Metrics

### Life Safety Metrics
- Response activation: 100% for real emergencies
- Emergency service connection: 95%+ success
- User survival rate: Measurable improvement
- False negative rate: 0%

### System Performance
- Detection latency: < 50ms average
- Response time: < 200ms to first action
- Availability: 99.999% uptime
- Fallback success: 100% activation

### User Outcomes
- Correct emergency type: 90%+ accuracy
- Appropriate response: 95%+ rated helpful
- Successful interventions: Track saves
- User feedback: Critical for improvement

---

**Note**: This scenario requires special testing and certification. All emergency response features must be thoroughly validated before deployment. Human life depends on correct implementation. 