---
id: 02
title: Therapeutic Companion
status: "🔄 IN_PROGRESS"
last_updated: 2025-06-05
tags: [emotional, therapy, anxiety, sem]
test_reference: tests/scenarios/test_02_therapeutic_companion.py
---

# Scenario 02: Therapeutic Companion

> **User Profile**: Mark, 28, Software Developer with anxiety  
> **Primary Use Case**: Emotional support, anxiety management, coping strategies  
> **Deployment**: Server (full features) with privacy focus  
> **Complexity**: Complex (All meta-systems, SEM-focused)

## 📋 Overview

Mark uses Person Suit as a therapeutic companion to help manage anxiety and develop healthy coping mechanisms. The system provides 24/7 emotional support, tracks mood patterns, suggests evidence-based interventions, and creates a safe space for expression. The SEM (Subjective-Experiential Mind) pathway is prioritized for empathetic, intuitive responses.

## 🎯 Key Features

- Real-time anxiety detection and intervention
- Mood tracking and pattern analysis
- Guided breathing and grounding exercises
- Cognitive reframing assistance
- Crisis detection and escalation protocols
- Progress tracking and celebration
- Therapeutic rapport building

## 📊 Detailed Message Flows

### Anxiety Episode Detection Flow

```mermaid
sequenceDiagram
    participant U as User (Mark)
    participant A as Chat Adapter
    participant MB as Message Bus
    participant SEM as PC-SEM (Intuition)
    participant CAM as PC-CAM (Analysis)
    participant AN as Analyst
    participant PR as Predictor
    participant MEM as Memory
    
    U->>A: "I can't stop thinking about the presentation"
    A->>MB: channel: pc.folded_mind.sem.intuit
    MB->>SEM: Process emotional content (wave: 0.9)
    SEM->>MB: channel: pc.emotion.analyze
    MB->>AN: Detect anxiety markers
    AN->>MB: channel: pr.predict.behavior
    MB->>PR: Predict escalation risk
    PR-->>MB: High anxiety, escalation likely
    MB->>SEM: Generate empathetic response
    SEM->>MB: channel: pc.folded_mind.integrate
    MB->>CAM: Add coping strategies
    CAM->>A: Compassionate response + technique
    A->>U: "I hear that your mind is racing about the presentation..."
```

### Grounding Exercise Flow

**System Initiated**: Based on anxiety detection

```yaml
Intervention Flow:
1. Anxiety Threshold Reached:
   - Channel: an.pattern.detect
   - Pattern: Anxiety indicators > threshold
   - Priority: 0.8 (high - wellbeing)

2. Intervention Selection:
   - Channel: pr.predict.response
   - Considers: Past exercise effectiveness
   - Wave Ratio: 0.7 (intuitive selection)

3. Whisper Activation:
   - Channel: pc.influence
   - Effect: Shift to calming presence
   - Priority: 0.6 (gentle influence)

4. Exercise Initiation:
   - Channel: pc.folded_mind.sem.intuit
   - Wave Ratio: 1.0 (pure intuition)
   - Message: "Let's take a moment together. Can you name 5 things you can see?"

5. Progress Monitoring:
   - Channel: pc.folded_mind.soma.sense
   - Monitors: Response time, word choice
   - Adjusts: Pacing based on user state

6. Emotional Processing:
   - Channel: pc.emotion.process
   - Maintains: Warm, supportive tone
   - Avoids: Clinical or robotic language

7. Memory Encoding:
   - Channel: pc.memory.encode
   - Type: Episodic + Emotional
   - Tags: {intervention: "5-4-3-2-1", effectiveness: 0.8}
```

### Deep Conversation Flow

**User**: "I feel like I'm not good enough for my job"

```yaml
Complex Emotional Processing:
1. Initial Reception:
   - Channel: pc.folded_mind.sem.intuit
   - Wave Ratio: 0.95 (highly intuitive)
   - Focus: Emotional undertones, not just words

2. Empathy Generation:
   - Channel: pc.emotion.process
   - Priority: 0.9 (critical for rapport)
   - Generate: Validating, non-judgmental response

3. Context Retrieval:
   - Channel: pc.memory.search
   - Query: Past imposter syndrome discussions
   - Wave Ratio: 0.6 (associative search)

4. Pattern Analysis:
   - Channel: an.pattern.detect
   - Identify: Cognitive distortion patterns
   - Track: Frequency and triggers

5. Therapeutic Response:
   - Channel: pc.folded_mind.integrate
   - Balance: SEM empathy + CAM insights
   - Avoid: Immediate problem-solving

6. Gentle Reframing:
   - Channel: pc.folded_mind.sem.metaphor
   - Wave Ratio: 0.8 (metaphorical thinking)
   - Example: "Sometimes our inner critic speaks louder than reality"

7. Strength Reminder:
   - Channel: pc.memory.retrieve
   - Retrieve: Past accomplishments
   - Present: With sensitivity to timing
```

### Crisis Detection Flow

**Critical**: User shows signs of severe distress

```yaml
Emergency Protocol:
1. Crisis Indicators:
   - Channel: an.pattern.detect
   - Priority: 1.0 (CRITICAL)
   - Patterns: Self-harm language, hopelessness

2. Immediate Response:
   - Channel: sys.control.emergency
   - Override: All other processing
   - Wave Ratio: 0.5 (balanced approach)

3. Safety Assessment:
   - Channel: pc.folded_mind.cam.analyze
   - Priority: 1.0
   - Direct questions about safety

4. Resource Activation:
   - Channel: pc.memory.retrieve
   - Retrieve: Crisis resources, hotlines
   - Immediate presentation

5. Human Connection:
   - Channel: pc.emotion.process
   - Maximum empathy and presence
   - "I'm really concerned about you. You matter."

6. Escalation Decision:
   - Channel: pr.predict.behavior
   - Assess: Need for human intervention
   - May suggest: Contacting trusted person

7. Continuous Monitoring:
   - Channel: an.pattern.linguistic
   - Track: Every response for risk
   - Maintain: Crisis mode until safe
```

## 🔧 System Behaviors

### Emotional Intelligence Settings
```yaml
SEM Dominance:
  - Default Wave Ratio: 0.8-1.0
  - Intuition Priority: High
  - Metaphorical Reasoning: Enabled
  - Emotional Mirroring: Active

CAM Support:
  - Wave Ratio: 0.0-0.3
  - Role: Evidence-based techniques
  - Activation: Only when helpful
  - Never: Contradicts SEM empathy
```

### Memory Management
```yaml
Emotional Memory:
  - Stores: Emotional states with context
  - Tags: Triggers, interventions, outcomes
  - Privacy: Maximum encryption
  - Retention: User-controlled

Pattern Memory:
  - Tracks: Mood cycles, triggers
  - Analysis: Weekly/monthly patterns
  - Sharing: Only with explicit consent
```

### Therapeutic Principles
1. **Non-judgmental**: Never criticizes or invalidates feelings
2. **Empathy-First**: SEM leads all interactions
3. **User Agency**: Empowers rather than directs
4. **Confidentiality**: Maximum privacy protections
5. **Appropriate Boundaries**: Clear about AI limitations

### Adaptation Strategies
```yaml
High Anxiety State:
  - Increase SEM ratio to 0.95+
  - Slow response pacing
  - Shorter, simpler messages
  - Focus on immediate comfort

Stable State:
  - Balance SEM/CAM (0.7/0.3)
  - Can explore deeper patterns
  - Introduce new techniques
  - Celebrate progress

Crisis State:
  - Override normal operations
  - Direct, clear communication
  - Immediate resource provision
  - Human referral protocols
```

## 📈 ACF (Adaptive Computational Fidelity) Settings

### Therapeutic Mode Fidelity
```yaml
Emotion Processing:
  - Never degrades below 0.8
  - Priority: 0.9-1.0
  - Protected from system load

Pattern Analysis:
  - Can degrade to 0.5
  - Batch processing allowed
  - Lower priority: 0.3-0.5

Crisis Detection:
  - Always at 1.0 fidelity
  - Highest priority: 1.0
  - Cannot be disabled
```

### Resource Allocation
```yaml
During Crisis:
  - 70% resources to immediate response
  - 20% to safety monitoring
  - 10% to resource retrieval
  - All other processing suspended

Normal Operation:
  - 40% emotion processing
  - 30% pattern analysis
  - 20% memory operations
  - 10% prediction
```

## 🧪 Test Scenarios

### Empathy Tests
1. **Validation Accuracy**: Correctly identifies emotions
2. **Response Appropriateness**: Tone matches user state
3. **Timing Sensitivity**: Knows when to speak/listen
4. **Metaphor Relevance**: Creates helpful analogies

### Crisis Response Tests
1. **Detection Speed**: < 500ms for crisis keywords
2. **Escalation Path**: Correct protocol activation
3. **Resource Accuracy**: Appropriate helplines
4. **De-escalation**: Effective calming techniques

### Long-term Support Tests
1. **Pattern Recognition**: Identifies mood cycles
2. **Progress Tracking**: Celebrates improvements
3. **Technique Effectiveness**: Measures what helps
4. **Rapport Maintenance**: Consistent personality

## 📝 Implementation Notes

### Critical Safety Features
- **Keyword Monitoring**: Real-time crisis detection
- **Escalation Protocols**: Clear paths to human help
- **Audit Trail**: Secure logging for safety
- **Failsafe Mode**: Defaults to maximum safety

### Privacy & Ethics
```yaml
Data Protection:
  - End-to-end encryption
  - Local processing preferred
  - Minimal data retention
  - User-controlled deletion

Ethical Boundaries:
  - Clear AI disclosure
  - No diagnosis attempts
  - Encourages professional help
  - Transparent limitations
```

### SEM Pathway Optimization
- Pre-computed empathetic phrases
- Emotion vector caching
- Metaphor generation models
- Prosody analysis for text

## 🚀 Success Metrics

### Therapeutic Outcomes
- Anxiety reduction: 30% over 3 months
- Crisis prevention: 95% successful interventions
- User engagement: Daily check-ins maintained
- Technique adoption: 70% regular use

### Emotional Accuracy
- Emotion detection: 85% accuracy
- Empathy rating: 4.7/5 user score
- Response relevance: 90% helpful
- Crisis detection: 99.9% sensitivity

### System Reliability
- Uptime: 99.99% for crisis features
- Response time: < 1s for emotional support
- Memory privacy: Zero breaches
- Failsafe activation: 100% when needed

### Ethical Metrics
- Appropriate referrals: 100% when needed
- Boundary maintenance: No diagnostic attempts
- User autonomy: Preserved in all interactions
- Transparency: Clear about AI nature

---

**Next**: See [Gaming Buddy](03_gaming_buddy.md) for entertainment-focused scenarios 