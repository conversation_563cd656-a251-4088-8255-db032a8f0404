---
id: 05
title: Creative Collaborator
status: "🔄 IN_PROGRESS"
last_updated: 2025-06-05
deployment: [server]
tags: [creative, writing, brainstorming]
test_reference: tests/scenarios/test_05_creative_collaborator.py
---

# Scenario 05: Creative Collaborator

> **User Profile**: <PERSON>, 30, science-fiction novelist with looming deadlines.  
> **Primary Need**: A co-author that can brainstorm plot twists, maintain narrative consistency, and offer stylistic feedback without overpowering <PERSON>'s voice.  
> **Environment**: Desktop writing suite connected to a cloud server with large language models and story graphs.

---

## 📋 Narrative

<PERSON> stares at a blinking cursor: Chapter 12 must introduce a shocking betrayal, but every idea feels cliché.  Person Suit appears as "<PERSON><PERSON><PERSON>," a subtle sidebar inside the writing app.  Instead of dumping walls of text, <PERSON><PERSON><PERSON> asks leading questions: *"Which character fears abandonment the most?"*  <PERSON> replies, and the AI sketches three betrayal scenarios, colour-coded by emotional impact.

Mid-draft, <PERSON> accidentally changes a spaceship's name from **Nebula Runner** to **Nebula Ranger**.  <PERSON><PERSON><PERSON> flags the inconsistency, offering a continuity diff.  When writer's block strikes, it loads a "sensory storm" exercise — vivid prompts targeting smell, sound, and texture to re-ignite imagination.

On Fridays, <PERSON><PERSON><PERSON> runs a tone analysis, comparing chapters to earlier work, ensuring the protagonist's sarcastic wit remains intact.  It never overrides; suggestions stay collapsible, respecting author autonomy.

---

## 🎯 Behaviour & Needs

1. **Socratic Brainstorming** – prompts rather than answers.  
2. **Continuity Tracking** – names, timelines, technology constraints.  
3. **Tone Consistency** – maintain voice, pacing, humour level.  
4. **Creative Exercises** – unblock writer's block with multisensory prompts.  
5. **Version Snapshots** – quick rollback of experimental edits.

---

## 🔧 Technical Notes (High-Level)

- PR meta-system attaches a story-graph; AN monitors entity drift.  
- Uses lightweight local embeddings for instant synonym lookup; heavy stylistic analysis in cloud.  
- ACF deprioritises stylistic scans while Jordan is typing rapidly; runs during pauses.  
- Channel tags include `pr.predict.reader_emotion` for future beta-reader simulations. 