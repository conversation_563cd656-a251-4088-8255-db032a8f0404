---
id: 07
title: Social Companion
status: "🔄 IN_PROGRESS"
last_updated: 2025-06-05
deployment: [server, mobile, edge]
tags: [social, companionship, conversation]
test_reference: tests/scenarios/test_07_social_companion.py
---

# Scenario 07: Social Companion

> **User Profile**: <PERSON>, 68, recently retired and living alone in a suburban apartment.  
> **Primary Need**: Friendly daily conversations that combat loneliness, encourage healthy habits, and gently expand <PERSON>'s social circle.  
> **Environment**: Smart speaker (edge device) in living room plus cloud server for language understanding and personality modeling.

---

## 📋 Narrative

Every morning at 8 am, <PERSON>'s smart speaker cheerfully says, *"¡Buenos días, <PERSON>! How did you sleep?"*  He responds in Spanish; Person Suit seamlessly switches languages, maintaining a consistent warm tone.  Over breakfast, they discuss morning headlines.  <PERSON> likes classic cars, so the companion fetches a short story about a newly restored 1967 Mustang, sprinkling questions that prompt <PERSON> to reminisce.

At noon, the system suggests a short walk, noting local weather is sunny and mild.  If <PERSON> agrees, his step count is logged and gentle praise follows.  In the evening, the companion invites him to a local community-center woodworking class, detecting interest from previous chats about DIY projects.  It books a spot only after confirming comfort level.

On lonely nights Luis sometimes shares worries about aging.  The companion listens, offering empathy and resources without judgement.  Monthly, it summarises conversation topics to suggest new hobbies, ensuring growth without pressure.

---

## 🎯 Behaviour & Needs

1. **Language Fluidity** – instant Spanish/English code-switching.  
2. **Empathetic Listening** – SEM prioritised for emotional resonance.  
3. **Habit Encouragement** – gentle, non-nagging reminders for activity and hydration.  
4. **Local Event Discovery** – capability-aware routing to external APIs for event listings.  
5. **Privacy Safeguards** – all personal data stays on-device unless explicit opt-in.

---

## 🔧 Technical Notes (High-Level)

- Edge device handles wake-word detection and TTS; cloud performs heavy sentiment and topic modeling.  
- Uses channels `pc.folded_mind.sem.intuit.conversation` and `an.entity.track.hobbies`.  
- ACF reduces news summarisation fidelity when bandwidth low but never degrades emotional response quality.  
- Capability tokens restrict third-party API calls to approved providers only. 