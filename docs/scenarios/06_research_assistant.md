---
id: 06
title: Research Assistant
status: "🔄 IN_PROGRESS"
last_updated: 2025-06-05
deployment: [server_farm]
tags: [research, academic, literature]
test_reference: tests/scenarios/test_06_research_assistant.py
---

# <PERSON><PERSON>rio 06: Research Assistant

> **User Profile**: Dr<PERSON> <PERSON>, 45, climate scientist leading a multi-institution study on microplastics in the Arctic.  
> **Primary Need**: A research aide that can scan thousands of new papers weekly, extract relevant findings, detect contradictory results, and draft literature-review summaries.  
> **Environment**: University HPC cluster (server-farm profile) with petabytes of open-access data.

---

## 📋 Narrative

Every Monday morning, Dr<PERSON> <PERSON> opens <PERSON>'s "Arctic Insight" dashboard.  Overnight, the system ingested 2 406 new publications, 17 conference preprints, and 300 GB of satellite imagery.  The dashboard highlights five papers with statistically significant but conflicting ppm measurements compared to last quarter's baseline.  

Arctic Insight auto-links each claim to its methodology section, funding source, and known equipment calibration biases.  <PERSON><PERSON> clicks on one controversial finding; the system generates a side-by-side Sankey diagram showing probable causal assumptions, colour-coded by confidence.

During grant-proposal sprints, <PERSON><PERSON> drafts an abstract.  Person Suit suggests citations in APA style, flags a missing acknowledgment section, and estimates novelty percentile among funded proposals.  It schedules a reminder to notify co-authors for ORCID approvals.

---

## 🎯 Behaviour & Needs

1. **Mass Literature Filtering** – semantic clustering, citation graph centrality.  
2. **Contradiction Detection** – highlight divergent findings with p-values.  
3. **Citation Management** – automatic formatting, deduplication, DOI lookup.  
4. **Data-Method Linkage** – map results to instrumentation metadata.  
5. **Grant Support** – novelty estimation, budget consistency checks.

---

## 🔧 Technical Notes (High-Level)

- Runs heavy vector-database similarity search (`an.pattern.semantic.lit_scan`).  
- Predictor estimates future citation impact (`pr.predict.citation_score`).  
- Adaptive Fidelity: downgrades deep satellite-image analysis during cluster peak hours.  
- All raw PDFs deleted after feature extraction to respect publisher rights. 