# PersonSuit Testing Strategy

This document outlines the testing methodologies, tools, and conventions for the PersonSuit project, ensuring code is reliable, robust, and architecturally compliant.

## Guiding Principles

1.  **Production-First Testing**: Tests must validate production-readiness. Test coverage is a means, not an end; the goal is to prove that code works in a real, integrated system.
2.  **The Test Pyramid**: We adhere to the testing pyramid, focusing on a large base of fast unit tests, a smaller layer of integration tests, and a minimal set of end-to-end tests.
3.  **Architectural Compliance**: Tests are a primary tool for enforcing architectural principles (e.g., no direct I/O in services).
4.  **Clarity and Readability**: Tests should be easy to understand and serve as living documentation for the components they cover.

## The Testing Pyramid

```mermaid
graph TD
    subgraph "Few, Slow, High-Fidelity"
        E2E(End-to-End Tests)
    end
    subgraph "More, Slower, Medium-Fidelity"
        Integration(Integration Tests)
    end
    subgraph "Many, Fast, Low-Fidelity"
        Unit(Unit Tests)
    end
    E2E --> Integration
    Integration --> Unit
```

### 1. Unit Tests (`tests/unit/`)

-   **Purpose**: To test individual functions or classes in isolation. They are fast, simple, and have no external dependencies (no network, no database, no filesystem).
-   **Location**: `tests/unit/` (or `tests/services`, `tests/core` for pure, isolated tests).
-   **Key Characteristics**:
    -   Focus on a single unit of logic.
    -   Use mocks and stubs for all external dependencies (`unittest.mock`).
    -   Execute in milliseconds.
    -   Must not require a running event loop or message bus.
-   **Example**: `tests/services/test_memory_encoder_pure.py` validates the pure business logic of the `MemoryEncoderService` without any I/O.

### 2. Integration Tests (`tests/integration/`)

-   **Purpose**: To verify that multiple components work together correctly. These tests are allowed to have external dependencies.
-   **Location**: `tests/integration/`
-   **Sub-Types**:
    -   **In-Memory Integration Tests**: Test the interaction of components using in-memory fakes for external services (e.g., in-memory event store, message bus). These are fast enough to run on every commit.
    -   **Full Integration Tests**: Use real infrastructure via `testcontainers` (e.g., a real Redpanda instance in a Docker container). These are slower and may only run on PRs or nightly builds.
-   **Example**: `tests/integration/test_cee_memory_encode.py` validates the full Command->Effect->Event flow, with one class for in-memory testing (`TestCEEMemoryEncodeFlowSimple`) and another for full testing with a real Redpanda container (`TestCEEMemoryEncodeFlow`).

### 3. End-to-End (E2E) Tests

-   **Purpose**: To simulate a real user workflow from start to finish. These are the slowest and most brittle tests.
-   **Implementation**: Instead of custom scripts, we use our `Makefile` targets to chain together production-like actions and verify the outcome. The `make verify` command serves as our primary E2E test.

## Tools and Conventions

-   **Test Runner**: `pytest` is our standard test runner.
-   **Mocking**: `unittest.mock` is used for mocking dependencies in unit tests.
-   **Integration Dependencies**: `testcontainers-python` is used to manage Dockerized dependencies like Redpanda for integration tests.
-   **Linting**: `Ruff` is used for linting, including our custom `ps-no-direct-io` plugin to enforce architectural rules.
-   **File Naming**: Test files must be prefixed with `test_` (e.g., `test_my_component.py`).
-   **Test Markers**: We use `pytest` markers (`@pytest.mark.integration`, `@pytest.mark.unit`) to categorize tests.

## How to Run Tests (via Makefile)

The `Makefile` provides standardized targets for running tests:

-   `make test`: Runs all fast tests (unit tests and in-memory integration tests). This should always be run before committing code.
-   `make test_integration`: Runs the full integration test suite, which requires Docker to be running. This is slower and should be run before merging a PR.
-   `make verify`: A comprehensive check that runs all fast tests, checks DI, and verifies custom plugins. This confirms a sprint or feature is complete.
-   `make coverage_sprint1`: Generates a code coverage report for a specific component.

## Adding a New Test

1.  **Determine the Test Type**: Is it a unit, integration, or E2E test?
2.  **Create the Test File**: Place the file in the appropriate directory (`tests/unit` or `tests/integration`).
3.  **Write the Test**: Follow the principles outlined above. Use `pytest` fixtures for setup/teardown.
4.  **Add Markers**: Add the appropriate `@pytest.mark` decorator.
5.  **Run Locally**: Use the `Makefile` targets to run your new test and ensure it passes.
6.  **Update `Makefile` (if needed)**: If your test requires a new category or command, update the `Makefile` accordingly.

This strategy ensures our testing is comprehensive, maintainable, and directly supports our goal of building a robust, production-ready system. 