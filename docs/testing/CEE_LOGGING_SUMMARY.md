# CEE Flow Logging Enhancements Summary

## Overview

This document summarizes the comprehensive debug logging that has been added throughout the Command-Effect-Event (CEE) flow to help identify issues and track message processing.

## Logging Enhancements Implemented

### 1. Command Handler (memory_encoder/service.py)

Added detailed logging with consistent prefixes:
- `[HANDLER]` prefix for all handler logs
- Entry/exit logging with separator lines (`="*60`)
- Logs channel, message ID, correlation ID, and payload
- Tracks content extraction and importance conversion
- Logs service instance retrieval and context creation
- Tracks effect creation and return

Key log points:
```
[HANDLER] Memory encode command handler called
[HANDLER] Channel: command.pc.memory.encode
[HANDLER] Calling service.register_encode_command()
[HANDLER] Service returned effect: WriteDatabaseEffect
[HANDLER] Created effect message: effect.database.write
[HANDLER] Returning effect message to bus
```

### 2. Effect Interpreter (effects/interpreter.py)

Enhanced logging in multiple methods:

#### _handle_effect_message
- `[INTERPRETER]` prefix for consistency
- Logs message receipt with full details
- Tracks effect and context extraction
- Shows queue assignment (URGENT vs NORMAL)
- Reports queue sizes

#### _process_effect_queue  
- `[WORKER-{id}]` prefix with worker ID
- Tracks queue source (NORMAL vs URGENT fallback)
- Logs effect processing with correlation ID
- Shows execution strategy selection
- Reports execution results and errors
- Tracks event publishing

Key log points:
```
[INTERPRETER] Received effect message
[INTERPRETER] Deserializing effect...
[INTERPRETER] Added to NORMAL queue (size: 1)
[WORKER-123] Processing effect for correlation_id: xyz
[WORKER-123] Execution strategy: PARTICLE
[WORKER-123] Publishing completion event...
```

### 3. Database Effect Handler (effects/handlers/database.py)

Added comprehensive logging:

#### _handle_write
- `[DB-HANDLER]` prefix for database operations
- Logs table, operation type, and fidelity
- Tracks batching decisions
- Reports cache invalidation
- Identifies memory table writes

#### _emit_memory_event
- `[DB-HANDLER-EVENT]` prefix for event publishing
- Logs bus availability check
- Shows event payload construction
- Tracks successful event sending
- Reports errors if bus unavailable

Key log points:
```
[DB-HANDLER] Writing to table memories: INSERT
[DB-HANDLER] Executing write operation immediately
[DB-HANDLER-EVENT] Creating event message...
[DB-HANDLER-EVENT] Event sent successfully!
```

### 4. Command Handler Registry (handlers/registry.py)

Enhanced registration logging:

#### bind_bus
- `[REGISTRY]` prefix throughout
- Reports handler count and channels
- Tracks bus binding and subscription

#### subscribe_all
- Logs each handler subscription
- Shows channel, handler name, and priority
- Reports successful subscriptions

Key log points:
```
[REGISTRY] Binding command handler registry to message bus
[REGISTRY] Number of registered handlers: 1
[REGISTRY] Subscribing handler for channel: command.pc.memory.encode
[REGISTRY] ✅ Successfully subscribed handler for: command.pc.memory.encode
```

## Logging Configuration Recommendations

### For Testing/Debugging

```python
logging.basicConfig(
    level=logging.DEBUG,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[logging.StreamHandler(sys.stdout)]
)

# Component-specific levels
logging.getLogger('person_suit.core.handlers').setLevel(logging.DEBUG)
logging.getLogger('person_suit.core.effects').setLevel(logging.DEBUG)
logging.getLogger('person_suit.services').setLevel(logging.DEBUG)
logging.getLogger('person_suit.core.infrastructure').setLevel(logging.INFO)
```

### For Production

```python
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.handlers.RotatingFileHandler(
            'person_suit.log',
            maxBytes=10485760,  # 10MB
            backupCount=5
        )
    ]
)
```

## Using the Logs for Debugging

### Tracking a Message Through the Flow

1. Look for the correlation ID in logs
2. Follow the prefixes in order:
   - `[HANDLER]` - Command received and processed
   - `[INTERPRETER]` - Effect queued
   - `[WORKER-*]` - Effect being processed
   - `[DB-HANDLER]` - Database operation
   - `[DB-HANDLER-EVENT]` - Event published

### Common Patterns to Look For

1. **Missing Handler**: No `[HANDLER]` logs after command sent
2. **Effect Not Routed**: `[INTERPRETER]` shows "missing effect or context data"
3. **Worker Issues**: No `[WORKER-*]` logs or worker stopped messages
4. **Event Not Published**: `[DB-HANDLER-EVENT]` shows "no message bus available"
5. **Queue Buildup**: Queue sizes increasing in `[INTERPRETER]` logs

## Benefits of Enhanced Logging

1. **Complete Visibility**: Every stage of CEE flow is logged
2. **Correlation Tracking**: Easy to follow a single request
3. **Performance Insights**: Queue sizes and execution times visible
4. **Error Diagnosis**: Detailed error messages with context
5. **Production Ready**: Configurable levels for different environments

## Next Steps

1. Add structured logging (JSON format) for easier parsing
2. Integrate with log aggregation systems (ELK, Splunk)
3. Add performance metrics based on log data
4. Create alerting rules for common error patterns
5. Build visualization dashboards from log data 