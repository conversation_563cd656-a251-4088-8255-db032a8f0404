# Command-Effect-Event (CEE) Flow Testing Guide

## Overview

This guide provides comprehensive recommendations for testing and debugging the CEE flow in the Person Suit system. The CEE flow is critical for the system's operation and requires thorough testing with detailed logging at each stage.

## CEE Flow Architecture

```
Command → Handler → Effect → Interpreter → Event
```

1. **Command**: Sent by a component requesting an action
2. **Handler**: Pure business logic that returns an Effect
3. **Effect**: Declaration of what should happen
4. **Interpreter**: Executes the effect and checks capabilities
5. **Event**: Published after successful execution

## Debug Logging Implementation

### 1. Command Handler Logging

Each command handler should log:
- Entry with command details
- Payload extraction
- Service invocation
- Effect creation
- Exit with correlation ID

```python
@command_handler("command.pc.memory.encode")
async def handle_memory_encode_command(cmd: HybridMessage):
    logger.info("="*60)
    logger.info("[HANDLER] Memory encode command handler called")
    logger.info(f"[HANDLER] Channel: {cmd.channel}")
    logger.info(f"[HANDLER] Message ID: {cmd.message_id}")
    logger.info(f"[HANDLER] Correlation ID: {getattr(cmd, 'correlation_id', 'None')}")
    logger.info(f"[HANDLER] Payload: {cmd.payload}")
    # ... handler logic ...
    logger.info("="*60)
```

### 2. Effect Interpreter Logging

The interpreter should log:
- Message receipt
- Effect deserialization
- Context extraction
- Queue assignment
- Worker processing
- Result publishing

```python
async def _handle_effect_message(self, message: HybridMessage):
    logger.info("="*60)
    logger.info(f"[INTERPRETER] Received effect message")
    logger.info(f"[INTERPRETER] Channel: {message.channel}")
    logger.info(f"[INTERPRETER] Message ID: {message.message_id}")
    logger.info(f"[INTERPRETER] Correlation ID: {getattr(message, 'correlation_id', 'None')}")
    # ... processing logic ...
    logger.info("="*60)
```

### 3. Effect Handler Logging

Effect handlers should log:
- Effect execution start
- Capability checks
- I/O operations
- Event publishing
- Error conditions

```python
async def _handle_write(self, effect: WriteDatabaseEffect, context: UnifiedContext, fidelity: int):
    logger.info("="*60)
    logger.info(f"[DB-HANDLER] Writing to table {effect.table}")
    logger.info(f"[DB-HANDLER] Fidelity: {fidelity}")
    # ... execution logic ...
    logger.info("[DB-HANDLER] Write operation complete")
    logger.info("="*60)
```

## Testing Recommendations

### 1. Unit Tests

Test each component in isolation:

```python
async def test_command_handler():
    """Test handler returns correct effect."""
    handler = get_memory_encoder_service()
    effect = handler.register_encode_command(
        content="test",
        importance=800000,
        context=None
    )
    assert isinstance(effect, WriteDatabaseEffect)
    assert effect.table == "memories"
```

### 2. Integration Tests

Test the complete flow with timeouts:

```python
async def test_cee_flow():
    """Test complete command → effect → event flow."""
    bus = await get_message_bus()
    interpreter = await get_effect_interpreter()
    
    # Capture events
    events = []
    await bus.subscribe("event.pc.memory.encoded", events.append)
    
    # Send command
    cmd = HybridMessage(
        message_type=MessageType.COMMAND.name,
        channel="command.pc.memory.encode",
        payload={"content": "test", "importance": 0.8}
    )
    
    await bus.send(cmd)
    
    # Wait for event with timeout
    start = time.time()
    while len(events) == 0 and time.time() - start < 5.0:
        await asyncio.sleep(0.1)
    
    assert len(events) == 1
    assert events[0].channel == "event.pc.memory.encoded"
```

### 3. Debug Tests

Create focused tests that verify each stage:

```python
async def test_handler_registration():
    """Verify handlers are properly registered."""
    registry = get_command_handler_registry()
    assert "command.pc.memory.encode" in registry.handlers
    
async def test_interpreter_initialization():
    """Verify interpreter has database handler."""
    interpreter = await get_effect_interpreter()
    await interpreter.initialize()
    
    handler = interpreter._effect_handlers.get(WriteDatabaseEffect)
    assert handler is not None
    assert hasattr(handler, '_bus')
    assert handler._bus is not None
    
async def test_bus_connectivity():
    """Verify bus can route messages."""
    bus = await get_message_bus()
    
    received = []
    await bus.subscribe("test.channel", received.append)
    
    msg = HybridMessage(
        message_type=MessageType.EVENT.name,
        channel="test.channel",
        payload={"test": True}
    )
    
    await bus.send(msg)
    await asyncio.sleep(0.1)
    
    assert len(received) == 1
```

### 4. Common Issues and Solutions

#### Issue: Handler Not Found
```
No subscribers found for channel: command.pc.memory.encode
```
**Solution**: Ensure service module is imported to trigger @command_handler registration:
```python
import person_suit.services.memory_encoder.service  # noqa: F401
```

#### Issue: Effect Not Routed
```
[INTERPRETER] Invalid effect message: missing effect or context data
```
**Solution**: Verify effect message structure:
```python
effect_msg = HybridMessage(
    message_type=MessageType.EFFECT.name,
    channel="effect.database.write",
    payload={
        "effect": db_effect.to_dict(),  # Must serialize effect
        "context": context_data,         # Must include context
    }
)
```

#### Issue: Event Not Published
```
[DB-HANDLER-EVENT] Cannot send memory encoded event - no message bus available
```
**Solution**: Ensure database handler is initialized with bus reference:
```python
# In EffectInterpreter._register_builtin_handlers()
db_handler = DatabaseEffectHandler(message_bus=self.message_bus)
self.register_handler(WriteDatabaseEffect, db_handler)
```

#### Issue: Timeout/Hanging
**Symptoms**: Tests hang indefinitely or timeout
**Debug Steps**:
1. Add debug logging at each stage
2. Check queue sizes in interpreter
3. Verify worker tasks are running
4. Check for exceptions in background tasks

### 5. Load Testing

Test system under load:

```python
async def test_cee_load():
    """Test CEE flow under load."""
    bus = await get_message_bus()
    
    # Send many commands
    for i in range(100):
        cmd = HybridMessage(
            message_type=MessageType.COMMAND.name,
            channel="command.pc.memory.encode",
            payload={"content": f"test-{i}", "importance": 0.5}
        )
        await bus.send(cmd)
    
    # Monitor metrics
    await asyncio.sleep(5.0)
    
    # Check queue depths, error rates, etc.
```

## Logging Configuration

### Development
```python
logging.basicConfig(
    level=logging.DEBUG,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(sys.stdout)
    ]
)

# Set specific loggers
logging.getLogger('person_suit.core.handlers').setLevel(logging.DEBUG)
logging.getLogger('person_suit.core.effects').setLevel(logging.DEBUG)
logging.getLogger('person_suit.services').setLevel(logging.DEBUG)
```

### Production
```python
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.handlers.RotatingFileHandler(
            'person_suit.log',
            maxBytes=10485760,  # 10MB
            backupCount=5
        )
    ]
)
```

## Monitoring and Metrics

Track these key metrics:
- Command processing rate
- Effect execution time
- Event publishing latency
- Queue depths
- Error rates by type
- Handler registration count

```python
# Example metrics
command_received_total = Counter('command_received_total', 'Total commands received', ['channel'])
effect_execution_time = Histogram('effect_execution_time_seconds', 'Effect execution time', ['effect_type'])
event_published_total = Counter('event_published_total', 'Total events published', ['channel'])
```

## Best Practices

1. **Always use correlation IDs** - Track messages through the entire flow
2. **Add timeouts** - Prevent tests from hanging indefinitely  
3. **Test error paths** - Verify graceful handling of failures
4. **Monitor queue depths** - Detect bottlenecks early
5. **Use structured logging** - Makes parsing logs easier
6. **Test with realistic data** - Include edge cases and malformed input
7. **Verify cleanup** - Ensure resources are properly released

## Troubleshooting Checklist

When CEE flow isn't working:

- [ ] Are all services imported to trigger handler registration?
- [ ] Is the command handler registry bound to the bus?
- [ ] Is the effect interpreter subscribed to effect channels?
- [ ] Does the database handler have a bus reference?
- [ ] Are correlation IDs properly propagated?
- [ ] Are all required capabilities present in context?
- [ ] Are worker tasks running in the interpreter?
- [ ] Are there any unhandled exceptions in background tasks?
- [ ] Is the message bus running and healthy?
- [ ] Are queue sizes within limits?

## Example Test Suite

```python
import pytest
import asyncio
import time
from typing import List

from person_suit.core.infrastructure.hybrid_message_bus import get_message_bus
from person_suit.core.infrastructure.hybrid_message import HybridMessage, MessageType
from person_suit.core.handlers.registry import get_command_handler_registry
from person_suit.core.effects.interpreter import get_effect_interpreter

# Import services to trigger registration
import person_suit.services.memory_encoder.service  # noqa: F401


class TestCEEFlow:
    """Comprehensive CEE flow test suite."""
    
    @pytest.fixture
    async def setup(self):
        """Set up test environment."""
        bus = await get_message_bus()
        interpreter = await get_effect_interpreter()
        registry = get_command_handler_registry()
        
        await interpreter.initialize()
        await registry.bind_bus(bus)
        
        yield bus, interpreter, registry
        
        await bus.stop()
    
    async def test_handler_registration(self, setup):
        """Verify command handlers are registered."""
        bus, interpreter, registry = setup
        
        assert "command.pc.memory.encode" in registry.handlers
        handler_info = registry.handlers["command.pc.memory.encode"]
        assert handler_info is not None
    
    async def test_effect_routing(self, setup):
        """Test effect message routing."""
        bus, interpreter, registry = setup
        
        # Track effect messages
        effects_received = []
        
        # Monkey-patch to capture effects
        original_handle = interpreter._handle_effect_message
        async def track_effects(msg):
            effects_received.append(msg)
            return await original_handle(msg)
        
        interpreter._handle_effect_message = track_effects
        
        # Send command
        cmd = HybridMessage(
            message_type=MessageType.COMMAND.name,
            channel="command.pc.memory.encode",
            payload={"content": "test", "importance": 0.8}
        )
        
        await bus.send(cmd)
        await asyncio.sleep(0.5)
        
        assert len(effects_received) > 0
        assert effects_received[0].channel == "effect.database.write"
    
    async def test_event_publishing(self, setup):
        """Test event publishing after effect execution."""
        bus, interpreter, registry = setup
        
        # Capture events
        events: List[HybridMessage] = []
        await bus.subscribe("event.pc.memory.encoded", events.append)
        
        # Send command
        cmd = HybridMessage(
            message_type=MessageType.COMMAND.name,
            channel="command.pc.memory.encode",
            payload={"content": "test memory", "importance": 0.8}
        )
        
        await bus.send(cmd)
        
        # Wait for event with timeout
        start = time.time()
        while len(events) == 0 and time.time() - start < 5.0:
            await asyncio.sleep(0.1)
        
        assert len(events) == 1
        assert events[0].channel == "event.pc.memory.encoded"
        assert events[0].payload["content"] == "test memory"
```

## Conclusion

Robust CEE testing requires:
1. Comprehensive logging at each stage
2. Timeout-based integration tests
3. Monitoring of queue depths and error rates
4. Verification of component initialization
5. Testing under realistic load conditions

By following these recommendations, you can ensure the CEE flow operates reliably in production. 