# file: /Users/<USER>/Lokalne_Kody/Coversational/person_suit/core/effects/handlers/network.py
# hypothesis_version: 6.135.11

[0.01, 0.05, 0.1, 0.5, 1.0, 200, 300, 1000, '10 mail.example.com', '***********', '***********', '20 mail2.example.com', '2001:db8::1', '2001:db8::2', 'A', 'AAAA', 'GET', 'MX', 'application/json', 'closed', 'connected', 'connected_at', 'connection_id', 'content-type', 'data', 'effect_type', 'fidelity', 'fidelity_used', 'headers', 'hostname', 'low', 'message', 'message_size', 'message_type', 'method', 'mock-server', 'params', 'protocols', 'record_type', 'records', 'request_headers', 'resolved_at', 'sent_at', 'server', 'status', 'status_code', 'timeout', 'timestamp', 'ttl', 'url', 'verify_ssl']