# file: /Users/<USER>/Lokalne_Kody/Coversational/person_suit/core/deployment/validation/environment_validator.py
# hypothesis_version: 6.135.11

[1024, 4096, '3.9.0', 'CPU Core Count Check', 'Darwin', 'ENV_CPU_CORES', 'ENV_ENV_VARS', 'ENV_MEMORY', 'ENV_PYTHON_VERSION', 'ENV_RUNTIME_ENV', 'INSUFFICIENT_MEMORY', 'Linux', 'MISSING_ENV_VARS', 'Memory Size Check', 'Metal support', 'Neural Engine', 'PERSON_SUIT_HOME', 'Python Version Check', 'UNKNOWN_RULE', 'UNKNOWN_RUNTIME_ENV', 'UNSUPPORTED_PLATFORM', 'Windows', 'current_cores', 'current_memory_mb', 'current_version', 'environment', 'environment.hardware', 'environment.platform', 'environment.runtime', 'has_neural_engine', 'is_m3_max', 'message', 'min_cores', 'min_memory_mb', 'min_version', 'missing_features', 'missing_vars', 'platform', 'required_vars', 'supports_metal']