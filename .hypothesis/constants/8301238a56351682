# file: /Users/<USER>/Lokalne_Kody/Coversational/person_suit/core/infrastructure/middleware/provenance.py
# hypothesis_version: 6.135.11

[1.0, -5000, 10000, '*', ',', '.', 'EVENT', 'No data available', 'PS_PROVENANCE_SINK', '[REDACTED]', '_meta', 'acf_fidelity', 'acf_metadata', 'auth', 'capabilities', 'capabilities_count', 'channel', 'context', 'correlation_id', 'credential', 'csv', 'data', 'domain', 'environment', 'event_type', 'fidelity', 'json', 'key', 'level', 'memory', 'message_id', 'message_type', 'meta_system', 'metadata', 'password', 'payload', 'priority', 'provenance_enabled', 'provenance_hash', 'provenance_timestamp', 'record_provenance', 'records_in_memory', 'redpanda', 'request_id', 'secret', 'security_context', 'security_level', 'session_id', 'size', 'stored', 'sys.provenance.*', 'timestamp', 'token', 'trust_domain', 'type', 'unknown', 'user_id']