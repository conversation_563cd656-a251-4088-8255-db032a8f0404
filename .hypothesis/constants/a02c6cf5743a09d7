# file: /Users/<USER>/Lokalne_Kody/Coversational/person_suit/shared/utils/docstring_validator.py
# hypothesis_version: 6.135.11

[100, '"""', '#', '*', '*.py', '-', '--exclude', '--format', '--output', '.git', ':', '<file>', 'Args', 'Attributes', 'Purpose', 'Returns', 'Summary:', '__main__', '__pycache__', '__stats__', 'body', 'build', 'class', 'classes', 'dist', 'docstring_type', 'error', 'files', 'function', 'functions', 'info', 'invalid', 'issues', 'json', 'line_number', 'message', 'method', 'methods', 'missing', 'missing_sections', 'module', 'modules', 'node_name', 'path', 'r', 'self', 'severity', 'stats', 'suggestion', 'suggestions', 'summary', 'text', 'utf-8', 'valid', 'venv', 'w', 'warning']