# file: /Users/<USER>/Lokalne_Kody/Coversational/person_suit/core/effects/__init__.py
# hypothesis_version: 6.135.11

[0.0, 0.5, 1.0, 30.0, 'EffectExecutionError', 'EffectResult', 'EffectTimeoutError', 'HybridMessageBus', 'UnifiedContext', 'channel', 'data', 'effect_result', 'effects.io.file.read', 'effects.state.delete', 'effects.state.query', 'effects.state.read', 'effects.state.update', 'effects.state.write', 'error', 'execute_effect', 'execution_time', 'fidelity_used', 'payload', 'raw_exception', 'resource_usage', 'result', 'stop_effects_api', 'success', 'wave_particle_ratio']