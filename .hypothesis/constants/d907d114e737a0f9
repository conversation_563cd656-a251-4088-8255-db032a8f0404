# file: /Users/<USER>/Lokalne_Kody/Coversational/person_suit/core/deployment/health/checks/resource.py
# hypothesis_version: 6.135.11

[0.1, 0.5, 10.0, 70.0, 80.0, 100, 125, 1024, '%', '% per minute', 'MB', 'MB/s', 'avg_cpu', 'cpu', 'cpu_sustained_usage', 'cpu_usage', 'disk_io', 'disk_read_bytes', 'disk_read_rate', 'disk_write_bytes', 'disk_write_rate', 'errin', 'errors', 'errout', 'exception', 'exception_type', 'greater', 'interface', 'io_usage', 'lo', 'loop', 'max_core_usage', 'max_cpu', 'memory', 'memory_available', 'memory_growth_rate', 'memory_usage', 'min_core_usage', 'min_cpu', 'net_bytes_recv', 'net_bytes_sent', 'net_recv_rate', 'net_send_rate', 'network', 'network_usage', 'ops', 'read_rate_mb_per_sec', 'recv_rate_mb_per_sec', 'resource', 'resource.cpu', 'resource.cpu.cores', 'resource.disk.io', 'resource.memory', 'resource.memory.swap', 'resource.network', 'send_rate_mb_per_sec', 'swap_usage', 'swap_used_mb', 'swap_used_percent']