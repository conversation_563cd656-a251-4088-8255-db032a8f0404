# file: /Users/<USER>/Lokalne_Kody/Coversational/person_suit/core/deployment/detection/platform_detector.py
# hypothesis_version: 6.135.11

['"\'', '-a', '-r', '/etc/debian_version', '/etc/os-release', '/etc/redhat-release', ':', '=', 'BuildVersion:', 'CurrentBuildNumber', 'Darwin', 'Debian', 'Distributor ID:', 'Kernel Version:', 'Linux', 'NAME=', 'ProductName', 'ProductVersion:', 'Red Hat', 'Release:', 'SPSoftwareDataType', 'System Version:', 'UBR', 'Unknown', 'VERSION_ID=', 'Windows', 'build', 'distribution', 'distribution_version', 'edition', 'full_version', 'kernel_version', 'lsb_release', 'macOS', 'name', 'r', 'release', 'sw_vers', 'system_profiler', 'uname', 'version', 'which']