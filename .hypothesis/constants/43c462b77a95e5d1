# file: /Users/<USER>/Lokalne_Kody/Coversational/person_suit/core/deployment/detection/runtime_detector.py
# hypothesis_version: 6.135.11

['.development', '.production', '.staging', '.testing', '/', '/.dockerenv', '/docker/', '/garden/', '/kubepods/', '/lxc/', '/proc/self/cgroup', '/run/.containerenv', '1', ':', 'APPVEYOR', 'AppVeyor', 'Azure DevOps', 'BUDDY', 'Buddy', 'CI', 'CIRCLECI', 'CI_NAME', 'CREDENTIAL', 'CircleCI', 'Docker', 'GITHUB_ACTIONS', 'GITLAB_CI', 'GitHub Actions', 'GitLab CI', 'HOSTNAME', 'JENKINS_URL', 'Jenkins', 'KEY', 'Kubernetes', 'LXC', 'PASSWORD', 'PERSON_SUIT_', 'PROD', 'PRODUCTION', 'PYTEST_CURRENT_TEST', 'SECRET', 'STAGE', 'STAGING', 'TEAMCITY_VERSION', 'TEST', 'TF_BUILD', 'TOKEN', 'TRAVIS', 'TeamCity', 'Travis CI', 'USER', 'USERNAME', 'Unknown', 'Unknown CI', '[REDACTED]', 'addr', 'addresses', 'fqdn', 'getaddrinfo', 'hostname', 'inet ', 'interfaces', 'ip', 'ip_addresses', 'linux', 'pid', 'ppid', 'python_executable', 'python_path', 'r', 'show', 'username', 'working_dir']