# file: /Users/<USER>/Lokalne_Kody/Coversational/person_suit/core/resources/manager.py
# hypothesis_version: 6.135.11

[0.0, 1e-09, 0.05, 0.1, 0.2, 0.25, 0.3, 0.5, 0.6, 0.7, 0.75, 0.8, 0.9, 0.95, 1.0, 1.5, 1.75, 2.0, 2.5, 3.0, 30.0, 1000.0, 100, 1024, '/', 'ResourceTelemetry', '_original_message_id', '_queued_internally', 'acf_can_degrade_hint', 'acf_fidelity_hint', 'active', 'can_degrade', 'capacities', 'cpu_count', 'cpu_percent', 'default enum value', 'default_capacity', 'demand', 'dequeued_rejected', 'disk_free', 'disk_percent', 'disk_total', 'disk_used', 'fully_granted', 'memory_available', 'memory_percent', 'memory_total', 'memory_used', 'model_copy', 'partially_granted', 'platform', 'priority', 'psutil discovery', 'python_version', 'queued', 'rejected_no_queue', 'requested_amount', 'resource_available', 'resource_manager', 'resource_reserved', 'resource_type', 'resource_used', 'revocation_reason', 'revocation_timestamp', 'revoked_by_priority', 'source_component_id', 'strategy', 'strategy_name', 'timeout_is_flexible', 'to_dict', 'unified_context_data', 'unified_context_id', 'value']