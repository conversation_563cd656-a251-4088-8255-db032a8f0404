# file: /Users/<USER>/Lokalne_Kody/Coversational/person_suit/core/effects/network.py
# hypothesis_version: 6.135.11

[10.0, 30.0, 300, 3600, 'A', 'DELETE', 'DNSLookupEffect', 'GET', 'HTTPRequestEffect', 'POST', 'PUT', 'WebSocketSendEffect', 'cache_ttl_seconds', 'can_degrade', 'connection_id', 'data', 'dns_lookup', 'follow_redirects', 'headers', 'hostname', 'http_request', 'id', 'json_data', 'message', 'message_type', 'metadata', 'method', 'min_fidelity', 'network', 'network:dns:lookup', 'operation', 'params', 'protocols', 'record_type', 'required_capability', 'text', 'timeout', 'timestamp', 'type', 'url', 'verify_ssl', 'websocket_connect', 'websocket_send']