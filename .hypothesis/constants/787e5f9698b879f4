# file: /Users/<USER>/Lokalne_Kody/Coversational/person_suit/core/effects/event.py
# hypothesis_version: 6.135.11

[0.2, 0.3, 0.5, 1.0, '*', 'BroadcastEventEffect', 'PublishEventEffect', 'SubscribeEventEffect', 'broadcast', 'cache_ttl_seconds', 'can_degrade', 'channel', 'channel_pattern', 'correlation_id', 'event', 'event:*:broadcast', 'event_type', 'exclude_channels', 'filter_criteria', 'generic', 'handler_id', 'id', 'metadata', 'min_fidelity', 'operation', 'payload', 'priority', 'publish', 'reply_to', 'required_capability', 'subscribe', 'timestamp', 'type', 'unsubscribe']