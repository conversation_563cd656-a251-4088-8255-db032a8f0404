# file: /Users/<USER>/Lokalne_Kody/Coversational/person_suit/core/deployment/detection/hardware_detector.py
# hypothesis_version: 6.135.11

[1024, 4096, '*', ', ', '-detailLevel', '-n', '.', '/proc/cpuinfo', '/proc/meminfo', '/sys/class/drm/', '0', '0x1002', '1', ':', 'AMD', 'Apple M', 'Apple M3', 'Apple M3 Max', 'Chipset Model', 'Chipset Model: (.+)', 'Darwin', 'Intel', 'K', 'L1d', 'L1i', 'L2', 'L3', 'Linux', 'M', 'MemAvailable', 'MemTotal', 'Metal: Supported', 'NVIDIA', 'PERSON_SUIT_M3_MAX', 'Pages free', 'Pages inactive', 'ProcessorNameString', 'SPDisplaysDataType', 'Unknown', 'Vendor: (.+)', 'Windows', 'adapter_ram', 'amd', 'arm64', 'available_memory', 'cpu_count', 'driver_version', 'efficiency_cores', 'full', 'has_gpu', 'has_neural_engine', 'hw.l1dcachesize', 'hw.l1icachesize', 'hw.l2cachesize', 'hw.l3cachesize', 'hw.memsize', 'hw.perflevel', 'intel', 'is_m3_max', 'memory', 'metal_support', 'name', 'nvidia', 'nvidia-smi', 'page size', 'page size of', 'performance_cores', 'product_name', 'r', 'radeon', 'supports_metal', 'sysctl', 'system_profiler', 'total_memory', 'vendor', 'vm_stat']