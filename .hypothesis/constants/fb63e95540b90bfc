# file: /Users/<USER>/Lokalne_Kody/Coversational/person_suit/core/infrastructure/middleware/security.py
# hypothesis_version: 6.135.11

[1.0, '*', '**', ':', 'Authorization denied', 'EVENT', 'acf_metadata', 'authorization_error', 'capabilities', 'capability_denied', 'channel', 'context', 'current_capability', 'details', 'error', 'event_type', 'fidelity', 'internal', 'message_denied', 'message_id', 'min_capability', 'name', 'no_capability', 'provided', 'reason', 'record_provenance', 'reply_channel', 'required', 'requires_auth', 'security_context', 'security_denials', 'subscription_denied', 'success', 'sys.', 'system:monitor', 'timestamp', 'trust_domain', 'trust_domains', 'unknown', 'untrusted_domain']