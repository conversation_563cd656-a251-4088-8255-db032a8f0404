# file: /Users/<USER>/Lokalne_Kody/Coversational/person_suit/core/effects/database.py
# hypothesis_version: 6.135.11

[600, '*', 'DeleteDatabaseEffect', 'READ_COMMITTED', 'ReadDatabaseEffect', 'TransactionEffect', 'WriteDatabaseEffect', 'cache_ttl_seconds', 'can_degrade', 'data', 'database', 'delete', 'fields', 'id', 'insert', 'isolation_level', 'limit', 'metadata', 'min_fidelity', 'offset', 'operation', 'operation_type', 'operations', 'order_by', 'query', 'read', 'required_capability', 'table', 'timestamp', 'transaction', 'type', 'where_clause', 'write']