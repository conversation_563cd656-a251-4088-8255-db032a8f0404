# file: /Users/<USER>/Lokalne_Kody/Coversational/person_suit/core/deployment/validation/resource_validator.py
# hypothesis_version: 6.135.11

[0.1, 100, 1024, 8080, 8081, 8082, '.person_suit_test', '127.0.0.1', 'CPU_CHECK_ERROR', 'INSUFFICIENT_CPU', 'INSUFFICIENT_MEMORY', 'INSUFFICIENT_STORAGE', 'MEMORY_CHECK_ERROR', 'Network Ports Check', 'PATH_CREATION_ERROR', 'PATH_WRITE_ERROR', 'PORT_UNAVAILABLE', 'RES_CPU_AVAILABILITY', 'RES_NETWORK_PORTS', 'STORAGE_CHECK_ERROR', 'UNKNOWN_RULE', 'api.person_suit.org', 'available_gb', 'available_mb', 'available_percent', 'cdn.person_suit.org', 'check_hosts', 'check_paths', 'checked_hosts', 'checked_paths', 'checked_ports', 'cpu_usage', 'min_available_gb', 'min_available_mb', 'required_ports', 'resource_validator', 'resources.cpu', 'resources.memory', 'resources.network', 'resources.storage', 'results', 'test', 'total_gb', 'total_mb', 'w', '~', '~/.person_suit']