# file: /Users/<USER>/Lokalne_Kody/Coversational/person_suit/core/constants/hardware.py
# hypothesis_version: 6.135.11

[0.8, 0.9, 1024, 4096, '/', 'Darwin', 'SC_PAGE_SIZE', 'apple_silicon_cores', 'apple_silicon_model', 'arm64', 'avx', 'avx2', 'avx2_supported', 'avx512', 'avx512_supported', 'avx_supported', 'battery_powered', 'cpu_architecture', 'cpu_count', 'cpu_frequency', 'cpu_model', 'current', 'disk_space_limit', 'disk_space_total', 'efficiency', 'gpu', 'gpu_available', 'gpu_count', 'gpu_memory', 'gpu_model', 'is_apple_silicon', 'memory_limit', 'metal', 'metal_available', 'neon', 'neon_supported', 'neural_engine', 'performance', 'power_efficient_mode', 'sensors_battery', 'sysconf', 'total', 'total_memory', 'vectorization']