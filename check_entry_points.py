#!/usr/bin/env python3
"""
Quick script to check entry points in person_suit directory.
"""

import subprocess
import sys

def main():
    """Check for entry points."""
    print("=== Entry Point Analysis ===")
    
    try:
        # Run the same grep command as the validation script
        result = subprocess.run([
            'grep', '-r', 'if __name__ == "__main__":', 'person_suit/',
            '--include=*.py'
        ], capture_output=True, text=True, cwd='.')
        
        if result.returncode == 0:
            lines = result.stdout.strip().split('\n')
            print(f"Found {len(lines)} entry points:")
            
            # Filter out the allowed ones
            forbidden = []
            for line in lines:
                if 'person_suit/main.py' not in line and 'person_suit/__main__.py' not in line:
                    forbidden.append(line)
            
            print(f"Forbidden entry points: {len(forbidden)}")
            for entry in forbidden[:10]:  # Show first 10
                print(f"  - {entry}")
            
            if len(forbidden) > 10:
                print(f"  ... and {len(forbidden) - 10} more")
                
        else:
            print("No entry points found or grep failed")
            
    except Exception as e:
        print(f"Error: {e}")

if __name__ == "__main__":
    main()
