"""Simple CLI helper for working with choreographies.

Usage::

    python -m person_suit.choreography.cli compile path/to/file.yml
    python -m person_suit.choreography.cli install path/to/file.yml
    python -m person_suit.choreography.cli start path/to/file.yml --workflow-id <uuid>

The *install* and *start* commands spin up an in-process HybridMessageBus if
none is running.  This is intended for development / integration tests – in
production the bus already runs in its own service.
"""
from __future__ import annotations

import argparse
import asyncio
import json
import sys
import uuid
from pathlib import Path
from typing import Any
from typing import Dict

from person_suit.choreography.compiler import compile_choreography
from person_suit.choreography.installer import install_choreography
from person_suit.core.infrastructure.hybrid_message_bus import get_message_bus


async def _cmd_compile(file: Path) -> None:  # noqa: D401
    compiled = compile_choreography(file)
    summary: Dict[str, Any] = {
        "id": compiled.model.id,
        "steps": [s.name for s in compiled.model.steps],
    }
    print(json.dumps(summary, indent=2))


async def _cmd_install(file: Path) -> None:  # noqa: D401
    bus = get_message_bus()
    compiled = compile_choreography(file)
    await install_choreography(bus, compiled)
    print(f"Installed choreography '{compiled.model.id}' with {len(compiled.steps)} steps")


async def _cmd_start(file: Path, workflow_id: str | None) -> None:  # noqa: D401
    bus = get_message_bus()
    compiled = compile_choreography(file)
    await install_choreography(bus, compiled)

    wf_id = workflow_id or str(uuid.uuid4())
    ok = await bus.start_workflow(wf_id, choreography_id=compiled.model.id, initial_context={})
    if ok:
        print(f"Started workflow {wf_id} for choreography '{compiled.model.id}'")
    else:
        print("Failed to start workflow", file=sys.stderr)
        sys.exit(1)


async def _main_async(argv: list[str]) -> None:  # noqa: D401
    parser = argparse.ArgumentParser(prog="ps-choreo", description="Choreography helper CLI")
    sub = parser.add_subparsers(dest="command", required=True)

    p_compile = sub.add_parser("compile", help="Validate and summarise choreography spec")
    p_compile.add_argument("file", type=Path)

    p_install = sub.add_parser("install", help="Install choreography on running message bus")
    p_install.add_argument("file", type=Path)

    p_start = sub.add_parser("start", help="Install spec (if needed) and start a workflow")
    p_start.add_argument("file", type=Path)
    p_start.add_argument("--workflow-id", type=str, default=None)

    p_list = sub.add_parser("list", help="List installed choreographies or active workflows")
    p_list.add_argument("target", choices=["choreos", "workflows"], help="What to list")

    p_inspect = sub.add_parser("inspect", help="Inspect a workflow by ID")
    p_inspect.add_argument("workflow_id", type=str)

    p_uninstall = sub.add_parser("uninstall", help="Remove choreography definition by id or file")
    p_uninstall_group = p_uninstall.add_mutually_exclusive_group(required=True)
    p_uninstall_group.add_argument("--id", dest="choreo_id", type=str)
    p_uninstall_group.add_argument("--file", dest="file", type=Path)

    p_graph = sub.add_parser("graph", help="Output Mermaid graph for a choreography spec")
    p_graph.add_argument("file", type=Path)
    p_graph.add_argument("--output", "-o", type=Path, help="Optional file to save the .mmd text")

    args = parser.parse_args(argv)

    if args.command == "compile":
        await _cmd_compile(args.file)
    elif args.command == "install":
        await _cmd_install(args.file)
    elif args.command == "start":
        await _cmd_start(args.file, args.workflow_id)
    elif args.command == "list":
        bus = get_message_bus()
        if args.target == "choreos":
            print(json.dumps(list(bus._choreographies.keys()), indent=2))  # noqa: WPS437
        else:
            print(json.dumps(list(bus._active_workflows.keys()), indent=2))  # noqa: WPS437
    elif args.command == "inspect":
        bus = get_message_bus()
        wf = bus._active_workflows.get(args.workflow_id)  # noqa: WPS437
        if wf is None:
            print("Workflow not found", file=sys.stderr)
            sys.exit(1)
        print(json.dumps(wf, indent=2, default=str))
    elif args.command == "uninstall":
        bus = get_message_bus()
        if args.choreo_id:
            cid = args.choreo_id
        else:
            cid = compile_choreography(args.file).model.id  # type: ignore[arg-type]

        removed = bus._choreographies.pop(cid, None)  # noqa: WPS437
        if removed is None:
            print("Choreography not found", file=sys.stderr)
            sys.exit(1)
        print(f"Uninstalled choreography '{cid}'")
    elif args.command == "graph":
        compiled = compile_choreography(args.file)
        lines = ["graph TD"]
        for step_name, comp_step in compiled.steps.items():
            if comp_step.downstream:
                for nxt in comp_step.downstream:
                    lines.append(f"  {step_name.replace('.', '_')} --> {nxt.replace('.', '_')}")
            else:
                # Ensure isolated node appears
                lines.append(f"  {step_name.replace('.', '_')}[\"{step_name}\"]")

        mermaid = "\n".join(lines)

        if args.output:
            args.output.write_text(mermaid)
            print(f"Mermaid graph written to {args.output}")
        else:
            print(mermaid)
    else:  # pragma: no cover – argparse guarantees above
        parser.error("Unknown command")


def run_cli(argv: list[str] | None = None) -> None:  # noqa: D401
    """Entry point for tests – calls asyncio.run on the async main."""
    asyncio.run(_main_async(argv or sys.argv[1:]))