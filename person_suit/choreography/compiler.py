from __future__ import annotations

"""Choreography compiler (Sprint-5 MVP).

Takes a YAML/JSON spec, validates it via `Choreography` schema and converts
it into an adjacency-list graph plus a list of *compiled* step descriptors
ready for runtime installation.
"""

import json
from pathlib import Path
from typing import Dict
from typing import List

import yaml
from pydantic import ValidationError

from person_suit.choreography.schema import Choreography
from person_suit.choreography.schema import Step


class CompiledStep:  # noqa: D101
    def __init__(self, step: Step):
        self.step = step
        self.downstream: List[str] = []  # list of step names


class CompiledChoreography:  # noqa: D101
    def __init__(self, model: Choreography):
        self.model = model
        self.steps: Dict[str, CompiledStep] = {s.name: CompiledStep(s) for s in model.steps}
        # Build simple adjacency links (linear flow for MVP)
        for s in model.steps:
            if s.next_step:
                self.steps[s.name].downstream.append(s.next_step)

    # Simple cycle check (DFS) – raises ValueError if cycle found
    def validate_no_cycle(self) -> None:  # noqa: D401
        visited, stack = set(), set()

        def dfs(name: str) -> None:
            if name in stack:
                raise ValueError(f"Cycle detected at step '{name}' in choreography '{self.model.id}'")
            if name in visited:
                return
            stack.add(name)
            for nxt in self.steps[name].downstream:
                dfs(nxt)
            stack.remove(name)
            visited.add(name)

        for n in self.steps:
            dfs(n)

    # ------------------------------------------------------------------
    # Reachability check – all steps must be reachable from at least one
    # entry step (a step that is not referenced as next_step by any other).
    # ------------------------------------------------------------------

    def validate_reachability(self) -> None:  # noqa: D401
        # Build reverse lookup of outbound edges
        referenced: set[str] = set()
        for step in self.steps.values():
            referenced.update(step.downstream)

        entry_points = [name for name in self.steps if name not in referenced]
        if not entry_points:
            raise ValueError(f"Choreography '{self.model.id}' has no entry point (possible cycle)")
        if len(entry_points) > 1:
            raise ValueError(
                f"Choreography '{self.model.id}' has multiple entry points: {', '.join(sorted(entry_points))}"
            )

        # Traverse from each entry point
        reachable: set[str] = set()

        def walk(name: str) -> None:
            if name in reachable:
                return
            reachable.add(name)
            for nxt in self.steps[name].downstream:
                walk(nxt)

        for ep in entry_points:
            walk(ep)

        # Any unreachable step?
        unreachable = set(self.steps) - reachable
        if unreachable:
            raise ValueError(
                f"Unreachable step(s) in choreography '{self.model.id}': {', '.join(sorted(unreachable))}"
            )

    # ------------------------------------------------------------------
    # Termination guarantee – ensure there is *exactly one* terminal step
    # (step with no downstream).  Multiple terminals in a strictly linear
    # choreography can lead to half-finished workflows.  Future versions
    # may support explicit join semantics but for Sprint-5 we forbid them.
    # ------------------------------------------------------------------

    def validate_single_terminal(self) -> None:  # noqa: D401
        terminals = [name for name, cs in self.steps.items() if not cs.downstream]
        if len(terminals) == 0:
            raise ValueError(f"Choreography '{self.model.id}' has no terminal step (infinite loop?)")
        if len(terminals) > 1:
            raise ValueError(
                f"Choreography '{self.model.id}' has multiple terminal steps: {', '.join(sorted(terminals))}"
            )


# ---------------------------------------------------------------------------
# Public helpers
# ---------------------------------------------------------------------------


def load_choreography(path: str | Path) -> Choreography:  # noqa: D401
    data = yaml.safe_load(Path(path).read_text())
    return Choreography.parse_obj(data)


def compile_choreography(path: str | Path) -> CompiledChoreography:  # noqa: D401
    try:
        model = load_choreography(path)
    except ValidationError as exc:  # bubbles up detailed errors
        raise ValueError(f"Invalid choreography definition: {exc}") from exc

    compiled = CompiledChoreography(model)
    compiled.validate_no_cycle()
    compiled.validate_reachability()
    compiled.validate_single_terminal()
    return compiled


# CLI helper for quick manual checks

    if len(sys.argv) != 2:
        print("Usage: python -m person_suit.choreography.compiler <file.yml>")
        sys.exit(1)

    c = compile_choreography(sys.argv[1])
    print(json.dumps({
        "id": c.model.id,
        "steps": {
            name: {
                "channel": cs.step.channel,
                "downstream": cs.downstream,
            } for name, cs in c.steps.items()
        }
    }, indent=2)) 