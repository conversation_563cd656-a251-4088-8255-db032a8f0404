from __future__ import annotations

"""Choreography DSL schema (Sprint-5 MVP).

This file defines Pydantic models that validate a YAML/JSON choreography
specification.  The MVP recognises three top-level keys:

* id:           unique choreography id (str)
* description:  human description (optional str)
* steps:        ordered list of *Step* definitions

Each Step references an *action* that will ultimately map to
"send message on channel" (COMMAND/EVENT) or invoke an Effect.  For this first
iteration we only support sending COMMAND messages to a channel.
"""

from enum import Enum
from typing import List
from typing import Optional

from pydantic import BaseModel
from pydantic import Field
from pydantic import validator


class MessageType(str, Enum):  # noqa: D101
    COMMAND = "COMMAND"
    EVENT = "EVENT"
    EFFECT = "EFFECT"  # reserved for later


class Step(BaseModel):  # noqa: D101
    name: str = Field(..., pattern=r"^[A-Za-z0-9_.-]+$")
    channel: str = Field(..., description="Target channel name pattern")
    message_type: MessageType = MessageType.COMMAND
    requires_capability: Optional[str] = Field(
        None, description="Capability token required to execute this step"
    )
    next_step: Optional[str] = Field(
        None, description="Name of the next step (simple linear flow for MVP)"
    )


class Choreography(BaseModel):  # noqa: D101
    id: str = Field(..., pattern=r"^[a-z0-9._-]+$")
    description: Optional[str] = None
    steps: List[Step]

    # Ensure step names unique and next_step references exist
    @validator("steps")
    def _validate_steps(cls, steps: List[Step]) -> List[Step]:  # noqa: D401,N805
        names = {s.name for s in steps}
        if len(names) != len(steps):
            raise ValueError("Duplicate step names in choreography")
        for s in steps:
            if s.next_step and s.next_step not in names:
                raise ValueError(f"Step '{s.name}' references unknown next_step '{s.next_step}'")
        return steps


__all__ = ["Choreography", "Step", "MessageType"] 