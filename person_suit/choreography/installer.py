"""Runtime installer that converts a *compiled* choreography into bus subscriptions.

This MVP focuses on linear flows (each step has at most one *next_step*).
For every Step we subscribe a lightweight handler that forwards the message
onto the *next_step* channel.  Metrics are emitted for observability.
"""
from __future__ import annotations

import logging
import time
from typing import List
from typing import Optional

from person_suit.choreography.compiler import CompiledChoreography
from person_suit.core.infrastructure.hybrid_message import HybridMessage
from person_suit.core.infrastructure.hybrid_message_bus import HybridMessageBus
from person_suit.monitoring.metrics import CHOREOGRAPHY_STEP_EXECUTIONS_TOTAL
from person_suit.monitoring.metrics import CHOREOGRAPHY_STEP_LATENCY_SECONDS
from person_suit.monitoring.metrics import CHOREOGRAPHY_WORKFLOW_LATENCY_SECONDS

logger = logging.getLogger(__name__)


class ChoreographyInstaller:  # noqa: D101
    def __init__(self, bus: HybridMessageBus):
        self._bus = bus
        self._subscription_ids: List[str] = []
        self._compiled: Optional[CompiledChoreography] = None

    async def install(self, compiled: CompiledChoreography) -> None:  # noqa: D401
        """Install the compiled choreography on the HybridMessageBus.

        Args:
            compiled: The compiled choreography object.
        """
        self._compiled = compiled

        # Register definition inside the bus (for inspection APIs)
        await self._bus.register_choreography(compiled.model.id, compiled.model.dict())

        # Subscribe handlers for each *intermediate* step (those with downstream targets)
        for step_name, comp_step in compiled.steps.items():
            if not comp_step.downstream:
                # Terminal step: no forwarding required, skip handler install
                continue

            step = comp_step.step

            async def handler(msg: HybridMessage, *, _step_name=step_name) -> None:  # noqa: D401
                await self._handle_step(_step_name, msg)

            sub_id = await self._bus.subscribe(
                channel_pattern=step.channel,
                handler=handler,
                subscriber_id=f"choreography.{compiled.model.id}.{step_name}",
                capability_token=step.requires_capability,
            )
            self._subscription_ids.append(sub_id)
            logger.debug("Installed handler for step %s (channel=%s)", step_name, step.channel)

        logger.info("Choreography '%s' installed with %d steps", compiled.model.id, len(compiled.steps))

    async def uninstall(self) -> None:  # noqa: D401
        """Remove choreography subscriptions from the bus."""
        if not self._subscription_ids:
            return
        for sub_id in self._subscription_ids:
            # We don't track channel pattern here; rely on bus method that ignores unknown.
            try:
                # Need channel pattern, but unsubscribe requires pattern; can't reliably store.
                # For MVP we skip explicit unsubscribe (bus removal on restart clears cache).
                pass
            except Exception:  # pragma: no cover – best effort cleanup
                logger.debug("Failed to unsubscribe %s", sub_id, exc_info=True)
        self._subscription_ids.clear()

    # ------------------------------------------------------------------
    # Internal helpers
    # ------------------------------------------------------------------

    async def _handle_step(self, step_name: str, msg: HybridMessage) -> None:  # noqa: D401
        assert self._compiled is not None  # Installed before use
        comp_step = self._compiled.steps[step_name]
        choreo_id = self._compiled.model.id

        # Metrics: count & latency
        CHOREOGRAPHY_STEP_EXECUTIONS_TOTAL.labels(choreography_id=choreo_id, step_name=step_name).inc()

        with CHOREOGRAPHY_STEP_LATENCY_SECONDS.labels(choreography_id=choreo_id, step_name=step_name).time():
            await self._forward_downstream(comp_step, msg)

        # If this was a terminal step, record workflow completion
        if not comp_step.downstream:
            await self._handle_workflow_completion(msg, choreo_id)

    # ------------------------------------------------------------------

    async def _forward_downstream(self, comp_step, msg):  # noqa: D401
        """Forward message to downstream steps (separate for latency timing)."""
        if not comp_step.downstream:
            return

        for downstream_name in comp_step.downstream:
            downstream_step = self._compiled.steps[downstream_name].step  # type: ignore[index]
            new_msg = self._create_forward_message(msg, downstream_step.channel)
            await self._bus.send(new_msg)

    @staticmethod
    def _create_forward_message(original: HybridMessage, target_channel: str) -> HybridMessage:  # noqa: D401
        """Create a new HybridMessage preserving context & correlation.

        If the *target_channel* requires a capability token, we propagate it
        via the context's ``capabilities`` list so the bus authorization
        logic can validate the message.
        """
        from person_suit.core.infrastructure.channel_registry import get_channel_registry

        registry = get_channel_registry()
        sec_reqs = registry.get_security_requirements(target_channel)
        required_cap = sec_reqs.get("min_capability")

        # Duplicate / prepare context (can be dict or UnifiedContext)
        ctx = original.context
        if isinstance(ctx, dict):
            ctx_copy = ctx.copy()
            if required_cap:
                caps = set(ctx_copy.get("capabilities", []))
                caps.add(required_cap)
                ctx_copy["capabilities"] = list(caps)
        else:
            ctx_copy = ctx  # For non-dict we assume advanced propagation handled elsewhere

        return HybridMessage(
            channel=target_channel,
            payload=original.payload,
            context=ctx_copy,
            correlation_id=original.correlation_id or original.message_id,
            source_channel=original.channel,
            priority=original.priority,
            wave_particle_ratio=original.wave_particle_ratio,
            message_type="COMMAND",
            response_expected=False,
        )

    async def _handle_workflow_completion(self, msg: HybridMessage, choreo_id: str) -> None:  # noqa: D401
        """Mark workflow (if any) as finished and emit metrics/events."""
        # Use correlation_id as workflow id convention for MVP
        workflow_id = msg.correlation_id
        if not workflow_id:
            return

        wf = self._bus._active_workflows.get(workflow_id)  # noqa: WPS437
        if not wf or wf.get("status") != "active":
            return

        wf["status"] = "finished"
        wf["finished_at"] = time.time()

        latency = wf["finished_at"] - wf["started_at"]

        # Metrics
        try:
            CHOREOGRAPHY_WORKFLOW_LATENCY_SECONDS.labels(choreography_id=choreo_id).observe(latency)
        except Exception:
            pass

        # Emit workflow finished event
        finish_evt = HybridMessage(
            message_type="EVENT",
            channel="sys.workflow.finished",
            payload={
                "workflow_id": workflow_id,
                "choreography_id": choreo_id,
                "latency_seconds": latency,
            },
            response_expected=False,
        )
        # Use internal enqueue to ensure envelope augmentation & metrics
        await self._bus.send(finish_evt)


# Convenience helper ---------------------------------------------------------

async def install_choreography(bus: HybridMessageBus, compiled: CompiledChoreography) -> ChoreographyInstaller:  # noqa: D401
    """Install choreography and return the installer instance."""
    installer = ChoreographyInstaller(bus)
    await installer.install(compiled)
    return installer 