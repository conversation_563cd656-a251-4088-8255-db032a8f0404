"""person_suit.core.information.dual

Canonical dual wave-particle representation for the Person Suit project.

This *replaces* all legacy `dual_wave.*` models and the (never-created)
`core/caw/dual.py`.  By living in `core/information/` the file celebrates
CAW principles without introducing an extra *caw* directory.

Key features
------------
* 2048-dimensional default vectors (configurable via ``dim`` argument).
* Simple `from_raw()` factory – pads, truncates or embeds arbitrary raw
  inputs into the dual space so existing callers can pass anything.
* Numpy-based implementation for easy vectorisation / ANN integration.
* 100 % type-hinted, Google-style docstrings, Ruff compliant.
"""

from __future__ import annotations

import hashlib
import math
import os
from dataclasses import dataclass
from dataclasses import field
from typing import Any
from typing import Dict

import numpy as np

# ----------------------------------------------------------------------------
# Constants & helpers
# ----------------------------------------------------------------------------

DEFAULT_DIM: int = 2048  # project-wide default – see workspace rule `dimetions`


def _pad_or_truncate(vec: np.ndarray, dim: int = DEFAULT_DIM) -> np.ndarray:  # noqa: D401
    """Pad or truncate a vector to the required dimensionality.

    Args:
        vec: Input vector of arbitrary length.
        dim: Target dimensionality (defaults to :pydata:`DEFAULT_DIM`).

    Returns:
        Numpy array of shape ``(dim,)``.
    """

    if vec.shape[0] == dim:
        return vec

    if vec.shape[0] > dim:
        return vec[:dim]

    # Pad with zeros (could switch to learned pad value later)
    padded = np.zeros(dim, dtype=vec.dtype)
    padded[: vec.shape[0]] = vec
    return padded


def _embed_text(text: str, dim: int = DEFAULT_DIM) -> np.ndarray:  # noqa: D401
    """Very lightweight text→vector embedding.

    This is **not** meant for production-grade semantics – it only ensures a
    deterministic 2048-dim vector without adding heavyweight ML deps.
    Algorithm: hash → bytes → map to floats in [-1, 1].
    """

    digest = hashlib.sha256(text.encode()).digest()
    # Repeat / truncate digest to fill *dim* elements
    repeats = math.ceil(dim / len(digest))
    full_bytes = (digest * repeats)[:dim]
    ints = np.frombuffer(full_bytes, dtype=np.uint8)
    return (ints.astype(np.float32) / 127.5) - 1.0  # scale to [-1, 1]


# ----------------------------------------------------------------------------
# Main model
# ----------------------------------------------------------------------------


@dataclass
class DualInformation:  # noqa: D401 – single-responsibility
    """Canonical wave-particle representation.

    Attributes:
        wave: Probabilistic/wave vector (length ``dim``).
        particle: Deterministic/particle vector (length ``dim``).
        metadata: Arbitrary metadata (provenance, timestamps, etc.).
        dim: Dimensionality (defaults to :pydata:`DEFAULT_DIM`).
    """

    wave: np.ndarray
    particle: np.ndarray
    metadata: Dict[str, Any] = field(default_factory=dict)
    dim: int = DEFAULT_DIM

    # ---------------------------------------------------------------------
    # Construction helpers
    # ---------------------------------------------------------------------

    @classmethod
    def from_raw(cls, data: Any, *, dim: int = DEFAULT_DIM) -> "DualInformation":  # noqa: D401
        """Create a :class:`DualInformation` from arbitrary raw data.

        Supported *data* types:
            • ``np.ndarray`` – assumed to be *wave*, copied & padded. Particle
              gets the same contents (caller can overwrite later).
            • ``list`` / ``tuple`` of floats – converted to ndarray.
            • ``str`` – embedded via simple hashing.
            • Any other – uses ``repr(data)`` then hashed.

        The same vector seeds both *wave* and *particle* fields so downstream
        code sees consistent dimensionality.  Callers aiming for richer
        semantics can supply wave/particle explicitly via constructor.
        """

        if isinstance(data, np.ndarray):
            base = _pad_or_truncate(data.astype(np.float32), dim)
        elif isinstance(data, (list, tuple)):
            base = _pad_or_truncate(np.asarray(data, dtype=np.float32), dim)
        elif isinstance(data, str):
            base = _embed_text(data, dim)
        else:
            base = _embed_text(repr(data), dim)

        return cls(wave=base.copy(), particle=base.copy(), dim=dim)

    # ------------------------------------------------------------------
    # Convenience accessors
    # ------------------------------------------------------------------

    def as_tuple(self) -> tuple[np.ndarray, np.ndarray]:  # noqa: D401
        """Return ``(wave, particle)`` tuple."""

        return self.wave, self.particle

    def norm(self) -> float:  # noqa: D401
        """Euclidean norm of the *wave* vector (for quick sanity checks)."""

        return float(np.linalg.norm(self.wave))

    # ------------------------------------------------------------------
    # Comparative helpers (Sprint-6 requirement)
    # ------------------------------------------------------------------

    def delta(self, other: "DualInformation", *, metric: str = "cosine") -> float:  # noqa: D401
        """Compute distance between *self* and *other*.

        Supported ``metric`` values:
        • ``cosine``  (1‒cosine-similarity) – default.
        • ``l2``      (Euclidean / L2 distance).
        """

        if self.dim != other.dim:
            raise ValueError("Dimensionality mismatch in DualInformation.delta()")

        if metric == "l2":
            return float(np.linalg.norm(self.wave - other.wave))

        if metric == "cosine":
            a: np.ndarray = self.wave
            b: np.ndarray = other.wave
            denom = np.linalg.norm(a) * np.linalg.norm(b)
            if denom == 0:
                return 1.0  # maximal distance when one vector is zero
            cos_sim = float(np.dot(a, b) / denom)
            return 1.0 - cos_sim

        raise ValueError(f"Unsupported metric: {metric}")

    def mixed_vector(self, ratio: float) -> np.ndarray:  # noqa: D401
        """Return linear blend ``ratio*wave + (1-ratio)*particle``.

        Args:
            ratio: 0.0 = pure particle, 1.0 = pure wave.
        """

        ratio = max(0.0, min(1.0, ratio))
        return (ratio * self.wave) + ((1.0 - ratio) * self.particle)

    # ------------------------------------------------------------------
    # Validation hooks
    # ------------------------------------------------------------------

    def __post_init__(self) -> None:  # noqa: D401 – dataclass hook
        if self.wave.shape[0] != self.dim:
            self.wave = _pad_or_truncate(self.wave, self.dim)
        if self.particle.shape[0] != self.dim:
            self.particle = _pad_or_truncate(self.particle, self.dim)

        # Keep metadata minimal – auto-inject creation context if absent
        self.metadata.setdefault("created_by", os.getenv("HOSTNAME", "unknown"))


__all__ = [
    "DualInformation",
    "DEFAULT_DIM",
] 