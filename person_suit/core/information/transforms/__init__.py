"""Transform registry for DualInformation advanced operations.

External/experimental maths lives in callable transforms that accept and
return ``DualInformation``.  Core infrastructure never imports heavy maths
unless the transform is explicitly used, supporting Adaptive Computational
Fidelity & plugin-based extensibility.

Usage
-----
>>> from person_suit.core.information import DualInformation
>>> from person_suit.core.information.transforms import register_transform, apply_transform

>>> def negate(di: DualInformation, **_):
...     di.wave = -di.wave
...     return di
...
>>> register_transform("negate", negate)
>>> di2 = apply_transform("negate", di1)
"""

from __future__ import annotations

from types import MappingProxyType
from typing import Any
from typing import Callable
from typing import Dict

from person_suit.core.information.dual import DualInformation

_Registry: Dict[str, Callable[[DualInformation, Any], DualInformation]] = {}


def register_transform(name: str, fn: Callable[[DualInformation, Any], DualInformation]) -> None:  # noqa: D401
    """Register a transform under *name* (overwrites existing)."""

    _Registry[name] = fn


def get_transform(name: str) -> Callable[[DualInformation, Any], DualInformation]:  # noqa: D401
    """Return transform callable or raise *KeyError*."""

    return _Registry[name]


def list_transforms() -> Dict[str, Callable]:  # noqa: D401
    """Return read-only mapping of registered transforms."""

    return MappingProxyType(_Registry)


def apply_transform(name: str, di: DualInformation, **kwargs) -> DualInformation:  # noqa: D401
    """Apply a named transform to *di* and return the new object."""

    fn = get_transform(name)
    return fn(di, **kwargs) 