"""Quantum-style transforms operating on DualInformation.

The legacy *dual_wave* prototype contained a rich set of physics-inspired
operations (complex amplitudes, interference, decoherence).  We salvage a
minimal interference operator as a proof-of-concept plugin that works on the
canonical dense vectors.
"""

from __future__ import annotations

import numpy as np

from person_suit.core.information.dual import DualInformation
from person_suit.core.information.transforms import register_transform


def interference(di: DualInformation, *, frequency: float = 0.1) -> DualInformation:  # noqa: D401
    """Apply a simple sinusoidal interference pattern to *wave* component.

    Args:
        di: The dual information instance (modified *in-place* for perf).
        frequency: Frequency multiplier for the interference wave.

    Returns:
        The same instance for chaining.
    """

    idx = np.arange(di.dim, dtype=np.float32)
    pattern = np.sin(2 * np.pi * frequency * idx / di.dim)
    di.wave = di.wave * pattern
    return di


# Register automatically when module imported
register_transform("quantum_interference", interference) 