"""
Context types for the unified context system.

This module provides common types used by the context system.
"""

from dataclasses import dataclass
from typing import Any
from typing import Dict


@dataclass
class ContextConstraint:
    """A constraint that influences context behavior."""
    type: str  # e.g., "time_limit", "memory_limit", "quality_requirement"
    value: Any
    priority: int = 1.0  # Higher values = more important
    
    def is_satisfied(self, current_state: Dict[str, Any]) -> bool:
        """Check if the constraint is satisfied given current state."""
        if self.type == "time_limit":
            elapsed_time = current_state.get("elapsed_time", 0)
            return bool(elapsed_time <= self.value)
        elif self.type == "memory_limit":
            memory_usage = current_state.get("memory_usage", 0)
            return bool(memory_usage <= self.value)
        elif self.type == "quality_requirement":
            quality_score = current_state.get("quality_score", 0)
            return bool(quality_score >= self.value)
        return True


# Export commonly used types
__all__ = [
    "ContextConstraint",
] 