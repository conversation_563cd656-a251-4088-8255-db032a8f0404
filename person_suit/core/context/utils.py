from __future__ import annotations

"""person_suit.core.context.utils
Utility helpers for UnifiedContext enforcement.
"""

import functools
import inspect
from typing import Any
from typing import Callable
from typing import TypeVar

from person_suit.core.context.unified import UnifiedContext

F = TypeVar("F", bound=Callable[..., Any])

def require_context(func: F) -> F:  # type: ignore[override]
    """Decorator that ensures a *context* argument is passed and is not None.

    Usage::

        @require_context
        async def process(self, data: Data, context: UnifiedContext):
            ...

    On violation it raises ``ValueError`` immediately – this makes bugs
    obvious during development and test runs.
    """

    sig = inspect.signature(func)
    if "context" not in sig.parameters:
        raise TypeError(
            f"@require_context: '{func.__qualname__}' must accept a 'context' parameter"
        )

    @functools.wraps(func)
    def _wrapper(*args: Any, **kwargs: Any):  # type: ignore[override]
        if "context" not in kwargs or kwargs["context"] is None:
            raise ValueError(
                f"{func.__qualname__} called without a 'context' argument; UnifiedContext is mandatory"
            )
        ctx = kwargs["context"]
        if not isinstance(ctx, UnifiedContext):
            raise ValueError(
                f"{func.__qualname__} expected context=UnifiedContext, got {type(ctx)}"
            )
        return func(*args, **kwargs)

    return _wrapper  # type: ignore[return-value] 