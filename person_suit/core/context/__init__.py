"""
Unified Context Module for Person Suit
=====================================

This module provides the single, unified context implementation for the entire
Person Suit system, replacing all previous context implementations.

File Purpose: Export unified context for CAW paradigm implementation
Related Files: 
- person_suit/core/context/unified.py - The unified context implementation
Dependencies: None
"""

from person_suit.core.context.unified import ACFParams
from person_suit.core.context.unified import ContextConstraint
from person_suit.core.context.unified import ObservationMode
from person_suit.core.context.unified import ResourceType
from person_suit.core.context.unified import UnifiedContext

__all__ = [
    'UnifiedContext',
    'ObservationMode',
    'ResourceType',
    'ACFParams',
    'ContextConstraint',
]
