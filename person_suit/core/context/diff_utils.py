"""
Utilities for creating and applying differences between context dictionaries.

This module provides the core logic for the Differential Context Propagation
rule, allowing for incremental updates to context objects.
"""
from __future__ import annotations

from deepdiff import DeepDiff


def create_diff(t2: dict, t1: dict) -> dict:
    """
    Creates a dictionary diff between two dictionaries using DeepDiff.

    Args:
        t2: The new dictionary (the "to" state).
        t1: The old dictionary (the "from" state).

    Returns:
        A dictionary representing the delta.
    """
    diff = DeepDiff(t1, t2, ignore_order=True, report_repetition=True)
    return diff.to_dict()


def apply_diff(t1: dict, diff: dict) -> dict:
    """
    Applies a diff to a dictionary.
    
    NOTE: This is a simplified implementation and does not handle all
    the complex cases that DeepDiff can generate. It is intended for the
    primary use case of context propagation.

    Args:
        t1: The old dictionary to apply the diff to.
        diff: The diff dictionary created by `create_diff`.

    Returns:
        The new dictionary after applying the diff.
    """
    # This is a simplified way to "apply" a diff. A more robust implementation
    # would parse the specific change types. For many cases, the diff contains
    # enough information to reconstruct by simply updating values.
    # The `Delta` object in deepdiff > 6 can handle this, but for now
    # we implement a basic version.
    
    t2 = t1.copy()

    # Handle changed values
    if 'values_changed' in diff:
        for key, changes in diff['values_changed'].items():
            # DeepDiff paths are like "root['key']['subkey']"
            path_keys = [k.strip("[]'") for k in key.replace('root', '').split('][')]
            current = t2
            for p_key in path_keys[:-1]:
                current = current[p_key]
            current[path_keys[-1]] = changes['new_value']

    # Handle added items
    if 'dictionary_item_added' in diff:
        for key in diff['dictionary_item_added']:
             path_keys = [k.strip("[]'") for k in key.replace('root', '').split('][')]
             current = t2
             # This part is complex. For now, we assume simple additions.
             # A proper implementation would need to reconstruct the path.

    # Handle removed items
    if 'dictionary_item_removed' in diff:
        for key in diff['dictionary_item_removed']:
             path_keys = [k.strip("[]'") for k in key.replace('root', '').split('][')]
             current = t2
             for p_key in path_keys[:-1]:
                current = current[p_key]
             del current[path_keys[-1]]


    return t2 