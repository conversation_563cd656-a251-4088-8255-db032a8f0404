"""
Effects Service Initialization - Ensure actor system is running for effects.

This module handles the initialization of all services required for the
effects system to function, including the actor system startup.

Related Files:
- person_suit/effects/__init__.py - Effects API that depends on actors
- person_suit/core/actors/__init__.py - Actor system bootstrap
- person_suit/effects/runtime_injector.py - Dependency injection

Dependencies: asyncio, logging (runtime injection)
"""

from __future__ import annotations

import asyncio
import logging
from typing import Any
from typing import Dict
from typing import Optional

logger = logging.getLogger(__name__)

# Initialization state
_services_initialized = False
_initialization_lock = asyncio.Lock()
_context_service = None  # Add global context service variable


async def initialize_effects_services():
    """
    Initialize all services required for effects system operation.
    
    This ensures:
    1. Actor system is running
    2. Message bus is available
    3. Context service is available
    4. Effect router is subscribed to channels
    """
    global _services_initialized
    
    if _services_initialized:
        return
    
    async with _initialization_lock:
        if _services_initialized:
            return
        
        logger.info("Initializing effects services...")
        
        try:
            # 1. Initialize and start the actor system
            await _initialize_actor_system()
            
            # 2. Initialize message bus
            await _initialize_message_bus()
            
            # 2b. Start Effect Interpreter (central executor for all effects)
            await _initialize_effect_interpreter()
            
            # 3. Initialize context service
            await _initialize_context_service()
            
            # 4. Initialize service locator for dependency injection
            await _initialize_service_locator()
            
            _services_initialized = True
            logger.info("Effects services initialized successfully")
            
        except Exception as e:
            logger.error(f"Failed to initialize effects services: {e}")
            raise


async def _initialize_actor_system():
    """Initialize and start the CAW actor system."""
    try:
        from person_suit.core.actors import get_bootstrap
        
        bootstrap = await get_bootstrap()

        # Check if actor system is already running
        status = await bootstrap.get_system_status()
        if status.get("status") == "running":
            logger.info("Actor system already running")
            return
        
        # Start the actor system
        logger.info("Starting CAW actor system...")
        await bootstrap.start()
        
        # Verify it started successfully
        status = await bootstrap.get_system_status()
        if status.get("status") == "running":
            logger.info(f"Actor system started with {status.get('registered_actors', 0)} actors")
        else:
            raise RuntimeError(f"Actor system failed to start: {status}")
            
    except ImportError as e:
        logger.error(f"Failed to import actor system components: {e}")
        raise
    except Exception as e:
        logger.error(f"Failed to initialize actor system: {e}")
        raise


async def _initialize_message_bus():
    """Initialize the hybrid message bus."""
    try:
        from person_suit.core.infrastructure.hybrid_message_bus import get_message_bus
        
        # Get message bus instance (this will initialize it if needed)
        message_bus = get_message_bus()
        if message_bus:
            logger.info("Message bus initialized")
            # DEPRECATED: Injection is no longer needed.
            # from person_suit.core.actors import set_message_bus
            # set_message_bus(message_bus)
            # logger.info("Message bus injected into actor system")
        else:
            raise RuntimeError("Failed to get message bus instance")
            
    except ImportError as e:
        logger.error(f"Failed to import message bus: {e}")
        raise
    except Exception as e:
        logger.error(f"Failed to initialize message bus: {e}")
        raise


async def _initialize_context_service():
    """Initialize the context service."""
    global _context_service
    
    try:
        from person_suit.core.context.unified import UnifiedContext
        
        # Create a mock context service for dependency injection
        class ContextService:
            """Simple context service for creating default contexts."""
            
            async def create_default_context(self) -> UnifiedContext:
                """Create a default UnifiedContext for effects."""
                return UnifiedContext.create_default(domain="effects")
        
        # Store context service globally for injection
        _context_service = ContextService()
        logger.info("Context service initialized")
        
    except ImportError as e:
        logger.error(f"Failed to import UnifiedContext: {e}")
        # Create mock context service as fallback
        class MockContextService:
            async def create_default_context(self):
                return {"context_id": "default", "wave_particle_ratio": 0.5}
        
        _context_service = MockContextService()
        logger.warning("Using mock context service as fallback")
    except Exception as e:
        logger.error(f"Failed to initialize context service: {e}")
        raise


async def _initialize_service_locator():
    """Initialize the service locator for dependency injection."""
    try:
        from .runtime_injector import get_service_locator
        
        locator = get_service_locator()
        
        # Register message bus service
        try:
            from person_suit.core.infrastructure.hybrid_message_bus import get_message_bus
            message_bus = get_message_bus()
            
            # Register as factory function, not instance
            def message_bus_factory():
                return message_bus
            
            locator.register_service("message_bus", message_bus_factory, is_factory=True, singleton=True)
            logger.info("Message bus registered with service locator")
        except Exception as e:
            logger.warning(f"Failed to register message bus: {e}")
        
        # Register context service
        if _context_service:
            # Register as factory function, not instance
            def context_service_factory():
                return _context_service
            
            locator.register_service("context", context_service_factory, is_factory=True, singleton=True)
            logger.info("Context service registered with service locator")
        else:
            logger.warning("Context service not available for registration")
        
        logger.info("Service locator initialized")
        
    except Exception as e:
        logger.error(f"Failed to initialize service locator: {e}")
        raise


async def cleanup_effects_services():
    """Cleanup effects services on shutdown."""
    global _services_initialized
    
    if not _services_initialized:
        return
    
    logger.info("Cleaning up effects services...")
    
    try:
        # Stop actor system
        from person_suit.core.actors import get_bootstrap
        bootstrap = await get_bootstrap()
        await bootstrap.stop()
        logger.info("Actor system stopped")
        
    except Exception as e:
        logger.error(f"Error during effects services cleanup: {e}")
    finally:
        _services_initialized = False


async def get_service_status() -> Dict[str, Any]:
    """Get status of all effects services."""
    status = {
        "effects_services_initialized": _services_initialized,
        "actor_system": {},
        "message_bus": "unknown",
        "context_service": "unknown"
    }
    
    # Get actor system status
    try:
        from person_suit.core.actors import get_bootstrap
        bootstrap = await get_bootstrap()
        status["actor_system"] = await bootstrap.get_system_status()
    except Exception as e:
        status["actor_system"] = {"status": "error", "error": str(e)}
    
    # Check message bus
    try:
        from person_suit.core.infrastructure.hybrid_message_bus import get_message_bus
        message_bus = get_message_bus()
        status["message_bus"] = "available" if message_bus else "unavailable"
    except Exception:
        status["message_bus"] = "error"
    
    # Check context service
    status["context_service"] = "available" if _context_service else "unavailable"
    
    return status


# Global context service instance
_context_service: Optional[Any] = None 


# ---------------------------------------------------------------------------
# Effect Interpreter Initialization
# ---------------------------------------------------------------------------


async def _initialize_effect_interpreter() -> None:
    """Create (or retrieve) the singleton EffectInterpreter and initialise it.

    The interpreter is the authoritative executor for all `Effect` objects and
    must be running before any actor can rely on side-effects.  We keep this
    logic here (instead of in the bus) to avoid circular bootstrap issues and
    to respect the single-responsibility of the service initialiser.
    """

    try:
        from person_suit.core.effects.interpreter import get_effect_interpreter  # canonical helper

        interpreter = await get_effect_interpreter()

        # Guard against double-initialisation (idempotent)
        if not getattr(interpreter, "_running", False):
            await interpreter.initialize()

        logger.info("Effect interpreter initialised and running")

    except Exception as exc:  # noqa: BLE001 – propagate after logging
        logger.error("Failed to initialise Effect Interpreter: %s", exc)
        raise 