"""
Effect Interpreter Compatibility Shim
====================================

This module aligns the existing, fully-featured `EffectInterpreter` implementation
(in `interpreter.py`) with the Sprint-2 day-level checklist that expects a
`core/effects/effect_interpreter.py` file to exist.

Key responsibilities of this shim:
1. Re-export the canonical `EffectInterpreter` class so callers importing from
   `person_suit.core.effects.effect_interpreter` get the authoritative executor.
2. Introduce the `InterpreterStrategy` enum (IO, STATE, EXTERNAL_API) that will
   be used by upcoming Sprint-2 tasks to register concrete strategy handlers.
3. Make the singleton interpreter discoverable via the shared DI container
   without forcing eager asynchronous initialisation (registration stores the
   *class* reference only — callers should still obtain the live instance via
   `await get_effect_interpreter()` to avoid event-loop misuse).

NOTE:
This file intentionally keeps the surface minimal to satisfy the scaffolding
requirements (Day-1). Functional strategy registration and message-bus routing
will be implemented in subsequent Sprint-2 commits.
"""

from __future__ import annotations

import logging
from enum import Enum

from person_suit.core.di import DIContainer  # Lightweight import (sync-only)
from person_suit.core.effects.interpreter import EffectInterpreter  # Canonical implementation

logger = logging.getLogger(__name__)


class InterpreterStrategy(Enum):
    """Execution strategy categories for EffectInterpreter.

    The strategy determines *what kind* of side-effect is being executed rather
    than *how faithfully* (which is controlled by `EffectExecutionStrategy`).
    """

    IO = "io"
    STATE = "state"
    EXTERNAL_API = "external_api"


# ---------------------------------------------------------------------------
# Dependency-Injection registration (singleton reference, *lazy* instance)
# ---------------------------------------------------------------------------

try:
    # We register the *class*; callers can still await `get_effect_interpreter()`
    # to obtain the running instance. This avoids event-loop access at import
    # time while making the interpreter discoverable through DI look-ups.
    _global_di = DIContainer()
    if not _global_di.resolve_optional(EffectInterpreter):  # type: ignore[arg-type]
        _global_di.register(EffectInterpreter, EffectInterpreter)
except Exception as exc:  # pragma: no-cover – defensive, should never fail
    logger.debug("EffectInterpreter DI registration skipped: %s", exc)


__all__ = [
    "EffectInterpreter",
    "InterpreterStrategy",
] 