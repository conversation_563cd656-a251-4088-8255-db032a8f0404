"""
IO Effects - Declarative I/O Operations
======================================

This module defines I/O effects that declare file and I/O operations
without performing them. The EffectInterpreter handles execution.

Related Files:
- base.py: Base effect classes
- interpreter.py: Effect execution
- handlers/io.py: IO effect handler

Dependencies:
- Base effect system only
"""

from typing import Any
from typing import Dict
from typing import Optional
from typing import Union

from person_suit.core.constants.fixed_point_scale import SCALE

# Use absolute imports to avoid circular import issues
try:
    from .base import CacheableEffect
    from .base import Effect
    from .base import EffectType
    from .base import IdempotentEffect
except ImportError:
    # Fallback for direct module import
    from base import CacheableEffect
    from base import Effect
    from base import EffectType
    from base import IdempotentEffect


class IOEffect(Effect):
    """Base class for all I/O effects."""
    
    def __init__(
        self,
        operation: str,
        path: str,
        required_capability: Optional[str] = None,
        metadata: Optional[Dict[str, Any]] = None
    ):
        super().__init__(
            effect_type=EffectType.IO,
            operation=operation,
            required_capability=required_capability or f"io:{operation}",
            metadata=metadata
        )
        self.path = path


class ReadFileEffect(IOEffect, CacheableEffect, IdempotentEffect):
    """Effect for reading files."""
    
    def __init__(
        self,
        path: str,
        encoding: str = "utf-8",
        binary_mode: bool = False,
        chunk_size: Optional[int] = None,
        metadata: Optional[Dict[str, Any]] = None
    ):
        super().__init__(
            operation="read_file",
            path=path,
            required_capability=f"io:file:read:{path}",
            metadata=metadata
        )
        self.encoding = encoding
        self.binary_mode = binary_mode
        self.chunk_size = chunk_size
        
        # File reads are highly cacheable
        self.cache_ttl_seconds = 300  # 5 minutes
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary."""
        return {
            "type": "io",
            "operation": "read_file",
            "id": self.id,
            "timestamp": self.timestamp.isoformat(),
            "path": self.path,
            "encoding": self.encoding,
            "binary_mode": self.binary_mode,
            "chunk_size": self.chunk_size,
            "required_capability": self.required_capability,
            "metadata": self.metadata,
            "can_degrade": self.can_degrade,
            "min_fidelity": self.min_fidelity,
            "cache_ttl_seconds": self.cache_ttl_seconds
        }
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'ReadFileEffect':
        """Create from dictionary."""
        return cls(
            path=data["path"],
            encoding=data.get("encoding", "utf-8"),
            binary_mode=data.get("binary_mode", False),
            chunk_size=data.get("chunk_size"),
            metadata=data.get("metadata")
        )


class WriteFileEffect(IOEffect):
    """Effect for writing files."""
    
    def __init__(
        self,
        path: str,
        content: Union[str, bytes],
        encoding: str = "utf-8",
        binary_mode: bool = False,
        append_mode: bool = False,
        create_dirs: bool = True,
        metadata: Optional[Dict[str, Any]] = None
    ):
        super().__init__(
            operation="write_file",
            path=path,
            required_capability=f"io:file:write:{path}",
            metadata=metadata
        )
        self.content = content
        self.encoding = encoding
        self.binary_mode = binary_mode
        self.append_mode = append_mode
        self.create_dirs = create_dirs
        
        # Write operations cannot be degraded
        self.can_degrade = False
        self.min_fidelity = SCALE
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary."""
        return {
            "type": "io",
            "operation": "write_file",
            "id": self.id,
            "timestamp": self.timestamp.isoformat(),
            "path": self.path,
            "content": self.content,
            "encoding": self.encoding,
            "binary_mode": self.binary_mode,
            "append_mode": self.append_mode,
            "create_dirs": self.create_dirs,
            "required_capability": self.required_capability,
            "metadata": self.metadata,
            "can_degrade": self.can_degrade,
            "min_fidelity": self.min_fidelity,
            "cache_ttl_seconds": self.cache_ttl_seconds
        }
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'WriteFileEffect':
        """Create from dictionary."""
        return cls(
            path=data["path"],
            content=data["content"],
            encoding=data.get("encoding", "utf-8"),
            binary_mode=data.get("binary_mode", False),
            append_mode=data.get("append_mode", False),
            create_dirs=data.get("create_dirs", True),
            metadata=data.get("metadata")
        )


class DeleteFileEffect(IOEffect):
    """Effect for deleting files."""
    
    def __init__(
        self,
        path: str,
        recursive: bool = False,
        metadata: Optional[Dict[str, Any]] = None
    ):
        super().__init__(
            operation="delete_file",
            path=path,
            required_capability=f"io:file:delete:{path}",
            metadata=metadata
        )
        self.recursive = recursive
        
        # Delete operations cannot be degraded
        self.can_degrade = False
        self.min_fidelity = SCALE
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary."""
        return {
            "type": "io",
            "operation": "delete_file",
            "id": self.id,
            "timestamp": self.timestamp.isoformat(),
            "path": self.path,
            "recursive": self.recursive,
            "required_capability": self.required_capability,
            "metadata": self.metadata,
            "can_degrade": self.can_degrade,
            "min_fidelity": self.min_fidelity,
            "cache_ttl_seconds": self.cache_ttl_seconds
        }
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'DeleteFileEffect':
        """Create from dictionary."""
        return cls(
            path=data["path"],
            recursive=data.get("recursive", False),
            metadata=data.get("metadata")
        )


class ListDirectoryEffect(IOEffect, CacheableEffect, IdempotentEffect):
    """Effect for listing directory contents."""
    
    def __init__(
        self,
        path: str,
        recursive: bool = False,
        include_hidden: bool = False,
        pattern: Optional[str] = None,
        metadata: Optional[Dict[str, Any]] = None
    ):
        super().__init__(
            operation="list_directory",
            path=path,
            required_capability=f"io:directory:list:{path}",
            metadata=metadata
        )
        self.recursive = recursive
        self.include_hidden = include_hidden
        self.pattern = pattern
        
        # Directory listings are cacheable
        self.cache_ttl_seconds = 60  # 1 minute
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary."""
        return {
            "type": "io",
            "operation": "list_directory",
            "id": self.id,
            "timestamp": self.timestamp.isoformat(),
            "path": self.path,
            "recursive": self.recursive,
            "include_hidden": self.include_hidden,
            "pattern": self.pattern,
            "required_capability": self.required_capability,
            "metadata": self.metadata,
            "can_degrade": self.can_degrade,
            "min_fidelity": self.min_fidelity,
            "cache_ttl_seconds": self.cache_ttl_seconds
        }
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'ListDirectoryEffect':
        """Create from dictionary."""
        return cls(
            path=data["path"],
            recursive=data.get("recursive", False),
            include_hidden=data.get("include_hidden", False),
            pattern=data.get("pattern"),
            metadata=data.get("metadata")
        )


class CreateDirectoryEffect(IOEffect):
    """Effect for creating directories."""
    
    def __init__(
        self,
        path: str,
        parents: bool = True,
        exist_ok: bool = True,
        metadata: Optional[Dict[str, Any]] = None
    ):
        super().__init__(
            operation="create_directory",
            path=path,
            required_capability=f"io:directory:create:{path}",
            metadata=metadata
        )
        self.parents = parents
        self.exist_ok = exist_ok
        
        # Directory creation cannot be degraded
        self.can_degrade = False
        self.min_fidelity = SCALE
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary."""
        return {
            "type": "io",
            "operation": "create_directory",
            "id": self.id,
            "timestamp": self.timestamp.isoformat(),
            "path": self.path,
            "parents": self.parents,
            "exist_ok": self.exist_ok,
            "required_capability": self.required_capability,
            "metadata": self.metadata,
            "can_degrade": self.can_degrade,
            "min_fidelity": self.min_fidelity,
            "cache_ttl_seconds": self.cache_ttl_seconds
        }
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'CreateDirectoryEffect':
        """Create from dictionary."""
        return cls(
            path=data["path"],
            parents=data.get("parents", True),
            exist_ok=data.get("exist_ok", True),
            metadata=data.get("metadata")
        )


# Convenience functions for creating I/O effects
def read_file(
    path: str,
    encoding: str = "utf-8",
    binary_mode: bool = False
) -> ReadFileEffect:
    """Create a read file effect."""
    return ReadFileEffect(
        path=path,
        encoding=encoding,
        binary_mode=binary_mode
    )


def write_file(
    path: str,
    content: Union[str, bytes],
    encoding: str = "utf-8",
    append_mode: bool = False
) -> WriteFileEffect:
    """Create a write file effect."""
    return WriteFileEffect(
        path=path,
        content=content,
        encoding=encoding,
        append_mode=append_mode
    )


def delete_file(path: str, recursive: bool = False) -> DeleteFileEffect:
    """Create a delete file effect."""
    return DeleteFileEffect(path=path, recursive=recursive)


def list_directory(
    path: str,
    recursive: bool = False,
    pattern: Optional[str] = None
) -> ListDirectoryEffect:
    """Create a list directory effect."""
    return ListDirectoryEffect(
        path=path,
        recursive=recursive,
        pattern=pattern
    )


def create_directory(
    path: str,
    parents: bool = True,
    exist_ok: bool = True
) -> CreateDirectoryEffect:
    """Create a create directory effect."""
    return CreateDirectoryEffect(
        path=path,
        parents=parents,
        exist_ok=exist_ok
    ) 