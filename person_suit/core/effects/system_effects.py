"""person_suit.core.effects.system_effects

System-level effects for the foundation actors.

Purpose: Define declarative effects for system operations
Related Files: core.actors.foundation_actors
Dependencies: core.effects.base
"""

from dataclasses import dataclass
from typing import Any
from typing import Dict
from typing import Optional

from person_suit.core.effects.base import Effect


@dataclass
class ScheduleTask(Effect):
    """Effect to schedule a periodic task."""
    task_id: str
    interval_seconds: float
    effect: Optional[Effect] = None  # The effect to execute periodically
    required_capability: str = "system:schedule:write"

    def to_dict(self) -> Dict[str, Any]:
        return {
            "type": "schedule_task",
            "task_id": self.task_id,
            "interval_seconds": self.interval_seconds,
            "effect": self.effect.to_dict() if (self.effect and hasattr(self.effect, "to_dict")) else str(self.effect),
            "required_capability": self.required_capability,
        }

    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> "ScheduleTask":
        # NOTE: Effect deserialization is context dependent – use placeholder
        return cls(
            task_id=data["task_id"],
            interval_seconds=data["interval_seconds"],
            effect=data.get("effect"),  # Caller should replace with real Effect
        )


@dataclass
class MonitorResources(Effect):
    """Effect to monitor system resources."""
    resource_type: str  # "cpu", "memory", "disk", "network"
    threshold: Optional[float] = None
    required_capability: str = "system:monitor:read"

    def to_dict(self) -> Dict[str, Any]:
        return {
            "type": "monitor_resources",
            "resource_type": self.resource_type,
            "threshold": self.threshold,
            "required_capability": self.required_capability,
        }

    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> "MonitorResources":
        return cls(resource_type=data["resource_type"], threshold=data.get("threshold"))


@dataclass
class HarvestEnergy(Effect):
    """Effect to harvest energy from a source."""
    source_id: str
    source_type: str  # "ambient", "thermal", "motion", etc.
    config: Dict[str, Any]
    required_capability: str = "energy:harvest:write"

    def to_dict(self) -> Dict[str, Any]:
        return {
            "type": "harvest_energy",
            "source_id": self.source_id,
            "source_type": self.source_type,
            "config": self.config,
            "required_capability": self.required_capability,
        }

    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> "HarvestEnergy":
        return cls(
            source_id=data["source_id"],
            source_type=data["source_type"],
            config=data.get("config", {}),
        ) 