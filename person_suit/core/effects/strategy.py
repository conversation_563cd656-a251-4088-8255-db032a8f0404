"""Execution strategy primitives for EffectInterpreter.

This module realises CAW's dual *wave* / *particle* computation modes with
comprehensive quantum management, batching, and back-pressure handling.

Sprint 2 enhancements:
- 20ms particle quantum with demotion tracking
- 5ms wave buffer with cooperative yields
- Batch operation support for database handlers
- Performance metrics integration
"""
from __future__ import annotations

import asyncio
import enum
import logging
import time
from dataclasses import dataclass
from typing import Any
from typing import Callable
from typing import Dict
from typing import List
from typing import Optional
from typing import TypeVar

logger = logging.getLogger(__name__)

__all__ = [
    "ExecutionStrategy",
    "QuantumManager",
    "BatchEngine",
    "StrategyMetrics",
    "ParticleExecutor",
    "WaveExecutor"
]

T = TypeVar('T')


class ExecutionStrategy(enum.Enum):
    """High-level execution modes for effects."""

    PARTICLE = "particle"  # Urgent, latency-sensitive work – 20ms quantum
    WAVE = "wave"  # Background, batchable work – 5ms buffer + cooperative yield


@dataclass
class StrategyMetrics:
    """Metrics for execution strategy performance."""
    particle_executions: int = 0
    particle_demotions: int = 0
    wave_batches: int = 0
    wave_batch_size_total: int = 0
    high_latency_violations: int = 0
    
    @property
    def avg_wave_batch_size(self) -> float:
        """Calculate average wave batch size."""
        return self.wave_batch_size_total / max(1, self.wave_batches)


class QuantumManager:
    """Manages quantum-based execution for particle strategy."""
    
    PARTICLE_QUANTUM_MS = 20  # 20ms quantum for particle mode
    DEMOTION_THRESHOLD_MS = 200  # Demote if exceeding 200ms
    
    def __init__(self):
        self.metrics = StrategyMetrics()
    
    async def execute_with_quantum(
        self,
        coro: Callable[[], Any],
        effect_type: str = "unknown"
    ) -> tuple[Any, bool]:
        """Execute coroutine with quantum limits.
        
        Returns:
            (result, was_demoted): Tuple of execution result and demotion flag
        """
        start_time = time.time()
        self.metrics.particle_executions += 1
        
        try:
            # Execute with quantum timeout
            result = await asyncio.wait_for(
                coro(),
                timeout=self.PARTICLE_QUANTUM_MS / 1000.0
            )
            
            execution_time_ms = (time.time() - start_time) * 1000
            
            # Check for high latency violation
            if execution_time_ms > self.DEMOTION_THRESHOLD_MS:
                self.metrics.particle_demotions += 1
                self.metrics.high_latency_violations += 1
                
                # Emit demotion metric
                try:
                    from person_suit.monitoring.metrics import PARTICLE_DEMOTIONS_TOTAL
                    PARTICLE_DEMOTIONS_TOTAL.labels(effect_type=effect_type).inc()
                except ImportError:
                    pass
                
                logger.warning(
                    "Particle execution exceeded demotion threshold: %sms for %s",
                    execution_time_ms, effect_type
                )
                return result, True
            
            return result, False
            
        except asyncio.TimeoutError:
            # Quantum exceeded - demote to wave
            self.metrics.particle_demotions += 1
            logger.debug("Particle quantum exceeded for %s, demoting to wave", effect_type)
            
            # Re-execute without timeout for wave processing
            result = await coro()
            return result, True


class BatchEngine:
    """Manages batched execution for wave strategy."""
    
    WAVE_BUFFER_MS = 5  # 5ms buffer for wave batching
    MAX_BATCH_SIZE = 50  # Maximum effects per batch
    
    def __init__(self):
        self.metrics = StrategyMetrics()
        self._pending_effects: List[tuple[Callable, str]] = []
        self._batch_timer: Optional[asyncio.Task] = None
        self._batch_lock = asyncio.Lock()
    
    async def add_to_batch(self, coro: Callable[[], Any], effect_type: str = "unknown") -> Any:
        """Add effect to wave batch for cooperative execution."""
        async with self._batch_lock:
            self._pending_effects.append((coro, effect_type))
            
            # Start batch timer if not running
            if self._batch_timer is None or self._batch_timer.done():
                self._batch_timer = asyncio.create_task(self._process_batch_after_delay())
            
            # Force batch processing if we hit max size
            if len(self._pending_effects) >= self.MAX_BATCH_SIZE:
                if self._batch_timer and not self._batch_timer.done():
                    self._batch_timer.cancel()
                await self._process_batch()
    
    async def _process_batch_after_delay(self) -> None:
        """Process batch after wave buffer delay."""
        await asyncio.sleep(self.WAVE_BUFFER_MS / 1000.0)
        async with self._batch_lock:
            if self._pending_effects:
                await self._process_batch()
    
    async def _process_batch(self) -> None:
        """Process current batch of effects cooperatively."""
        if not self._pending_effects:
            return
        
        batch = self._pending_effects.copy()
        self._pending_effects.clear()
        
        self.metrics.wave_batches += 1
        self.metrics.wave_batch_size_total += len(batch)
        
        # Emit wave batch metrics
        try:
            from person_suit.monitoring.metrics import WAVE_BATCH_SIZE
            from person_suit.monitoring.metrics import WAVE_BATCHES_TOTAL
            WAVE_BATCHES_TOTAL.inc()
            WAVE_BATCH_SIZE.observe(len(batch))
        except ImportError:
            pass
        
        logger.debug("Processing wave batch with %d effects", len(batch))
        
        # Execute batch with cooperative yields
        for i, (coro, effect_type) in enumerate(batch):
            try:
                await coro()
                
                # Cooperative yield every 5 effects
                if i % 5 == 4:
                    await asyncio.sleep(0)  # Yield control
                    
            except Exception as e:
                logger.error("Wave batch effect failed: %s", e)
                continue


class ParticleExecutor:
    """Executor for particle strategy with quantum management."""
    
    def __init__(self):
        self.quantum_manager = QuantumManager()
    
    async def execute(self, coro: Callable[[], Any], effect_type: str = "unknown") -> Any:
        """Execute with particle strategy (20ms quantum)."""
        result, was_demoted = await self.quantum_manager.execute_with_quantum(coro, effect_type)
        
        if was_demoted:
            logger.debug("Effect %s was demoted from particle to wave execution", effect_type)
        
        return result
    
    def get_metrics(self) -> StrategyMetrics:
        """Get particle execution metrics."""
        return self.quantum_manager.metrics


class WaveExecutor:
    """Executor for wave strategy with batching."""
    
    def __init__(self):
        self.batch_engine = BatchEngine()
    
    async def execute(self, coro: Callable[[], Any], effect_type: str = "unknown") -> Any:
        """Execute with wave strategy (batched with 5ms buffer)."""
        return await self.batch_engine.add_to_batch(coro, effect_type)
    
    def get_metrics(self) -> StrategyMetrics:
        """Get wave execution metrics."""
        return self.batch_engine.metrics
    
    async def flush_pending(self) -> None:
        """Force flush any pending batch."""
        await self.batch_engine._process_batch()


# Global strategy executors
_particle_executor: Optional[ParticleExecutor] = None
_wave_executor: Optional[WaveExecutor] = None


def get_particle_executor() -> ParticleExecutor:
    """Get global particle executor instance."""
    global _particle_executor
    if _particle_executor is None:
        _particle_executor = ParticleExecutor()
    return _particle_executor


def get_wave_executor() -> WaveExecutor:
    """Get global wave executor instance."""
    global _wave_executor
    if _wave_executor is None:
        _wave_executor = WaveExecutor()
    return _wave_executor


def get_strategy_metrics() -> Dict[str, StrategyMetrics]:
    """Get metrics from all strategy executors."""
    return {
        "particle": get_particle_executor().get_metrics(),
        "wave": get_wave_executor().get_metrics()
    }