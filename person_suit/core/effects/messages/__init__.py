"""
Effect Message Types - Structured message definitions for all effect channels.

This module defines the message payload structures for all effect operations,
providing type safety and validation for the message-based effects system.

Related Files:
- ../channels.py: Effect channel definitions
- ../__init__.py: Message-based effects API
- ../actors/: Effect actor implementations

Dependencies:
- Core Python libraries only - no internal imports for maximum decoupling
"""

from dataclasses import dataclass
from dataclasses import field
from typing import Any
from typing import Dict
from typing import List
from typing import Op<PERSON>
from typing import Union

from person_suit.core.constants.fixed_point_scale import PRIO_CRITICAL
from person_suit.core.constants.fixed_point_scale import PRIO_HIGH
from person_suit.core.constants.fixed_point_scale import PRIO_LOW
from person_suit.core.constants.fixed_point_scale import PRIO_NORMAL

# Fixed-point scale
from person_suit.core.constants.fixed_point_scale import SCALE


# Base message types
@dataclass
class BaseEffectMessage:
    """Base class for all effect messages."""
    
    execution_id: str = ""
    timestamp: float = 0.0
    priority: int = PRIO_NORMAL  # 0..SCALE bucket
    timeout: float = 30.0
    wave_particle_ratio: float = 0.5


# I/O Effect Messages
@dataclass
class FileReadMessage(BaseEffectMessage):
    """Message for file read operations."""
    
    path: str = ""
    encoding: str = "utf-8"
    max_size: Optional[int] = None  # Max file size in bytes
    cache_enabled: bool = True


@dataclass
class FileWriteMessage(BaseEffectMessage):
    """Message for file write operations."""
    
    path: str = ""
    content: str = ""
    encoding: str = "utf-8"
    atomic: bool = True  # Use atomic write operations
    backup: bool = False  # Create backup before writing


@dataclass
class FileDeleteMessage(BaseEffectMessage):
    """Message for file delete operations."""
    
    path: str = ""
    recursive: bool = False  # For directories
    backup: bool = False  # Create backup before deletion


@dataclass
class NetworkRequestMessage(BaseEffectMessage):
    """Message for network request operations."""
    
    url: str = ""
    method: str = "GET"
    headers: Optional[Dict[str, str]] = None
    data: Optional[Union[str, bytes, Dict]] = None
    timeout: float = 30.0
    retries: int = 3
    verify_ssl: bool = True


# Database Effect Messages
@dataclass
class DatabaseQueryMessage(BaseEffectMessage):
    """Message for database query operations."""
    
    sql: str = ""
    params: List[Any] = field(default_factory=list)
    database: Optional[str] = None  # Database name
    connection_pool: Optional[str] = None
    transaction_id: Optional[str] = None


@dataclass
class DatabaseTransactionMessage(BaseEffectMessage):
    """Message for database transaction operations."""
    
    operations: List[Dict[str, Any]] = field(default_factory=list)
    isolation_level: str = "READ_COMMITTED"
    timeout: float = 60.0
    rollback_on_error: bool = True


@dataclass
class DatabaseMigrationMessage(BaseEffectMessage):
    """Message for database migration operations."""
    
    migration_id: str = ""
    sql_up: str = ""
    sql_down: Optional[str] = None  # Rollback migration SQL
    version: str = "1.0.0"
    description: str = ""


# State Effect Messages
@dataclass
class StateUpdateMessage(BaseEffectMessage):
    """Message for state update operations."""
    
    key: str = ""
    value: Any = None
    namespace: str = "default"
    ttl: Optional[float] = None  # Time to live in seconds
    atomic: bool = True


@dataclass
class StateSyncMessage(BaseEffectMessage):
    """Message for state synchronization operations."""
    
    namespace: str = "default"
    target_nodes: Optional[List[str]] = None  # Specific nodes to sync
    full_sync: bool = False  # Full state sync vs incremental


@dataclass
class StateSnapshotMessage(BaseEffectMessage):
    """Message for state snapshot operations."""
    
    namespace: str = "default"
    snapshot_path: str = ""
    compression: bool = True
    include_metadata: bool = True


@dataclass
class StateGetMessage(BaseEffectMessage):
    """Message for state retrieval operations."""
    
    key: str = ""
    namespace: str = "default"
    default_value: Any = None
    include_metadata: bool = False


# State Actor Messages - Direct state operations for central state actor
@dataclass
class StateCreateMessage(BaseEffectMessage):
    """Message for state actor create operations."""
    
    entity_id: str = ""
    entity_type: str = ""
    initial_data: Dict[str, Any] = field(default_factory=dict)
    metadata: Optional[Dict[str, Any]] = None


@dataclass
class StateUpdateActorMessage(BaseEffectMessage):
    """Message for state actor update operations."""
    
    entity_id: str = ""
    updates: Dict[str, Any] = field(default_factory=dict)
    merge_strategy: str = "deep"  # deep, shallow, replace
    version: Optional[str] = None


@dataclass
class StateDeleteMessage(BaseEffectMessage):
    """Message for state actor delete operations."""
    
    entity_id: str = ""
    soft_delete: bool = True
    backup_before_delete: bool = True


@dataclass
class StateReadMessage(BaseEffectMessage):
    """Message for state actor read operations."""
    
    entity_id: str = ""
    fields: Optional[List[str]] = None  # Specific fields to read
    include_metadata: bool = False
    consistency_level: str = "eventual"  # strong, eventual


# Memory Effect Messages
@dataclass
class MemoryStoreMessage(BaseEffectMessage):
    """Message for memory storage operations."""
    
    data: Any = None
    layer: str = "working"  # sensory, working, long_term
    importance: float = 0.5  # 0.0-1.0
    metadata: Optional[Dict[str, Any]] = None
    consolidation_strategy: str = "default"


@dataclass
class MemoryRetrieveMessage(BaseEffectMessage):
    """Message for memory retrieval operations."""
    
    query: Union[str, Dict[str, Any]] = ""
    layer: Optional[str] = None  # None = search all layers
    max_results: int = 10
    similarity_threshold: float = 0.7
    include_metadata: bool = True


@dataclass
class MemoryConsolidateMessage(BaseEffectMessage):
    """Message for memory consolidation operations."""
    
    source_layer: str = ""
    target_layer: str = ""
    consolidation_strategy: str = "importance_based"
    threshold: float = 0.8
    batch_size: int = 100


# Computation Effect Messages
@dataclass
class ComputeTaskMessage(BaseEffectMessage):
    """Message for computation task operations."""
    
    task_type: str = ""
    input_data: Any = None
    parameters: Dict[str, Any] = field(default_factory=dict)
    resource_requirements: Optional[Dict[str, Union[int, float]]] = None
    distributed: bool = False


@dataclass
class ComputeBatchMessage(BaseEffectMessage):
    """Message for batch computation operations."""
    
    tasks: List[Dict[str, Any]] = field(default_factory=list)
    batch_strategy: str = "parallel"  # parallel, sequential, adaptive
    resource_limit: Optional[Dict[str, Union[int, float]]] = None
    progress_callback: bool = False


@dataclass
class ComputeAnalyzeMessage(BaseEffectMessage):
    """Message for data analysis operations."""
    
    data: Any = None
    analysis_type: str = ""
    parameters: Dict[str, Any] = field(default_factory=dict)
    output_format: str = "json"
    fidelity_level: int = SCALE


# Security Effect Messages
@dataclass
class SecurityAuditMessage(BaseEffectMessage):
    """Message for security audit operations."""
    
    event_type: str = ""
    event_data: Dict[str, Any] = field(default_factory=dict)
    severity: str = "info"  # debug, info, warning, error, critical
    source: str = ""
    user_id: Optional[str] = None


@dataclass
class SecurityAuthorizeMessage(BaseEffectMessage):
    """Message for authorization operations."""
    
    operation: str = ""
    resource: str = ""
    capabilities: List[str] = field(default_factory=list)
    context_data: Dict[str, Any] = field(default_factory=dict)
    strict_mode: bool = True


# Monitoring Effect Messages
@dataclass
class MonitoringMetricMessage(BaseEffectMessage):
    """Message for metric collection operations."""
    
    metric_name: str = ""
    metric_value: Union[int, float] = 0
    metric_type: str = "gauge"  # gauge, counter, histogram
    tags: Optional[Dict[str, str]] = None
    timestamp: Optional[float] = None


@dataclass
class MonitoringTraceMessage(BaseEffectMessage):
    """Message for distributed tracing operations."""
    
    trace_id: str = ""
    span_id: str = ""
    operation_name: str = ""
    duration: Optional[float] = None
    tags: Optional[Dict[str, str]] = None
    logs: Optional[List[Dict[str, Any]]] = None


# Communication Effect Messages
@dataclass
class CommunicationMessage(BaseEffectMessage):
    """Message for inter-component communication."""
    
    target: str = ""
    content: Any = None
    message_type: str = "data"
    reply_expected: bool = False
    broadcast: bool = False


@dataclass
class CommunicationBroadcastMessage(BaseEffectMessage):
    """Message for broadcast communication."""
    
    targets: List[str] = field(default_factory=list)
    content: Any = None
    message_type: str = "broadcast"
    delivery_confirmation: bool = False


# Resource Effect Messages
@dataclass
class ResourceAllocateMessage(BaseEffectMessage):
    """Message for resource allocation operations."""
    
    resource_type: str = ""
    amount: Union[int, float] = 0
    duration: Optional[float] = None  # Allocation duration in seconds
    priority: str = PRIO_NORMAL
    constraints: Optional[Dict[str, Any]] = None


@dataclass
class ResourceReleaseMessage(BaseEffectMessage):
    """Message for resource release operations."""
    
    allocation_id: str = ""
    resource_type: str = ""
    force_release: bool = False


# Meta-System Effect Messages
@dataclass
class PersonaAnalyzeMessage(BaseEffectMessage):
    """Message for persona core analysis operations."""
    
    input_data: Any = None
    analysis_type: str = ""
    fidelity_level: int = SCALE
    context_depth: int = 3
    include_emotional_state: bool = True


@dataclass
class AnalystEvaluateMessage(BaseEffectMessage):
    """Message for analyst evaluation operations."""
    
    data: Any = None
    evaluation_type: str = ""
    patterns_to_detect: Optional[List[str]] = None
    confidence_threshold: int = int(0.7 * SCALE)
    include_recommendations: bool = True


@dataclass
class PredictorForecastMessage(BaseEffectMessage):
    """Message for predictor forecasting operations."""
    
    input_data: Any = None
    forecast_horizon: int = 0
    model_type: str = "neural"
    confidence_intervals: bool = True
    include_scenarios: bool = False


# Context Effect Messages
@dataclass
class ContextPropagateMessage(BaseEffectMessage):
    """Message for context propagation operations."""
    
    context_delta: Dict[str, Any] = field(default_factory=dict)
    target_scope: str = "global"  # global, local, specific
    targets: Optional[List[str]] = None
    merge_strategy: str = "deep"


@dataclass
class ContextMergeMessage(BaseEffectMessage):
    """Message for context merge operations."""
    
    contexts: List[Dict[str, Any]] = field(default_factory=list)
    merge_strategy: str = "priority_based"
    conflict_resolution: str = "latest_wins"
    preserve_history: bool = True


# Event Effect Messages
@dataclass
class EventEmitMessage(BaseEffectMessage):
    """Message for event emission operations."""
    
    event_type: str = ""
    event_data: Dict[str, Any] = field(default_factory=dict)
    targets: Optional[List[str]] = None
    persistent: bool = False


@dataclass
class EventSubscribeMessage(BaseEffectMessage):
    """Message for event subscription operations."""
    
    event_types: List[str] = field(default_factory=list)
    callback_channel: str = ""
    filter_criteria: Optional[Dict[str, Any]] = None
    subscription_id: Optional[str] = None


# Workflow Effect Messages
@dataclass
class WorkflowOrchestrateMessage(BaseEffectMessage):
    """Message for workflow orchestration operations."""
    
    workflow_definition: Dict[str, Any] = field(default_factory=dict)
    input_data: Dict[str, Any] = field(default_factory=dict)
    execution_strategy: str = "sequential"
    failure_handling: str = "stop_on_error"


@dataclass
class WorkflowComposeMessage(BaseEffectMessage):
    """Message for workflow composition operations."""
    
    effects: List[Dict[str, Any]] = field(default_factory=list)
    composition_type: str = "choreography"  # choreography, orchestration
    dependencies: Optional[Dict[str, List[str]]] = None
    parallel_execution: bool = True


# Message validation and serialization
class MessageValidator:
    """Validator for effect messages."""
    
    @staticmethod
    def validate_message(message: BaseEffectMessage) -> bool:
        """Validate message structure and content."""
        if not isinstance(message, BaseEffectMessage):
            return False
        
        # Check required fields
        if not hasattr(message, 'execution_id') or not message.execution_id:
            return False
        
        if not hasattr(message, 'timestamp') or message.timestamp <= 0:
            return False
        
        # Validate priority bucket range
        if not isinstance(message.priority, int) or not (0 <= message.priority <= SCALE):
            return False
        
        # Validate wave_particle_ratio
        if not 0.0 <= message.wave_particle_ratio <= 1.0:
            return False
        
        return True
    
    @staticmethod
    def serialize_message(message: BaseEffectMessage) -> Dict[str, Any]:
        """Serialize message to dictionary."""
        if not MessageValidator.validate_message(message):
            raise ValueError("Invalid message structure")
        
        # Convert dataclass to dict
        result = {}
        for field_name in message.__dataclass_fields__:
            value = getattr(message, field_name)
            if value is not None:
                result[field_name] = value
        
        return result
    
    @staticmethod
    def deserialize_message(data: Dict[str, Any], message_type: type) -> BaseEffectMessage:
        """Deserialize dictionary to message."""
        if not issubclass(message_type, BaseEffectMessage):
            raise ValueError("Invalid message type")
        
        return message_type(**data)


# Message type registry for dynamic message creation
MESSAGE_TYPE_REGISTRY = {
    # I/O Messages
    "effects.io.file.read": FileReadMessage,
    "effects.io.file.write": FileWriteMessage,
    "effects.io.file.delete": FileDeleteMessage,
    "effects.io.network.request": NetworkRequestMessage,
    
    # Database Messages
    "effects.db.query": DatabaseQueryMessage,
    "effects.db.transaction": DatabaseTransactionMessage,
    "effects.db.migration": DatabaseMigrationMessage,
    
    # State Messages
    "effects.state.update": StateUpdateMessage,
    "effects.state.sync": StateSyncMessage,
    "effects.state.snapshot": StateSnapshotMessage,
    "effects.state.get": StateGetMessage,
    
    # State Actor Messages (direct operations)
    "state_create": StateCreateMessage,
    "state_update": StateUpdateActorMessage,
    "state_delete": StateDeleteMessage,
    "state_read": StateReadMessage,
    
    # Memory Messages
    "effects.memory.store": MemoryStoreMessage,
    "effects.memory.retrieve": MemoryRetrieveMessage,
    "effects.memory.consolidate": MemoryConsolidateMessage,
    
    # Computation Messages
    "effects.compute.task": ComputeTaskMessage,
    "effects.compute.batch": ComputeBatchMessage,
    "effects.compute.analyze": ComputeAnalyzeMessage,
    
    # Security Messages
    "effects.security.audit": SecurityAuditMessage,
    "effects.security.authorize": SecurityAuthorizeMessage,
    
    # Monitoring Messages
    "effects.monitoring.metric": MonitoringMetricMessage,
    "effects.monitoring.trace": MonitoringTraceMessage,
    
    # Communication Messages
    "effects.communication.message": CommunicationMessage,
    "effects.communication.broadcast": CommunicationBroadcastMessage,
    
    # Resource Messages
    "effects.resource.allocate": ResourceAllocateMessage,
    "effects.resource.release": ResourceReleaseMessage,
    
    # Meta-System Messages
    "effects.persona.analyze": PersonaAnalyzeMessage,
    "effects.analyst.evaluate": AnalystEvaluateMessage,
    "effects.predictor.forecast": PredictorForecastMessage,
    
    # Context Messages
    "effects.context.propagate": ContextPropagateMessage,
    "effects.context.merge": ContextMergeMessage,
    
    # Event Messages
    "effects.event.emit": EventEmitMessage,
    "effects.event.subscribe": EventSubscribeMessage,
    
    # Workflow Messages
    "effects.workflow.orchestrate": WorkflowOrchestrateMessage,
    "effects.workflow.compose": WorkflowComposeMessage,
}


def get_message_type(channel: str) -> Optional[type]:
    """Get message type for effect channel."""
    return MESSAGE_TYPE_REGISTRY.get(channel)


def create_message(channel: str, data: Dict[str, Any]) -> BaseEffectMessage:
    """Create typed message for channel."""
    message_type = get_message_type(channel)
    if not message_type:
        raise ValueError(f"No message type registered for channel: {channel}")
    
    return MessageValidator.deserialize_message(data, message_type)


# Export all message types
__all__ = [
    # Base types
    "BaseEffectMessage",
    "MessageValidator",
    
    # I/O Messages
    "FileReadMessage",
    "FileWriteMessage", 
    "FileDeleteMessage",
    "NetworkRequestMessage",
    
    # Database Messages
    "DatabaseQueryMessage",
    "DatabaseTransactionMessage",
    "DatabaseMigrationMessage",
    
    # State Messages
    "StateUpdateMessage",
    "StateSyncMessage",
    "StateSnapshotMessage",
    "StateGetMessage",
    
    # State Actor Messages
    "StateCreateMessage",
    "StateUpdateActorMessage",
    "StateDeleteMessage",
    "StateReadMessage",
    
    # Memory Messages
    "MemoryStoreMessage",
    "MemoryRetrieveMessage",
    "MemoryConsolidateMessage",
    
    # Computation Messages
    "ComputeTaskMessage",
    "ComputeBatchMessage",
    "ComputeAnalyzeMessage",
    
    # Security Messages
    "SecurityAuditMessage",
    "SecurityAuthorizeMessage",
    
    # Monitoring Messages
    "MonitoringMetricMessage",
    "MonitoringTraceMessage",
    
    # Communication Messages
    "CommunicationMessage",
    "CommunicationBroadcastMessage",
    
    # Resource Messages
    "ResourceAllocateMessage",
    "ResourceReleaseMessage",
    
    # Meta-System Messages
    "PersonaAnalyzeMessage",
    "AnalystEvaluateMessage", 
    "PredictorForecastMessage",
    
    # Context Messages
    "ContextPropagateMessage",
    "ContextMergeMessage",
    
    # Event Messages
    "EventEmitMessage",
    "EventSubscribeMessage",
    
    # Workflow Messages
    "WorkflowOrchestrateMessage",
    "WorkflowComposeMessage",
    
    # Utilities
    "MESSAGE_TYPE_REGISTRY",
    "get_message_type",
    "create_message",
] 