"""strategies.py
Default Effect execution strategies used by the EffectInterpreter registry.

Sprint-2 introduces a new layer of indirection – *strategies* – that map
high-level effect *categories* (IO, STATE, EXTERNAL_API, …) to concrete async
routines.  For the MVP we provide minimal placeholder implementations that
simply return `NOT_IMPLEMENTED` so that legacy code paths continue to work
while we migrate logic out of actor classes.
"""

from __future__ import annotations

import asyncio
import hashlib
import logging
import time
from pathlib import Path
from typing import Any

from person_suit.core.context.unified import UnifiedContext
from person_suit.core.effects.base import Effect
from person_suit.core.effects.base import EffectResult
from person_suit.core.effects.base import EffectType
from person_suit.core.effects.io_effects import ReadFileEffect
from person_suit.core.effects.io_effects import WriteFileEffect
from person_suit.core.effects.monitoring_effects import CheckHealth
from person_suit.core.effects.monitoring_effects import CollectMetrics
from person_suit.core.effects.monitoring_effects import DetectAnomaly
from person_suit.core.effects.monitoring_effects import UpdateDashboard
from person_suit.core.effects.state_effects import StateDeleteEffect
from person_suit.core.effects.state_effects import StateQueryEffect
from person_suit.core.effects.state_effects import StateReadEffect
from person_suit.core.effects.state_effects import StateSnapshotEffect
from person_suit.core.effects.state_effects import StateUpdateEffect
from person_suit.core.effects.system_effects import HarvestEnergy
from person_suit.core.effects.system_effects import MonitorResources
from person_suit.core.effects.system_effects import ScheduleTask

# In-memory store for STATE effect strategy (module-level singleton)
_STATE_STORE: dict[str, Any] = {}

logger = logging.getLogger(__name__)

async def io_effect_strategy(effect: Effect, context: UnifiedContext | None = None) -> EffectResult:  # noqa: D401
    """Handle basic file IO effects synchronously inside a thread.

    Currently supports *ReadFileEffect* and *WriteFileEffect*; all other IO
    effects fall back to *NOT_IMPLEMENTED*.
    """

    start_ts = time.time()

    try:
        if isinstance(effect, WriteFileEffect):
            path_obj = Path(effect.path)

            if effect.create_dirs:
                path_obj.parent.mkdir(parents=True, exist_ok=True)

            mode = "ab" if effect.append_mode else "wb" if effect.binary_mode else "a" if effect.append_mode else "w"

            # Convert content based on binary/text mode
            content = effect.content
            if isinstance(content, str) and "b" in mode:
                content = content.encode(effect.encoding)

            def _write():  # noqa: WPS430 – inner helper
                with open(path_obj, mode) as fptr:
                    fptr.write(content)

            await asyncio.to_thread(_write)

            return EffectResult(
                success=True,
                value=str(path_obj),
                execution_time_ms=(time.time() - start_ts) * 1000,
            )

        if isinstance(effect, ReadFileEffect):
            path_obj = Path(effect.path)

            if not path_obj.exists():
                return EffectResult(success=False, error="file_not_found")

            mode = "rb" if effect.binary_mode else "r"

            def _read():  # noqa: WPS430
                with open(path_obj, mode) as fptr:
                    data = fptr.read()
                if not effect.binary_mode and isinstance(data, str):
                    return data
                if effect.binary_mode and isinstance(data, bytes):
                    return data
                return data  # type: ignore[return-value]

            content = await asyncio.to_thread(_read)

            return EffectResult(
                success=True,
                value=content,
                execution_time_ms=(time.time() - start_ts) * 1000,
            )

        # Unsupported IO effect for now
        return EffectResult(success=False, error="IOEffectStrategy NOT_IMPLEMENTED")

    except Exception as exc:  # pragma: no cover – unexpected errors
        return EffectResult(success=False, error=str(exc))


async def state_effect_strategy(effect: Effect, context: UnifiedContext | None = None) -> EffectResult:  # noqa: D401
    """In-memory key-value state operations (MVP).

    Supports `StateUpdateEffect`, `StateReadEffect`, `StateDeleteEffect`, and `StateQueryEffect`.
    Uses a module-level `_STATE_STORE` dict as backing store; in future this will migrate to a
    differential dataflow engine with persistent backing.
    """

    start_ts = time.time()

    # Shared store (simple dict)
    global _STATE_STORE  # noqa: WPS420 – intentional global cache

    if isinstance(effect, StateUpdateEffect):
        # Provide version & provenance hash for each update
        prev_entry = _STATE_STORE.get(effect.key)
        version = (prev_entry.get("version", 0) + 1) if isinstance(prev_entry, dict) else 1

        provenance_hash = hashlib.sha256(repr(effect.value).encode()).hexdigest()

        entry = {
            "value": effect.value,
            "version": version,
            "provenance": provenance_hash,
            "updated_at": time.time(),
        }

        _STATE_STORE[effect.key] = entry

        return EffectResult(
            success=True,
            value=entry,
            metadata={"version": version, "provenance": provenance_hash},
            execution_time_ms=(time.time() - start_ts) * 1000,
        )

    if isinstance(effect, StateReadEffect):
        if effect.key not in _STATE_STORE:
            return EffectResult(success=False, error="key_not_found")
        return EffectResult(
            success=True,
            value=_STATE_STORE[effect.key],
            execution_time_ms=(time.time() - start_ts) * 1000,
        )

    if isinstance(effect, StateDeleteEffect):
        existed = _STATE_STORE.pop(effect.key, None)
        return EffectResult(
            success=existed is not None,
            value=existed,
            execution_time_ms=(time.time() - start_ts) * 1000,
        )

    if isinstance(effect, StateQueryEffect):
        import re
        regex = re.compile(effect.pattern)
        matches = {k: v for k, v in _STATE_STORE.items() if regex.search(k)}
        return EffectResult(success=True, value=matches, execution_time_ms=(time.time() - start_ts) * 1000)

    if isinstance(effect, StateSnapshotEffect):
        return EffectResult(success=True, value=_STATE_STORE.copy(), execution_time_ms=(time.time() - start_ts) * 1000)

    return EffectResult(success=False, error="Unsupported STATE effect")


# ==============================================================================
# Monitoring Effect Strategies
# ==============================================================================

async def execute_collect_metrics(effect: CollectMetrics, context: UnifiedContext) -> EffectResult:
    """Execute metrics collection effect."""
    try:
        logger.info(f"Collecting metrics for: {effect.collector_name}")
        
        # In a real implementation, this would interact with the metrics system
        # For now, we'll simulate the collection
        metrics_data = {
            "collector": effect.collector_name,
            "type": effect.metrics_type,
            "timestamp": asyncio.get_event_loop().time(),
            "values": {
                "cpu_usage": 45.2,
                "memory_usage": 62.1,
                "queue_depth": 12,
            }
        }
        
        # Simulate some async work
        await asyncio.sleep(0.01)
        
        return EffectResult(
            success=True,
            value=metrics_data,
            metadata={"collector": effect.collector_name}
        )
    except Exception as e:
        logger.error(f"Failed to collect metrics: {e}")
        return EffectResult(success=False, error=str(e))


async def execute_check_health(effect: CheckHealth, context: UnifiedContext) -> EffectResult:
    """Execute health check effect."""
    try:
        logger.info(f"Checking health for: {effect.check_id}")
        
        # Simulate health check
        health_status = {
            "check_id": effect.check_id,
            "component": effect.component,
            "status": "healthy",
            "timestamp": asyncio.get_event_loop().time(),
        }
        
        await asyncio.sleep(0.01)
        
        return EffectResult(
            success=True,
            value=health_status,
            metadata={"check_id": effect.check_id}
        )
    except Exception as e:
        logger.error(f"Failed to check health: {e}")
        return EffectResult(success=False, error=str(e))


async def execute_detect_anomaly(effect: DetectAnomaly, context: UnifiedContext) -> EffectResult:
    """Execute anomaly detection effect."""
    try:
        logger.info(f"Detecting anomalies for: {effect.detector_name}")
        
        # Simulate anomaly detection
        anomaly_result = {
            "detector_name": effect.detector_name,
            "data": effect.data,
            "threshold": effect.threshold,
            "anomalies_found": False,
            "confidence": 0.95,
            "timestamp": asyncio.get_event_loop().time(),
        }
        
        await asyncio.sleep(0.02)  # Anomaly detection might take longer
        
        return EffectResult(
            success=True,
            value=anomaly_result,
            metadata={"detector_name": effect.detector_name}
        )
    except Exception as e:
        logger.error(f"Failed to detect anomalies: {e}")
        return EffectResult(success=False, error=str(e))


async def execute_update_dashboard(effect: UpdateDashboard, context: UnifiedContext) -> EffectResult:
    """Execute dashboard update effect."""
    try:
        logger.info(f"Updating dashboard: {effect.dashboard_id}")
        
        # Simulate dashboard update
        update_result = {
            "dashboard_id": effect.dashboard_id,
            "data_points": len(effect.data),
            "timestamp": asyncio.get_event_loop().time(),
        }
        
        await asyncio.sleep(0.005)  # Dashboard updates should be fast
        
        return EffectResult(
            success=True,
            value=update_result,
            metadata={"dashboard_id": effect.dashboard_id}
        )
    except Exception as e:
        logger.error(f"Failed to update dashboard: {e}")
        return EffectResult(success=False, error=str(e))


# ==============================================================================
# System Effect Strategies
# ==============================================================================

async def execute_schedule_task(effect: ScheduleTask, context: UnifiedContext) -> EffectResult:
    """Execute task scheduling effect."""
    try:
        logger.info(f"Scheduling task: {effect.task_id}")
        
        # In a real implementation, this would interact with the scheduler
        schedule_result = {
            "task_id": effect.task_id,
            "interval_seconds": effect.interval_seconds,
            "scheduled": True,
            "next_run": asyncio.get_event_loop().time() + effect.interval_seconds,
        }
        
        await asyncio.sleep(0.001)  # Scheduling should be very fast
        
        return EffectResult(
            success=True,
            value=schedule_result,
            metadata={"task_id": effect.task_id}
        )
    except Exception as e:
        logger.error(f"Failed to schedule task: {e}")
        return EffectResult(success=False, error=str(e))


async def execute_monitor_resources(effect: MonitorResources, context: UnifiedContext) -> EffectResult:
    """Execute resource monitoring effect."""
    try:
        logger.info(f"Monitoring resources: {effect.resource_type}")
        
        # Simulate resource monitoring
        resource_data = {
            "resource_type": effect.resource_type,
            "usage": 67.8,
            "available": 32.2,
            "timestamp": asyncio.get_event_loop().time(),
        }
        
        await asyncio.sleep(0.01)
        
        return EffectResult(
            success=True,
            value=resource_data,
            metadata={"resource_type": effect.resource_type}
        )
    except Exception as e:
        logger.error(f"Failed to monitor resources: {e}")
        return EffectResult(success=False, error=str(e))


async def execute_harvest_energy(effect: HarvestEnergy, context: UnifiedContext) -> EffectResult:
    """Execute energy harvesting effect."""
    try:
        logger.info(f"Harvesting energy from: {effect.source_id}")
        
        # Simulate energy harvesting
        harvest_result = {
            "source_id": effect.source_id,
            "source_type": effect.source_type,
            "energy_harvested": 42.0,  # Arbitrary units
            "timestamp": asyncio.get_event_loop().time(),
        }
        
        await asyncio.sleep(0.02)  # Energy harvesting might be slower
        
        return EffectResult(
            success=True,
            value=harvest_result,
            metadata={"source_id": effect.source_id}
        )
    except Exception as e:
        logger.error(f"Failed to harvest energy: {e}")
        return EffectResult(success=False, error=str(e))


# ==============================================================================
# Strategy Registry
# ==============================================================================

# Map effect types to their execution strategies
MONITORING_STRATEGIES = {
    CollectMetrics: execute_collect_metrics,
    CheckHealth: execute_check_health,
    DetectAnomaly: execute_detect_anomaly,
    UpdateDashboard: execute_update_dashboard,
}

SYSTEM_STRATEGIES = {
    ScheduleTask: execute_schedule_task,
    MonitorResources: execute_monitor_resources,
    HarvestEnergy: execute_harvest_energy,
}

# Combined registry for all strategies
ALL_STRATEGIES = {
    **MONITORING_STRATEGIES,
    **SYSTEM_STRATEGIES,
}

# Default strategies by effect type (for backward compatibility)
DEFAULT_STRATEGIES = {
    EffectType.IO: None,  # Handled by existing IOEffectHandler
    EffectType.STATE: None,  # Handled by existing StateEffectHandler
}

# =====================================================================
# Database Effect Strategy (Async stub)
# =====================================================================

from person_suit.core.effects.database import DeleteDatabaseEffect
from person_suit.core.effects.database import ReadDatabaseEffect
from person_suit.core.effects.database import TransactionEffect
from person_suit.core.effects.database import WriteDatabaseEffect


async def database_effect_strategy(effect: Effect, context: UnifiedContext | None = None) -> EffectResult:  # noqa: D401
    """Handle database CRUD effects.

    NOTE: This is an MVP stub that **does not** perform real I/O.  It
    maintains CAW compliance by staying async and returning an EffectResult
    without spawning blocking threads.  A future implementation will call an
    async DB driver (e.g. asyncpg) here.
    """

    start_ts = time.time()

    logger.info("Database strategy received %s", type(effect).__name__)

    # TODO: Replace with real async DB adapter
    await asyncio.sleep(0)  # Yield control to event loop (non-blocking)

    return EffectResult(
        success=False,
        error="DATABASE_STRATEGY_NOT_IMPLEMENTED",
        metadata={"effect_class": type(effect).__name__},
        execution_time_ms=(time.time() - start_ts) * 1000,
    )


# =====================================================================
# Computation Effect Strategy (CPU-bound via executor)
# =====================================================================

from person_suit.core.effects.computation import ComputationEffect
from person_suit.core.effects.computation import ProcessTextEffect


async def computation_effect_strategy(effect: Effect, context: UnifiedContext | None = None) -> EffectResult:  # noqa: D401
    """Execute CPU-heavy computations using default executor in a CAW-safe way."""

    loop = asyncio.get_running_loop()
    start_ts = time.time()

    try:
        if isinstance(effect, ProcessTextEffect):
            # Simple word-count demo instead of heavyweight NLP model
            def _word_count() -> dict[str, int]:
                from collections import Counter
                return Counter(effect.text.split())

            result = await loop.run_in_executor(None, _word_count)
            return EffectResult(
                success=True,
                value=result,
                execution_time_ms=(time.time() - start_ts) * 1000,
            )

        # Generic computation placeholder
        await asyncio.sleep(0)
        return EffectResult(success=False, error="COMPUTATION_NOT_IMPLEMENTED")

    except Exception as exc:  # pragma: no cover
        return EffectResult(success=False, error=str(exc))


# ----------------------------------------------------------------------
# Extend strategy registry
# ----------------------------------------------------------------------

ALL_STRATEGIES.update(
    {
        ReadDatabaseEffect: database_effect_strategy,
        WriteDatabaseEffect: database_effect_strategy,
        DeleteDatabaseEffect: database_effect_strategy,
        TransactionEffect: database_effect_strategy,
        ComputationEffect: computation_effect_strategy,
        ProcessTextEffect: computation_effect_strategy,
    }
)

# =====================================================================
# Chat Effect Strategy (bus relay)
# =====================================================================

from person_suit.io_layer.adapters.chat.effects_adapter import ChatReceiveMessageEffect
from person_suit.io_layer.adapters.chat.effects_adapter import ChatSendMessageEffect


async def chat_effect_strategy(effect: Effect, context: UnifiedContext | None = None) -> EffectResult:  # noqa: D401
    """Relay chat effects through the Hybrid Message Bus.

    For Send: publish a command to the chat gateway channel.
    For Receive: this is usually generated by the gateway; interpreter just ACKs.
    """

    start_ts = time.time()

    # MVP stub – no I/O; will integrate with ChatGatewayActor later
    if isinstance(effect, (ChatSendMessageEffect, ChatReceiveMessageEffect)):
        return EffectResult(
            success=False,
            error="CHAT_STRATEGY_NOT_IMPLEMENTED",
            execution_time_ms=(time.time() - start_ts) * 1000,
        )

    return EffectResult(success=False, error="CHAT_EFFECT_UNSUPPORTED")


# Extend registry
ALL_STRATEGIES.update(
    {
        ChatSendMessageEffect: chat_effect_strategy,
        ChatReceiveMessageEffect: chat_effect_strategy,
    }
)

__all__ = [
    "io_effect_strategy",
    "state_effect_strategy",
    "DEFAULT_STRATEGIES",
] 