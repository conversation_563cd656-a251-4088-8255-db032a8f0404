"""
Event Effects - Declarative Event Operations
===========================================

This module defines event effects that declare event operations
without performing them. The EffectInterpreter handles execution.

Related Files:
- base.py: Base effect classes
- interpreter.py: Effect execution
- handlers/event.py: Event effect handler

Dependencies:
- Base effect system only
"""

from typing import Any
from typing import Dict
from typing import List
from typing import Optional

from person_suit.core.constants.fixed_point_scale import FIDELITY_LOW

# Fixed-point priority buckets
from person_suit.core.constants.fixed_point_scale import PRIO_LOW
from person_suit.core.constants.fixed_point_scale import SCALE

# Use absolute imports to avoid circular import issues
try:
    from .base import Effect
    from .base import EffectType
    from .base import IdempotentEffect
except ImportError:
    # Fallback for direct module import
    from base import Effect
    from base import EffectType
    from base import IdempotentEffect


class EventEffect(Effect):
    """Base class for all event effects."""
    
    def __init__(
        self,
        operation: str,
        channel: str,
        required_capability: Optional[str] = None,
        metadata: Optional[Dict[str, Any]] = None
    ):
        super().__init__(
            effect_type=EffectType.EVENT,
            operation=operation,
            required_capability=required_capability or f"event:{channel}:{operation}",
            metadata=metadata
        )
        self.channel = channel


class PublishEventEffect(EventEffect, IdempotentEffect):
    """Effect for publishing events to the message bus."""
    
    def __init__(
        self,
        channel: str,
        payload: Dict[str, Any],
        event_type: str = "generic",
        priority: int = PRIO_LOW,
        correlation_id: Optional[str] = None,
        reply_to: Optional[str] = None,
        metadata: Optional[Dict[str, Any]] = None
    ):
        super().__init__(
            operation="publish",
            channel=channel,
            required_capability=f"event:{channel}:publish",
            metadata=metadata
        )
        self.payload = payload
        self.event_type = event_type
        self.priority = priority
        self.correlation_id = correlation_id
        self.reply_to = reply_to
        
        # Events can be degraded by reducing payload detail
        self.can_degrade = True
        self.min_fidelity = FIDELITY_LOW
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary."""
        return {
            "type": "event",
            "operation": "publish",
            "id": self.id,
            "timestamp": self.timestamp.isoformat(),
            "channel": self.channel,
            "payload": self.payload,
            "event_type": self.event_type,
            "priority": self.priority,
            "correlation_id": self.correlation_id,
            "reply_to": self.reply_to,
            "required_capability": self.required_capability,
            "metadata": self.metadata,
            "can_degrade": self.can_degrade,
            "min_fidelity": self.min_fidelity,
            "cache_ttl_seconds": self.cache_ttl_seconds
        }
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'PublishEventEffect':
        """Create from dictionary."""
        return cls(
            channel=data["channel"],
            payload=data["payload"],
            event_type=data.get("event_type", "generic"),
            priority=data.get("priority", PRIO_LOW),
            correlation_id=data.get("correlation_id"),
            reply_to=data.get("reply_to"),
            metadata=data.get("metadata")
        )


class SubscribeEventEffect(EventEffect):
    """Effect for subscribing to events."""
    
    def __init__(
        self,
        channel_pattern: str,
        handler_id: str,
        filter_criteria: Optional[Dict[str, Any]] = None,
        metadata: Optional[Dict[str, Any]] = None
    ):
        super().__init__(
            operation="subscribe",
            channel=channel_pattern,
            required_capability=f"event:{channel_pattern}:subscribe",
            metadata=metadata
        )
        self.channel_pattern = channel_pattern
        self.handler_id = handler_id
        self.filter_criteria = filter_criteria or {}
        
        # Subscriptions cannot be degraded
        self.can_degrade = False
        self.min_fidelity = SCALE
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary."""
        return {
            "type": "event",
            "operation": "subscribe",
            "id": self.id,
            "timestamp": self.timestamp.isoformat(),
            "channel_pattern": self.channel_pattern,
            "handler_id": self.handler_id,
            "filter_criteria": self.filter_criteria,
            "required_capability": self.required_capability,
            "metadata": self.metadata,
            "can_degrade": self.can_degrade,
            "min_fidelity": self.min_fidelity,
            "cache_ttl_seconds": self.cache_ttl_seconds
        }
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'SubscribeEventEffect':
        """Create from dictionary."""
        return cls(
            channel_pattern=data["channel_pattern"],
            handler_id=data["handler_id"],
            filter_criteria=data.get("filter_criteria"),
            metadata=data.get("metadata")
        )


class UnsubscribeEventEffect(EventEffect):
    """Effect for unsubscribing from events."""
    
    def __init__(
        self,
        channel_pattern: str,
        handler_id: str,
        metadata: Optional[Dict[str, Any]] = None
    ):
        super().__init__(
            operation="unsubscribe",
            channel=channel_pattern,
            required_capability=f"event:{channel_pattern}:unsubscribe",
            metadata=metadata
        )
        self.channel_pattern = channel_pattern
        self.handler_id = handler_id
        
        # Unsubscriptions cannot be degraded
        self.can_degrade = False
        self.min_fidelity = SCALE
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary."""
        return {
            "type": "event",
            "operation": "unsubscribe",
            "id": self.id,
            "timestamp": self.timestamp.isoformat(),
            "channel_pattern": self.channel_pattern,
            "handler_id": self.handler_id,
            "required_capability": self.required_capability,
            "metadata": self.metadata,
            "can_degrade": self.can_degrade,
            "min_fidelity": self.min_fidelity,
            "cache_ttl_seconds": self.cache_ttl_seconds
        }
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'UnsubscribeEventEffect':
        """Create from dictionary."""
        return cls(
            channel_pattern=data["channel_pattern"],
            handler_id=data["handler_id"],
            metadata=data.get("metadata")
        )


class BroadcastEventEffect(EventEffect):
    """Effect for broadcasting events to all subscribers."""
    
    def __init__(
        self,
        payload: Dict[str, Any],
        event_type: str = "broadcast",
        exclude_channels: Optional[List[str]] = None,
        metadata: Optional[Dict[str, Any]] = None
    ):
        super().__init__(
            operation="broadcast",
            channel="*",
            required_capability="event:*:broadcast",
            metadata=metadata
        )
        self.payload = payload
        self.event_type = event_type
        self.exclude_channels = exclude_channels or []
        
        # Broadcasts can be degraded
        self.can_degrade = True
        self.min_fidelity = int(0.3 * SCALE)
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary."""
        return {
            "type": "event",
            "operation": "broadcast",
            "id": self.id,
            "timestamp": self.timestamp.isoformat(),
            "payload": self.payload,
            "event_type": self.event_type,
            "exclude_channels": self.exclude_channels,
            "required_capability": self.required_capability,
            "metadata": self.metadata,
            "can_degrade": self.can_degrade,
            "min_fidelity": self.min_fidelity,
            "cache_ttl_seconds": self.cache_ttl_seconds
        }
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'BroadcastEventEffect':
        """Create from dictionary."""
        return cls(
            payload=data["payload"],
            event_type=data.get("event_type", "broadcast"),
            exclude_channels=data.get("exclude_channels"),
            metadata=data.get("metadata")
        )


# Convenience functions for creating event effects
def publish_event(
    channel: str,
    payload: Dict[str, Any],
    event_type: str = "generic",
    priority: int = PRIO_LOW
) -> PublishEventEffect:
    """Create a publish event effect."""
    return PublishEventEffect(
        channel=channel,
        payload=payload,
        event_type=event_type,
        priority=priority
    )


def subscribe_to_events(
    channel_pattern: str,
    handler_id: str,
    filter_criteria: Optional[Dict[str, Any]] = None
) -> SubscribeEventEffect:
    """Create a subscribe event effect."""
    return SubscribeEventEffect(
        channel_pattern=channel_pattern,
        handler_id=handler_id,
        filter_criteria=filter_criteria
    )


def unsubscribe_from_events(
    channel_pattern: str,
    handler_id: str
) -> UnsubscribeEventEffect:
    """Create an unsubscribe event effect."""
    return UnsubscribeEventEffect(
        channel_pattern=channel_pattern,
        handler_id=handler_id
    )


def broadcast_event(
    payload: Dict[str, Any],
    event_type: str = "broadcast",
    exclude_channels: Optional[List[str]] = None
) -> BroadcastEventEffect:
    """Create a broadcast event effect."""
    return BroadcastEventEffect(
        payload=payload,
        event_type=event_type,
        exclude_channels=exclude_channels
    ) 