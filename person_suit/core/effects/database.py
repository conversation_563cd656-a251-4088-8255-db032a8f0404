"""
Database Effects - Declarative Database Operations
=================================================

This module defines database effects that declare database operations
without performing them. The EffectInterpreter handles execution.

Related Files:
- base.py: Base effect classes
- interpreter.py: Effect execution
- handlers/database.py: Database effect handler

Dependencies:
- Base effect system only
"""

from typing import Any
from typing import Dict
from typing import List
from typing import Optional

from person_suit.core.constants.fixed_point_scale import SCALE

# Use absolute imports to avoid circular import issues
try:
    from .base import CacheableEffect
    from .base import Effect
    from .base import EffectType
    from .base import IdempotentEffect
except ImportError:
    # Fallback for direct module import
    from base import CacheableEffect
    from base import Effect
    from base import EffectType
    from base import IdempotentEffect


class DatabaseEffect(Effect):
    """Base class for all database effects."""
    
    def __init__(
        self,
        operation: str,
        table: str,
        required_capability: Optional[str] = None,
        metadata: Optional[Dict[str, Any]] = None
    ):
        super().__init__(
            effect_type=EffectType.DATABASE,
            operation=operation,
            required_capability=required_capability or f"database:{table}:{operation}",
            metadata=metadata
        )
        self.table = table


class ReadDatabaseEffect(DatabaseEffect, CacheableEffect, IdempotentEffect):
    """Effect for reading data from database."""
    
    def __init__(
        self,
        table: str,
        query: Optional[Dict[str, Any]] = None,
        fields: Optional[List[str]] = None,
        limit: Optional[int] = None,
        offset: Optional[int] = None,
        order_by: Optional[str] = None,
        metadata: Optional[Dict[str, Any]] = None
    ):
        super().__init__(
            operation="read",
            table=table,
            required_capability=f"database:{table}:read",
            metadata=metadata
        )
        self.query = query or {}
        self.fields = fields
        self.limit = limit
        self.offset = offset
        self.order_by = order_by
        
        # Read operations are highly cacheable
        self.cache_ttl_seconds = 600  # 10 minutes
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary."""
        return {
            "type": "database",
            "operation": "read",
            "id": self.id,
            "timestamp": self.timestamp.isoformat(),
            "table": self.table,
            "query": self.query,
            "fields": self.fields,
            "limit": self.limit,
            "offset": self.offset,
            "order_by": self.order_by,
            "required_capability": self.required_capability,
            "metadata": self.metadata,
            "can_degrade": self.can_degrade,
            "min_fidelity": self.min_fidelity,
            "cache_ttl_seconds": self.cache_ttl_seconds
        }
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'ReadDatabaseEffect':
        """Create from dictionary."""
        return cls(
            table=data["table"],
            query=data.get("query"),
            fields=data.get("fields"),
            limit=data.get("limit"),
            offset=data.get("offset"),
            order_by=data.get("order_by"),
            metadata=data.get("metadata")
        )


class WriteDatabaseEffect(DatabaseEffect):
    """Effect for writing data to database."""
    
    def __init__(
        self,
        table: str,
        data: Dict[str, Any],
        operation_type: str = "insert",  # insert, update, upsert
        where_clause: Optional[Dict[str, Any]] = None,
        metadata: Optional[Dict[str, Any]] = None
    ):
        super().__init__(
            operation="write",
            table=table,
            required_capability=f"database:{table}:write",
            metadata=metadata
        )
        self.data = data
        self.operation_type = operation_type
        self.where_clause = where_clause
        
        # Write operations cannot be degraded
        self.can_degrade = False
        self.min_fidelity = SCALE
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary."""
        return {
            "type": "database",
            "operation": "write",
            "id": self.id,
            "timestamp": self.timestamp.isoformat(),
            "table": self.table,
            "data": self.data,
            "operation_type": self.operation_type,
            "where_clause": self.where_clause,
            "required_capability": self.required_capability,
            "metadata": self.metadata,
            "can_degrade": self.can_degrade,
            "min_fidelity": self.min_fidelity,
            "cache_ttl_seconds": self.cache_ttl_seconds
        }
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'WriteDatabaseEffect':
        """Create from dictionary."""
        return cls(
            table=data["table"],
            data=data["data"],
            operation_type=data.get("operation_type", "insert"),
            where_clause=data.get("where_clause"),
            metadata=data.get("metadata")
        )


class DeleteDatabaseEffect(DatabaseEffect):
    """Effect for deleting data from database."""
    
    def __init__(
        self,
        table: str,
        where_clause: Dict[str, Any],
        metadata: Optional[Dict[str, Any]] = None
    ):
        super().__init__(
            operation="delete",
            table=table,
            required_capability=f"database:{table}:delete",
            metadata=metadata
        )
        self.where_clause = where_clause
        
        # Delete operations cannot be degraded
        self.can_degrade = False
        self.min_fidelity = SCALE
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary."""
        return {
            "type": "database",
            "operation": "delete",
            "id": self.id,
            "timestamp": self.timestamp.isoformat(),
            "table": self.table,
            "where_clause": self.where_clause,
            "required_capability": self.required_capability,
            "metadata": self.metadata,
            "can_degrade": self.can_degrade,
            "min_fidelity": self.min_fidelity,
            "cache_ttl_seconds": self.cache_ttl_seconds
        }
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'DeleteDatabaseEffect':
        """Create from dictionary."""
        return cls(
            table=data["table"],
            where_clause=data["where_clause"],
            metadata=data.get("metadata")
        )


class TransactionEffect(DatabaseEffect):
    """Effect for database transactions."""
    
    def __init__(
        self,
        operations: List[DatabaseEffect],
        isolation_level: str = "READ_COMMITTED",
        metadata: Optional[Dict[str, Any]] = None
    ):
        super().__init__(
            operation="transaction",
            table="*",  # Transactions can span multiple tables
            required_capability="database:transaction:execute",
            metadata=metadata
        )
        self.operations = operations
        self.isolation_level = isolation_level
        
        # Transactions cannot be degraded
        self.can_degrade = False
        self.min_fidelity = SCALE
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary."""
        return {
            "type": "database",
            "operation": "transaction",
            "id": self.id,
            "timestamp": self.timestamp.isoformat(),
            "operations": [op.to_dict() for op in self.operations],
            "isolation_level": self.isolation_level,
            "required_capability": self.required_capability,
            "metadata": self.metadata,
            "can_degrade": self.can_degrade,
            "min_fidelity": self.min_fidelity,
            "cache_ttl_seconds": self.cache_ttl_seconds
        }
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'TransactionEffect':
        """Create from dictionary."""
        # This would need to deserialize operations
        return cls(
            operations=[],
            isolation_level=data.get("isolation_level", "READ_COMMITTED"),
            metadata=data.get("metadata")
        )


# Convenience functions for creating database effects
def read_from_table(
    table: str,
    query: Optional[Dict[str, Any]] = None,
    fields: Optional[List[str]] = None,
    limit: Optional[int] = None
) -> ReadDatabaseEffect:
    """Create a read database effect."""
    return ReadDatabaseEffect(
        table=table,
        query=query,
        fields=fields,
        limit=limit
    )


def write_to_table(
    table: str,
    data: Dict[str, Any],
    operation_type: str = "insert"
) -> WriteDatabaseEffect:
    """Create a write database effect."""
    return WriteDatabaseEffect(
        table=table,
        data=data,
        operation_type=operation_type
    )


def delete_from_table(
    table: str,
    where_clause: Dict[str, Any]
) -> DeleteDatabaseEffect:
    """Create a delete database effect."""
    return DeleteDatabaseEffect(
        table=table,
        where_clause=where_clause
    )


def transaction(*operations: DatabaseEffect) -> TransactionEffect:
    """Create a transaction effect."""
    return TransactionEffect(operations=list(operations)) 