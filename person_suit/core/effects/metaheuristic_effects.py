"""
Metaheuristic Effects for Core Effect System

This module provides effect handlers for metaheuristic algorithms,
properly integrated with the core effect system and CAW principles.

Related Files:
- person_suit/core/infrastructure/dual_wave/metaheuristics.py: Core algorithms
- person_suit/core/effects/base.py: Base effect classes
- person_suit/core/effects/interpreter.py: Effect interpreter

Dependencies:
- Core effect system
- Dual wave metaheuristics
"""

import logging
from dataclasses import dataclass
from enum import Enum
from enum import auto
from typing import Any
from typing import Callable
from typing import Dict
from typing import Optional

from person_suit.core.context.unified import UnifiedContext
from person_suit.core.effects.base import Effect
from person_suit.core.effects.base import EffectResult
from person_suit.core.effects.base import EffectType
from person_suit.core.effects.handlers.base import BaseEffectHandler
from person_suit.core.infrastructure.dual_wave.metaheuristics import simulated_annealing_search
from person_suit.core.infrastructure.dual_wave.space import ConceptualSpace

logger = logging.getLogger(__name__)


class MetaheuristicEffectType(Enum):
    """Types of metaheuristic effects."""
    SIMULATED_ANNEALING = auto()
    ANT_COLONY = auto()
    PARTICLE_SWARM = auto()
    QUANTUM_ANNEALING = auto()
    HARMONY_SEARCH = auto()
    GRAVITATIONAL_SEARCH = auto()


@dataclass
class SimulatedAnnealingEffect(Effect):
    """Effect for simulated annealing optimization."""
    space: ConceptualSpace
    start_node_id: str
    energy_fn: Callable[[Any, Optional[UnifiedContext]], float]
    initial_temp: float = 1.0
    cooling_rate: float = 0.95
    min_temp: float = 1e-3
    max_steps: int = 100
    
    def __post_init__(self):
        super().__post_init__()
        self.effect_type = EffectType.COMPUTATION
        self.required_capability = "computation.metaheuristic.simulated_annealing"


@dataclass
class MetaheuristicParams:
    """Parameters for metaheuristic algorithms."""
    max_iterations: int = 100
    tolerance: float = 1e-4
    initial_temperature: float = 1.0
    cooling_rate: float = 0.95
    min_temperature: float = 1e-3
    custom_params: Dict[str, Any] = None
    
    def __post_init__(self):
        if self.custom_params is None:
            self.custom_params = {}


class MetaheuristicEffectHandler(BaseEffectHandler):
    """Handler for metaheuristic effects."""
    
    def __init__(self):
        super().__init__()
        self.supported_effects = {
            MetaheuristicEffectType.SIMULATED_ANNEALING: self._handle_simulated_annealing,
        }
    
    async def handle_effect(
        self, 
        effect: Effect, 
        context: UnifiedContext
    ) -> EffectResult:
        """
        Handle metaheuristic effects.
        
        Args:
            effect: The effect to handle
            context: Execution context
            
        Returns:
            Effect execution result
        """
        try:
            if isinstance(effect, SimulatedAnnealingEffect):
                return await self._handle_simulated_annealing(effect, context)
            else:
                return EffectResult(
                    success=False,
                    error=f"Unsupported metaheuristic effect: {type(effect)}"
                )
                
        except Exception as e:
            logger.error(f"Error handling metaheuristic effect: {e}")
            return EffectResult(
                success=False,
                error=str(e)
            )
    
    async def _handle_simulated_annealing(
        self, 
        effect: SimulatedAnnealingEffect, 
        context: UnifiedContext
    ) -> EffectResult:
        """Handle simulated annealing effect."""
        try:
            # Context-driven parameter adaptation
            params = self._adapt_parameters_to_context(effect, context)
            
            # Execute simulated annealing
            best_node, best_energy = simulated_annealing_search(
                space=effect.space,
                start_node_id=effect.start_node_id,
                energy_fn=effect.energy_fn,
                context=context,
                initial_temp=params.initial_temperature,
                cooling_rate=params.cooling_rate,
                min_temp=params.min_temperature,
                max_steps=params.max_iterations
            )
            
            return EffectResult(
                success=True,
                result={
                    "best_node": best_node,
                    "best_energy": best_energy,
                    "algorithm": "simulated_annealing",
                    "parameters": {
                        "initial_temp": params.initial_temperature,
                        "cooling_rate": params.cooling_rate,
                        "min_temp": params.min_temperature,
                        "max_steps": params.max_iterations
                    }
                }
            )
            
        except Exception as e:
            logger.error(f"Simulated annealing failed: {e}")
            return EffectResult(
                success=False,
                error=str(e)
            )
    
    def _adapt_parameters_to_context(
        self, 
        effect: SimulatedAnnealingEffect, 
        context: UnifiedContext
    ) -> MetaheuristicParams:
        """Adapt metaheuristic parameters based on context."""
        params = MetaheuristicParams(
            initial_temperature=effect.initial_temp,
            cooling_rate=effect.cooling_rate,
            min_temperature=effect.min_temp,
            max_iterations=effect.max_steps
        )
        
        # Context-driven adaptations
        if context.acf_settings:
            # Adjust iterations based on computational fidelity
            fidelity = getattr(context.acf_settings, 'fidelity', 'medium')
            if fidelity == 'high':
                params.max_iterations = int(params.max_iterations * 1.5)
            elif fidelity == 'low':
                params.max_iterations = int(params.max_iterations * 0.5)
        
        # Priority-based adaptations
        if context.priority:
            priority_value = getattr(context.priority, 'value', 2)
            if priority_value >= 3:  # High priority
                params.initial_temperature *= 1.2
                params.cooling_rate *= 0.98  # Slower cooling for better exploration
            elif priority_value <= 1:  # Low priority
                params.initial_temperature *= 0.8
                params.cooling_rate *= 1.02  # Faster cooling for quicker results
        
        return params
    
    async def execute_high_fidelity(
        self, 
        effect: Effect, 
        context: UnifiedContext
    ) -> EffectResult:
        """Execute with high fidelity parameters."""
        if isinstance(effect, SimulatedAnnealingEffect):
            # Increase iterations and improve exploration for high fidelity
            effect.max_steps = int(effect.max_steps * 2)
            effect.cooling_rate *= 0.95  # Slower cooling
        
        return await self.handle_effect(effect, context)
    
    async def execute_medium_fidelity(
        self, 
        effect: Effect, 
        context: UnifiedContext
    ) -> EffectResult:
        """Execute with medium fidelity parameters."""
        return await self.handle_effect(effect, context)
    
    async def execute_low_fidelity(
        self, 
        effect: Effect, 
        context: UnifiedContext
    ) -> EffectResult:
        """Execute with low fidelity parameters."""
        if isinstance(effect, SimulatedAnnealingEffect):
            # Reduce iterations for faster execution
            effect.max_steps = int(effect.max_steps * 0.5)
            effect.cooling_rate *= 1.05  # Faster cooling
        
        return await self.handle_effect(effect, context)


# Factory function for creating metaheuristic effects
def create_simulated_annealing_effect(
    space: ConceptualSpace,
    start_node_id: str,
    energy_fn: Callable[[Any, Optional[UnifiedContext]], float],
    **kwargs
) -> SimulatedAnnealingEffect:
    """
    Create a simulated annealing effect.
    
    Args:
        space: The ConceptualSpace to search
        start_node_id: Starting node ID
        energy_fn: Energy function for optimization
        **kwargs: Additional parameters
        
    Returns:
        SimulatedAnnealingEffect instance
    """
    return SimulatedAnnealingEffect(
        space=space,
        start_node_id=start_node_id,
        energy_fn=energy_fn,
        **kwargs
    ) 