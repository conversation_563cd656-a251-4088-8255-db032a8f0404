"""
Message-Based Effects API - The ONLY way to execute effects.

This module provides the unified message-based API for effect execution, replacing
all direct runtime and handler imports. All effects execute via the hybrid message
bus for true decoupling and CAW integration.

Related Files:
- channels.py: Effect channel definitions
- messages/: Effect message types
- actors/: Effect actor implementations
- runtime_injector.py: Runtime dependency injection

Dependencies:
- Uses runtime dependency injection to avoid import-time coupling
- All functionality loaded on-demand via service locator pattern
"""

import asyncio
import logging
from dataclasses import dataclass
from typing import TYPE_CHECKING
from typing import Any
from typing import Dict
from typing import Optional
from typing import Set

# This file should NOT have dependencies on 'application' or 'security' layers.
# All communication must be done via the message bus or dependency injection.

logger = logging.getLogger(__name__)

# Lazy-loaded imports for runtime dependency resolution
def _lazy_load_dependencies():
    global get_message_bus, HybridMessage, UnifiedContext, initialize_effects_services
    from person_suit.core.context.unified import UnifiedContext
    from person_suit.core.infrastructure.hybrid_message import HybridMessage
    from person_suit.core.infrastructure.hybrid_message_bus import get_message_bus

    # runtime_injector has been removed in Sprint-2.  Service locator pattern is
    # deprecated; initialisation happens via *initialize_effects_services* only.
    from .service_initialization import initialize_effects_services

# Execute lazy loading at module level to populate global namespace
_lazy_load_dependencies()

# Import-time safe type hints
if TYPE_CHECKING:
    from person_suit.core.context.unified import UnifiedContext
    from person_suit.core.infrastructure.hybrid_message import HybridMessage
    from person_suit.core.infrastructure.hybrid_message_bus import HybridMessageBus

from person_suit.core.effects.base import Effect
from person_suit.core.effects.base import EffectResult as _BaseResult
from person_suit.core.effects.computation import MLInferenceEffect
from person_suit.core.effects.computation import ProcessTextEffect
from person_suit.core.effects.database import DeleteDatabaseEffect
from person_suit.core.effects.database import ReadDatabaseEffect
from person_suit.core.effects.database import WriteDatabaseEffect
from person_suit.core.effects.event import PublishEventEffect
from person_suit.core.effects.interpreter import EffectInterpreter
from person_suit.core.effects.interpreter import get_effect_interpreter
from person_suit.core.effects.io_effects import DeleteFileEffect as IODeleteEffect
from person_suit.core.effects.io_effects import ReadFileEffect
from person_suit.core.effects.io_effects import WriteFileEffect
from person_suit.core.effects.network import HTTPRequestEffect
from person_suit.core.effects.state_effects import StateDeleteEffect
from person_suit.core.effects.state_effects import StateQueryEffect
from person_suit.core.effects.state_effects import StateReadEffect
from person_suit.core.effects.state_effects import StateSnapshotEffect
from person_suit.core.effects.state_effects import StateUpdateEffect

# Dictionary to map channel strings to Effect classes
CHANNEL_TO_EFFECT_MAP = {
    "effects.database.read": ReadDatabaseEffect,
    "effects.database.write": WriteDatabaseEffect,
    "effects.database.delete": DeleteDatabaseEffect,
    "effects.event.publish": PublishEventEffect,
    "effects.io.file.read": ReadFileEffect,
    "effects.io.file.write": WriteFileEffect,
    "effects.io.file.delete": IODeleteEffect,
    "effects.network.http_request": HTTPRequestEffect,
    "effects.computation.process_text": ProcessTextEffect,
    "effects.computation.ml_inference": MLInferenceEffect,
    "effects.state.update": StateUpdateEffect,
    "effects.state.read": StateReadEffect,
    "effects.state.delete": StateDeleteEffect,
    "effects.state.query": StateQueryEffect,
    "effects.state.snapshot": StateSnapshotEffect,
    "effects.state.write": StateUpdateEffect,  # Alias for write -> update
}

def _payload_to_effect(channel: str, payload: Dict[str, Any]) -> Effect:
    """Maps a channel and payload to a declarative Effect object."""
    effect_class = CHANNEL_TO_EFFECT_MAP.get(channel)
    if not effect_class:
        raise ValueError(f"No Effect class found for channel: {channel}")
    
    # This assumes payload keys match the Effect's __init__ parameters
    try:
        return effect_class(**payload)
    except TypeError as e:
        raise ValueError(f"Payload mismatch for {effect_class.__name__}: {e}") from e

@dataclass
class EffectResult:
    """Result of effect execution with metadata - Compatible with dict-like access."""
    
    success: bool
    result: Any
    execution_time: float
    fidelity_used: float
    resource_usage: Dict[str, float]
    wave_particle_ratio: float
    error: Optional[str] = None
    
    def get(self, key: str, default: Any = None) -> Any:
        """Dictionary-like get method for backwards compatibility."""
        if hasattr(self, key):
            return getattr(self, key)
        
        # Handle legacy key mappings
        if key == "effect_result":
            # Return a dict-like representation of the result
            return {
                "success": self.success,
                "data": self.result,
                "error": self.error,
                "execution_time": self.execution_time,
                "fidelity_used": self.fidelity_used,
                "resource_usage": self.resource_usage,
                "wave_particle_ratio": self.wave_particle_ratio
            }
        elif key == "data":
            return self.result
        
        return default
    
    def __getitem__(self, key: str) -> Any:
        """Dictionary-like indexing for backwards compatibility."""
        if hasattr(self, key):
            return getattr(self, key)
        
        # Handle legacy key mappings
        if key == "effect_result":
            return self.get("effect_result")
        elif key == "data":
            return self.result
        
        raise KeyError(f"'{key}' not found in EffectResult")
    
    def __contains__(self, key: str) -> bool:
        """Dictionary-like 'in' operator support."""
        return hasattr(self, key) or key in ["effect_result", "data"]
    
    def keys(self):
        """Dictionary-like keys() method."""
        base_keys = ["success", "result", "execution_time", "fidelity_used", 
                    "resource_usage", "wave_particle_ratio", "error"]
        legacy_keys = ["effect_result", "data"]
        return base_keys + legacy_keys
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary for full compatibility."""
        return {
            "success": self.success,
            "result": self.result,
            "data": self.result,  # Legacy alias
            "execution_time": self.execution_time,
            "fidelity_used": self.fidelity_used,
            "resource_usage": self.resource_usage,
            "wave_particle_ratio": self.wave_particle_ratio,
            "error": self.error,
            "effect_result": {
                "success": self.success,
                "data": self.result,
                "error": self.error,
                "execution_time": self.execution_time,
                "fidelity_used": self.fidelity_used,
                "resource_usage": self.resource_usage,
                "wave_particle_ratio": self.wave_particle_ratio
            }
        }


class MessageBasedEffectsAPI:
    """Message-based effects API now powered by the EffectInterpreter."""

    def __init__(self):
        self._interpreter: Optional[EffectInterpreter] = None
        self._initialized = False
        self._lock = asyncio.Lock()

    async def _ensure_initialized(self):
        """Ensure the interpreter is initialized."""
        if self._initialized:
            return
        async with self._lock:
            if self._initialized:
                return
            self._interpreter = await get_effect_interpreter()
            self._initialized = True
            logger.info("MessageBasedEffectsAPI (Unified) initialized successfully.")

    async def execute_effect(
        self,
        channel: str,
        payload: Dict[str, Any],
        context: Optional["UnifiedContext"] = None,
        timeout: float = 30.0,
        **kwargs # absorb legacy args
    ) -> EffectResult:
        """Main method to execute an effect via the new EffectInterpreter."""
        await self._ensure_initialized()

        if context is None:
            context = UnifiedContext.create_default(domain=f"effect.{channel}")

        try:
            # Step 1: Convert message to a declarative Effect object
            effect = _payload_to_effect(channel, payload)
            
            # Step 2: Execute via the interpreter
            result = await self._interpreter.execute_effect(
                effect, context, timeout_seconds=timeout
            )
            return result

        except (ValueError, TypeError) as e:
            logger.error(f"Failed to create effect for channel '{channel}': {e}", exc_info=True)
            return EffectResult(
                success=False,
                result=None,
                execution_time=0.0,
                fidelity_used=1.0,
                resource_usage={},
                wave_particle_ratio=context.wave_particle_ratio if context else 0.5,
                error=str(e),
            )
        except Exception as e:
            logger.error(f"Error executing effect '{channel}': {e}", exc_info=True)
            # This should use the EffectResult structure from base.py
            return _BaseResult(
                success=False,
                value=None,
                error=str(e),
                metadata={"raw_exception": True},
                execution_time_ms=0.0,
            )

    async def batch_execute_effects(
        self,
        effect_requests: list[Dict[str, Any]],
        context: Optional["UnifiedContext"] = None,
        parallel: bool = True
    ) -> list[EffectResult]:
        """
        Execute multiple effects in batch.
        
        Args:
            effect_requests: List of effect requests, each with 'channel' and 'payload'
            context: Optional shared context for all effects
            parallel: Whether to execute effects in parallel
            
        Returns:
            List of EffectResults in order of requests
        """
        if parallel:
            tasks = [
                self.execute_effect(
                    req["channel"],
                    req["payload"],
                    context,
                    wave_particle_ratio=req.get("wave_particle_ratio")
                )
                for req in effect_requests
            ]
            return await asyncio.gather(*tasks)
        else:
            results = []
            for req in effect_requests:
                result = await self.execute_effect(
                    req["channel"],
                    req["payload"],
                    context,
                    wave_particle_ratio=req.get("wave_particle_ratio")
                )
                results.append(result)
            return results
    
    async def get_available_channels(self) -> Set[str]:
        """Get all available effect channels."""
        from .channels import EFFECT_CHANNELS
        return set(EFFECT_CHANNELS.keys())
    
    async def get_channels_for_capabilities(self, capabilities: Set[str]) -> Set[str]:
        """Get channels accessible with given capabilities."""
        # This is a placeholder. A real implementation would query a capability service.
        # For now, we assume all channels are accessible if any capability is present.
        if capabilities:
            return self.get_available_channels()
        return set()

    async def stop(self):
        """Gracefully stops the API and its managed components."""
        if not self._initialized:
            return
        logger.info("Stopping MessageBasedEffectsAPI...")
        self._initialized = False
        logger.info("MessageBasedEffectsAPI stopped.")


# Global API instance (singleton)
_effects_api_instance: Optional[MessageBasedEffectsAPI] = None
_effects_api_lock = asyncio.Lock()


async def get_effects_api(
    bus: Optional["HybridMessageBus"] = None, 
    security_manager: Optional[Any] = None
) -> MessageBasedEffectsAPI:
    """Provides a singleton instance of the MessageBasedEffectsAPI."""
    global _effects_api_instance
    if _effects_api_instance is None:
        async with _effects_api_lock:
            if _effects_api_instance is None:
                _effects_api_instance = MessageBasedEffectsAPI()
                await _effects_api_instance._ensure_initialized()
    return _effects_api_instance


async def stop_effects_api():
    """Public function to stop the global effects API."""
    global _effects_api_instance
    if _effects_api_instance and _effects_api_instance._initialized:
        await _effects_api_instance.stop()
        _effects_api_instance = None


# Primary API functions - The ONLY way to execute effects
async def execute_effect(
    channel: str,
    payload: Dict[str, Any],
    context: Optional["UnifiedContext"] = None,
    timeout: float = 30.0,
    wave_particle_ratio: Optional[float] = None
) -> EffectResult:
    """
    Execute effect via message bus - replaces all direct imports.
    
    This is THE primary function for effect execution in the system.
    All other effect execution methods are deprecated.
    
    Args:
        channel: Effect channel (e.g., 'effects.io.file.read')
        payload: Effect parameters
        context: Optional UnifiedContext
        timeout: Maximum execution time
        wave_particle_ratio: Optional wave-particle processing ratio
        
    Returns:
        EffectResult with execution metadata
    """
    api = await get_effects_api()
    return await api.execute_effect(channel, payload, context, timeout, wave_particle_ratio)


async def batch_execute_effects(
    effect_requests: list[Dict[str, Any]],
    context: Optional["UnifiedContext"] = None,
    parallel: bool = True
) -> list[EffectResult]:
    """
    Executes a batch of effects.
    
    This is a convenience wrapper around the singleton API instance.
    """
    api = await get_effects_api()
    return await api.batch_execute_effects(
        effect_requests, context=context, parallel=parallel
    )


# Legacy compatibility layer removed - Phase 2.2 Complete
# All code should now use execute_effect() function directly


# Effect execution exceptions
class EffectExecutionError(Exception):
    """Base exception for effect execution failures."""
    pass


class EffectTimeoutError(EffectExecutionError):
    """Exception for effect timeouts."""
    pass


class EffectAuthorizationError(EffectExecutionError):
    """Exception for effect authorization failures."""
    pass


# CONVENIENCE FUNCTIONS REMOVED - ARCHITECTURAL VIOLATION
# 
# These functions violated Principle I: "Components shall not act. They shall only declare intent."
# By providing direct I/O functions, they allowed components to bypass the declarative effect system.
#
# MIGRATION GUIDE:
# OLD: content = await read_file("path.txt")
# NEW: result = await execute_effect("effects.io.file.read", {"path": "path.txt"})
#      content = result.result
#
# OLD: await write_file("path.txt", "content")
# NEW: await execute_effect("effects.io.file.write", {"path": "path.txt", "content": "content"})
#
# OLD: rows = await query_database("SELECT * FROM users")
# NEW: result = await execute_effect("effects.database.read", {"sql": "SELECT * FROM users", "params": []})
#      rows = result.result
#
# OLD: memory_id = await store_memory(data, "working")
# NEW: result = await execute_effect("effects.memory.store", {"data": data, "layer": "working"})
#      memory_id = result.result
#
# This enforces that ALL operations go through the declarative effect system,
# enabling proper capability checking, ACF adaptation, and choreographed execution.


# Module exports - Only expose message-based API
__all__ = [
    # Primary API - The ONLY way to execute effects
    "execute_effect",
    "batch_execute_effects",
    "EffectResult",
    "stop_effects_api",

    # Exceptions
    "EffectExecutionError",
    "EffectTimeoutError",
    "EffectAuthorizationError",

    # Convenience functions REMOVED to enforce Principle I
    # All code must use execute_effect() with proper declarative effects
]

 