"""
Effect Actor Protocols - Import-Independent Definitions

This module defines actor protocols for the effects system without any
import-time dependencies on Person Suit infrastructure.

All type hints use TYPE_CHECKING to avoid import coupling while maintaining
type safety during development and IDE support.
"""

from abc import abstractmethod
from dataclasses import dataclass
from typing import TYPE_CHECKING
from typing import Any
from typing import Dict
from typing import List
from typing import Optional
from typing import Protocol
from typing import Set
from typing import runtime_checkable

# Use TYPE_CHECKING to avoid import-time dependencies
if TYPE_CHECKING:
    from person_suit.core.infrastructure.hybrid_message import HybridMessage

# =============================================================================
# Actor Registration & Metadata
# =============================================================================

@dataclass
class ActorRegistrationInfo:
    """Registration information for an actor."""
    actor_id: str
    protocols: List[str]
    capabilities: Set[str] 
    domain: str
    load_factor: float = 0.0
    health_status: Dict[str, Any] = None
    
    def __post_init__(self):
        if self.health_status is None:
            self.health_status = {"status": "unknown", "last_check": 0}
    
    def is_healthy(self) -> bool:
        """Check if actor is healthy."""
        return self.health_status.get("status") == "healthy"


# =============================================================================
# Base Actor Protocol
# =============================================================================

@runtime_checkable
class ActorProtocol(Protocol):
    """Base protocol for all CAW actors - import independent."""
    
    @abstractmethod
    async def get_actor_id(self) -> str:
        """Return unique actor identifier."""
        pass
    
    @abstractmethod  
    async def get_capabilities(self) -> Set[str]:
        """Return set of capabilities this actor provides."""
        pass
    
    @abstractmethod
    async def handle_message(self, message: "HybridMessage") -> Any:
        """Handle incoming message based on capabilities."""
        pass
    
    @abstractmethod
    async def get_load_factor(self) -> float:
        """Return current load factor (0.0 = idle, 1.0 = fully loaded)."""
        pass
    
    @abstractmethod
    async def get_health_status(self) -> Dict[str, Any]:
        """Return current health status."""
        pass


# =============================================================================
# Effect Actor Protocol
# =============================================================================

@runtime_checkable  
class EffectActorProtocol(Protocol):
    """Protocol for effect-handling actors with CAW capabilities."""
    
    @abstractmethod
    async def execute_effect(self, effect_message: "HybridMessage") -> Any:
        """Execute effect with differential execution and wave-particle duality.
        
        Args:
            effect_message: Effect message with payload and context
            
        Returns:
            Effect execution result
        """
        pass
    
    @abstractmethod
    async def get_wave_particle_state(self) -> Dict[str, float]:
        """Return current wave-particle processing ratios."""
        pass
    
    @abstractmethod
    async def get_differential_state(self) -> Dict[str, Any]:
        """Return differential execution state for caching."""
        pass
    
    @abstractmethod
    async def supports_acf_adaptation(self) -> bool:
        """Return whether actor supports Adaptive Computational Fidelity."""
        pass


# =============================================================================
# Domain-Specific Actor Protocols  
# =============================================================================

@runtime_checkable
class CognitionActorProtocol(Protocol):
    """Protocol for cognition-related actors."""
    
    @abstractmethod
    async def process_cognitive_task(self, task_data: Dict[str, Any]) -> Any:
        """Process cognitive task."""
        pass


@runtime_checkable  
class AnalysisActorProtocol(Protocol):
    """Protocol for analysis actors."""
    
    @abstractmethod
    async def analyze_data(self, data: Any, analysis_type: str) -> Dict[str, Any]:
        """Analyze data and return results."""
        pass


@runtime_checkable
class PredictionActorProtocol(Protocol):
    """Protocol for prediction actors."""
    
    @abstractmethod
    async def generate_prediction(self, input_data: Any) -> Dict[str, Any]:
        """Generate predictions from input data."""
        pass


@runtime_checkable
class MemoryActorProtocol(Protocol):
    """Protocol for memory system actors."""
    
    @abstractmethod
    async def store_memory(self, content: Any, memory_type: str) -> str:
        """Store memory and return ID."""
        pass
    
    @abstractmethod
    async def retrieve_memory(self, memory_id: str) -> Optional[Any]:
        """Retrieve memory by ID."""
        pass


@runtime_checkable
class IOActorProtocol(Protocol):
    """Protocol for I/O actors."""
    
    @abstractmethod
    async def read_data(self, source: str, params: Dict[str, Any]) -> Any:
        """Read data from source.""" 
        pass
    
    @abstractmethod
    async def write_data(self, target: str, data: Any, params: Dict[str, Any]) -> bool:
        """Write data to target."""
        pass


# =============================================================================
# Specialized Effect Actor Protocols
# =============================================================================

@runtime_checkable
class DatabaseActorProtocol(Protocol):
    """Protocol for database effect actors."""
    
    @abstractmethod
    async def execute_query(self, sql: str, params: List[Any]) -> List[Dict[str, Any]]:
        """Execute database query."""
        pass
    
    @abstractmethod
    async def execute_transaction(self, operations: List[Dict[str, Any]]) -> bool:
        """Execute database transaction."""
        pass


@runtime_checkable  
class NetworkActorProtocol(Protocol):
    """Protocol for network effect actors."""
    
    @abstractmethod
    async def make_request(self, url: str, method: str, params: Dict[str, Any]) -> Dict[str, Any]:
        """Make network request."""
        pass


@runtime_checkable
class FileActorProtocol(Protocol):  
    """Protocol for file system effect actors."""
    
    @abstractmethod
    async def read_file(self, path: str, encoding: str) -> str:
        """Read file contents."""
        pass
    
    @abstractmethod
    async def write_file(self, path: str, content: str, encoding: str) -> bool:
        """Write file contents."""
        pass
    
    @abstractmethod
    async def delete_file(self, path: str) -> bool:
        """Delete file."""
        pass


# =============================================================================
# Protocol Validation Utilities
# =============================================================================

def validate_actor_protocol(actor: Any, protocol_class: type) -> bool:
    """Validate that an actor implements a specific protocol.
    
    Args:
        actor: Actor instance to validate
        protocol_class: Protocol class to check against
        
    Returns:
        True if actor implements the protocol
    """
    return isinstance(actor, protocol_class)


def get_supported_protocols(actor: Any) -> List[str]:
    """Get list of protocol names that an actor supports.
    
    Args:
        actor: Actor instance to inspect
        
    Returns:
        List of protocol names
    """
    protocols = []
    
    protocol_classes = [
        ActorProtocol,
        EffectActorProtocol,
        CognitionActorProtocol,
        AnalysisActorProtocol,
        PredictionActorProtocol,
        MemoryActorProtocol,
        IOActorProtocol,
        DatabaseActorProtocol,
        NetworkActorProtocol,
        FileActorProtocol,
    ]
    
    for protocol_class in protocol_classes:
        if isinstance(actor, protocol_class):
            protocols.append(protocol_class.__name__)
    
    return protocols


def check_capability_compatibility(
    required_capabilities: Set[str],
    available_capabilities: Set[str]
) -> bool:
    """Check if available capabilities satisfy requirements.
    
    Args:
        required_capabilities: Set of required capabilities
        available_capabilities: Set of available capabilities
        
    Returns:
        True if all requirements are satisfied
    """
    return required_capabilities.issubset(available_capabilities)


# Export the public API
__all__ = [
    'ActorRegistrationInfo',
    'ActorProtocol',
    'EffectActorProtocol', 
    'CognitionActorProtocol',
    'AnalysisActorProtocol',
    'PredictionActorProtocol',
    'MemoryActorProtocol',
    'IOActorProtocol',
    'DatabaseActorProtocol',
    'NetworkActorProtocol',
    'FileActorProtocol',
    'validate_actor_protocol',
    'get_supported_protocols',
    'check_capability_compatibility',
] 