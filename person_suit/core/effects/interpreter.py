"""
Effect Interpreter - The Heart of Principle I Implementation
===========================================================

This module implements the EffectInterpreter, the cornerstone component that realizes
Principle I: "Components shall not act. They shall only declare intent. The system 
choreographs the fulfillment of that intent."

The EffectInterpreter is the ONLY component in the system that performs actual I/O
and side effects. All other components return declarative Effect descriptions.

Related Files:
- ../infrastructure/hybrid_message_bus.py: Message routing
- ../context/unified.py: Context propagation  
- ../capabilities/validator.py: Security validation
- ./base.py: Effect definitions

Dependencies:
- HybridMessageBus for effect message routing
- UnifiedContext for contextual execution
- CapabilityValidator for security enforcement
"""

import asyncio
import logging
import time
from typing import TYPE_CHECKING
from typing import Any
from typing import Dict
from typing import Optional
from typing import Type

from person_suit.core.capabilities.validator import CapabilityValidator
from person_suit.core.constants.fixed_point_scale import FIDELITY_MED
from person_suit.core.constants.fixed_point_scale import FIDELITY_MIN
from person_suit.core.constants.fixed_point_scale import PRIO_HIGH
from person_suit.core.constants.fixed_point_scale import PRIO_LOW
from person_suit.core.constants.fixed_point_scale import PRIO_NORMAL
from person_suit.core.constants.fixed_point_scale import SCALE
from person_suit.core.context.unified import UnifiedContext

# Effect system imports
from person_suit.core.effects.base import Effect
from person_suit.core.effects.base import EffectExecutionContext
from person_suit.core.effects.base import EffectExecutionStrategy
from person_suit.core.effects.base import EffectResult
from person_suit.core.effects.computation import ComputationEffect
from person_suit.core.effects.database import DatabaseEffect
from person_suit.core.effects.event import EventEffect
from person_suit.core.effects.io_effects import IOEffect
from person_suit.core.effects.network import NetworkEffect
from person_suit.core.effects.strategy import ExecutionStrategy
from person_suit.core.infrastructure.hybrid_message import HybridMessage
from person_suit.core.infrastructure.hybrid_message import MessageType

# Core infrastructure imports
from person_suit.core.infrastructure.hybrid_message_bus import HybridMessageBus
from person_suit.core.infrastructure.hybrid_message_bus import get_message_bus
from person_suit.monitoring.metrics import PARTICLE_DEMOTIONS_TOTAL

logger = logging.getLogger(__name__)

if TYPE_CHECKING:  # pragma: no cover
    from person_suit.core.capabilities.protocols import ACFManagerProtocol  # noqa: WPS433

# EffectExecutionStrategy and EffectExecutionContext are imported from base.py
# to avoid duplication and maintain single source of truth

# Avoid top-level import to prevent circular; import lazily in methods

# ---------------------------------------------------------------------------
# Metric initialisation – EFFECT_DESERIALISATION_ERRORS_TOTAL
# ---------------------------------------------------------------------------

try:
    from person_suit.core.infrastructure.monitoring.metrics import MetricUnit
    from person_suit.core.infrastructure.monitoring.metrics import get_metrics_registry

    _METRICS = get_metrics_registry()
    _EFFECT_DESERIALISATION_ERRORS_TOTAL = _METRICS.get_or_create_counter(
        name="effect_deserialisation_errors_total",
        description="Total number of effect message deserialisation failures",
        unit=MetricUnit.COUNT,
        namespace="effect_interpreter",
    )
except Exception:  # noqa: BLE001 – optional dependency
    _EFFECT_DESERIALISATION_ERRORS_TOTAL = None  # type: ignore

class EffectInterpreter:
    """
    The central Effect Interpreter that executes all effects in the system.
    
    This is the ONLY component that performs actual I/O and side effects.
    All business logic components return declarative Effect descriptions
    that are executed here with proper capability checking and ACF adaptation.
    """
    
    def __init__(
        self,
        message_bus: Optional[HybridMessageBus] = None,
        capability_validator: Optional[CapabilityValidator] = None,
        acf_manager: Optional["ACFManagerProtocol"] = None
    ):
        self.message_bus = message_bus
        self.capability_validator = capability_validator
        self.acf_manager = acf_manager
        # Legacy handler map kept for backward compatibility but legacy IOEffectHandler
        # is now removed.  Use strategy registry instead.
        self._effect_handlers: Dict[Type[Effect], Any] = {}
        # Strategy registry (new in Sprint-2)
        # Maps Effect subclasses to async callables returning EffectResult
        self._strategy_registry: Dict[Type[Effect], Any] = {}
        self._execution_cache: Dict[str, EffectResult] = {}
        self._system_metrics = {
            "cpu_usage": 0.0,
            "memory_usage": 0.0,
            "queue_depth": 0,
            "error_rate": 0.0
        }
        self._running = False
        # Priority-aware normal queue and dedicated urgent queue (fast-path)
        self._effect_queue: asyncio.PriorityQueue[
            tuple[int, float, tuple[Effect, UnifiedContext, "HybridMessage"]]
        ] = asyncio.PriorityQueue()
        self._urgent_queue: asyncio.Queue[
            tuple[int, float, tuple[Effect, UnifiedContext, "HybridMessage"]]
        ] = asyncio.Queue()

        # Worker accounting for utilisation metric
        self._worker_tasks: list[asyncio.Task] = []
        self._active_workers: int = 0  # incremented when worker busy
        
        # Concurrency limiter for low-priority effects – prevents starvation
        import os  # noqa: WPS433
        cpu_total = os.cpu_count() or 4
        low_conc_limit = max(1, int(cpu_total * 0.25))  # quarter logical CPUs
        self._low_prio_sema: asyncio.Semaphore = asyncio.Semaphore(low_conc_limit)
        
        # Register built-in effect handlers (non-IO legacy only)
        self._register_builtin_handlers()
        # Register default placeholder strategies (IO, STATE) – Day-3
        self._register_default_strategies()

        # Wave batching buffer
        self._wave_buffer: list[tuple[Effect, UnifiedContext, Optional["HybridMessage"]]] = []
        self._wave_batch_window_s: float = 0.005
        self._wave_batch_task: Optional[asyncio.Task] = None
    
    async def initialize(self) -> None:
        """Initialize the interpreter and start processing effects."""
        if not self.message_bus:
            self.message_bus = get_message_bus()
        
        if not self.capability_validator:
            from person_suit.core.capabilities.validator import get_capability_validator
            self.capability_validator = await get_capability_validator()
        
        # Re-register handlers with new bus if needed
        if self.message_bus:
            self._register_builtin_handlers()
        
        # Subscribe to system metrics for ACF decisions
        await self.message_bus.subscribe(
            "sys.metrics.update",
            self._handle_metrics_update,
            subscriber_id="effect_interpreter_metrics"
        )
        
        # idempotent subscribe (will ignore if already exists)
        await self.message_bus.subscribe(
            "effect.*",
            self._handle_effect_message,
            subscriber_id="effect_interpreter_effects",
            handler_priority=0,
        )
        
        self._running = True
        
        # --------------------------------------------------------------
        # Start multiple effect processing workers to prevent head-of-line
        # blocking when low-priority effects are long-running.  Worker
        # count mirrors the BusKernel logic: if env ``PS_AUTO_WORKERS`` is
        # set we derive worker count from logical CPUs (75 %) otherwise we
        # default to 1.  This creates a **fast-path** for newly arrived
        # high-priority effects because they can occupy an idle worker
        # instead of waiting behind low-priority tasks in a single queue.
        # --------------------------------------------------------------
        import asyncio as _aio
        import os  # noqa: WPS433

        auto_flag = os.getenv("PS_AUTO_WORKERS", "0") == "1"
        if auto_flag:
            cpu_total = os.cpu_count() or 4
            num_workers = max(1, int(cpu_total * 0.5))  # 50 % CPU count
        else:
            num_workers = 1

        # Dedicated urgent worker pool (fast-path for PRIO_HIGH+)
        urgent_cnt_env = os.getenv("PS_URGENT_WORKERS")
        if urgent_cnt_env is not None:
            urgent_workers = max(1, int(urgent_cnt_env))
        elif auto_flag:
            urgent_workers = max(1, int((os.cpu_count() or 4) * 0.25))  # 25% CPUs
        else:
            urgent_workers = 1

        for _ in range(urgent_workers):
            task = _aio.create_task(self._urgent_worker_loop())
            self._worker_tasks.append(task)

        # Normal worker pool (handles both queues but prefers normal)
        for _ in range(num_workers):
            task = _aio.create_task(self._process_effect_queue())
            self._worker_tasks.append(task)

        # Start scaler/monitor task and keep reference for clean shutdown
        autoscaler_task = _aio.create_task(self._autoscaler_loop(base_workers=num_workers))
        self._worker_tasks.append(autoscaler_task)
        logger.info("EffectInterpreter started %d worker(s)", num_workers)
        logger.info("EffectInterpreter started %d urgent worker(s)", urgent_workers)
        
        logger.info("EffectInterpreter initialized and ready")
    
    async def shutdown(self) -> None:
        """Shutdown the interpreter gracefully."""
        self._running = False
        
        if self.message_bus:
            # Safely unsubscribe, handling potential errors if already unsubscribed
            try:
                if self.message_bus.is_running:  # type: ignore[attr-defined]
                    self.message_bus.unsubscribe("effect_interpreter_metrics", "sys.metrics.update")
                    self.message_bus.unsubscribe("effect_interpreter_effects", "effect.*")
            except Exception as e:
                logger.debug(f"Error during bus unsubscribe on shutdown: {e}")

        for task in self._worker_tasks:
            task.cancel()
        
        await asyncio.gather(*self._worker_tasks, return_exceptions=True)
        
        logger.info("EffectInterpreter shutdown complete")
    
    async def execute_effect(
        self,
        effect: Effect,
        context: UnifiedContext,
        timeout_seconds: float = 30.0,
        correlation_id: Optional[str] = None
    ) -> EffectResult:
        """
        Execute a single effect with full capability checking and ACF adaptation.
        
        This is the core method that implements Principle I - all effects
        go through this single point of execution.
        
        Args:
            effect: The effect to execute
            context: The unified context for execution
            timeout_seconds: Maximum execution time
            correlation_id: The correlation ID for the effect
            
        Returns:
            EffectResult containing the execution result
            
        Raises:
            CapabilityError: If required capabilities are missing
            EffectExecutionError: If execution fails
        """
        label_val = str(effect.effect_type.value).lower()

        # ------------------------------------------------------------------
        # Metric: count every attempt as early as possible (CAW provenance)
        # ------------------------------------------------------------------
        try:
            from person_suit.monitoring.metrics import EFFECT_ATTEMPTS_TOTAL  # noqa: WPS433

            EFFECT_ATTEMPTS_TOTAL.labels(effect_type=label_val).inc()
        except Exception:  # pragma: no cover – metric optional
            pass

        execution_context = EffectExecutionContext(
            effect=effect,
            unified_context=context,
            strategy=self._determine_fidelity_strategy(effect, context),
            timeout_seconds=timeout_seconds,
            execution_start_time=time.time()
        )
        
        try:
            # Step 1: Capability Check (Principle III)
            if self.capability_validator is None:
                from person_suit.core.capabilities.validator import (
                    get_capability_validator,  # noqa: WPS433
                )
                self.capability_validator = await get_capability_validator()

            try:
                await self._validate_capabilities(effect, context)
            except Exception as auth_exc:
                # Capability failed – record denial metric and return early
                try:
                    from person_suit.monitoring.metrics import EFFECT_DENIALS_TOTAL  # noqa: WPS433

                    EFFECT_DENIALS_TOTAL.labels(effect_type=label_val).inc()
                except Exception:  # pragma: no cover
                    pass

                from person_suit.core.effects import EffectAuthorizationError  # noqa: WPS433

                if isinstance(auth_exc, EffectAuthorizationError):
                    return EffectResult(success=False, error=str(auth_exc))
                raise  # Unexpected error – propagate
            
            # Step 2: Context-aware execution strategy (Principle II)
            logger.debug(f"Execution strategy chosen: {execution_context.strategy.value}")
            
            if execution_context.strategy == EffectExecutionStrategy.CACHED:
                cached_result = self._get_cached_result(effect, context)
                if cached_result:
                    return cached_result
            
            # Step 3: Execute via handler or registered strategy
            try:
                time.perf_counter()

                # Apply strategy-specific execution inside worker loop; execute_effect stays pure.
                raise RuntimeError("execute_effect called directly; use worker loop")
            except ValueError as handler_err:
                # No handler – try custom strategy registry
                strategy_func = self._get_registered_strategy(type(effect))
                if strategy_func is None:
                    raise handler_err  # re-raise original error
                result = await strategy_func(effect, context)
            
            # Step 4: Cache result if appropriate
            if self._should_cache_result(effect, context, result):
                self._cache_result(effect, context, result)
            
            # Step 5: Publish completion event
            await self._publish_effect_completion(effect, context, result, correlation_id=correlation_id)
            
            # Metrics – success path
            try:
                from person_suit.monitoring.metrics import EFFECT_EXECUTION_LATENCY_SECONDS
                from person_suit.monitoring.metrics import EFFECT_EXECUTIONS_TOTAL

                EFFECT_EXECUTIONS_TOTAL.labels(effect_type=label_val).inc()
                EFFECT_EXECUTION_LATENCY_SECONDS.labels(effect_type=label_val).observe(
                    time.time() - execution_context.execution_start_time,
                )
            except Exception:  # pragma: no cover
                pass
            
            # Map strategy to numeric fidelity bucket for handlers that accept int
            if execution_context.strategy == EffectExecutionStrategy.HIGH_FIDELITY:
                execution_context.fidelity_bucket = SCALE  # type: ignore[attr-defined]
            elif execution_context.strategy == EffectExecutionStrategy.MEDIUM_FIDELITY:
                execution_context.fidelity_bucket = FIDELITY_MED  # type: ignore[attr-defined]
            elif execution_context.strategy == EffectExecutionStrategy.LOW_FIDELITY:
                execution_context.fidelity_bucket = FIDELITY_MIN  # type: ignore[attr-defined]
            
            return result
            
        except Exception as e:
            # Handle execution failure
            error_result = EffectResult(
                success=False,
                error=str(e),
                metadata={
                    "effect_type": type(effect).__name__,
                    "execution_time": time.time() - execution_context.execution_start_time,
                    "strategy": execution_context.strategy.value
                }
            )
            
            await self._publish_effect_error(effect, context, error_result, correlation_id=correlation_id)
            return error_result
    
    def _determine_fidelity_strategy(
        self,
        effect: Effect,
        context: UnifiedContext
    ) -> EffectExecutionStrategy:
        """
        Determine the optimal execution strategy based on context and system state.
        
        This implements Adaptive Computational Fidelity (ACF) at the interpreter level.
        """
        # Ask external ACF manager if available (Sprint-2 Day-4)
        if self.acf_manager:
            try:
                policy = self.acf_manager.get_execution_policy(context, effect)  # type: ignore[attr-defined]
                strategy_name = policy.get("strategy")
                if strategy_name == "high":
                    return EffectExecutionStrategy.HIGH_FIDELITY
                if strategy_name == "low":
                    return EffectExecutionStrategy.LOW_FIDELITY
                if strategy_name == "cached":
                    return EffectExecutionStrategy.CACHED
            except Exception as exc:  # pragma: no cover – policy fetch failures fall back
                logger.debug("ACFManager policy retrieval failed: %s", exc)
        
        # High priority contexts always get high fidelity – accept both
        # ``Priority`` enum values and the numerically-typed legacy
        # ``MessagePriority`` alias coming from _message_bridge.

        # Handle different priority types
        prio_val = context.priority if isinstance(context.priority, int) else 0

        logger.debug(
            "Numeric priority bucket: %d (HIGH threshold %d)", prio_val, PRIO_HIGH
        )

        if prio_val >= PRIO_HIGH:
            return EffectExecutionStrategy.HIGH_FIDELITY
        
        # Check system load
        cpu_usage = self._system_metrics.get("cpu_usage", 0.0)
        memory_usage = self._system_metrics.get("memory_usage", 0.0)
        
        # Under high load, degrade fidelity for non-critical effects
        if cpu_usage > 80.0 or memory_usage > 85.0:
            if context.priority <= PRIO_LOW:
                return EffectExecutionStrategy.LOW_FIDELITY
            else:
                return EffectExecutionStrategy.MEDIUM_FIDELITY
        
        # Check if we have a cached result
        if self._has_cached_result(effect, context):
            cache_age = self._get_cache_age(effect, context)
            if cache_age < 300:  # 5 minutes
                return EffectExecutionStrategy.CACHED
        
        # Wave-particle ratio influence (Sprint-6 upgrade)
        try:
            ratio: float = float(getattr(context, "wave_particle_ratio", 0.5))
        except Exception:  # pragma: no cover – malformed/non-float attribute
            ratio = 0.5

        if ratio >= 0.75:
            return EffectExecutionStrategy.HIGH_FIDELITY
        if ratio <= 0.25:
            return EffectExecutionStrategy.LOW_FIDELITY
        
        # Effects that cannot degrade always execute at high fidelity
        if hasattr(effect, "can_degrade") and not getattr(effect, "can_degrade"):
            return EffectExecutionStrategy.HIGH_FIDELITY
        
        # Default to medium fidelity
        return EffectExecutionStrategy.MEDIUM_FIDELITY

    def _determine_execution_strategy(
        self,
        effect: Effect,
        context: UnifiedContext,
    ) -> ExecutionStrategy:
        """Determines the processing model (Wave vs Particle) for an effect."""
        # Simple priority-based determination for now.
        # This can be expanded with more complex logic (e.g., effect cost).
        prio_val = context.priority if isinstance(context.priority, int) else 0
        if prio_val >= PRIO_HIGH:
            return ExecutionStrategy.PARTICLE
        
        # Check for effect-specific hints
        if hasattr(effect, 'execution_hint'):
            if getattr(effect, 'execution_hint') == 'particle':
                return ExecutionStrategy.PARTICLE
            if getattr(effect, 'execution_hint') == 'wave':
                return ExecutionStrategy.WAVE

        # Default to wave for normal/low priority
        return ExecutionStrategy.WAVE
    
    async def _validate_capabilities(self, effect: Effect, context: UnifiedContext) -> None:
        """Validate that the context has required capabilities for the effect."""
        required_capability = getattr(effect, 'required_capability', None)
        
        if not required_capability:
            return  # No capability needed

        # Determine capability presence
        if self.capability_validator is not None:
            has_capability = await self.capability_validator.has(context, required_capability)
        else:
            # Fallback to context self-reporting
            try:
                has_capability = context.has_capability(required_capability)  # type: ignore[attr-defined]
            except AttributeError:
                # Legacy contexts without helper; fall back to attribute lookup
                caps = getattr(context, "capabilities", [])  # type: ignore[attr-defined]
                has_capability = required_capability in caps

        if not has_capability:
            from person_suit.core.effects import EffectAuthorizationError  # noqa: WPS433
            raise EffectAuthorizationError(
                f"Missing required capability: {required_capability} for effect {type(effect).__name__}"
            )
    
    async def _execute_with_strategy(
        self,
        execution_context: EffectExecutionContext
    ) -> EffectResult:
        """Execute effect with the determined strategy."""
        effect = execution_context.effect
        strategy = execution_context.strategy
        
        # Get the appropriate handler
        handler = self._get_effect_handler(type(effect))
        if not handler:
            raise ValueError(f"No handler registered for effect type: {type(effect).__name__}")
        
        # Execute based on strategy
        if strategy == EffectExecutionStrategy.HIGH_FIDELITY:
            return await handler.execute_high_fidelity(effect, execution_context)
        elif strategy == EffectExecutionStrategy.MEDIUM_FIDELITY:
            return await handler.execute_medium_fidelity(effect, execution_context)
        elif strategy == EffectExecutionStrategy.LOW_FIDELITY:
            return await handler.execute_low_fidelity(effect, execution_context)
        else:
            # Default execution
            return await handler.execute(effect, execution_context)
    
    def _register_builtin_handlers(self) -> None:
        """Register hard-wired handlers for legacy Effect subclasses.

        IOEffectHandler has been deprecated; IO effects are executed via
        `io_effect_strategy`.  We retain registration for any other still-live
        legacy handlers here.
        """
        try:
            # Currently no core legacy handlers required; placeholder for future
            pass
        except Exception as exc:  # pragma: no cover
            logger.debug("Skipping legacy handler registration: %s", exc)
    
    def _get_effect_handler(self, effect_type: Type[Effect]) -> Optional[Any]:
        """Get the handler for a specific effect type."""
        # Check for exact match first
        if effect_type in self._effect_handlers:
            return self._effect_handlers[effect_type]
        
        # Check for parent class matches
        for registered_type, handler in self._effect_handlers.items():
            if issubclass(effect_type, registered_type):
                return handler
        
        return None
    
    async def _handle_effect_message(self, message: HybridMessage) -> None:
        """Handle incoming effect messages from the message bus."""
        logger.info("="*60)
        logger.info("[INTERPRETER] Received effect message")
        logger.info(f"[INTERPRETER] Channel: {message.channel}")
        logger.info(f"[INTERPRETER] Message ID: {message.message_id}")
        logger.info(f"[INTERPRETER] Correlation ID: {getattr(message, 'correlation_id', 'None')}")
        logger.info(f"[INTERPRETER] Message type: {message.message_type}")
        
        try:
            # Extract effect and context from message
            effect_data = message.payload.get("effect")
            context_data = message.payload.get("context")
            
            logger.info(f"[INTERPRETER] Effect data present: {effect_data is not None}")
            logger.info(f"[INTERPRETER] Context data present: {context_data is not None}")
            
            if effect_data:
                logger.debug(f"[INTERPRETER] Effect type from data: {effect_data.get('type')}")
                logger.debug(f"[INTERPRETER] Effect operation: {effect_data.get('operation')}")
                logger.debug(f"[INTERPRETER] Effect table: {effect_data.get('table')}")
            
            if not effect_data or not context_data:
                logger.error("[INTERPRETER] Invalid effect message: missing effect or context data")
                logger.error(f"[INTERPRETER] Payload keys: {list(message.payload.keys())}")
                return

            # Deserialize effect and context
            logger.info("[INTERPRETER] Deserializing effect...")
            effect = self._deserialize_effect(effect_data)
            logger.info(f"[INTERPRETER] Deserialized effect type: {type(effect).__name__}")
            logger.debug(f"[INTERPRETER] Effect class: {effect.__class__}")
            logger.debug(f"[INTERPRETER] Effect required capability: {getattr(effect, 'required_capability', 'None')}")
            
            logger.info("[INTERPRETER] Creating context from data...")
            context = UnifiedContext.from_dict(context_data)
            logger.info(f"[INTERPRETER] Context domain: {context.domain}")
            logger.debug(f"[INTERPRETER] Context capabilities: {getattr(context, 'capabilities', [])}")
            
            # Decide target queue
            try:
                prio_val: int = getattr(message, "priority", PRIO_NORMAL)
            except Exception:  # noqa: BLE001
                prio_val = PRIO_NORMAL
            
            logger.info(f"[INTERPRETER] Message priority: {prio_val}")
            
            item = (-prio_val, time.time(), (effect, context, message))

            if prio_val >= PRIO_HIGH:
                await self._urgent_queue.put(item)
                logger.info(f"[INTERPRETER] Added to URGENT queue (size: {self._urgent_queue.qsize()})")
            else:
                await self._effect_queue.put(item)
                logger.info(f"[INTERPRETER] Added to NORMAL queue (size: {self._effect_queue.qsize()})")
            
            logger.info("[INTERPRETER] Effect queued for processing")
            logger.info("="*60)
            
        except Exception as e:
            logger.error(f"[INTERPRETER] Error handling effect message: {e}", exc_info=True)
            logger.error("="*60)
            # Increment metric counter if available
            try:
                if _EFFECT_DESERIALISATION_ERRORS_TOTAL is not None:
                    _EFFECT_DESERIALISATION_ERRORS_TOTAL.increment()
            except Exception:  # noqa: BLE001 – keep handler robust
                pass
                
    async def _handle_metrics_update(self, message: HybridMessage) -> None:
        """Handle system metrics updates for ACF decisions."""
        try:
            metrics = message.payload.get("metrics", {})
            self._system_metrics.update(metrics)
        except Exception as e:
            logger.error(f"Error handling metrics update: {e}")
    
    async def _process_effect_queue(self) -> None:
        """Process effects from the queue continuously."""
        worker_id = id(asyncio.current_task())
        logger.info(f"[WORKER-{worker_id}] Effect processing worker started")
        
        while self._running:
            try:
                # Normal workers focus on regular queue but fall back to urgent
                try:
                    item = await asyncio.wait_for(self._effect_queue.get(), timeout=1.0)
                    logger.debug(f"[WORKER-{worker_id}] Got item from NORMAL queue")
                except asyncio.TimeoutError:
                    # nothing in normal queue; check urgent queue
                    try:
                        item = self._urgent_queue.get_nowait()
                        logger.debug(f"[WORKER-{worker_id}] Got item from URGENT queue (fallback)")
                    except asyncio.QueueEmpty:  # type: ignore[attr-defined]
                        continue

                if item is None:
                    logger.info(f"[WORKER-{worker_id}] Received None, stopping")
                    break

                _, _, effect_data = item
                effect, context, original_message = effect_data
                
                correlation_id = getattr(original_message, 'correlation_id', 'unknown')
                logger.info(f"[WORKER-{worker_id}] Processing effect for correlation_id: {correlation_id}")
                logger.info(f"[WORKER-{worker_id}] Effect type: {type(effect).__name__}")
                logger.debug(f"[WORKER-{worker_id}] Effect details: {effect}")
                
                # Determine execution strategy
                strategy = self._determine_execution_strategy(effect, context)
                logger.info(f"[WORKER-{worker_id}] Execution strategy: {strategy.value}")

                # ---------------- CAW BRANCH RECORDING ----------------
                branch_val = "PARTICLE" if strategy == ExecutionStrategy.PARTICLE else "WAVE"
                try:
                    original_message.caw_branch_taken = branch_val  # type: ignore[attr-defined]
                    # Emit branch decision event for WaveTrace / Prometheus bridge
                    from person_suit.core.infrastructure.hybrid_message import HybridMessage
                    from person_suit.core.infrastructure.hybrid_message import MessageType
                    branch_evt = HybridMessage(
                        message_type=MessageType.EVENT,
                        channel="sys.monitor.caw_branch",
                        payload={
                            "effect_type": type(effect).__name__,
                            "branch": branch_val,
                            "actor_worker_id": worker_id,
                        },
                        response_expected=False,
                        trace_id=original_message.trace_id,
                        parent_span_id=original_message.span_id,
                    )
                    import uuid as _uuid
                    branch_evt.span_id = _uuid.uuid4().hex[:16]
                    if self.message_bus:
                        await self.message_bus.send(branch_evt)
                except Exception as exc:  # noqa: BLE001
                    logger.debug("Failed to emit CAW branch event: %s", exc)
                
                # Execute based on strategy
                logger.info(f"[WORKER-{worker_id}] Executing effect...")
                result = await self._run_effect_with_strategy(effect, context, strategy)
                
                logger.info(f"[WORKER-{worker_id}] Effect execution complete")
                logger.info(f"[WORKER-{worker_id}] Result success: {result.success}")
                if result.error:
                    logger.error(f"[WORKER-{worker_id}] Result error: {result.error}")
                logger.debug(f"[WORKER-{worker_id}] Result value: {result.value}")
                
                # Publish completion event
                if result.success:
                    logger.info(f"[WORKER-{worker_id}] Publishing completion event...")
                    await self._publish_effect_completion(
                        effect, context, result, 
                        correlation_id=correlation_id
                    )
                    logger.info(f"[WORKER-{worker_id}] Completion event published")
                else:
                    logger.error(f"[WORKER-{worker_id}] Publishing error event...")
                    await self._publish_effect_error(
                        effect, context, result,
                        correlation_id=correlation_id
                    )
                
            except Exception as e:
                logger.error(f"[WORKER-{worker_id}] Error processing effect: {e}", exc_info=True)
                
        logger.info(f"[WORKER-{worker_id}] Effect processing worker stopped")

    async def _emit_completion_event(self, effect: Effect, result: EffectResult) -> None:
        """Emit completion event after effect execution."""
        try:
            from person_suit.core.infrastructure.hybrid_message import HybridMessage
            from person_suit.core.infrastructure.hybrid_message import MessageType
            
            completion_event = HybridMessage(
                message_type=MessageType.EVENT.name,
                channel="event.effect.completed",
                payload={
                    "effect_type": effect.__class__.__name__,
                    "effect_id": getattr(effect, 'id', 'unknown'),
                    "success": result.success,
                    "error": result.error,
                    "execution_time_ms": result.execution_time_ms
                },
                correlation_id=getattr(effect, 'correlation_id', None)
            )
            
            # Get bus instance and publish completion event
            from person_suit.core.infrastructure.hybrid_message_bus import get_message_bus
            bus = get_message_bus()
            await bus.send(completion_event)
            
            logger.info("Emitted completion event for effect: %s (success=%s)", 
                       effect.__class__.__name__, result.success)
                       
        except Exception as e:
            logger.error("Failed to emit completion event: %s", e)

    def _deserialize_effect(self, effect_data: Dict[str, Any]) -> Effect:
        """Deserialize effect from dictionary data."""
        effect_type_str = effect_data.get("type")
        if not isinstance(effect_type_str, str):
            raise ValueError(f"Invalid or missing effect type in payload: {effect_data}")
        
        # Map effect type strings to classes
        effect_classes = {
            "database": DatabaseEffect,
            "event": EventEffect,
            "io": IOEffect,
            "network": NetworkEffect,
            "computation": ComputationEffect
        }
        
        effect_class = effect_classes.get(effect_type_str)

        if effect_class is None:
            raise ValueError(f"Unknown effect type: {effect_type_str}")

        # Specialised dispatch for DATABASE sub-types (write / read / delete)
        if effect_class is DatabaseEffect:
            op = effect_data.get("operation")
            if not isinstance(op, str):
                 raise ValueError(f"Invalid or missing operation for DatabaseEffect: {effect_data}")
            try:
                from person_suit.core.effects.database import DeleteDatabaseEffect
                from person_suit.core.effects.database import ReadDatabaseEffect
                from person_suit.core.effects.database import WriteDatabaseEffect

                db_map = {
                    "read": ReadDatabaseEffect,
                    "write": WriteDatabaseEffect,
                    "delete": DeleteDatabaseEffect,
                }
        
                specific_cls = db_map.get(op)
                if specific_cls is not None:
                    return specific_cls.from_dict(effect_data)
            except Exception as exc:  # noqa: BLE001 – fallback below
                logger.debug("DatabaseEffect deserialisation fallback: %s", exc)

        # ------------------------------------------------------------------
        # Smart dispatch for generic categories (IO, DATABASE, etc.) – select
        # the concrete subclass based on the *operation* field so that
        # e.g. WriteFileEffect serialised as {"type": "io", "operation": "write_file", ...}
        # deserialises back to the correct subclass.
        # ------------------------------------------------------------------
        if effect_class is IOEffect:
            op = effect_data.get("operation")
            if not isinstance(op, str):
                raise ValueError(f"Invalid or missing operation for IOEffect: {effect_data}")
            try:
                from person_suit.core.effects.io_effects import CreateDirectoryEffect
                from person_suit.core.effects.io_effects import DeleteFileEffect
                from person_suit.core.effects.io_effects import ListDirectoryEffect
                from person_suit.core.effects.io_effects import ReadFileEffect
                from person_suit.core.effects.io_effects import WriteFileEffect

                op_map = {
                    "read_file": ReadFileEffect,
                    "write_file": WriteFileEffect,
                    "delete_file": DeleteFileEffect,
                    "list_directory": ListDirectoryEffect,
                    "create_directory": CreateDirectoryEffect,
                }

                effect_cls = op_map.get(op)
                if effect_cls:
                    return effect_cls.from_dict(effect_data)
            except Exception as exc:  # noqa: BLE001 – fallback below
                logger.debug("IOEffect deserialisation fallback: %s", exc)

        # Fallback to generic class deserialiser (must implement from_dict)
        return effect_class.from_dict(effect_data)
            
    def _get_cached_result(self, effect: Effect, context: UnifiedContext) -> Optional[EffectResult]:
        """Get cached result for effect if available."""
        cache_key = self._generate_cache_key(effect, context)
        return self._execution_cache.get(cache_key)
    
    def _has_cached_result(self, effect: Effect, context: UnifiedContext) -> bool:
        """Check if cached result exists."""
        cache_key = self._generate_cache_key(effect, context)
        return cache_key in self._execution_cache
    
    def _get_cache_age(self, effect: Effect, context: UnifiedContext) -> float:
        """Get age of cached result in seconds."""
        cache_key = self._generate_cache_key(effect, context)
        cached_result = self._execution_cache.get(cache_key)
        if cached_result and cached_result.metadata.get("cached_at"):
            return time.time() - cached_result.metadata["cached_at"]
        return float('inf')
    
    def _should_cache_result(self, effect: Effect, context: UnifiedContext, result: EffectResult) -> bool:
        """Determine if result should be cached."""
        # Don't cache errors
        if not result.success:
            return False
        
        # Don't cache high-priority or real-time contexts
        if context.priority >= PRIO_HIGH:
            return False
        
        # Cache based on effect type
        cacheable_types = {DatabaseEffect, ComputationEffect}
        return type(effect) in cacheable_types
    
    def _cache_result(self, effect: Effect, context: UnifiedContext, result: EffectResult) -> None:
        """Cache execution result."""
        cache_key = self._generate_cache_key(effect, context)
        result.metadata["cached_at"] = time.time()
        self._execution_cache[cache_key] = result
    
    def _generate_cache_key(self, effect: Effect, context: UnifiedContext) -> str:
        """Generate cache key for effect and context."""
        import hashlib
        
        effect_str = f"{type(effect).__name__}:{effect.to_dict()}"
        context_str = f"{context.priority}:{context.domain}"
        
        combined = f"{effect_str}:{context_str}"
        return hashlib.sha256(combined.encode()).hexdigest()
    
    async def _publish_effect_completion(
        self,
        effect: Effect,
        context: UnifiedContext,
        result: EffectResult,
        correlation_id: Optional[str] = None,
    ) -> None:
        """Publish effect completion event."""
        if self.message_bus:
            completion_message = HybridMessage(
                message_type=MessageType.EVENT.name,
                channel="event.effect.completed",
                payload={
                    "effect_type": type(effect).__name__,
                    "success": result.success,
                    "execution_time": result.metadata.get("execution_time", 0.0),
                    "context_priority": context.priority
                }
            )
            await self.message_bus.send(completion_message)

    async def _publish_effect_error(
        self,
        effect: Effect,
        context: UnifiedContext,
        result: EffectResult,
        correlation_id: Optional[str] = None,
    ) -> None:
        """Publish effect error event."""
        if self.message_bus:
            error_message = HybridMessage(
                message_type=MessageType.EVENT.name,
                channel="event.effect.error",
                payload={
                    "effect_type": type(effect).__name__,
                    "error": result.error,
                    "context_priority": context.priority
                }
            )
            await self.message_bus.send(error_message)

        # Prometheus metric
        try:
            from person_suit.monitoring.metrics import EFFECT_ERRORS_TOTAL  # noqa: WPS433

            EFFECT_ERRORS_TOTAL.labels(effect_type=type(effect).__name__).inc()
        except Exception:
            pass

    # ------------------------------------------------------------------
    # Strategy Registry helpers
    # ------------------------------------------------------------------

    def register_strategy(self, effect_type: Type[Effect], coroutine_fn: Any) -> None:
        """Register or replace an execution strategy for a given Effect subclass."""
        self._strategy_registry[effect_type] = coroutine_fn

    def _get_registered_strategy(self, effect_type: Type[Effect]):  # noqa: D401
        """Return the nearest matching registered strategy based on subclass walk."""
        # Exact match first
        if effect_type in self._strategy_registry:
            return self._strategy_registry[effect_type]

        # Walk MRO for parent match
        for base in effect_type.__mro__[1:]:
            if base in self._strategy_registry:
                return self._strategy_registry[base]
        return None

    def _register_default_strategies(self) -> None:
        """Register async execution strategies provided in `effects.strategies`.

        This replaces the earlier placeholder logic and centralises strategy
        discovery.  Any `Effect` subclass that appears in `ALL_STRATEGIES` of
        `person_suit.core.effects.strategies` will now be executed via that
        coroutine instead of legacy handler classes.  This is a **hard
        migration** step toward CAW Principle-I compliance and paves the way
        for deleting obsolete *handler* modules.
        """

        try:
            from person_suit.core.effects.strategies import ALL_STRATEGIES  # noqa: WPS433

            for _effect_cls, _coro in ALL_STRATEGIES.items():
                try:
                    self.register_strategy(_effect_cls, _coro)
                except Exception as reg_exc:  # pragma: no cover – log & continue
                    logger.debug(
                        "Failed to register strategy for %s: %s", _effect_cls, reg_exc,
                    )

            logger.info("EffectInterpreter registered %d strategy handlers", len(ALL_STRATEGIES))

        except ModuleNotFoundError as exc:  # pragma: no cover – strategies module missing
            logger.warning("Strategy module not found, skipping default registration: %s", exc)

    # ------------------------------------------------------------------
    # Autoscaler monitors queue depth & spawns/terminates workers
    # ------------------------------------------------------------------

    async def _autoscaler_loop(self, base_workers: int) -> None:  # noqa: D401
        """Dynamically scale worker pool based on queue pressure."""
        import os  # noqa: WPS433
        cpu_total = os.cpu_count() or 4
        max_workers = int(cpu_total * 0.75)

        import asyncio as _aio

        from person_suit.monitoring.metrics import WORKER_UTILISATION  # noqa: WPS433

        while self._running:
            try:
                await _aio.sleep(1.0)

                pending = self._effect_queue.qsize() + self._urgent_queue.qsize()
                active = sum(1 for t in self._worker_tasks if not t.done())

                utilisation = pending / max(1, active * 10)  # heuristic denominator
                WORKER_UTILISATION.set(min(1.0, utilisation))

                # Scale up
                if pending > active * 5 and active < max_workers:  # High backlog
                    new_task = _aio.create_task(self._process_effect_queue())
                    self._worker_tasks.append(new_task)
                    logger.debug("Autoscaler added worker: total=%d", active + 1)

                # Scale down
                if pending < active and active > base_workers:
                    # Don't actually scale down - just log it
                    # Putting None in the queue causes workers to exit
                    logger.debug("Autoscaler would scale down but keeping workers: active=%d", active)
            except Exception as exc:  # noqa: BLE001
                logger.debug("Autoscaler loop error: %s", exc)

    # ------------------------------------------------------------------
    # Urgent worker loop (consumes only urgent queue)
    # ------------------------------------------------------------------

    async def _urgent_worker_loop(self) -> None:  # noqa: D401
        """Continuously process effects from the urgent queue only."""
        import asyncio as _aio

        while self._running:
            try:
                item = await _aio.wait_for(self._urgent_queue.get(), timeout=1.0)
                if item is None:
                    continue

                _, _, effect_data = item
                effect, context, _orig = effect_data

                strat = self._determine_execution_strategy(effect, context)
                result = await self._run_effect_with_strategy(effect, context, strat)

                if not result.success and result.error == "demoted":
                    continue

            except _aio.TimeoutError:
                continue
            except Exception as exc:  # noqa: BLE001
                logger.error("Urgent worker error: %s", exc)

    # ------------------------------------------------------------------
    # Helper to run effect according to strategy, with quantum & demotion
    # ------------------------------------------------------------------

    async def _run_effect_with_strategy(
        self,
        effect: Effect,
        context: UnifiedContext,
        strategy: ExecutionStrategy,
    ) -> EffectResult:
        """Execute *effect* respecting *strategy* and quantum limits."""
        # The direct call to `self.execute_effect` is problematic as it's meant
        # to be the entry point which contains the `raise RuntimeError`.
        # The actual execution logic should be here.
        
        # We will replicate the core logic from `execute_effect` here,
        # but without the recursive/forbidden call.
        
        execution_start_time = time.time()
        
        # Step 1: Capability Check
        try:
            await self._validate_capabilities(effect, context)
        except Exception as auth_exc:
            label_val = str(effect.effect_type.value).lower()
            try:
                from person_suit.monitoring.metrics import EFFECT_DENIALS_TOTAL
                EFFECT_DENIALS_TOTAL.labels(effect_type=label_val).inc()
            except Exception:  # pragma: no cover
                pass
            return EffectResult(success=False, error=str(auth_exc))
            
        # Step 2: Determine Fidelity & Create Execution Context
        fidelity_strategy = self._determine_fidelity_strategy(effect, context)
        execution_context = EffectExecutionContext(
            effect=effect,
            unified_context=context,
            strategy=fidelity_strategy,
            timeout_seconds=30.0, # default timeout
            execution_start_time=execution_start_time
        )

        # Step 3: Execute based on Wave/Particle strategy
        actual_result: EffectResult

        if strategy == ExecutionStrategy.WAVE and context.priority < PRIO_HIGH:
            async with self._low_prio_sema:
                actual_result = await self._execute_with_strategy(execution_context)

        # PARTICLE path with quantum guard
        else: # Particle is default
            try:
                actual_result = await asyncio.wait_for(
                    self._execute_with_strategy(execution_context), 
                    timeout=0.02
                )
            except asyncio.TimeoutError:
                PARTICLE_DEMOTIONS_TOTAL.labels(
                    effect_type=effect.__class__.__name__.lower()
                ).inc()
                # enqueue for wave processing (original message not critical)
                self._wave_buffer.append((effect, context, None))
                return EffectResult(success=False, error="demoted", execution_time_ms=20)

        # Step 4: Publish completion/error and return
        if actual_result.success:
            await self._publish_effect_completion(effect, context, actual_result)
        else:
            await self._publish_effect_error(effect, context, actual_result)
            
        return actual_result


# Global interpreter instance
_global_interpreter: Optional[EffectInterpreter] = None


async def get_effect_interpreter(bus: Optional[HybridMessageBus] = None) -> EffectInterpreter:  # noqa: D401
    """Return a singleton interpreter, rebinding to *bus* if necessary.

    The global singleton simplifies dependency injection across the codebase,
    but we still need to cope with *bus* instances being restarted during
    test-runs (e.g. ``stop_message_bus`` + ``get_message_bus``).  When the
    caller provides a *bus* that differs from the interpreter's current
    ``message_bus`` we:

    1. Update ``interpreter.message_bus`` so subsequent internal ``send``
       calls target the correct queue.
    2. Re-subscribe the *effect.* pattern to the new bus so that incoming
       effect messages are processed.
    3. Keep the interpreter's internal state (caches, running loop) intact –
       this avoids re-initialising heavy handler dependencies between tests.
    """

    global _global_interpreter  # noqa: WPS420 – singleton mutable state

    if _global_interpreter is None:
        # First-time creation – honour caller-provided *bus* or fall back to
        # the global singleton bus helper.
        if bus is None:
            bus = get_message_bus()
        _global_interpreter = EffectInterpreter(bus)
        await _global_interpreter.initialize()
        return _global_interpreter

    # Already exists – rebind if the bus changed.
    if bus is not None and bus is not _global_interpreter.message_bus:
        old_bus = _global_interpreter.message_bus
        _global_interpreter.message_bus = bus
        
        # Re-register handlers with new bus
        _global_interpreter._register_builtin_handlers()

        # Re-create subscription on *new* bus.
        try:
            # Unsubscribe from old bus if still active – ignore errors.
            if old_bus is not None:
                old_bus.unsubscribe("effect_interpreter_effects", "effect.*")  # type: ignore[arg-type]
        except Exception:  # pragma: no cover – diagnostic only
            pass

        await bus.subscribe(
            "effect.*",
            _global_interpreter._handle_effect_message,  # noqa: WPS437 – private method access is fine within module
            subscriber_id="effect_interpreter_effects",
            handler_priority=0,
        )

        logger.info("Re-bound EffectInterpreter to new bus instance")

    return _global_interpreter


async def execute_effect(effect: Effect, context: UnifiedContext) -> EffectResult:
    """
    Convenience function to execute an effect through the global interpreter.
    
    This is the main entry point for effect execution in the system.
    """
    interpreter = await get_effect_interpreter()
    return await interpreter.execute_effect(effect, context) 