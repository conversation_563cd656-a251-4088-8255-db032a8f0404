"""
Network Effects - Declarative Network Operations
===============================================

This module defines network effects that declare network operations
without performing them. The EffectInterpreter handles execution.

Related Files:
- base.py: Base effect classes
- interpreter.py: Effect execution
- handlers/network.py: Network effect handler

Dependencies:
- Base effect system only
"""

from typing import Any
from typing import Dict
from typing import List
from typing import Optional
from typing import Union

from person_suit.core.constants.fixed_point_scale import SCALE

# Use absolute imports to avoid circular import issues
try:
    from .base import CacheableEffect
    from .base import Effect
    from .base import EffectType
    from .base import IdempotentEffect
except ImportError:
    # Fallback for direct module import
    from base import CacheableEffect
    from base import Effect
    from base import EffectType
    from base import IdempotentEffect


class NetworkEffect(Effect):
    """Base class for all network effects."""
    
    def __init__(
        self,
        operation: str,
        url: str,
        required_capability: Optional[str] = None,
        metadata: Optional[Dict[str, Any]] = None
    ):
        super().__init__(
            effect_type=EffectType.NETWORK,
            operation=operation,
            required_capability=required_capability or f"network:{operation}",
            metadata=metadata
        )
        self.url = url


class HTTPRequestEffect(NetworkEffect, CacheableEffect):
    """Effect for making HTTP requests."""
    
    def __init__(
        self,
        url: str,
        method: str = "GET",
        headers: Optional[Dict[str, str]] = None,
        params: Optional[Dict[str, Any]] = None,
        data: Optional[Union[str, bytes, Dict[str, Any]]] = None,
        json_data: Optional[Dict[str, Any]] = None,
        timeout: float = 30.0,
        follow_redirects: bool = True,
        verify_ssl: bool = True,
        metadata: Optional[Dict[str, Any]] = None
    ):
        super().__init__(
            operation="http_request",
            url=url,
            required_capability=f"network:http:{method.lower()}",
            metadata=metadata
        )
        self.method = method.upper()
        self.headers = headers or {}
        self.params = params or {}
        self.data = data
        self.json_data = json_data
        self.timeout = timeout
        self.follow_redirects = follow_redirects
        self.verify_ssl = verify_ssl
        
        # GET requests are cacheable, others are not
        if self.method == "GET":
            self.cache_ttl_seconds = 300  # 5 minutes
        else:
            self.can_degrade = False
            self.min_fidelity = SCALE
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary."""
        return {
            "type": "network",
            "operation": "http_request",
            "id": self.id,
            "timestamp": self.timestamp.isoformat(),
            "url": self.url,
            "method": self.method,
            "headers": self.headers,
            "params": self.params,
            "data": self.data,
            "json_data": self.json_data,
            "timeout": self.timeout,
            "follow_redirects": self.follow_redirects,
            "verify_ssl": self.verify_ssl,
            "required_capability": self.required_capability,
            "metadata": self.metadata,
            "can_degrade": self.can_degrade,
            "min_fidelity": self.min_fidelity,
            "cache_ttl_seconds": self.cache_ttl_seconds
        }
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'HTTPRequestEffect':
        """Create from dictionary."""
        return cls(
            url=data["url"],
            method=data.get("method", "GET"),
            headers=data.get("headers"),
            params=data.get("params"),
            data=data.get("data"),
            json_data=data.get("json_data"),
            timeout=data.get("timeout", 30.0),
            follow_redirects=data.get("follow_redirects", True),
            verify_ssl=data.get("verify_ssl", True),
            metadata=data.get("metadata")
        )


class WebSocketConnectEffect(NetworkEffect):
    """Effect for establishing WebSocket connections."""
    
    def __init__(
        self,
        url: str,
        protocols: Optional[List[str]] = None,
        headers: Optional[Dict[str, str]] = None,
        timeout: float = 30.0,
        metadata: Optional[Dict[str, Any]] = None
    ):
        super().__init__(
            operation="websocket_connect",
            url=url,
            required_capability="network:websocket:connect",
            metadata=metadata
        )
        self.protocols = protocols or []
        self.headers = headers or {}
        self.timeout = timeout
        
        # WebSocket connections cannot be degraded
        self.can_degrade = False
        self.min_fidelity = SCALE
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary."""
        return {
            "type": "network",
            "operation": "websocket_connect",
            "id": self.id,
            "timestamp": self.timestamp.isoformat(),
            "url": self.url,
            "protocols": self.protocols,
            "headers": self.headers,
            "timeout": self.timeout,
            "required_capability": self.required_capability,
            "metadata": self.metadata,
            "can_degrade": self.can_degrade,
            "min_fidelity": self.min_fidelity,
            "cache_ttl_seconds": self.cache_ttl_seconds
        }
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'WebSocketConnectEffect':
        """Create from dictionary."""
        return cls(
            url=data["url"],
            protocols=data.get("protocols"),
            headers=data.get("headers"),
            timeout=data.get("timeout", 30.0),
            metadata=data.get("metadata")
        )


class WebSocketSendEffect(NetworkEffect):
    """Effect for sending WebSocket messages."""
    
    def __init__(
        self,
        connection_id: str,
        message: Union[str, bytes],
        message_type: str = "text",
        metadata: Optional[Dict[str, Any]] = None
    ):
        super().__init__(
            operation="websocket_send",
            url=f"ws://connection/{connection_id}",
            required_capability="network:websocket:send",
            metadata=metadata
        )
        self.connection_id = connection_id
        self.message = message
        self.message_type = message_type
        
        # WebSocket sends cannot be degraded
        self.can_degrade = False
        self.min_fidelity = SCALE
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary."""
        return {
            "type": "network",
            "operation": "websocket_send",
            "id": self.id,
            "timestamp": self.timestamp.isoformat(),
            "connection_id": self.connection_id,
            "message": self.message,
            "message_type": self.message_type,
            "required_capability": self.required_capability,
            "metadata": self.metadata,
            "can_degrade": self.can_degrade,
            "min_fidelity": self.min_fidelity,
            "cache_ttl_seconds": self.cache_ttl_seconds
        }
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'WebSocketSendEffect':
        """Create from dictionary."""
        return cls(
            connection_id=data["connection_id"],
            message=data["message"],
            message_type=data.get("message_type", "text"),
            metadata=data.get("metadata")
        )


class DNSLookupEffect(NetworkEffect, CacheableEffect, IdempotentEffect):
    """Effect for DNS lookups."""
    
    def __init__(
        self,
        hostname: str,
        record_type: str = "A",
        timeout: float = 10.0,
        metadata: Optional[Dict[str, Any]] = None
    ):
        super().__init__(
            operation="dns_lookup",
            url=f"dns://{hostname}",
            required_capability="network:dns:lookup",
            metadata=metadata
        )
        self.hostname = hostname
        self.record_type = record_type
        self.timeout = timeout
        
        # DNS lookups are highly cacheable
        self.cache_ttl_seconds = 3600  # 1 hour
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary."""
        return {
            "type": "network",
            "operation": "dns_lookup",
            "id": self.id,
            "timestamp": self.timestamp.isoformat(),
            "hostname": self.hostname,
            "record_type": self.record_type,
            "timeout": self.timeout,
            "required_capability": self.required_capability,
            "metadata": self.metadata,
            "can_degrade": self.can_degrade,
            "min_fidelity": self.min_fidelity,
            "cache_ttl_seconds": self.cache_ttl_seconds
        }
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'DNSLookupEffect':
        """Create from dictionary."""
        return cls(
            hostname=data["hostname"],
            record_type=data.get("record_type", "A"),
            timeout=data.get("timeout", 10.0),
            metadata=data.get("metadata")
        )


# Convenience functions for creating network effects
def http_get(
    url: str,
    headers: Optional[Dict[str, str]] = None,
    params: Optional[Dict[str, Any]] = None,
    timeout: float = 30.0
) -> HTTPRequestEffect:
    """Create an HTTP GET effect."""
    return HTTPRequestEffect(
        url=url,
        method="GET",
        headers=headers,
        params=params,
        timeout=timeout
    )


def http_post(
    url: str,
    data: Optional[Union[str, bytes, Dict[str, Any]]] = None,
    json_data: Optional[Dict[str, Any]] = None,
    headers: Optional[Dict[str, str]] = None,
    timeout: float = 30.0
) -> HTTPRequestEffect:
    """Create an HTTP POST effect."""
    return HTTPRequestEffect(
        url=url,
        method="POST",
        headers=headers,
        data=data,
        json_data=json_data,
        timeout=timeout
    )


def http_put(
    url: str,
    data: Optional[Union[str, bytes, Dict[str, Any]]] = None,
    json_data: Optional[Dict[str, Any]] = None,
    headers: Optional[Dict[str, str]] = None,
    timeout: float = 30.0
) -> HTTPRequestEffect:
    """Create an HTTP PUT effect."""
    return HTTPRequestEffect(
        url=url,
        method="PUT",
        headers=headers,
        data=data,
        json_data=json_data,
        timeout=timeout
    )


def http_delete(
    url: str,
    headers: Optional[Dict[str, str]] = None,
    timeout: float = 30.0
) -> HTTPRequestEffect:
    """Create an HTTP DELETE effect."""
    return HTTPRequestEffect(
        url=url,
        method="DELETE",
        headers=headers,
        timeout=timeout
    )


def websocket_connect(
    url: str,
    protocols: Optional[List[str]] = None,
    headers: Optional[Dict[str, str]] = None
) -> WebSocketConnectEffect:
    """Create a WebSocket connect effect."""
    return WebSocketConnectEffect(
        url=url,
        protocols=protocols,
        headers=headers
    )


def websocket_send(
    connection_id: str,
    message: Union[str, bytes],
    message_type: str = "text"
) -> WebSocketSendEffect:
    """Create a WebSocket send effect."""
    return WebSocketSendEffect(
        connection_id=connection_id,
        message=message,
        message_type=message_type
    )


def dns_lookup(
    hostname: str,
    record_type: str = "A",
    timeout: float = 10.0
) -> DNSLookupEffect:
    """Create a DNS lookup effect."""
    return DNSLookupEffect(
        hostname=hostname,
        record_type=record_type,
        timeout=timeout
    ) 