"""Chat Effect Handler
====================

Executes chat-related `Effect`s declared by business-logic components.  This
module is imported by the central *EffectInterpreter* during bootstrap.  It
relies on fixed-point priority/fidelity buckets introduced in Sprint-1.

Related Files
-------------
• person_suit/io_layer/adapters/chat/effects_adapter.py – Effect definitions
• person_suit/core/effects/base.py – Base effect classes
• person_suit/core/effects/interpreter.py – Effect interpreter
"""

# ---------------------------------------------------------------------------
# Fixed-point buckets needed for stub fallbacks and fidelity calculations.
# ---------------------------------------------------------------------------
import asyncio
import logging
import time
from typing import Any
from typing import Dict
from typing import List
from typing import Union

from person_suit.core.constants.fixed_point_scale import PRIO_NORMAL
from person_suit.core.constants.fixed_point_scale import SCALE

# Use absolute imports to avoid circular import issues
try:
    from person_suit.core.context.unified import UnifiedContext
    from person_suit.core.effects.base import EffectExecutionContext
    from person_suit.core.effects.base import EffectResult
    from person_suit.io_layer.adapters.chat.effects_adapter import ChatReceiveMessageEffect
    from person_suit.io_layer.adapters.chat.effects_adapter import ChatSendMessageEffect
except ImportError:
    # Fallback for direct module import
    import sys
    from pathlib import Path
    
    # Add parent directories to path
    current_dir = Path(__file__).parent
    effects_dir = current_dir.parent
    sys.path.insert(0, str(effects_dir))
    
    from base import EffectExecutionContext
    from base import EffectResult
    
    # Mock classes for testing
    class UnifiedContext:
        def __init__(self):
            self.domain = "test"
            self.priority = PRIO_NORMAL
    
    class ChatSendMessageEffect:
        def __init__(self, **kwargs):
            for k, v in kwargs.items():
                setattr(self, k, v)
    
    class ChatReceiveMessageEffect:
        def __init__(self, **kwargs):
            for k, v in kwargs.items():
                setattr(self, k, v)

logger = logging.getLogger(__name__)

# ---------------------------------------------------------------------------
# Helper – normalise fidelity value (bucket int or ratio float) to 0-1 float.
# ---------------------------------------------------------------------------

def _to_ratio(raw: Union[int, float]) -> float:  # noqa: D401 – tiny utility
    """Convert *raw* fidelity (bucket or ratio) to 0-1 float."""
    if isinstance(raw, int) and raw > 1:
        return max(0.0, min(1.0, raw / float(SCALE)))
    try:
        return max(0.0, min(1.0, float(raw)))
    except Exception:  # pragma: no cover – defensive fallback
        return 1.0

class ChatEffectHandler:
    """Handler for executing chat effects."""
    
    def __init__(self):
        # Simple in-memory storage for chat messages
        self._message_store: Dict[str, List[Dict[str, Any]]] = {}
        self._active_channels: set = set()
        
        # Performance tracking
        self._total_operations = 0
        self._successful_operations = 0
        self._failed_operations = 0
        
        # Chat session tracking
        self._active_sessions: Dict[str, Dict[str, Any]] = {}
        
    async def initialize(self) -> None:
        """Initialize chat handler and resources."""
        logger.info("Chat effect handler initialized")
        
        # Create default channels
        self._active_channels.add("general")
        self._active_channels.add("persona:default")
        
    async def execute(
        self,
        effect: Union[ChatSendMessageEffect, ChatReceiveMessageEffect],
        execution_context: EffectExecutionContext
    ) -> EffectResult:
        """
        Execute a chat effect with standard fidelity.
        
        Args:
            effect: The chat effect to execute
            execution_context: Execution context
            
        Returns:
            EffectResult with execution outcome
        """
        return await self._execute_with_fidelity(effect, execution_context, 1.0)
    
    async def execute_high_fidelity(
        self,
        effect: Union[ChatSendMessageEffect, ChatReceiveMessageEffect],
        execution_context: EffectExecutionContext
    ) -> EffectResult:
        """Execute chat effect with high fidelity."""
        return await self._execute_with_fidelity(effect, execution_context, 1.0)
    
    async def execute_medium_fidelity(
        self,
        effect: Union[ChatSendMessageEffect, ChatReceiveMessageEffect],
        execution_context: EffectExecutionContext
    ) -> EffectResult:
        """Execute chat effect with medium fidelity."""
        return await self._execute_with_fidelity(effect, execution_context, 0.7)
    
    async def execute_low_fidelity(
        self,
        effect: Union[ChatSendMessageEffect, ChatReceiveMessageEffect],
        execution_context: EffectExecutionContext
    ) -> EffectResult:
        """Execute chat effect with low fidelity."""
        return await self._execute_with_fidelity(effect, execution_context, 0.3)
    
    async def _execute_with_fidelity(
        self,
        effect: Union[ChatSendMessageEffect, ChatReceiveMessageEffect],
        execution_context: EffectExecutionContext,
        fidelity: Union[int, float]
    ) -> EffectResult:
        """Execute effect with specified fidelity level."""
        fidelity_ratio = _to_ratio(fidelity)
        start_time = time.time()
        self._total_operations += 1
        
        try:
            # Route to appropriate handler
            if isinstance(effect, ChatSendMessageEffect):
                result = await self._handle_send_message(effect, fidelity_ratio)
            elif isinstance(effect, ChatReceiveMessageEffect):
                result = await self._handle_receive_message(effect, fidelity_ratio)
            else:
                raise ValueError(f"Unsupported chat effect type: {type(effect)}")
            
            execution_time = time.time() - start_time
            self._successful_operations += 1
            
            return EffectResult(
                success=True,
                value=result,
                execution_time_ms=execution_time * 1000,
                metadata={
                    "effect_type": type(effect).__name__,
                    "fidelity_used": fidelity_ratio,
                    "channel": getattr(effect, 'channel', 'unknown'),
                    "handler": "chat_effect_handler"
                }
            )
            
        except Exception as e:
            execution_time = time.time() - start_time
            self._failed_operations += 1
            logger.error(f"Chat effect execution failed: {e}")
            
            return EffectResult(
                success=False,
                error=str(e),
                execution_time_ms=execution_time * 1000,
                metadata={
                    "effect_type": type(effect).__name__,
                    "fidelity_used": fidelity_ratio,
                    "channel": getattr(effect, 'channel', 'unknown'),
                    "handler": "chat_effect_handler"
                }
            )
    
    async def _handle_send_message(
        self,
        effect: ChatSendMessageEffect,
        fidelity: float
    ) -> Dict[str, Any]:
        """Handle chat message sending."""
        # Ensure channel exists
        if effect.channel not in self._active_channels:
            self._active_channels.add(effect.channel)
            self._message_store[effect.channel] = []
        
        # Apply fidelity to message processing
        content = effect.content
        if fidelity < 0.5:
            # Low fidelity: truncate long messages
            content = content[:100] + "..." if len(content) > 100 else content
        elif fidelity < 0.8:
            # Medium fidelity: limit message length
            content = content[:500] + "..." if len(content) > 500 else content
        
        # Create message record
        message_record = {
            "id": f"msg_{int(time.time() * 1000)}",
            "channel": effect.channel,
            "content": content,
            "user_id": effect.user_id or "system",
            "timestamp": time.time(),
            "fidelity": fidelity,
            "original_length": len(effect.content)
        }
        
        # Store message
        if effect.channel not in self._message_store:
            self._message_store[effect.channel] = []
        
        self._message_store[effect.channel].append(message_record)
        
        # Keep only last 100 messages per channel
        if len(self._message_store[effect.channel]) > 100:
            self._message_store[effect.channel] = self._message_store[effect.channel][-100:]
        
        # Simulate processing delay based on fidelity
        delay = 0.1 if fidelity >= 0.7 else 0.05 if fidelity >= 0.3 else 0.01
        await asyncio.sleep(delay)
        
        # Output to console for demonstration
        print(f"[CHAT:{effect.channel}] {effect.user_id or 'System'}: {content}")
        
        logger.info(f"Chat message sent to {effect.channel}: {content[:50]}...")
        
        return {
            "message_id": message_record["id"],
            "channel": effect.channel,
            "content_sent": content,
            "user_id": effect.user_id,
            "timestamp": message_record["timestamp"],
            "fidelity_applied": fidelity,
            "truncated": len(content) < len(effect.content)
        }
    
    async def _handle_receive_message(
        self,
        effect: ChatReceiveMessageEffect,
        fidelity: float
    ) -> Dict[str, Any]:
        """Handle chat message receiving."""
        # Ensure channel exists
        if effect.channel not in self._active_channels:
            self._active_channels.add(effect.channel)
            self._message_store[effect.channel] = []
        
        # Process incoming message
        content = effect.content
        if fidelity < 0.5:
            # Low fidelity: basic processing only
            content = content.strip()
        elif fidelity < 0.8:
            # Medium fidelity: some text processing
            content = content.strip().lower()
        else:
            # High fidelity: full text processing
            content = content.strip()
            # Could add more sophisticated processing here
        
        # Create message record
        message_record = {
            "id": f"msg_{int(time.time() * 1000)}",
            "channel": effect.channel,
            "content": content,
            "user_id": effect.user_id,
            "timestamp": time.time(),
            "fidelity": fidelity,
            "direction": "incoming"
        }
        
        # Store message
        if effect.channel not in self._message_store:
            self._message_store[effect.channel] = []
        
        self._message_store[effect.channel].append(message_record)
        
        # Keep only last 100 messages per channel
        if len(self._message_store[effect.channel]) > 100:
            self._message_store[effect.channel] = self._message_store[effect.channel][-100:]
        
        # Simulate processing delay
        delay = 0.05 if fidelity >= 0.5 else 0.02
        await asyncio.sleep(delay)
        
        logger.info(f"Chat message received in {effect.channel} from {effect.user_id}: {content[:50]}...")
        
        return {
            "message_id": message_record["id"],
            "channel": effect.channel,
            "content_processed": content,
            "user_id": effect.user_id,
            "timestamp": message_record["timestamp"],
            "fidelity_applied": fidelity,
            "processing_applied": fidelity >= 0.8
        }
    
    def get_channel_history(
        self,
        channel: str,
        limit: int = 10
    ) -> List[Dict[str, Any]]:
        """Get message history for a channel."""
        if channel not in self._message_store:
            return []
        
        messages = self._message_store[channel]
        return messages[-limit:] if limit > 0 else messages
    
    def get_active_channels(self) -> List[str]:
        """Get list of active chat channels."""
        return list(self._active_channels)
    
    def get_performance_metrics(self) -> Dict[str, Any]:
        """Get performance metrics for the handler."""
        return {
            "total_operations": self._total_operations,
            "successful_operations": self._successful_operations,
            "failed_operations": self._failed_operations,
            "success_rate": (
                self._successful_operations / self._total_operations 
                if self._total_operations > 0 else 0.0
            ),
            "active_channels": len(self._active_channels),
            "total_messages": sum(len(msgs) for msgs in self._message_store.values()),
            "channels": list(self._active_channels)
        }
    
    async def shutdown(self) -> None:
        """Shutdown the chat handler."""
        logger.info("Chat effect handler shutdown complete")
        
        # Could save message history to persistent storage here
        print(f"\n[CHAT HANDLER] Shutdown complete. Processed {self._total_operations} operations.") 