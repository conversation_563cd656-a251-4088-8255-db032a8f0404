"""
Effect Handlers Package
======================

This package contains the concrete handlers that execute specific effect types.
Each handler implements the actual I/O operations for its effect type.

Related Files:
- database.py: Database effect handler
- event.py: Event effect handler
- io.py: I/O effect handler
- network.py: Network effect handler
- computation.py: Computation effect handler
"""

# Legacy handlers removed in strategy migration

__all__ = [
    # Legacy handlers removed in strategy migration
] 