"""
Computation Effects - Declarative Computational Operations
=========================================================

This module defines computation effects that declare computational operations
without performing them. The EffectInterpreter handles execution.

Related Files:
- base.py: Base effect classes
- interpreter.py: Effect execution
- handlers/computation.py: Computation effect handler

Dependencies:
- Base effect system only
"""

from typing import Any
from typing import Dict
from typing import Optional
from typing import Union

from person_suit.core.constants.fixed_point_scale import SCALE

# Use absolute imports to avoid circular import issues
try:
    from .base import CacheableEffect
    from .base import Effect
    from .base import EffectType
    from .base import IdempotentEffect
except ImportError:
    # Fallback for direct module import
    from base import CacheableEffect
    from base import Effect
    from base import EffectType
    from base import IdempotentEffect


class ComputationEffect(Effect):
    """Base class for all computation effects."""
    
    def __init__(
        self,
        operation: str,
        computation_type: str,
        required_capability: Optional[str] = None,
        metadata: Optional[Dict[str, Any]] = None
    ):
        super().__init__(
            effect_type=EffectType.COMPUTATION,
            operation=operation,
            required_capability=required_capability or f"computation:{operation}",
            metadata=metadata
        )
        self.computation_type = computation_type
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary."""
        return {
            "type": "computation",
            "operation": self.operation,
            "id": self.id,
            "timestamp": self.timestamp.isoformat(),
            "computation_type": self.computation_type,
            "required_capability": self.required_capability,
            "metadata": self.metadata,
            "can_degrade": self.can_degrade,
            "min_fidelity": self.min_fidelity
        }
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'ComputationEffect':
        """Create from dictionary."""
        return cls(
            operation=data["operation"],
            computation_type=data["computation_type"],
            required_capability=data.get("required_capability"),
            metadata=data.get("metadata")
        )


class ProcessTextEffect(ComputationEffect, CacheableEffect):
    """Effect for text processing operations."""
    
    def __init__(
        self,
        text: str,
        operation: str = "analyze",
        model_name: Optional[str] = None,
        parameters: Optional[Dict[str, Any]] = None,
        max_tokens: Optional[int] = None,
        temperature: float = 0.7,
        metadata: Optional[Dict[str, Any]] = None
    ):
        super().__init__(
            operation="process_text",
            computation_type="nlp",
            required_capability="computation:nlp:process",
            metadata=metadata
        )
        self.text = text
        self.text_operation = operation
        self.model_name = model_name
        self.parameters = parameters or {}
        self.max_tokens = max_tokens
        self.temperature = temperature
        
        # Text processing is cacheable
        self.cache_ttl_seconds = 1800  # 30 minutes
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary."""
        return {
            "type": "computation",
            "operation": "process_text",
            "id": self.id,
            "timestamp": self.timestamp.isoformat(),
            "text": self.text,
            "text_operation": self.text_operation,
            "model_name": self.model_name,
            "parameters": self.parameters,
            "max_tokens": self.max_tokens,
            "temperature": self.temperature,
            "required_capability": self.required_capability,
            "metadata": self.metadata,
            "can_degrade": self.can_degrade,
            "min_fidelity": self.min_fidelity,
            "cache_ttl_seconds": self.cache_ttl_seconds
        }
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'ProcessTextEffect':
        """Create from dictionary."""
        return cls(
            text=data["text"],
            operation=data.get("text_operation", "analyze"),
            model_name=data.get("model_name"),
            parameters=data.get("parameters"),
            max_tokens=data.get("max_tokens"),
            temperature=data.get("temperature", 0.7),
            metadata=data.get("metadata")
        )


class ExecuteFunctionEffect(ComputationEffect):
    """Effect for executing arbitrary functions."""
    
    def __init__(
        self,
        function_name: str,
        args: tuple = (),
        kwargs: Optional[Dict[str, Any]] = None,
        timeout: float = 60.0,
        allow_async: bool = True,
        metadata: Optional[Dict[str, Any]] = None
    ):
        super().__init__(
            operation="execute_function",
            computation_type="function",
            required_capability=f"computation:function:{function_name}",
            metadata=metadata
        )
        self.function_name = function_name
        self.args = args
        self.kwargs = kwargs or {}
        self.timeout = timeout
        self.allow_async = allow_async
        
        # Function execution cannot be degraded by default
        self.can_degrade = False
        self.min_fidelity = SCALE
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary."""
        return {
            "type": "computation",
            "operation": "execute_function",
            "id": self.id,
            "timestamp": self.timestamp.isoformat(),
            "function_name": self.function_name,
            "args": self.args,
            "kwargs": self.kwargs,
            "timeout": self.timeout,
            "allow_async": self.allow_async,
            "required_capability": self.required_capability,
            "metadata": self.metadata,
            "can_degrade": self.can_degrade,
            "min_fidelity": self.min_fidelity,
            "cache_ttl_seconds": self.cache_ttl_seconds
        }
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'ExecuteFunctionEffect':
        """Create from dictionary."""
        return cls(
            function_name=data["function_name"],
            args=tuple(data.get("args", ())),
            kwargs=data.get("kwargs"),
            timeout=data.get("timeout", 60.0),
            allow_async=data.get("allow_async", True),
            metadata=data.get("metadata")
        )


class MLInferenceEffect(ComputationEffect, CacheableEffect):
    """Effect for machine learning inference."""
    
    def __init__(
        self,
        model_name: str,
        input_data: Any,
        model_version: Optional[str] = None,
        batch_size: Optional[int] = None,
        confidence_threshold: int = int(0.5 * SCALE),
        return_probabilities: bool = False,
        metadata: Optional[Dict[str, Any]] = None
    ):
        super().__init__(
            operation="ml_inference",
            computation_type="ml",
            required_capability=f"computation:ml:inference:{model_name}",
            metadata=metadata
        )
        self.model_name = model_name
        self.input_data = input_data
        self.model_version = model_version
        self.batch_size = batch_size
        self.confidence_threshold = confidence_threshold
        self.return_probabilities = return_probabilities
        
        # ML inference is cacheable for deterministic models
        self.cache_ttl_seconds = 900  # 15 minutes
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary."""
        return {
            "type": "computation",
            "operation": "ml_inference",
            "id": self.id,
            "timestamp": self.timestamp.isoformat(),
            "model_name": self.model_name,
            "input_data": self.input_data,
            "model_version": self.model_version,
            "batch_size": self.batch_size,
            "confidence_threshold": self.confidence_threshold,
            "return_probabilities": self.return_probabilities,
            "required_capability": self.required_capability,
            "metadata": self.metadata,
            "can_degrade": self.can_degrade,
            "min_fidelity": self.min_fidelity,
            "cache_ttl_seconds": self.cache_ttl_seconds
        }
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'MLInferenceEffect':
        """Create from dictionary."""
        return cls(
            model_name=data["model_name"],
            input_data=data["input_data"],
            model_version=data.get("model_version"),
            batch_size=data.get("batch_size"),
            confidence_threshold=data.get("confidence_threshold", 0.5),
            return_probabilities=data.get("return_probabilities", False),
            metadata=data.get("metadata")
        )


class DataTransformEffect(ComputationEffect, CacheableEffect, IdempotentEffect):
    """Effect for data transformation operations."""
    
    def __init__(
        self,
        data: Any,
        transformation: str,
        parameters: Optional[Dict[str, Any]] = None,
        output_format: str = "json",
        validate_output: bool = True,
        metadata: Optional[Dict[str, Any]] = None
    ):
        super().__init__(
            operation="data_transform",
            computation_type="data",
            required_capability=f"computation:data:transform:{transformation}",
            metadata=metadata
        )
        self.data = data
        self.transformation = transformation
        self.parameters = parameters or {}
        self.output_format = output_format
        self.validate_output = validate_output
        
        # Data transformations are highly cacheable
        self.cache_ttl_seconds = 3600  # 1 hour
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary."""
        return {
            "type": "computation",
            "operation": "data_transform",
            "id": self.id,
            "timestamp": self.timestamp.isoformat(),
            "data": self.data,
            "transformation": self.transformation,
            "parameters": self.parameters,
            "output_format": self.output_format,
            "validate_output": self.validate_output,
            "required_capability": self.required_capability,
            "metadata": self.metadata,
            "can_degrade": self.can_degrade,
            "min_fidelity": self.min_fidelity,
            "cache_ttl_seconds": self.cache_ttl_seconds
        }
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'DataTransformEffect':
        """Create from dictionary."""
        return cls(
            data=data["data"],
            transformation=data["transformation"],
            parameters=data.get("parameters"),
            output_format=data.get("output_format", "json"),
            validate_output=data.get("validate_output", True),
            metadata=data.get("metadata")
        )


class CryptographicEffect(ComputationEffect):
    """Effect for cryptographic operations."""
    
    def __init__(
        self,
        operation: str,  # encrypt, decrypt, sign, verify, hash
        data: Union[str, bytes],
        algorithm: str,
        key: Optional[str] = None,
        parameters: Optional[Dict[str, Any]] = None,
        metadata: Optional[Dict[str, Any]] = None
    ):
        super().__init__(
            operation="cryptographic",
            computation_type="crypto",
            required_capability=f"computation:crypto:{operation}",
            metadata=metadata
        )
        self.crypto_operation = operation
        self.data = data
        self.algorithm = algorithm
        self.key = key
        self.parameters = parameters or {}
        
        # Cryptographic operations cannot be degraded
        self.can_degrade = False
        self.min_fidelity = SCALE
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary."""
        return {
            "type": "computation",
            "operation": "cryptographic",
            "id": self.id,
            "timestamp": self.timestamp.isoformat(),
            "crypto_operation": self.crypto_operation,
            "data": self.data,
            "algorithm": self.algorithm,
            "key": self.key,
            "parameters": self.parameters,
            "required_capability": self.required_capability,
            "metadata": self.metadata,
            "can_degrade": self.can_degrade,
            "min_fidelity": self.min_fidelity,
            "cache_ttl_seconds": self.cache_ttl_seconds
        }
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'CryptographicEffect':
        """Create from dictionary."""
        return cls(
            operation=data["crypto_operation"],
            data=data["data"],
            algorithm=data["algorithm"],
            key=data.get("key"),
            parameters=data.get("parameters"),
            metadata=data.get("metadata")
        )


# Convenience functions for creating computation effects
def process_text(
    text: str,
    operation: str = "analyze",
    model_name: Optional[str] = None,
    max_tokens: Optional[int] = None
) -> ProcessTextEffect:
    """Create a text processing effect."""
    return ProcessTextEffect(
        text=text,
        operation=operation,
        model_name=model_name,
        max_tokens=max_tokens
    )


def execute_function(
    function_name: str,
    *args,
    timeout: float = 60.0,
    **kwargs
) -> ExecuteFunctionEffect:
    """Create a function execution effect."""
    return ExecuteFunctionEffect(
        function_name=function_name,
        args=args,
        kwargs=kwargs,
        timeout=timeout
    )


def ml_inference(
    model_name: str,
    input_data: Any,
    confidence_threshold: int = int(0.5 * SCALE),
    return_probabilities: bool = False
) -> MLInferenceEffect:
    """Create an ML inference effect."""
    return MLInferenceEffect(
        model_name=model_name,
        input_data=input_data,
        confidence_threshold=confidence_threshold,
        return_probabilities=return_probabilities
    )


def transform_data(
    data: Any,
    transformation: str,
    parameters: Optional[Dict[str, Any]] = None,
    output_format: str = "json"
) -> DataTransformEffect:
    """Create a data transformation effect."""
    return DataTransformEffect(
        data=data,
        transformation=transformation,
        parameters=parameters,
        output_format=output_format
    )


def encrypt_data(
    data: Union[str, bytes],
    algorithm: str,
    key: str,
    parameters: Optional[Dict[str, Any]] = None
) -> CryptographicEffect:
    """Create an encryption effect."""
    return CryptographicEffect(
        operation="encrypt",
        data=data,
        algorithm=algorithm,
        key=key,
        parameters=parameters
    )


def decrypt_data(
    data: Union[str, bytes],
    algorithm: str,
    key: str,
    parameters: Optional[Dict[str, Any]] = None
) -> CryptographicEffect:
    """Create a decryption effect."""
    return CryptographicEffect(
        operation="decrypt",
        data=data,
        algorithm=algorithm,
        key=key,
        parameters=parameters
    )


def hash_data(
    data: Union[str, bytes],
    algorithm: str = "sha256",
    parameters: Optional[Dict[str, Any]] = None
) -> CryptographicEffect:
    """Create a hashing effect."""
    return CryptographicEffect(
        operation="hash",
        data=data,
        algorithm=algorithm,
        parameters=parameters
    ) 