"""Configuration effects for declarative configuration management.

This module defines effects for configuration operations, ensuring all
config loading goes through the effect system.

Purpose: Declarative configuration management
Related Files: config/loader.py, effects/interpreter.py
Dependencies: dataclasses, pathlib
"""

from __future__ import annotations

from dataclasses import dataclass
from pathlib import Path
from typing import Any
from typing import Dict
from typing import Optional

from person_suit.core.effects.base import Effect
from person_suit.core.effects.base import EffectType


@dataclass
class LoadConfigEffect(Effect):
    """Effect to load configuration from a file."""
    
    config_path: Path
    encoding: str = "utf-8"
    format: str = "yaml"  # yaml, json, toml
    
    @property
    def effect_type(self) -> EffectType:
        """Return the effect type."""
        return EffectType.IO
    
    @property
    def required_capability(self) -> str:
        """Return the required capability."""
        return "config:read"


@dataclass
class SaveConfigEffect(Effect):
    """Effect to save configuration to a file."""
    
    config_path: Path
    config_data: Dict[str, Any]
    encoding: str = "utf-8"
    format: str = "yaml"  # yaml, json, toml
    backup: bool = True
    
    @property
    def effect_type(self) -> EffectType:
        """Return the effect type."""
        return EffectType.IO
    
    @property
    def required_capability(self) -> str:
        """Return the required capability."""
        return "config:write"


@dataclass
class MergeConfigEffect(Effect):
    """Effect to merge multiple configuration sources."""
    
    base_config: Dict[str, Any]
    override_config: Dict[str, Any]
    merge_strategy: str = "deep"  # deep, shallow, replace
    
    @property
    def effect_type(self) -> EffectType:
        """Return the effect type."""
        return EffectType.STATE
    
    @property
    def required_capability(self) -> str:
        """Return the required capability."""
        return "config:merge"


@dataclass
class ValidateConfigEffect(Effect):
    """Effect to validate configuration against a schema."""
    
    config_data: Dict[str, Any]
    schema: Optional[Dict[str, Any]] = None
    schema_path: Optional[Path] = None
    strict: bool = True
    
    @property
    def effect_type(self) -> EffectType:
        """Return the effect type."""
        return EffectType.STATE
    
    @property
    def required_capability(self) -> str:
        """Return the required capability."""
        return "config:validate"


@dataclass
class WatchConfigEffect(Effect):
    """Effect to watch configuration file for changes."""
    
    config_path: Path
    callback_channel: str  # Channel to send change events
    poll_interval_seconds: float = 1.0
    
    @property
    def effect_type(self) -> EffectType:
        """Return the effect type."""
        return EffectType.IO
    
    @property
    def required_capability(self) -> str:
        """Return the required capability."""
        return "config:watch" 