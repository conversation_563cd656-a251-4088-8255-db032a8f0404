"""
Base Effect Classes - Foundation of Declarative Effect System
============================================================

This module defines the base classes for the Effect system that implements
Principle I: "Components shall not act. They shall only declare intent."

Effects are pure data structures that describe intended side effects without
performing them. The EffectInterpreter is responsible for execution.

Related Files:
- interpreter.py: Effect execution engine
- handlers/: Specific effect handlers
- ../context/unified.py: Context propagation

Dependencies:
- Standard library only (no circular imports)
"""

import uuid
from abc import ABC
from abc import abstractmethod
from dataclasses import dataclass
from dataclasses import field
from datetime import datetime
from enum import Enum
from typing import Any
from typing import Dict
from typing import List
from typing import Optional
from typing import Type
from typing import TypeVar

from person_suit.core.constants.fixed_point_scale import SCALE
from person_suit.core.context.unified import UnifiedContext

T = TypeVar('T')


class EffectType(Enum):
    """Types of effects that can be declared."""
    DATABASE = "database"
    EVENT = "event"
    IO = "io"
    NETWORK = "network"
    COMPUTATION = "computation"
    MEMORY = "memory"
    SECURITY = "security"
    MONITORING = "monitoring"
    STATE = "state"  # Persistent key-value or object state operations


@dataclass
class EffectResult:
    """Result of effect execution."""
    success: bool
    value: Any = None
    error: Optional[str] = None
    metadata: Dict[str, Any] = field(default_factory=dict)
    execution_time_ms: float = 0.0
    
    def get(self, key: str, default: Any = None) -> Any:
        """Dictionary-like get method for backwards compatibility."""
        if hasattr(self, key):
            return getattr(self, key)
        
        # Handle legacy key mappings
        if key == "effect_result":
            return self.to_dict()
        elif key == "data":
            return self.value
        elif key == "result":
            return self.value
        
        return default
    
    def __getitem__(self, key: str) -> Any:
        """Dictionary-like indexing for backwards compatibility."""
        if hasattr(self, key):
            return getattr(self, key)
        
        # Handle legacy key mappings
        if key == "effect_result":
            return self.to_dict()
        elif key == "data":
            return self.value
        elif key == "result":
            return self.value
        
        raise KeyError(f"'{key}' not found in EffectResult")
    
    def __contains__(self, key: str) -> bool:
        """Dictionary-like 'in' operator support."""
        return hasattr(self, key) or key in ["effect_result", "data", "result"]
    
    def keys(self):
        """Dictionary-like keys() method."""
        base_keys = ["success", "value", "error", "metadata", "execution_time_ms"]
        legacy_keys = ["effect_result", "data", "result"]
        return base_keys + legacy_keys
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary for serialization."""
        return {
            "success": self.success,
            "value": self.value,
            "error": self.error,
            "metadata": self.metadata,
            "execution_time_ms": self.execution_time_ms
        }
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'EffectResult':
        """Create from dictionary."""
        return cls(
            success=data.get("success", False),
            value=data.get("value"),
            error=data.get("error"),
            metadata=data.get("metadata", {}),
            execution_time_ms=data.get("execution_time_ms", 0.0)
        )


class Effect(ABC):
    """
    Base class for all effects in the system.
    
    Effects are pure data structures that describe intended side effects
    without performing them. They are executed by the EffectInterpreter.
    
    This implements the core of Principle I - components declare intent
    rather than performing actions directly.
    """
    
    def __init__(
        self,
        effect_type: EffectType,
        operation: str,
        required_capability: Optional[str] = None,
        metadata: Optional[Dict[str, Any]] = None
    ):
        self.id = str(uuid.uuid4())
        self.timestamp = datetime.now()
        self.effect_type = effect_type
        self.operation = operation
        self.required_capability = required_capability
        self.metadata = metadata or {}
        
        # ACF metadata
        self.can_degrade = True  # Can this effect be executed with lower fidelity?
        self.min_fidelity = int(0.3 * SCALE)  # Minimum acceptable fidelity level
        self.cache_ttl_seconds = 300  # How long results can be cached
    
    @abstractmethod
    def to_dict(self) -> Dict[str, Any]:
        """Convert effect to dictionary for serialization."""
        pass
    
    @classmethod
    @abstractmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'Effect':
        """Create effect from dictionary."""
        pass
    
    def with_capability(self, capability: str) -> 'Effect':
        """Set the required capability for this effect."""
        self.required_capability = capability
        return self
    
    def with_metadata(self, key: str, value: Any) -> 'Effect':
        """Add metadata to this effect."""
        self.metadata[key] = value
        return self
    
    def non_degradable(self) -> 'Effect':
        """Mark this effect as non-degradable (must execute at full fidelity)."""
        self.can_degrade = False
        self.min_fidelity = SCALE
        return self
    
    def with_cache_ttl(self, seconds: int) -> 'Effect':
        """Set cache TTL for this effect's results."""
        self.cache_ttl_seconds = seconds
        return self
    
    def __repr__(self) -> str:
        return f"{self.__class__.__name__}(operation={self.operation}, id={self.id[:8]})"


class CompositeEffect(Effect):
    """
    An effect that combines multiple effects into a single unit.
    
    This allows for complex operations that require multiple side effects
    to be declared as a single, atomic unit.
    """
    
    def __init__(
        self,
        effects: List[Effect],
        execution_strategy: str = "sequential",  # "sequential" or "parallel"
        required_capability: Optional[str] = None,
        metadata: Optional[Dict[str, Any]] = None
    ):
        super().__init__(
            effect_type=EffectType.COMPUTATION,
            operation="composite",
            required_capability=required_capability,
            metadata=metadata
        )
        self.effects = effects
        self.execution_strategy = execution_strategy
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary."""
        return {
            "type": "composite",
            "id": self.id,
            "timestamp": self.timestamp.isoformat(),
            "effects": [effect.to_dict() for effect in self.effects],
            "execution_strategy": self.execution_strategy,
            "required_capability": self.required_capability,
            "metadata": self.metadata,
            "can_degrade": self.can_degrade,
            "min_fidelity": self.min_fidelity,
            "cache_ttl_seconds": self.cache_ttl_seconds
        }
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'CompositeEffect':
        """Create from dictionary."""
        # This would need to deserialize individual effects
        # For now, return a placeholder
        return cls(effects=[], execution_strategy=data.get("execution_strategy", "sequential"))


# Convenience functions for creating composite effects
def sequence(*effects: Effect) -> CompositeEffect:
    """Create a composite effect that executes effects sequentially."""
    return CompositeEffect(list(effects), execution_strategy="sequential")


def parallel(*effects: Effect) -> CompositeEffect:
    """Create a composite effect that executes effects in parallel."""
    return CompositeEffect(list(effects), execution_strategy="parallel")


# Effect creation helpers
def effect(
    effect_type: EffectType,
    operation: str,
    **kwargs
) -> Type[Effect]:
    """
    Decorator/factory for creating simple effect classes.
    
    Usage:
        @effect(EffectType.DATABASE, "read")
        class ReadUserEffect(Effect):
            def __init__(self, user_id: str):
                super().__init__()
                self.user_id = user_id
    """
    def decorator(cls):
        # Add default implementations
        original_init = cls.__init__
        
        def new_init(self, *args, **init_kwargs):
            super(cls, self).__init__(
                effect_type=effect_type,
                operation=operation,
                **kwargs
            )
            original_init(self, *args, **init_kwargs)
        
        cls.__init__ = new_init
        return cls
    
    return decorator


# Common effect mixins
class CacheableEffect:
    """Mixin for effects that can be cached."""
    
    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.cacheable = True
    
    def cache_key(self) -> str:
        """Generate cache key for this effect."""
        import hashlib
        key_data = f"{self.__class__.__name__}:{self.operation}:{self.to_dict()}"
        return hashlib.sha256(key_data.encode()).hexdigest()


class IdempotentEffect:
    """Mixin for effects that are idempotent (safe to retry)."""
    
    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.idempotent = True
        self.max_retries = 3


class CriticalEffect:
    """Mixin for effects that are critical and cannot be degraded."""
    
    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.can_degrade = False
        self.min_fidelity = SCALE


# Error classes
class EffectError(Exception):
    """Base exception for effect-related errors."""
    pass


class EffectExecutionError(EffectError):
    """Error during effect execution."""
    
    def __init__(self, effect: Effect, message: str, cause: Optional[Exception] = None):
        self.effect = effect
        self.cause = cause
        super().__init__(f"Effect {effect} failed: {message}")


class EffectValidationError(EffectError):
    """Error during effect validation."""
    pass


class EffectTimeoutError(EffectError):
    """Effect execution timed out."""
    pass


# Effect registry for dynamic effect creation
class EffectRegistry:
    """Registry for effect types and their handlers."""
    
    def __init__(self):
        self._effect_types: Dict[str, Type[Effect]] = {}
    
    def register(self, name: str, effect_class: Type[Effect]) -> None:
        """Register an effect type."""
        self._effect_types[name] = effect_class
    
    def get(self, name: str) -> Optional[Type[Effect]]:
        """Get an effect type by name."""
        return self._effect_types.get(name)
    
    def create(self, name: str, **kwargs) -> Optional[Effect]:
        """Create an effect instance by name."""
        effect_class = self.get(name)
        if effect_class:
            return effect_class(**kwargs)
        return None
    
    def list_types(self) -> List[str]:
        """List all registered effect types."""
        return list(self._effect_types.keys())


# Global effect registry
_global_registry = EffectRegistry()


def register_effect(name: str, effect_class: Type[Effect]) -> None:
    """Register an effect type globally."""
    _global_registry.register(name, effect_class)


def get_effect_type(name: str) -> Optional[Type[Effect]]:
    """Get an effect type from the global registry."""
    return _global_registry.get(name)


def create_effect(name: str, **kwargs) -> Optional[Effect]:
    """Create an effect from the global registry."""
    return _global_registry.create(name, **kwargs)


@dataclass
class EffectExecutionContext:
    """Context for effect execution including ACF decisions."""
    effect: 'Effect'
    unified_context: 'UnifiedContext'
    strategy: 'EffectExecutionStrategy'
    timeout_seconds: float
    retry_count: int = 0
    max_retries: int = 3
    execution_start_time: float = 0.0


class EffectExecutionStrategy(Enum):
    """Strategies for executing effects based on context and system state."""
    HIGH_FIDELITY = "high_fidelity"
    MEDIUM_FIDELITY = "medium_fidelity"
    LOW_FIDELITY = "low_fidelity"
    CACHED = "cached"
    DEFERRED = "deferred" 