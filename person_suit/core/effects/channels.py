"""
Effect Channel Definitions - No imports between consumers and effects.

This module defines all effect channels for the message-based effects system.
Consumers send messages to channels, effect actors subscribe and handle effects.

Related Files:
- __init__.py: Message-based effects API
- actors/: Effect actor implementations
- messages/: Effect message type definitions

Dependencies:
- Core Python libraries only - no internal imports for maximum decoupling
"""

from dataclasses import dataclass
from typing import Dict
from typing import Optional
from typing import Set


@dataclass
class EffectChannelDefinition:
    """Definition of an effect channel with metadata."""
    
    channel: str
    description: str
    required_capabilities: Set[str]
    resource_type: str
    priority_level: str = "normal"
    acf_adaptable: bool = True


# Core Effect Channels - The foundation of message-based effects
EFFECT_CHANNELS: Dict[str, EffectChannelDefinition] = {
    # I/O Effects - File system and network operations
    "effects.io.file.read": EffectChannelDefinition(
        channel="effects.io.file.read",
        description="Read file contents with differential caching",
        required_capabilities={"io.file.read"},
        resource_type="io",
        priority_level="normal",
        acf_adaptable=True
    ),
    "effects.io.file.write": EffectChannelDefinition(
        channel="effects.io.file.write", 
        description="Write file contents with atomic operations",
        required_capabilities={"io.file.write"},
        resource_type="io",
        priority_level="high",
        acf_adaptable=False  # Write operations must be reliable
    ),
    "effects.io.file.delete": EffectChannelDefinition(
        channel="effects.io.file.delete",
        description="Delete files with safety checks",
        required_capabilities={"io.file.delete"},
        resource_type="io",
        priority_level="high",
        acf_adaptable=False
    ),
    "effects.io.file.exists": EffectChannelDefinition(
        channel="effects.io.file.exists",
        description="Check if file or directory exists",
        required_capabilities={"io.file.read"},
        resource_type="io",
        priority_level="normal",
        acf_adaptable=True
    ),
    "effects.io.file.mkdir": EffectChannelDefinition(
        channel="effects.io.file.mkdir",
        description="Create directories recursively",
        required_capabilities={"io.file.write"},
        resource_type="io",
        priority_level="normal",
        acf_adaptable=False
    ),
    "effects.io.network.get": EffectChannelDefinition(
        channel="effects.io.network.get",
        description="HTTP GET requests with caching and retries",
        required_capabilities={"io.network.request"},
        resource_type="network",
        priority_level="normal",
        acf_adaptable=True
    ),
    "effects.io.network.post": EffectChannelDefinition(
        channel="effects.io.network.post",
        description="HTTP POST requests with data submission",
        required_capabilities={"io.network.request"},
        resource_type="network",
        priority_level="normal",
        acf_adaptable=True
    ),
    "effects.io.network.put": EffectChannelDefinition(
        channel="effects.io.network.put",
        description="HTTP PUT requests for resource updates",
        required_capabilities={"io.network.request"},
        resource_type="network",
        priority_level="normal",
        acf_adaptable=True
    ),
    "effects.io.network.delete": EffectChannelDefinition(
        channel="effects.io.network.delete",
        description="HTTP DELETE requests for resource removal",
        required_capabilities={"io.network.request"},
        resource_type="network",
        priority_level="normal",
        acf_adaptable=True
    ),
    "effects.io.network.request": EffectChannelDefinition(
        channel="effects.io.network.request",
        description="HTTP/network requests with adaptive retry",
        required_capabilities={"io.network.request"},
        resource_type="network",
        priority_level="normal",
        acf_adaptable=True
    ),
    
    # Database Effects - Data persistence operations
    "effects.database.query": EffectChannelDefinition(
        channel="effects.database.query",
        description="Database queries with connection pooling",
        required_capabilities={"database.query"},
        resource_type="database",
        priority_level="normal",
        acf_adaptable=True
    ),
    "effects.db.query": EffectChannelDefinition(
        channel="effects.db.query",
        description="Database queries with connection pooling",
        required_capabilities={"database.query"},
        resource_type="database",
        priority_level="normal",
        acf_adaptable=True
    ),
    "effects.db.transaction": EffectChannelDefinition(
        channel="effects.db.transaction",
        description="Database transactions with ACID guarantees",
        required_capabilities={"database.transaction"},
        resource_type="database",
        priority_level="high",
        acf_adaptable=False  # Transactions must be reliable
    ),
    "effects.db.migration": EffectChannelDefinition(
        channel="effects.db.migration",
        description="Schema migrations and database updates",
        required_capabilities={"database.admin"},
        resource_type="database",
        priority_level="critical",
        acf_adaptable=False
    ),
    
    # State Effects - System state management
    "effects.state.write": EffectChannelDefinition(
        channel="effects.state.write",
        description="Write/update state with differential propagation",
        required_capabilities={"state.write"},
        resource_type="memory",
        priority_level="high",
        acf_adaptable=True
    ),
    "effects.state.read": EffectChannelDefinition(
        channel="effects.state.read",
        description="Read state data from storage",
        required_capabilities={"state.read"},
        resource_type="memory",
        priority_level="normal",
        acf_adaptable=True
    ),
    "effects.state.create": EffectChannelDefinition(
        channel="effects.state.create",
        description="Create new state entities",
        required_capabilities={"state.create"},
        resource_type="memory",
        priority_level="high",
        acf_adaptable=True
    ),
    "effects.state.delete": EffectChannelDefinition(
        channel="effects.state.delete",
        description="Delete state entities with cleanup",
        required_capabilities={"state.delete"},
        resource_type="memory",
        priority_level="high",
        acf_adaptable=False  # Deletion should be deterministic
    ),
    "effects.state.query": EffectChannelDefinition(
        channel="effects.state.query",
        description="Query state with filters",
        required_capabilities={"state.read"},
        resource_type="memory",
        priority_level="normal",
        acf_adaptable=True
    ),
    "effects.state.history": EffectChannelDefinition(
        channel="effects.state.history",
        description="Access state change history",
        required_capabilities={"state.read"},
        resource_type="memory",
        priority_level="normal",
        acf_adaptable=True
    ),
    "effects.state.batch": EffectChannelDefinition(
        channel="effects.state.batch",
        description="Batch state operations",
        required_capabilities={"state.write", "state.read"},
        resource_type="memory",
        priority_level="high",
        acf_adaptable=True
    ),
    "effects.state.update": EffectChannelDefinition(
        channel="effects.state.update",
        description="Update system state with differential propagation",
        required_capabilities={"state.write"},
        resource_type="memory",
        priority_level="high",
        acf_adaptable=True
    ),
    "effects.state.sync": EffectChannelDefinition(
        channel="effects.state.sync",
        description="Synchronize state across system components",
        required_capabilities={"state.sync"},
        resource_type="network",
        priority_level="normal",
        acf_adaptable=True
    ),
    "effects.state.snapshot": EffectChannelDefinition(
        channel="effects.state.snapshot",
        description="Create state snapshots for backup/recovery",
        required_capabilities={"state.read", "io.file.write"},
        resource_type="io",
        priority_level="low",
        acf_adaptable=True
    ),
    "effects.state.get": EffectChannelDefinition(
        channel="effects.state.get",
        description="Retrieve state values by key",
        required_capabilities={"state.read"},
        resource_type="memory",
        priority_level="normal",
        acf_adaptable=True
    ),
    
    # State Actor Channels - Used by central state actor
    "state_create": EffectChannelDefinition(
        channel="state_create",
        description="Create new entity state",
        required_capabilities={"state.create"},
        resource_type="memory",
        priority_level="high",
        acf_adaptable=False
    ),
    "state_update": EffectChannelDefinition(
        channel="state_update", 
        description="Update existing entity state",
        required_capabilities={"state.update"},
        resource_type="memory",
        priority_level="high",
        acf_adaptable=False
    ),
    "state_delete": EffectChannelDefinition(
        channel="state_delete",
        description="Delete entity state",
        required_capabilities={"state.delete"},
        resource_type="memory",
        priority_level="high",
        acf_adaptable=False
    ),
    "state_read": EffectChannelDefinition(
        channel="state_read",
        description="Read entity state",
        required_capabilities={"state.read"},
        resource_type="memory",
        priority_level="normal",
        acf_adaptable=True
    ),
    
    # Memory Effects - Layered memory system operations
    "effects.memory.store": EffectChannelDefinition(
        channel="effects.memory.store",
        description="Store data in layered memory system",
        required_capabilities={"memory.write"},
        resource_type="memory",
        priority_level="normal",
        acf_adaptable=True
    ),
    "effects.memory.retrieve": EffectChannelDefinition(
        channel="effects.memory.retrieve",
        description="Retrieve data from memory with wave-particle optimization",
        required_capabilities={"memory.read"},
        resource_type="memory",
        priority_level="normal",
        acf_adaptable=True
    ),
    "effects.memory.consolidate": EffectChannelDefinition(
        channel="effects.memory.consolidate",
        description="Consolidate memories across layers",
        required_capabilities={"memory.consolidate"},
        resource_type="compute",
        priority_level="low",
        acf_adaptable=True
    ),
    
    # Computation Effects - Processing and analysis
    "effects.compute.task": EffectChannelDefinition(
        channel="effects.compute.task",
        description="Execute computational tasks with resource management",
        required_capabilities={"compute.execute"},
        resource_type="compute",
        priority_level="normal",
        acf_adaptable=True
    ),
    "effects.compute.batch": EffectChannelDefinition(
        channel="effects.compute.batch",
        description="Batch processing with optimal resource utilization",
        required_capabilities={"compute.batch"},
        resource_type="compute",
        priority_level="low",
        acf_adaptable=True
    ),
    "effects.compute.analyze": EffectChannelDefinition(
        channel="effects.compute.analyze",
        description="Data analysis with adaptive fidelity",
        required_capabilities={"compute.analyze"},
        resource_type="compute",
        priority_level="normal",
        acf_adaptable=True
    ),
    
    # Security Effects - Security and audit operations
    "effects.security.audit": EffectChannelDefinition(
        channel="effects.security.audit",
        description="Security audit logging and compliance",
        required_capabilities={"security.audit"},
        resource_type="io",
        priority_level="critical",
        acf_adaptable=False
    ),
    "effects.security.authorize": EffectChannelDefinition(
        channel="effects.security.authorize",
        description="Authorization and capability validation",
        required_capabilities={"security.authorize"},
        resource_type="compute",
        priority_level="critical",
        acf_adaptable=False
    ),
    
    # Monitoring Effects - System observability
    "effects.monitoring.metric": EffectChannelDefinition(
        channel="effects.monitoring.metric",
        description="Collect and emit system metrics",
        required_capabilities={"monitoring.metric"},
        resource_type="network",
        priority_level="low",
        acf_adaptable=True
    ),
    "effects.monitoring.trace": EffectChannelDefinition(
        channel="effects.monitoring.trace",
        description="Distributed tracing for effect chains",
        required_capabilities={"monitoring.trace"},
        resource_type="network",
        priority_level="low",
        acf_adaptable=True
    ),
    
    # Communication Effects - Inter-component messaging
    "effects.communication.message": EffectChannelDefinition(
        channel="effects.communication.message",
        description="Send messages between system components",
        required_capabilities={"communication.send"},
        resource_type="network",
        priority_level="normal",
        acf_adaptable=True
    ),
    "effects.communication.broadcast": EffectChannelDefinition(
        channel="effects.communication.broadcast",
        description="Broadcast messages to multiple recipients",
        required_capabilities={"communication.broadcast"},
        resource_type="network",
        priority_level="normal",
        acf_adaptable=True
    ),
    
    # Resource Effects - Resource management and allocation
    "effects.resource.allocate": EffectChannelDefinition(
        channel="effects.resource.allocate",
        description="Allocate system resources with quotas",
        required_capabilities={"resource.allocate"},
        resource_type="system",
        priority_level="high",
        acf_adaptable=False
    ),
    "effects.resource.release": EffectChannelDefinition(
        channel="effects.resource.release",
        description="Release allocated system resources",
        required_capabilities={"resource.release"},
        resource_type="system",
        priority_level="high",
        acf_adaptable=False
    ),
    
    # Meta-System Effects - Cross-domain operations
    "effects.persona.analyze": EffectChannelDefinition(
        channel="effects.persona.analyze",
        description="Persona core analysis operations",
        required_capabilities={"persona.analyze"},
        resource_type="compute",
        priority_level="normal",
        acf_adaptable=True
    ),
    "effects.analyst.evaluate": EffectChannelDefinition(
        channel="effects.analyst.evaluate",
        description="Analyst evaluation and pattern detection",
        required_capabilities={"analyst.evaluate"},
        resource_type="compute",
        priority_level="normal",
        acf_adaptable=True
    ),
    "effects.predictor.forecast": EffectChannelDefinition(
        channel="effects.predictor.forecast",
        description="Predictor forecasting and hypothesis generation",
        required_capabilities={"predictor.forecast"},
        resource_type="compute",
        priority_level="normal",
        acf_adaptable=True
    ),
    
    # Context Effects - UnifiedContext operations
    "effects.context.propagate": EffectChannelDefinition(
        channel="effects.context.propagate",
        description="Propagate context changes differentially",
        required_capabilities={"context.propagate"},
        resource_type="memory",
        priority_level="high",
        acf_adaptable=True
    ),
    "effects.context.merge": EffectChannelDefinition(
        channel="effects.context.merge",
        description="Merge contexts with conflict resolution",
        required_capabilities={"context.merge"},
        resource_type="compute",
        priority_level="normal",
        acf_adaptable=True
    ),
    
    # Event Effects - Event system operations
    "effects.event.emit": EffectChannelDefinition(
        channel="effects.event.emit",
        description="Emit events to the event bus",
        required_capabilities={"event.emit"},
        resource_type="network",
        priority_level="normal",
        acf_adaptable=True
    ),
    "effects.event.subscribe": EffectChannelDefinition(
        channel="effects.event.subscribe",
        description="Subscribe to event channels",
        required_capabilities={"event.subscribe"},
        resource_type="network",
        priority_level="low",
        acf_adaptable=True
    ),
    
    # Workflow Effects - Complex effect coordination
    "effects.workflow.orchestrate": EffectChannelDefinition(
        channel="effects.workflow.orchestrate",
        description="Orchestrate complex effect workflows",
        required_capabilities={"workflow.orchestrate"},
        resource_type="compute",
        priority_level="normal",
        acf_adaptable=True
    ),
    "effects.workflow.compose": EffectChannelDefinition(
        channel="effects.workflow.compose",
        description="Compose effects into choreographed sequences",
        required_capabilities={"workflow.compose"},
        resource_type="compute",
        priority_level="normal",
        acf_adaptable=True
    ),
}


def get_channel_definition(channel: str) -> Optional[EffectChannelDefinition]:
    """Get channel definition by channel name."""
    return EFFECT_CHANNELS.get(channel)


def get_channels_by_resource_type(resource_type: str) -> Dict[str, EffectChannelDefinition]:
    """Get all channels that use a specific resource type."""
    return {
        channel: definition 
        for channel, definition in EFFECT_CHANNELS.items()
        if definition.resource_type == resource_type
    }


def get_channels_by_capability(capability: str) -> Dict[str, EffectChannelDefinition]:
    """Get all channels that require a specific capability."""
    return {
        channel: definition
        for channel, definition in EFFECT_CHANNELS.items()
        if capability in definition.required_capabilities
    }


def validate_channel_access(channel: str, available_capabilities: Set[str]) -> bool:
    """Validate if a request has required capabilities for a channel."""
    definition = get_channel_definition(channel)
    if not definition:
        return False
    
    return definition.required_capabilities.issubset(available_capabilities)


# Channel routing helpers for the message bus
def get_effect_channels_pattern() -> str:
    """Get the pattern that matches all effect channels."""
    return "effects.*"


def is_effect_channel(channel: str) -> bool:
    """Check if a channel is an effect channel."""
    return channel.startswith("effects.")


def get_channel_priority(channel: str) -> str:
    """Get the priority level for a channel."""
    definition = get_channel_definition(channel)
    return definition.priority_level if definition else "normal"


def is_channel_acf_adaptable(channel: str) -> bool:
    """Check if a channel supports ACF adaptation."""
    definition = get_channel_definition(channel)
    return definition.acf_adaptable if definition else True 