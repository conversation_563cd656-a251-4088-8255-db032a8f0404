"""decorators.py
Effect helper decorators.

This module currently provides `requires_capability` which attaches the
`required_capability` attribute to an Effect subclass automatically.  It can
be used as either a class decorator or a function-style decorator on the
`__init__` method.
"""

from __future__ import annotations

from typing import Callable
from typing import Type
from typing import TypeVar

from person_suit.core.effects.base import Effect

_T = TypeVar("_T", bound=Type[Effect])


def requires_capability(cap: str) -> Callable[[_T], _T]:  # noqa: D401
    """Annotate an Effect subclass with a required capability.

    Usage::

        @requires_capability("state.write")
        class MyEffect(Effect):
            ...
    """

    def decorator(effect_cls: _T) -> _T:  # noqa: D401
        setattr(effect_cls, "required_capability", cap)
        return effect_cls

    return decorator 