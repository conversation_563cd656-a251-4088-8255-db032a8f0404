"""state_effects.py
State management Effect definitions (Sprint-2).

These effects provide a minimal key–value store implementation that will later
be wired into differential dataflow + persistent layers.  For now the
*state_effect_strategy* implements an in-memory dictionary shared at module
level.
"""

from __future__ import annotations

from dataclasses import dataclass
from typing import Any

from person_suit.core.effects.base import Effect
from person_suit.core.effects.base import EffectType
from person_suit.core.effects.decorators import requires_capability


@dataclass
class StateUpdateEffect(Effect):
    """Set or update a key in the global state store."""

    key: str
    value: Any

    def __init__(self, key: str, value: Any) -> None:  # noqa: D401 – doc handled above
        super().__init__(effect_type=EffectType.STATE, operation="update")
        self.key = key
        self.value = value

    # ------------------------------------------------------------------
    # (De)serialization helpers – used by message bus
    # ------------------------------------------------------------------
    def to_dict(self) -> dict[str, Any]:  # noqa: D401
        return {"key": self.key, "value": self.value}

    @classmethod
    def from_dict(cls, data: dict[str, Any]) -> "StateUpdateEffect":  # noqa: D401
        return cls(key=data["key"], value=data["value"])


@dataclass
class StateReadEffect(Effect):
    """Read a key from the global state store."""

    key: str

    def __init__(self, key: str) -> None:  # noqa: D401
        super().__init__(effect_type=EffectType.STATE, operation="read")
        self.key = key

    def to_dict(self) -> dict[str, Any]:  # noqa: D401
        return {"key": self.key}

    @classmethod
    def from_dict(cls, data: dict[str, Any]) -> "StateReadEffect":  # noqa: D401
        return cls(key=data["key"])


# ---------------------------------------------------------------------------
# Additional operations
# ---------------------------------------------------------------------------


@requires_capability("state.delete")
@dataclass
class StateDeleteEffect(Effect):
    key: str

    def __init__(self, key: str) -> None:
        super().__init__(effect_type=EffectType.STATE, operation="delete")
        self.key = key

    def to_dict(self) -> dict[str, Any]:
        return {"key": self.key}

    @classmethod
    def from_dict(cls, data: dict[str, Any]) -> "StateDeleteEffect":
        return cls(key=data["key"])


@requires_capability("state.query")
@dataclass
class StateQueryEffect(Effect):
    pattern: str

    def __init__(self, pattern: str) -> None:
        super().__init__(effect_type=EffectType.STATE, operation="query")
        self.pattern = pattern

    def to_dict(self) -> dict[str, Any]:
        return {"pattern": self.pattern}

    @classmethod
    def from_dict(cls, data: dict[str, Any]) -> "StateQueryEffect":
        return cls(pattern=data["pattern"])


@requires_capability("state.snapshot")
@dataclass
class StateSnapshotEffect(Effect):
    """Return full snapshot of in-memory state store."""

    def __init__(self) -> None:
        super().__init__(effect_type=EffectType.STATE, operation="snapshot")

    def to_dict(self) -> dict[str, Any]:  # noqa: D401
        return {}

    @classmethod
    def from_dict(cls, data: dict[str, Any]) -> "StateSnapshotEffect":  # noqa: D401
        return cls() 