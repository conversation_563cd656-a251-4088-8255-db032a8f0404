"""
Service Contracts for Effects Module

This module defines explicit service contracts for dependency injection,
ensuring type safety and clear interface definitions for all injected services.

Key Features:
- Protocol-based service contracts
- Clear interface definitions
- Type safety for dependency injection
- CAW-compliant service design
- Runtime validation capabilities

Design:
- Protocols define expected service interfaces
- Contracts are import-independent
- Services can be validated against contracts
- Supports both sync and async service patterns
"""

from typing import TYPE_CHECKING
from typing import Any
from typing import Dict
from typing import List
from typing import Optional
from typing import Protocol
from typing import Set

# Type checking imports to avoid runtime dependencies
if TYPE_CHECKING:
    from person_suit.core.context.unified import UnifiedContext
    from person_suit.core.infrastructure.hybrid_message import HybridMessage
    from person_suit.core.infrastructure.hybrid_message import MessageResult
    from person_suit.core.infrastructure.hybrid_message_bus import HybridMessageBus

# =============================================================================
# Core Service Contracts
# =============================================================================

class ContextContract(Protocol):
    """Contract for context service providers."""
    
    def create_context(
        self,
        persona_id: Optional[str] = None,
        capabilities: Optional[Set[str]] = None,
        priority: Optional[float] = None,
        **kwargs
    ) -> "UnifiedContext":
        """Create a new UnifiedContext instance."""
        ...
    
    def get_default_context(self) -> "UnifiedContext":
        """Get or create default context instance."""
        ...
    
    async def create_context_async(
        self,
        persona_id: Optional[str] = None,
        capabilities: Optional[Set[str]] = None,
        priority: Optional[float] = None,
        **kwargs
    ) -> "UnifiedContext":
        """Create context asynchronously if needed."""
        ...


class MessageBusContract(Protocol):
    """Contract for message bus service providers."""
    
    async def get_bus(self) -> "HybridMessageBus":
        """Get or create message bus instance."""
        ...
    
    async def send_message(self, message: "HybridMessage") -> "MessageResult":
        """Send message via bus and return result."""
        ...
    
    async def subscribe(self, channel: str, handler: Any) -> None:
        """Subscribe to messages on specific channel."""
        ...
    
    async def unsubscribe(self, channel: str, handler: Any) -> None:
        """Unsubscribe from messages on specific channel."""
        ...
    
    def is_available(self) -> bool:
        """Check if message bus is available."""
        ...


class MessageContract(Protocol):
    """Contract for message service providers."""
    
    def create_message(
        self,
        channel: str,
        payload: Dict[str, Any],
        context: Optional["UnifiedContext"] = None,
        priority: Optional[float] = None,
        timeout_seconds: Optional[float] = None,
        require_response: bool = True,
        **kwargs
    ) -> "HybridMessage":
        """Create a new HybridMessage instance."""
        ...
    
    def create_response_message(
        self,
        original_message: "HybridMessage", 
        response_data: Any,
        success: bool = True,
        error: Optional[str] = None
    ) -> "HybridMessage":
        """Create response message for original message."""
        ...
    
    def validate_message(self, message: "HybridMessage") -> bool:
        """Validate message structure and content."""
        ...


# =============================================================================
# Capability-Aware Service Contracts
# =============================================================================

class CapabilityContract(Protocol):
    """Contract for capability-aware services."""
    
    def check_capability(self, capability: str, context: "UnifiedContext") -> bool:
        """Check if context has required capability."""
        ...
    
    def get_required_capabilities(self, channel: str) -> Set[str]:
        """Get required capabilities for channel access."""
        ...
    
    def validate_access(self, channel: str, context: "UnifiedContext") -> bool:
        """Validate context has access to channel."""
        ...


class MonitoringContract(Protocol):
    """Contract for monitoring service providers."""
    
    def record_metric(self, name: str, value: float, tags: Optional[Dict] = None) -> None:
        """Record a metric value."""
        ...
    
    def start_trace(self, operation: str) -> str:
        """Start tracing operation and return trace ID."""
        ...
    
    def end_trace(self, trace_id: str, success: bool = True) -> None:
        """End tracing operation."""
        ...
    
    def log_event(self, event: str, data: Dict[str, Any]) -> None:
        """Log an event with associated data."""
        ...


# =============================================================================
# Composite Service Contracts  
# =============================================================================

class EffectExecutionContract(Protocol):
    """Complete contract for effect execution services."""
    
    context_service: ContextContract
    message_bus_service: MessageBusContract
    message_service: MessageContract
    capability_service: Optional[CapabilityContract]
    monitoring_service: Optional[MonitoringContract]
    
    async def execute_effect(
        self,
        channel: str,
        payload: Dict[str, Any],
        context: Optional["UnifiedContext"] = None,
        **kwargs
    ) -> Any:
        """Execute effect with all required services."""
        ...
    
    def is_fully_configured(self) -> bool:
        """Check if all required services are configured."""
        ...


# =============================================================================
# Contract Validation
# =============================================================================

class ContractValidator:
    """Validates services against their contracts."""
    
    @staticmethod
    def validate_context_service(service: Any) -> bool:
        """Validate service implements ContextContract."""
        required_methods = ['create_context', 'get_default_context']
        return all(hasattr(service, method) for method in required_methods)
    
    @staticmethod 
    def validate_message_bus_service(service: Any) -> bool:
        """Validate service implements MessageBusContract."""
        required_methods = ['get_bus', 'send_message', 'is_available']
        return all(hasattr(service, method) for method in required_methods)
    
    @staticmethod
    def validate_message_service(service: Any) -> bool:
        """Validate service implements MessageContract."""
        required_methods = ['create_message', 'validate_message']
        return all(hasattr(service, method) for method in required_methods)
    
    @staticmethod
    def validate_all_services(services: Dict[str, Any]) -> Dict[str, bool]:
        """Validate all services against their contracts."""
        results = {}
        
        if 'context' in services:
            results['context'] = ContractValidator.validate_context_service(
                services['context']
            )
        
        if 'message_bus' in services:
            results['message_bus'] = ContractValidator.validate_message_bus_service(
                services['message_bus']
            )
            
        if 'message' in services:
            results['message'] = ContractValidator.validate_message_service(
                services['message']
            )
        
        return results


# =============================================================================
# Contract-Based Service Factory
# =============================================================================

class ServiceFactory:
    """Factory for creating contract-compliant services."""
    
    def __init__(self):
        self._service_factories: Dict[str, Any] = {}
    
    def register_context_factory(self, factory: Any) -> None:
        """Register context service factory."""
        if not callable(factory):
            raise ValueError("Context factory must be callable")
        self._service_factories['context'] = factory
    
    def register_message_bus_factory(self, factory: Any) -> None:
        """Register message bus service factory."""
        if not callable(factory):
            raise ValueError("Message bus factory must be callable")
        self._service_factories['message_bus'] = factory
    
    def register_message_factory(self, factory: Any) -> None:
        """Register message service factory."""  
        if not callable(factory):
            raise ValueError("Message factory must be callable")
        self._service_factories['message'] = factory
    
    def create_services(self) -> Dict[str, Any]:
        """Create all registered services."""
        services = {}
        
        for service_name, factory in self._service_factories.items():
            try:
                services[service_name] = factory()
            except Exception as e:
                raise RuntimeError(f"Failed to create service '{service_name}': {e}")
        
        # Validate created services
        validation_results = ContractValidator.validate_all_services(services)
        failed_validations = [
            name for name, valid in validation_results.items() if not valid
        ]
        
        if failed_validations:
            raise RuntimeError(
                f"Service validation failed for: {failed_validations}"
            )
        
        return services
    
    def get_registered_factories(self) -> List[str]:
        """Get list of registered factory names."""
        return list(self._service_factories.keys())


# =============================================================================
# Contract Utilities
# =============================================================================

def check_service_contract(service: Any, contract_name: str) -> bool:
    """Check if service satisfies a specific contract."""
    validators = {
        'context': ContractValidator.validate_context_service,
        'message_bus': ContractValidator.validate_message_bus_service,
        'message': ContractValidator.validate_message_service,
    }
    
    if contract_name not in validators:
        raise ValueError(f"Unknown contract: {contract_name}")
    
    return validators[contract_name](service)


def get_contract_requirements(contract_name: str) -> List[str]:
    """Get list of required methods for a contract."""
    requirements = {
        'context': ['create_context', 'get_default_context'],
        'message_bus': ['get_bus', 'send_message', 'is_available'],
        'message': ['create_message', 'validate_message'],
    }
    
    return requirements.get(contract_name, []) 