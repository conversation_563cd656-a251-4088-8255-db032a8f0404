"""RL-based ACF Policy (PPO-Lite placeholder)
=================================================
Provides an Adaptive Computational Fidelity manager that decides execution
strategy (high / medium / low / cached) using a lightweight reinforcement
learning agent.  For now the agent is stubbed with a trivial heuristic and a
placeholder PyTorch model path so production code paths are exercised without
requiring an actual trained model.
"""

from __future__ import annotations

import logging
from pathlib import Path
from typing import Any
from typing import Dict
from typing import Optional

from person_suit.core.context.unified import UnifiedContext
from person_suit.core.effects.base import Effect

logger = logging.getLogger(__name__)

# Optional dependency – guard import so core dependency tree stays lean.
try:
    import torch  # type: ignore
except ModuleNotFoundError:  # pragma: no cover – torch optional for now
    torch = None  # type: ignore


class RLACFManager:  # noqa: D101 – minimal public API
    POLICY_PATH = Path("models/rl_acf_policy.pt")

    def __init__(self) -> None:  # noqa: D401
        self._policy = None
        if torch and self.POLICY_PATH.exists():
            try:
                self._policy = torch.jit.load(self.POLICY_PATH)
                self._policy.eval()
            except Exception as exc:  # pragma: no cover – keep robust
                logger.debug("Failed to load RL ACF policy – falling back: %s", exc)

    # ------------------------------------------------------------------ public API
    def get_execution_policy(self, context: UnifiedContext, effect: Effect) -> Dict[str, Any]:  # noqa: D401
        """Return execution policy dict for *effect* given *context*.

        The contract mirrors the ad-hoc acf_manager previously passed to
        EffectInterpreter.  Keys may include:
          • strategy  – 'high'|'medium'|'low'|'cached'
          • fidelity  – int bucket (0-1_000_000)

        Falls back to heuristic when RL model unavailable.
        """
        if self._policy is None:
            return self._heuristic_policy(context, effect)

        # Convert context + effect features into tensor – placeholder impl
        try:
            import torch  # type: ignore
            features = torch.tensor([
                context.priority / 1_000_000,
                context.wave_particle_ratio if hasattr(context, "wave_particle_ratio") else 0.5,
            ]).float()
            out = self._policy(features.unsqueeze(0))  # type: ignore[attr-defined]
            action = int(out.argmax())
        except Exception as exc:  # pragma: no cover
            logger.debug("RL policy inference failed: %s", exc)
            return self._heuristic_policy(context, effect)

        mapping = {0: "high", 1: "medium", 2: "low", 3: "cached"}
        return {"strategy": mapping.get(action, "medium")}

    # ------------------------------------------------------------------ helpers
    def _heuristic_policy(self, context: UnifiedContext, effect: Effect) -> Dict[str, Any]:  # noqa: D401
        """Very simple fallback: priority drives strategy."""
        prio = context.priority if isinstance(context.priority, int) else 0
        if prio >= int(0.8 * 1_000_000):  # high bucket
            return {"strategy": "high"}
        if prio <= int(0.2 * 1_000_000):
            return {"strategy": "low"}
        return {"strategy": "medium"}


_default_manager: Optional[RLACFManager] = None


def get_acf_manager() -> RLACFManager:  # noqa: D401
    """Return singleton RL ACF manager."""
    global _default_manager
    if _default_manager is None:
        _default_manager = RLACFManager()
    return _default_manager 