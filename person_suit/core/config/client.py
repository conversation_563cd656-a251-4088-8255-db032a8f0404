from __future__ import annotations

"""client.py – async config retrieval via HybridMessageBus.

External layers MUST use this helper instead of importing `person_suit.core.config.get_config`.
This honours Absolute Decoupling: configuration values flow over the bus,
not through direct calls.
"""

from typing import Any
from typing import Optional

from person_suit.core.context.unified import UnifiedContext
from person_suit.core.infrastructure.hybrid_message import HybridMessage
from person_suit.core.infrastructure.hybrid_message_bus import get_message_bus


async def request_config(
    key: str,
    default: Any = None,
    context: Optional[UnifiedContext] = None,
    timeout: float = 2.0,
) -> Any:
    """Fetch a config value asynchronously via the message bus.

    Args:
        key: dotted config key (e.g. "database.url").
        default: value returned if key missing or service unavailable.
        context: optional UnifiedContext to propagate.
        timeout: seconds to wait for the reply.

    Returns:
        The config value or *default* if not found.
    """
    bus = get_message_bus()
    await bus.start()

    request = HybridMessage(
        channel="config.request",
        payload={"key": key},
        context=context or {},
        response_expected=True,
        reply_channel="config.response",
        priority=0.4,
    )

    result = await bus.send(request, timeout=timeout)
    if result and result.success and result.response:
        return result.response.payload.get("value", default)

    return default 