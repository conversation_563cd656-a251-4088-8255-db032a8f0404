from dataclasses import dataclass
from dataclasses import field
from typing import Dict
from typing import Optional

from person_suit.core.resources.common_types import ResourcePriority
from person_suit.core.resources.common_types import ResourceType

DEFAULT_MAX_DEMAND_QUEUE_SIZE = 100
DEFAULT_TELEMETRY_INTERVAL = 30.0

@dataclass
class ResourceManagerConfig:
    """Configuration for the ResourceManager."""
    
    # Default priority boost factors for resource demands
    # Higher value means more likely to preempt others or get resources.
    priority_boost: Dict[ResourcePriority, float] = field(
        default_factory=lambda: {
            ResourcePRIO_CRITICAL: 1.5,
            ResourcePRIO_HIGH: 1.2,
            ResourcePRIO_NORMAL: 1.0,
            ResourcePRIO_LOW: 0.8,
            ResourcePRIO_LOWEST: 0.5,
        }
    )
    
    # Initial total capacities for various resource types.
    # These can supplement or override discovered capacities (e.g., from psutil).
    # Keyed by ResourceType enum member.
    initial_capacities: Dict[ResourceType, float] = field(default_factory=dict)

    # Telemetry settings
    enable_telemetry_publishing: bool = True
    telemetry_interval_seconds: float = DEFAULT_TELEMETRY_INTERVAL # Set default here

    # Demand queue settings
    max_demand_queue_size: int = DEFAULT_MAX_DEMAND_QUEUE_SIZE # Set default here

    # Specifies if psutil discovery for CPU/Memory should be enabled.
    # If false, initial_capacities must provide these if they are to be managed.
    enable_psutil_discovery: bool = True

    # Default estimated duration for allocations if not specified in demand (in seconds)
    # Optional: If None, demands must specify duration if expiration is needed.
    default_allocation_duration_seconds: Optional[float] = None

    def __post_init__(self):
        # Ensure telemetry_interval_seconds is positive if telemetry is enabled
        if self.enable_telemetry_publishing and self.telemetry_interval_seconds <= 0:
            self.telemetry_interval_seconds = DEFAULT_TELEMETRY_INTERVAL
            print(f"Warning: ResourceManagerConfig.telemetry_interval_seconds was <= 0, reset to default {DEFAULT_TELEMETRY_INTERVAL}s.")

        # Ensure resource types in initial_capacities are valid
        valid_keys = {rt: val for rt, val in self.initial_capacities.items() if isinstance(rt, ResourceType)}
        if len(valid_keys) != len(self.initial_capacities):
            print(f"Warning: ResourceManagerConfig.initial_capacities had invalid keys. Original: {self.initial_capacities.keys()}, Valid: {valid_keys.keys()}")
            self.initial_capacities = valid_keys
        
        for rt, cap in self.initial_capacities.items():
            if cap < 0:
                print(f"Warning: ResourceManagerConfig.initial_capacities for {rt.name} was negative ({cap}). Setting to 0.")
                self.initial_capacities[rt] = 0.0 