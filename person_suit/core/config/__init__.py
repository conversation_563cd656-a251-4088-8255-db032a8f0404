"""
Configuration Module
===================

Provides simplified configuration access for the Person Suit system.
"""

import logging
import os
from typing import Any
from typing import Dict
from typing import Optional

import yaml

# NOTE: Removed client import to prevent circular dependencies during bootstrap
# from .client import request_config  # Async bus-based retrieval
from .loader import get_config_value

logger = logging.getLogger(__name__)

# Global config cache
_config_cache: Optional[Dict[str, Any]] = None


def load_config() -> Dict[str, Any]:
    """Load configuration from file or environment."""
    global _config_cache
    
    if _config_cache is not None:
        return _config_cache
    
    config = {}
    
    # Try to load from config.yaml
    config_file = "config.yaml"
    if os.path.exists(config_file):
        try:
            with open(config_file, 'r') as f:
                config = yaml.safe_load(f) or {}
            logger.info(f"Loaded configuration from {config_file}")
        except Exception as e:
            logger.error(f"Failed to load {config_file}: {e}")
    
    # Override with environment variables
    env_mapping = {
        "DATABASE_URL": "database.url",
        "REDIS_URL": "redis.url",
        "SECRET_KEY": "security.secret_key",
        "LOG_LEVEL": "logging.level",
        "ENVIRONMENT": "environment"
    }
    
    for env_var, config_key in env_mapping.items():
        value = os.getenv(env_var)
        if value:
            # Set nested config value
            parts = config_key.split('.')
            current = config
            for part in parts[:-1]:
                if part not in current:
                    current[part] = {}
                current = current[part]
            current[parts[-1]] = value
    
    _config_cache = config
    return config


def get_config() -> Dict[str, Any]:
    """Get the full configuration dictionary."""
    return load_config()


def get_config_value(key: str, default: Any = None) -> Any:
    """Get a specific configuration value by dot-notation key."""
    config = load_config()
    
    # Navigate nested structure
    parts = key.split('.')
    current = config
    
    for part in parts:
        if isinstance(current, dict) and part in current:
            current = current[part]
        else:
            return default
    
    return current


__all__ = ['get_config', 'get_config_value', 'load_config']
