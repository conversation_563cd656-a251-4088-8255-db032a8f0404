from __future__ import annotations

"""config_service_actor.py – exposes configuration over the message bus.

Lives inside the *core.config* layer, so it can safely access synchronous
`get_config` implementation, but all external callers must go through the bus.
"""

import logging

from person_suit.core.config import get_config  # local layer import OK
from person_suit.core.infrastructure.hybrid_message import HybridMessage
from person_suit.core.infrastructure.hybrid_message_bus import get_message_bus

logger = logging.getLogger(__name__)


class ConfigServiceActor:
    """Listens on `config.request` and replies with config values."""

    CHANNEL_REQUEST = "config.request"

    def __init__(self) -> None:
        self._sub_id: str | None = None

    async def start(self) -> None:
        bus = get_message_bus()
        await bus.start()
        self._sub_id = await bus.subscribe(
            channel_pattern=self.CHANNEL_REQUEST,
            handler=self._handle_request,
            subscriber_id="core.config.service_actor",
        )
        logger.info("ConfigServiceActor subscribed on %s", self.CHANNEL_REQUEST)

    async def stop(self) -> None:
        if self._sub_id is None:
            return
        bus = get_message_bus()
        bus.unsubscribe(self._sub_id, self.CHANNEL_REQUEST)
        self._sub_id = None

    # ------------------------------------------------------------------
    async def _handle_request(self, message: HybridMessage) -> None:
        key: str = message.payload.get("key", "")
        default = message.payload.get("default")

        try:
            value = get_config().get(key, default)  # simple dict access for now
        except Exception:  # pragma: no cover – shouldn't fail
            logger.exception("Config retrieval failed for %s", key)
            value = default

        reply = message.create_reply(
            payload={"key": key, "value": value},
            channel=message.reply_channel or "config.response",
        )

        bus = get_message_bus()
        await bus.send(reply)


# Singleton helpers
_actor_instance: ConfigServiceActor | None = None


async def start_config_service() -> None:
    global _actor_instance
    if _actor_instance is None:
        _actor_instance = ConfigServiceActor()
        await _actor_instance.start()


async def stop_config_service() -> None:
    if _actor_instance is not None:
        await _actor_instance.stop() 