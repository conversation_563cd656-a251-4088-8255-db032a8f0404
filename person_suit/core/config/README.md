# Person Suit Core - Configuration Module

## Purpose

This directory (`person_suit/core/config/`) contains the Python code responsible for **loading, parsing, validating, and providing access to configuration data** for the Person Suit framework.

It is distinct from the root `/config/` directory, which stores the actual configuration files (e.g., `.yaml`, `.json`). This module provides the *logic* to interact with that configuration data.

## Environment Variables

### Provenance Configuration (Sprint 1)

The system supports configurable provenance backends for audit trails and observability:

- **`PS_PROVENANCE_SINK`**: Controls the provenance backend
  - `memory` (default) - In-memory storage for development/testing
  - `redpanda` - Production Redpanda/Kafka streaming backend
  
- **`PS_REDPANDA_BROKERS`**: Comma-separated list of Redpanda brokers
  - Default: `localhost:9092`
  - Example: `broker1:9092,broker2:9092,broker3:9092`
  
- **`PS_REDPANDA_TOPIC`**: Topic name for provenance events
  - Default: `person_suit.provenance`
  - Must be created before starting the system

### Example Configuration

```bash
# Development (default)
export PS_PROVENANCE_SINK=memory

# Production
export PS_PROVENANCE_SINK=redpanda
export PS_REDPANDA_BROKERS=redpanda-1:9092,redpanda-2:9092,redpanda-3:9092
export PS_REDPANDA_TOPIC=person_suit_prod_provenance
```

## Core Components

-   **`loader.py`**: Implements the primary configuration loading mechanism. This likely defines a `ConfigManager` class or similar service that:
    -   Reads configuration files from specified sources (e.g., `/config/config.yaml`).
    -   Handles different file formats (YAML, JSON, potentially environment variables).
    -   Validates the loaded configuration against a schema (potentially defined elsewhere).
    -   Provides an interface (e.g., `IConfigManager`) for other components to access configuration settings in a structured way (e.g., `get_section`, `get_setting`).

## Design Alignment

-   **Separation of Concerns:** Clearly separates configuration *loading/management code* from configuration *data*.
-   **Modularity:** Provides a centralized service for configuration access, which can be injected into other components via Dependency Injection.
-   **Infrastructure:** Aligns with the concept of core infrastructure providing essential services like configuration management.

## Usage

Components should typically obtain configuration values by depending on the `IConfigManager` interface (defined in `person_suit/core/application/config_interface.py`) or a concrete `ConfigManager` implementation provided by this module, often resolved through the Dependency Injection container.

```python
# Conceptual Example using DI

from person_suit.core.application.config_interface import IConfigManager
from person_suit.core.infrastructure.dependency_injection import inject

@inject
def my_component_function(config: IConfigManager = inject(IConfigManager)):
    db_host = config.get_setting("database.host", "localhost")
    api_key = config.get_setting("external_api.key")
    logging_config = config.get_section("logging")
    
    print(f"DB Host: {db_host}")
``` 