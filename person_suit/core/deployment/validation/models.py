"""
Person Suit - Deployment Validation Models (DE-1)

This module defines data models for the deployment validation system,
providing structures for validation rules, results, and validator interfaces.

Models:
- ValidationSeverity: Severity levels for validation issues
- ValidationIssue: Detected validation issue
- ValidationResult: Results of a validation check
- ValidationRule: Rule defining a validation check

Related Files:
- validator.py: Base validator implementation
- environment_validator.py: Environment-specific validation
- resource_validator.py: Resource-specific validation
- security_validator.py: Security-specific validation
- manager.py: Validation orchestration and management

Dependencies:
- person_suit.core.deployment.detection: For environment data to validate
"""

import logging
from dataclasses import dataclass
from dataclasses import field
from enum import Enum
from enum import auto
from typing import Any
from typing import Dict
from typing import List
from typing import Optional

# Configure logger
logger = logging.getLogger("person_suit.deployment.validation.models")


class ValidationSeverity(Enum):
    """Severity levels for validation issues."""

    INFO = auto()
    WARNING = auto()
    ERROR = auto()
    CRITICAL = auto()


@dataclass
class ValidationIssue:
    """
    Detected validation issue.

    Attributes:
        code: Unique code identifying the issue type
        message: Human-readable description of the issue
        severity: Severity level of the issue
        component: Component where the issue was detected
        details: Additional issue-specific details
        remediation: Suggested remediation steps
    """

    code: str
    message: str
    severity: ValidationSeverity
    component: str
    details: Dict[str, Any] = field(default_factory=dict)
    remediation: Optional[str] = None


@dataclass
class ValidationResult:
    """
    Results of a validation check.

    Attributes:
        rule_id: ID of the rule that was validated
        is_valid: Whether the validation passed
        issues: List of validation issues, if any
        metadata: Additional validation metadata
    """

    rule_id: str
    is_valid: bool
    issues: List[ValidationIssue] = field(default_factory=list)
    metadata: Dict[str, Any] = field(default_factory=dict)

    @property
    def has_errors(self) -> bool:
        """Check if the result has any errors or critical issues."""
        return any(
            issue.severity in (ValidationSeverity.ERROR, ValidationSeverity.CRITICAL)
            for issue in self.issues
        )

    @property
    def has_warnings(self) -> bool:
        """Check if the result has any warnings."""
        return any(
            issue.severity == ValidationSeverity.WARNING for issue in self.issues
        )


@dataclass
class ValidationRule:
    """
    Rule defining a validation check.

    Attributes:
        rule_id: Unique identifier for the rule
        name: Human-readable name of the rule
        description: Description of what the rule validates
        component: Component this rule validates
        is_critical: Whether this rule is critical for functionality
        is_enabled: Whether this rule is enabled
        parameters: Rule-specific parameters
    """

    rule_id: str
    name: str
    description: str
    component: str
    is_critical: bool = False
    is_enabled: bool = True
    parameters: Dict[str, Any] = field(default_factory=dict)
