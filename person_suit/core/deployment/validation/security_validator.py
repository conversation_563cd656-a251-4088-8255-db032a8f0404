"""
Person Suit - Security Validator (DE-1)

This module provides security validation for the Person Suit framework,
ensuring that security configurations and requirements are met.

Classes:
- SecurityValidator: Validator for security configurations

Related Files:
- models.py: Validation data models
- validator.py: Base validator implementation
- manager.py: Validation orchestration
- person_suit.core.deployment.security: Security configuration and management
"""

import logging
import os
import socket
import ssl
from typing import List

from ..detection import EnvironmentData
from .models import ValidationIssue
from .models import ValidationResult
from .models import ValidationSeverity
from .validator import Validator

# Try to import security package components
try:
    from person_suit.core.deployment.security import SecurityLevel
    from person_suit.core.deployment.security import SecurityPolicy
    from person_suit.core.deployment.security import get_security_manager

    HAVE_SECURITY = True
except ImportError:
    HAVE_SECURITY = False

# Configure logger
logger = logging.getLogger("person_suit.deployment.validation.security")


class SecurityValidator(Validator):
    """
    Validator for security configurations and requirements.

    Validates:
    - Security policy configuration
    - Security boundary setup
    - Token validation
    - File permissions
    - Network security
    - Sandbox environment
    """

    def __init__(self):
        """Initialize the security validator."""
        super().__init__()
        self._register_rules()
        logger.debug("Security validator initialized")

    def _register_rules(self):
        """Register validation rules."""
        # Security package presence
        self.register_rule(
            "SECURITY_PACKAGE",
            "Checks if the security package is available",
            self._validate_security_package,
        )

        # Security policy configuration
        self.register_rule(
            "SECURITY_POLICY",
            "Checks if the security policy is properly configured",
            self._validate_security_policy,
        )

        # Security level
        self.register_rule(
            "SECURITY_LEVEL",
            "Checks if the security level is appropriate",
            self._validate_security_level,
        )

        # Sandbox environment
        self.register_rule(
            "SANDBOX_ENVIRONMENT",
            "Checks if the sandbox environment is properly configured",
            self._validate_sandbox_environment,
        )

        # File permissions
        self.register_rule(
            "FILE_PERMISSIONS",
            "Checks if file permissions are properly set",
            self._validate_file_permissions,
        )

        # SSL/TLS configuration
        self.register_rule(
            "SSL_TLS_CONFIG",
            "Checks if SSL/TLS configuration is secure",
            self._validate_ssl_tls_config,
        )

        # Network security
        self.register_rule(
            "NETWORK_SECURITY",
            "Checks if network security settings are appropriate",
            self._validate_network_security,
        )

        # Security boundaries
        self.register_rule(
            "SECURITY_BOUNDARIES",
            "Checks if security boundaries are properly defined",
            self._validate_security_boundaries,
        )

    def validate(self, env_data: EnvironmentData) -> List[ValidationResult]:
        """
        Validate security configurations.

        Args:
            env_data: Environment data for validation

        Returns:
            List[ValidationResult]: List of validation results
        """
        results = []

        # Validate using registered rules
        for rule_id, rule_info in self._rules.items():
            try:
                logger.debug(f"Validating rule: {rule_id}")
                result = rule_info["validator"](env_data)
                results.append(result)

                # Log validation issues
                if not result.is_valid:
                    for issue in result.issues:
                        if issue.severity == ValidationSeverity.ERROR:
                            logger.error(
                                f"Security validation error - {rule_id}: {issue.message}"
                            )
                        elif issue.severity == ValidationSeverity.WARNING:
                            logger.warning(
                                f"Security validation warning - {rule_id}: {issue.message}"
                            )
                        else:
                            logger.info(
                                f"Security validation info - {rule_id}: {issue.message}"
                            )

            except Exception as e:
                logger.error(f"Error validating rule {rule_id}: {e}", exc_info=True)
                # Create error result
                error_result = ValidationResult(
                    rule_id=rule_id,
                    is_valid=False,
                    issues=[
                        ValidationIssue(
                            code="VALIDATOR_EXCEPTION",
                            message=f"Error validating security rule: {str(e)}",
                            severity=ValidationSeverity.ERROR,
                            component="security_validator",
                        )
                    ],
                    metadata={"exception": str(e), "exception_type": type(e).__name__},
                )
                results.append(error_result)

        return results

    def _validate_security_package(self, env_data: EnvironmentData) -> ValidationResult:
        """
        Validate that the security package is available.

        Args:
            env_data: Environment data for validation

        Returns:
            ValidationResult: Validation result
        """
        issues = []

        if not HAVE_SECURITY:
            issues.append(
                ValidationIssue(
                    code="SECURITY_PACKAGE_MISSING",
                    message="The security package is not available. This is required for secure operation.",
                    severity=ValidationSeverity.ERROR,
                    component="security",
                    remediation="Ensure the security package is installed and properly configured.",
                )
            )

        return ValidationResult(
            rule_id="SECURITY_PACKAGE", is_valid=len(issues) == 0, issues=issues
        )

    def _validate_security_policy(self, env_data: EnvironmentData) -> ValidationResult:
        """
        Validate that the security policy is properly configured.

        Args:
            env_data: Environment data for validation

        Returns:
            ValidationResult: Validation result
        """
        issues = []

        if not HAVE_SECURITY:
            issues.append(
                ValidationIssue(
                    code="SECURITY_PACKAGE_MISSING",
                    message="The security package is not available. Cannot validate security policy.",
                    severity=ValidationSeverity.ERROR,
                    component="security_policy",
                    remediation="Ensure the security package is installed and properly configured.",
                )
            )
            return ValidationResult(
                rule_id="SECURITY_POLICY", is_valid=False, issues=issues
            )

        # Check security policy configuration
        try:
            security_manager = get_security_manager()
            policy = security_manager.get_active_policy()

            if policy is None:
                issues.append(
                    ValidationIssue(
                        code="NO_ACTIVE_POLICY",
                        message="No active security policy found.",
                        severity=ValidationSeverity.ERROR,
                        component="security_policy",
                        remediation="Ensure a security policy is activated.",
                    )
                )
            else:
                # Check policy configuration
                if not policy.has_constraint("data_access"):
                    issues.append(
                        ValidationIssue(
                            code="MISSING_DATA_ACCESS_CONSTRAINT",
                            message="Security policy does not define data access constraints.",
                            severity=ValidationSeverity.WARNING,
                            component="security_policy",
                            remediation="Update security policy to include data access constraints.",
                        )
                    )

                if not policy.has_constraint("external_connections"):
                    issues.append(
                        ValidationIssue(
                            code="MISSING_EXTERNAL_CONNECTIONS_CONSTRAINT",
                            message="Security policy does not define external connection constraints.",
                            severity=ValidationSeverity.WARNING,
                            component="security_policy",
                            remediation="Update security policy to include external connection constraints.",
                        )
                    )

                if not policy.has_constraint("code_execution"):
                    issues.append(
                        ValidationIssue(
                            code="MISSING_CODE_EXECUTION_CONSTRAINT",
                            message="Security policy does not define code execution constraints.",
                            severity=ValidationSeverity.ERROR,
                            component="security_policy",
                            remediation="Update security policy to include code execution constraints.",
                        )
                    )

                # Check policy version
                if hasattr(policy, "version") and policy.version < "1.2.0":
                    issues.append(
                        ValidationIssue(
                            code="OUTDATED_POLICY_VERSION",
                            message=f"Security policy version ({policy.version}) is outdated.",
                            severity=ValidationSeverity.WARNING,
                            component="security_policy",
                            remediation="Update to the latest security policy version (1.2.0 or higher).",
                        )
                    )

        except Exception as e:
            issues.append(
                ValidationIssue(
                    code="POLICY_VALIDATION_ERROR",
                    message=f"Error validating security policy: {str(e)}",
                    severity=ValidationSeverity.ERROR,
                    component="security_policy",
                    remediation="Check security manager and policy configuration.",
                )
            )

        return ValidationResult(
            rule_id="SECURITY_POLICY", is_valid=len(issues) == 0, issues=issues
        )

    def _validate_security_level(self, env_data: EnvironmentData) -> ValidationResult:
        """
        Validate that the security level is appropriate.

        Args:
            env_data: Environment data for validation

        Returns:
            ValidationResult: Validation result
        """
        issues = []

        if not HAVE_SECURITY:
            issues.append(
                ValidationIssue(
                    code="SECURITY_PACKAGE_MISSING",
                    message="The security package is not available. Cannot validate security level.",
                    severity=ValidationSeverity.ERROR,
                    component="security_level",
                    remediation="Ensure the security package is installed and properly configured.",
                )
            )
            return ValidationResult(
                rule_id="SECURITY_LEVEL", is_valid=False, issues=issues
            )

        # Check security level
        try:
            security_manager = get_security_manager()
            current_level = security_manager.get_security_level()

            # Check if security level is appropriate for environment
            if env_data.is_production:
                if current_level < SecurityLevel.HIGH:
                    issues.append(
                        ValidationIssue(
                            code="LOW_SECURITY_LEVEL_IN_PRODUCTION",
                            message=f"Security level ({current_level.name}) is too low for production environment.",
                            severity=ValidationSeverity.ERROR,
                            component="security_level",
                            remediation=f"Set security level to at least {SecurityLevel.HIGH.name} for production environment.",
                        )
                    )
            elif env_data.is_staging:
                if current_level < SecurityLevel.MEDIUM:
                    issues.append(
                        ValidationIssue(
                            code="LOW_SECURITY_LEVEL_IN_STAGING",
                            message=f"Security level ({current_level.name}) is too low for staging environment.",
                            severity=ValidationSeverity.WARNING,
                            component="security_level",
                            remediation=f"Set security level to at least {SecurityLevel.MEDIUM.name} for staging environment.",
                        )
                    )

            # Check if debug mode is enabled in production
            if env_data.is_production and security_manager.is_debug_mode_enabled():
                issues.append(
                    ValidationIssue(
                        code="DEBUG_MODE_IN_PRODUCTION",
                        message="Security debug mode is enabled in production environment.",
                        severity=ValidationSeverity.ERROR,
                        component="security_level",
                        remediation="Disable debug mode in production environment.",
                    )
                )

        except Exception as e:
            issues.append(
                ValidationIssue(
                    code="SECURITY_LEVEL_VALIDATION_ERROR",
                    message=f"Error validating security level: {str(e)}",
                    severity=ValidationSeverity.ERROR,
                    component="security_level",
                    remediation="Check security manager configuration.",
                )
            )

        return ValidationResult(
            rule_id="SECURITY_LEVEL", is_valid=len(issues) == 0, issues=issues
        )

    def _validate_sandbox_environment(
        self, env_data: EnvironmentData
    ) -> ValidationResult:
        """
        Validate that the sandbox environment is properly configured.

        Args:
            env_data: Environment data for validation

        Returns:
            ValidationResult: Validation result
        """
        issues = []

        # Check sandbox configuration
        try:
            import importlib.util

            # Check if sandbox packages are available
            sandbox_packages = ["seccomp", "resource"]
            missing_packages = []

            for package in sandbox_packages:
                if importlib.util.find_spec(package) is None:
                    missing_packages.append(package)

            if missing_packages:
                issues.append(
                    ValidationIssue(
                        code="MISSING_SANDBOX_PACKAGES",
                        message=f"Missing sandbox packages: {', '.join(missing_packages)}",
                        severity=ValidationSeverity.WARNING,
                        component="sandbox",
                        remediation="Install missing packages for better sandbox security.",
                    )
                )

            # Check resource limits if available
            if "resource" not in missing_packages:
                import resource

                # Check file descriptor limit
                soft, hard = resource.getrlimit(resource.RLIMIT_NOFILE)
                if soft < 1024:
                    issues.append(
                        ValidationIssue(
                            code="LOW_FD_LIMIT",
                            message=f"File descriptor soft limit ({soft}) is too low.",
                            severity=ValidationSeverity.WARNING,
                            component="sandbox",
                            remediation="Increase file descriptor limit to at least 1024.",
                        )
                    )

                # Check process limit
                if hasattr(resource, "RLIMIT_NPROC"):
                    soft, hard = resource.getrlimit(resource.RLIMIT_NPROC)
                    if soft < 32:
                        issues.append(
                            ValidationIssue(
                                code="LOW_PROCESS_LIMIT",
                                message=f"Process soft limit ({soft}) is too low.",
                                severity=ValidationSeverity.WARNING,
                                component="sandbox",
                                remediation="Increase process limit to at least 32.",
                            )
                        )

            # Check if running as root (insecure)
            if os.geteuid() == 0:
                issues.append(
                    ValidationIssue(
                        code="RUNNING_AS_ROOT",
                        message="Process is running with root privileges, which is insecure.",
                        severity=ValidationSeverity.ERROR,
                        component="sandbox",
                        remediation="Run the process as a non-root user.",
                    )
                )

        except Exception as e:
            issues.append(
                ValidationIssue(
                    code="SANDBOX_VALIDATION_ERROR",
                    message=f"Error validating sandbox environment: {str(e)}",
                    severity=ValidationSeverity.WARNING,
                    component="sandbox",
                    remediation="Check sandbox configuration.",
                )
            )

        return ValidationResult(
            rule_id="SANDBOX_ENVIRONMENT", is_valid=len(issues) == 0, issues=issues
        )

    def _validate_file_permissions(self, env_data: EnvironmentData) -> ValidationResult:
        """
        Validate that file permissions are properly set.

        Args:
            env_data: Environment data for validation

        Returns:
            ValidationResult: Validation result
        """
        issues = []

        # Check config directory permissions
        config_dir = os.path.join(env_data.base_path, "config")
        if os.path.exists(config_dir):
            try:
                # Get permissions
                config_dir_stat = os.stat(config_dir)
                mode = config_dir_stat.st_mode

                # Check if world-writable
                if mode & 0o2:
                    issues.append(
                        ValidationIssue(
                            code="WORLD_WRITABLE_CONFIG",
                            message="Config directory is world-writable, which is insecure.",
                            severity=ValidationSeverity.ERROR,
                            component="file_permissions",
                            remediation="Change permissions to restrict write access (chmod o-w).",
                        )
                    )

                # Check sensitive file permissions
                sensitive_files = [
                    os.path.join(config_dir, "security.json"),
                    os.path.join(config_dir, "credentials.json"),
                    os.path.join(config_dir, "tokens.json"),
                ]

                for file_path in sensitive_files:
                    if os.path.exists(file_path):
                        file_stat = os.stat(file_path)
                        file_mode = file_stat.st_mode

                        # Check if readable by others
                        if file_mode & 0o4:
                            issues.append(
                                ValidationIssue(
                                    code="WORLD_READABLE_SENSITIVE_FILE",
                                    message=f"Sensitive file {os.path.basename(file_path)} is world-readable.",
                                    severity=ValidationSeverity.ERROR,
                                    component="file_permissions",
                                    remediation=f"Change permissions to restrict read access (chmod o-r {file_path}).",
                                )
                            )

            except Exception as e:
                issues.append(
                    ValidationIssue(
                        code="FILE_PERMISSION_CHECK_ERROR",
                        message=f"Error checking file permissions: {str(e)}",
                        severity=ValidationSeverity.WARNING,
                        component="file_permissions",
                        remediation="Check file permissions manually.",
                    )
                )

        return ValidationResult(
            rule_id="FILE_PERMISSIONS", is_valid=len(issues) == 0, issues=issues
        )

    def _validate_ssl_tls_config(self, env_data: EnvironmentData) -> ValidationResult:
        """
        Validate that SSL/TLS configuration is secure.

        Args:
            env_data: Environment data for validation

        Returns:
            ValidationResult: Validation result
        """
        issues = []

        # Check SSL/TLS configuration
        try:
            # Check SSL protocol version
            context = ssl.create_default_context()

            # Check minimum TLS version
            if context.minimum_version < ssl.TLSVersion.TLSv1_2:
                issues.append(
                    ValidationIssue(
                        code="INSECURE_TLS_VERSION",
                        message="TLS version is below 1.2, which is insecure.",
                        severity=ValidationSeverity.ERROR,
                        component="ssl_config",
                        remediation="Configure SSL context to use TLS 1.2 or higher.",
                    )
                )

            # Check if using secure cipher suites
            if hasattr(context, "get_ciphers"):
                ciphers = context.get_ciphers()
                insecure_ciphers = [
                    cipher
                    for cipher in ciphers
                    if "RC4" in cipher["name"] or "DES" in cipher["name"]
                ]

                if insecure_ciphers:
                    issues.append(
                        ValidationIssue(
                            code="INSECURE_CIPHER_SUITES",
                            message=f"Using insecure cipher suites: {', '.join(c['name'] for c in insecure_ciphers)}",
                            severity=ValidationSeverity.ERROR,
                            component="ssl_config",
                            remediation="Configure SSL context to use secure cipher suites only.",
                        )
                    )

            # Check certificate verification
            if not context.check_hostname:
                issues.append(
                    ValidationIssue(
                        code="HOSTNAME_VERIFICATION_DISABLED",
                        message="SSL hostname verification is disabled.",
                        severity=ValidationSeverity.WARNING,
                        component="ssl_config",
                        remediation="Enable hostname verification in SSL context.",
                    )
                )

            if context.verify_mode != ssl.CERT_REQUIRED:
                issues.append(
                    ValidationIssue(
                        code="CERTIFICATE_VERIFICATION_NOT_REQUIRED",
                        message="SSL certificate verification is not required.",
                        severity=ValidationSeverity.ERROR,
                        component="ssl_config",
                        remediation="Set SSL context verification mode to CERT_REQUIRED.",
                    )
                )

        except Exception as e:
            issues.append(
                ValidationIssue(
                    code="SSL_CONFIG_VALIDATION_ERROR",
                    message=f"Error validating SSL/TLS configuration: {str(e)}",
                    severity=ValidationSeverity.WARNING,
                    component="ssl_config",
                    remediation="Check SSL configuration manually.",
                )
            )

        return ValidationResult(
            rule_id="SSL_TLS_CONFIG", is_valid=len(issues) == 0, issues=issues
        )

    def _validate_network_security(self, env_data: EnvironmentData) -> ValidationResult:
        """
        Validate that network security settings are appropriate.

        Args:
            env_data: Environment data for validation

        Returns:
            ValidationResult: Validation result
        """
        issues = []

        # Check network security configuration
        try:
            # Check if any network services are exposed
            local_ports = []

            # Use socket to check listening ports
            for port in range(1024, 10000):
                s = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
                s.settimeout(0.1)
                result = s.connect_ex(("127.0.0.1", port))
                s.close()

                if result == 0:
                    local_ports.append(port)

            # Check if any unexpected ports are open
            expected_ports = env_data.metadata.get("expected_ports", [8080, 8443])
            unexpected_ports = [
                port for port in local_ports if port not in expected_ports
            ]

            if unexpected_ports:
                issues.append(
                    ValidationIssue(
                        code="UNEXPECTED_OPEN_PORTS",
                        message=f"Unexpected ports are open: {', '.join(map(str, unexpected_ports))}",
                        severity=ValidationSeverity.WARNING,
                        component="network_security",
                        remediation="Close or restrict access to unexpected open ports.",
                    )
                )

            # Check firewall configuration
            if env_data.platform.system == "Linux":
                # Check iptables
                import subprocess

                try:
                    # Check if iptables is active
                    result = subprocess.run(
                        ["iptables", "-L"],
                        stdout=subprocess.PIPE,
                        stderr=subprocess.PIPE,
                        text=True,
                        timeout=5,
                    )

                    if result.returncode != 0:
                        issues.append(
                            ValidationIssue(
                                code="IPTABLES_NOT_CONFIGURED",
                                message="Firewall (iptables) appears to be not configured.",
                                severity=ValidationSeverity.WARNING,
                                component="network_security",
                                remediation="Configure iptables for network protection.",
                            )
                        )
                except:
                    # Ignore if can't check iptables
                    pass

        except Exception as e:
            issues.append(
                ValidationIssue(
                    code="NETWORK_SECURITY_VALIDATION_ERROR",
                    message=f"Error validating network security: {str(e)}",
                    severity=ValidationSeverity.WARNING,
                    component="network_security",
                    remediation="Check network security configuration manually.",
                )
            )

        return ValidationResult(
            rule_id="NETWORK_SECURITY", is_valid=len(issues) == 0, issues=issues
        )

    def _validate_security_boundaries(
        self, env_data: EnvironmentData
    ) -> ValidationResult:
        """
        Validate that security boundaries are properly defined.

        Args:
            env_data: Environment data for validation

        Returns:
            ValidationResult: Validation result
        """
        issues = []

        if not HAVE_SECURITY:
            issues.append(
                ValidationIssue(
                    code="SECURITY_PACKAGE_MISSING",
                    message="The security package is not available. Cannot validate security boundaries.",
                    severity=ValidationSeverity.ERROR,
                    component="security_boundaries",
                    remediation="Ensure the security package is installed and properly configured.",
                )
            )
            return ValidationResult(
                rule_id="SECURITY_BOUNDARIES", is_valid=False, issues=issues
            )

        # Check security boundaries
        try:
            security_manager = get_security_manager()
            boundaries = security_manager.list_boundaries()

            # Check if system boundaries are defined
            required_boundaries = ["filesystem", "network", "process", "memory"]
            missing_boundaries = [
                b
                for b in required_boundaries
                if not any(x.type_name == b for x in boundaries)
            ]

            if missing_boundaries:
                issues.append(
                    ValidationIssue(
                        code="MISSING_SECURITY_BOUNDARIES",
                        message=f"Missing required security boundaries: {', '.join(missing_boundaries)}",
                        severity=ValidationSeverity.ERROR,
                        component="security_boundaries",
                        remediation=f"Define missing security boundaries: {', '.join(missing_boundaries)}",
                    )
                )

            # Check if boundaries are enforced
            unenforced_boundaries = [b for b in boundaries if not b.is_enforced]
            if unenforced_boundaries:
                issues.append(
                    ValidationIssue(
                        code="UNENFORCED_SECURITY_BOUNDARIES",
                        message=f"Some security boundaries are not enforced: {', '.join(b.name for b in unenforced_boundaries)}",
                        severity=ValidationSeverity.WARNING,
                        component="security_boundaries",
                        remediation="Enable enforcement for all security boundaries.",
                    )
                )

        except Exception as e:
            issues.append(
                ValidationIssue(
                    code="SECURITY_BOUNDARIES_VALIDATION_ERROR",
                    message=f"Error validating security boundaries: {str(e)}",
                    severity=ValidationSeverity.ERROR,
                    component="security_boundaries",
                    remediation="Check security manager and boundary configuration.",
                )
            )

        return ValidationResult(
            rule_id="SECURITY_BOUNDARIES", is_valid=len(issues) == 0, issues=issues
        )
