"""
Person Suit - Deployment Validation Base (DE-1)

This module provides base classes for deployment environment validation,
defining interfaces and abstract classes for all validator implementations.

Classes:
- Validator: Abstract base class for all validators

Related Files:
- models.py: Validation data models and enumerations
- environment_validator.py: Environment-specific validation
- resource_validator.py: Resource-specific validation
- security_validator.py: Security-specific validation
- manager.py: Validation orchestration and management

Dependencies:
- person_suit.core.deployment.detection: For environment data to validate
"""

import abc
import logging
from typing import Dict
from typing import List
from typing import Optional

from ..detection import EnvironmentData
from .models import ValidationIssue
from .models import ValidationResult
from .models import ValidationRule
from .models import ValidationSeverity

# Configure logger
logger = logging.getLogger("person_suit.deployment.validation.validator")


class Validator(metaclass=abc.ABCMeta):
    """
    Base class for deployment environment validators.

    This abstract class defines the interface for all validator implementations,
    providing standard methods for rule management and validation.
    """

    def __init__(self):
        """Initialize the validator."""
        self._rules: Dict[str, ValidationRule] = {}
        self._initialized = False

    def initialize(self) -> bool:
        """
        Initialize the validator.

        This method should be overridden by subclasses to register
        their specific validation rules.

        Returns:
            bool: True if initialization was successful
        """
        if self._initialized:
            return True

        self._register_rules()
        self._initialized = True

        logger.debug(f"Initialized validator with {len(self._rules)} rules")
        return True

    @abc.abstractmethod
    def _register_rules(self) -> None:
        """
        Register validation rules.

        This method must be implemented by subclasses to register
        their specific validation rules.
        """
        pass

    def register_rule(self, rule: ValidationRule) -> bool:
        """
        Register a validation rule.

        Args:
            rule: The validation rule to register

        Returns:
            bool: True if the rule was registered successfully
        """
        if rule.rule_id in self._rules:
            logger.warning(f"Rule {rule.rule_id} already registered, overwriting")

        self._rules[rule.rule_id] = rule
        return True

    def get_rule(self, rule_id: str) -> Optional[ValidationRule]:
        """
        Get a validation rule by ID.

        Args:
            rule_id: The ID of the rule to get

        Returns:
            Optional[ValidationRule]: The rule, if found
        """
        return self._rules.get(rule_id)

    def get_rules(self) -> Dict[str, ValidationRule]:
        """
        Get all registered validation rules.

        Returns:
            Dict[str, ValidationRule]: All registered rules
        """
        return self._rules.copy()

    def enable_rule(self, rule_id: str) -> bool:
        """
        Enable a validation rule.

        Args:
            rule_id: The ID of the rule to enable

        Returns:
            bool: True if the rule was enabled
        """
        if rule_id in self._rules:
            self._rules[rule_id].is_enabled = True
            logger.debug(f"Enabled rule {rule_id}")
            return True
        return False

    def disable_rule(self, rule_id: str) -> bool:
        """
        Disable a validation rule.

        Args:
            rule_id: The ID of the rule to disable

        Returns:
            bool: True if the rule was disabled
        """
        if rule_id in self._rules:
            self._rules[rule_id].is_enabled = False
            logger.debug(f"Disabled rule {rule_id}")
            return True
        return False

    @abc.abstractmethod
    def validate(self, env_data: EnvironmentData) -> List[ValidationResult]:
        """
        Validate the environment data.

        Args:
            env_data: Environment data to validate

        Returns:
            List[ValidationResult]: Validation results
        """
        pass

    def validate_rule(
        self, rule_id: str, env_data: EnvironmentData
    ) -> ValidationResult:
        """
        Validate a specific rule.

        This method should be implemented by subclasses to run validation
        for a specific rule.

        Args:
            rule_id: The ID of the rule to validate
            env_data: Environment data to validate

        Returns:
            ValidationResult: The validation result
        """
        rule = self.get_rule(rule_id)
        if not rule:
            issue = ValidationIssue(
                code="UNKNOWN_RULE",
                message=f"Unknown rule: {rule_id}",
                severity=ValidationSeverity.ERROR,
                component=self.__class__.__name__,
            )
            return ValidationResult(rule_id=rule_id, is_valid=False, issues=[issue])

        if not rule.is_enabled:
            return ValidationResult(
                rule_id=rule_id,
                is_valid=True,
                metadata={"skipped": True, "reason": "Rule disabled"},
            )

        # This is a placeholder - subclasses should override this
        return ValidationResult(rule_id=rule_id, is_valid=True)
