"""
Person Suit - Validation Manager (DE-1)

This module provides validation orchestration for the Person Suit framework,
managing various validators and coordinating validation processes.

Classes:
- ValidationManager: Central validation orchestration

Functions:
- get_validation_manager: Get the singleton validation manager instance
- validate_environment: Convenience function to validate the environment
- validate_resources: Convenience function to validate resources
- validate_security: Convenience function to validate security
- validate_all: Convenience function to run all validations

Related Files:
- models.py: Validation data models
- validator.py: Base validator implementation
- environment_validator.py: Environment validation
- resource_validator.py: Resource validation
- security_validator.py: Security validation
- person_suit.core.deployment.health: Health monitoring system
"""

import logging
import threading
from typing import Any
from typing import Dict
from typing import List
from typing import Optional
from typing import Tuple

from ..detection import EnvironmentData
from ..detection import detect_environment
from .environment_validator import EnvironmentValidator
from .models import ValidationIssue
from .models import ValidationResult
from .models import ValidationSeverity
from .resource_validator import ResourceValidator
from .security_validator import SecurityValidator
from .validator import Validator

# Try to import health monitoring (optional integration)
try:
    from person_suit.core.deployment.health import HealthReport
    from person_suit.core.deployment.health import HealthSeverity
    from person_suit.core.deployment.health import HealthStatus
    from person_suit.core.deployment.health import get_health_monitor

    HAVE_HEALTH_MONITORING = True
except ImportError:
    HAVE_HEALTH_MONITORING = False

# Configure logger
logger = logging.getLogger("person_suit.deployment.validation.manager")


class ValidationManager:
    """
    Validation manager for deployment validation.

    Orchestrates validation of environment, resources, security, and other
    aspects of the Person Suit deployment using various validators.
    """

    _instance = None
    _lock = threading.Lock()

    def __new__(cls):
        """Implement singleton pattern."""
        with cls._lock:
            if cls._instance is None:
                cls._instance = super(ValidationManager, cls).__new__(cls)
                cls._instance._initialized = False
            return cls._instance

    def __init__(self):
        """Initialize the validation manager (only once for the singleton)."""
        if self._initialized:
            return

        self._initialized = True
        self._validators: Dict[str, Validator] = {}

        # Register standard validators
        self.register_validator("environment", EnvironmentValidator())
        self.register_validator("resource", ResourceValidator())
        self.register_validator("security", SecurityValidator())

        # Environment data cache
        self._environment_data = None
        self._environment_data_timestamp = None

        # Validation results cache
        self._validation_results: Dict[str, List[ValidationResult]] = {}

        logger.info("Validation manager initialized")

    def register_validator(self, name: str, validator: Validator) -> None:
        """
        Register a validator with the manager.

        Args:
            name: The name of the validator
            validator: The validator instance
        """
        if not isinstance(validator, Validator):
            raise TypeError("Validator must be an instance of Validator")

        self._validators[name] = validator
        logger.debug(f"Registered validator: {name}")

    def unregister_validator(self, name: str) -> bool:
        """
        Unregister a validator by name.

        Args:
            name: The name of the validator to unregister

        Returns:
            bool: True if the validator was unregistered, False otherwise
        """
        if name in self._validators:
            del self._validators[name]
            logger.debug(f"Unregistered validator: {name}")
            return True
        return False

    def get_validator(self, name: str) -> Optional[Validator]:
        """
        Get a registered validator by name.

        Args:
            name: The name of the validator to get

        Returns:
            Optional[Validator]: The validator if found, None otherwise
        """
        return self._validators.get(name)

    def list_validators(self) -> List[str]:
        """
        List all registered validators.

        Returns:
            List[str]: List of validator names
        """
        return list(self._validators.keys())

    def get_environment_data(self, refresh: bool = False) -> EnvironmentData:
        """
        Get environment data, using cached data if available.

        Args:
            refresh: Whether to force a refresh of the environment data

        Returns:
            EnvironmentData: The environment data
        """
        # If we have cached data and we're not forcing a refresh, use it
        if self._environment_data is not None and not refresh:
            return self._environment_data

        # Otherwise, get fresh environment data
        self._environment_data = detect_environment()
        logger.debug("Refreshed environment data")

        return self._environment_data

    def validate(
        self, validator_name: str, env_data: Optional[EnvironmentData] = None
    ) -> List[ValidationResult]:
        """
        Validate using a specific validator.

        Args:
            validator_name: The name of the validator to use
            env_data: Optional environment data to use (None to use cached or detect)

        Returns:
            List[ValidationResult]: The validation results

        Raises:
            ValueError: If the validator is not registered
        """
        # Check if validator exists
        validator = self._validators.get(validator_name)
        if validator is None:
            raise ValueError(f"Validator not found: {validator_name}")

        # Get environment data if not provided
        if env_data is None:
            env_data = self.get_environment_data()

        # Validate
        logger.info(f"Running validator: {validator_name}")
        results = validator.validate(env_data)

        # Cache the results
        self._validation_results[validator_name] = results

        # Log any critical validation failures
        critical_failures = [
            result
            for result in results
            if not result.is_valid
            and any(
                issue.severity == ValidationSeverity.ERROR for issue in result.issues
            )
        ]

        if critical_failures:
            logger.warning(
                f"Validator {validator_name} found {len(critical_failures)} critical validation failures"
            )
            for failure in critical_failures:
                logger.warning(
                    f"  - {failure.rule_id}: {', '.join(issue.message for issue in failure.issues if issue.severity == ValidationSeverity.ERROR)}"
                )

        return results

    def validate_all(
        self,
        env_data: Optional[EnvironmentData] = None,
        include_validators: Optional[List[str]] = None,
    ) -> Dict[str, List[ValidationResult]]:
        """
        Run all validators or a specific subset.

        Args:
            env_data: Optional environment data to use (None to use cached or detect)
            include_validators: Specific validators to include (None for all)

        Returns:
            Dict[str, List[ValidationResult]]: Dictionary mapping validator names to results
        """
        # Get environment data if not provided
        if env_data is None:
            env_data = self.get_environment_data()

        # Determine which validators to run
        validators_to_run = {}
        if include_validators:
            for name in include_validators:
                if name in self._validators:
                    validators_to_run[name] = self._validators[name]
        else:
            validators_to_run = self._validators

        # Run each validator
        all_results = {}
        for name in validators_to_run:
            try:
                results = self.validate(name, env_data)
                all_results[name] = results
            except Exception as e:
                logger.error(f"Error running validator {name}: {e}", exc_info=True)
                # Add an error result
                all_results[name] = [
                    ValidationResult(
                        rule_id="VALIDATOR_ERROR",
                        is_valid=False,
                        issues=[
                            ValidationIssue(
                                code="VALIDATOR_EXCEPTION",
                                message=f"Error running validator: {str(e)}",
                                severity=ValidationSeverity.ERROR,
                                component=name,
                            )
                        ],
                        metadata={
                            "exception": str(e),
                            "exception_type": type(e).__name__,
                        },
                    )
                ]

        return all_results

    def get_validation_summary(
        self, results: Optional[Dict[str, List[ValidationResult]]] = None
    ) -> Dict[str, Any]:
        """
        Get a summary of validation results.

        Args:
            results: Optional validation results to summarize (None to use cached)

        Returns:
            Dict[str, Any]: Dictionary with validation summary
        """
        # Use cached results if not provided
        if results is None:
            results = self._validation_results

        # If no results available, return empty summary
        if not results:
            return {
                "valid": False,
                "validators": 0,
                "rules_checked": 0,
                "rules_passed": 0,
                "rules_failed": 0,
                "error_count": 0,
                "warning_count": 0,
                "info_count": 0,
                "components": {},
            }

        # Count issues by severity
        error_count = 0
        warning_count = 0
        info_count = 0

        # Count rules
        rules_checked = 0
        rules_passed = 0
        rules_failed = 0

        # Track components
        components = {}

        # Process each validator's results
        for validator_name, validator_results in results.items():
            rules_checked += len(validator_results)

            for result in validator_results:
                # Count passed/failed rules
                if result.is_valid:
                    rules_passed += 1
                else:
                    rules_failed += 1

                # Count issues by severity
                for issue in result.issues:
                    if issue.severity == ValidationSeverity.ERROR:
                        error_count += 1
                    elif issue.severity == ValidationSeverity.WARNING:
                        warning_count += 1
                    elif issue.severity == ValidationSeverity.INFO:
                        info_count += 1

                    # Track components
                    component = issue.component
                    if component not in components:
                        components[component] = {
                            "error_count": 0,
                            "warning_count": 0,
                            "info_count": 0,
                            "issues": [],
                        }

                    # Add issue to component
                    components[component]["issues"].append(
                        {
                            "code": issue.code,
                            "message": issue.message,
                            "severity": issue.severity.name,
                            "remediation": issue.remediation,
                        }
                    )

                    # Update component counts
                    if issue.severity == ValidationSeverity.ERROR:
                        components[component]["error_count"] += 1
                    elif issue.severity == ValidationSeverity.WARNING:
                        components[component]["warning_count"] += 1
                    elif issue.severity == ValidationSeverity.INFO:
                        components[component]["info_count"] += 1

        # Determine overall validity
        valid = error_count == 0

        return {
            "valid": valid,
            "validators": len(results),
            "rules_checked": rules_checked,
            "rules_passed": rules_passed,
            "rules_failed": rules_failed,
            "error_count": error_count,
            "warning_count": warning_count,
            "info_count": info_count,
            "components": components,
        }

    def check_health(self) -> Tuple[bool, Dict[str, Any]]:
        """
        Check system health via health monitoring integration.

        Returns:
            Tuple[bool, Dict[str, Any]]: Tuple of (is_healthy, health_data)
        """
        if not HAVE_HEALTH_MONITORING:
            logger.warning("Health monitoring is not available")
            return False, {
                "available": False,
                "message": "Health monitoring is not available",
            }

        # Get health monitor and check health
        try:
            health_monitor = get_health_monitor()
            health_report = health_monitor.check_health()

            # Extract key information
            health_data = {
                "available": True,
                "status": health_report.status.name,
                "is_healthy": health_report.is_healthy,
                "is_operational": health_report.is_operational,
                "summary": health_report.summary,
                "component_statuses": {
                    component: status.name
                    for component, status in health_report.components.items()
                },
                "issues": [
                    {
                        "component": issue.component,
                        "message": issue.message,
                        "severity": issue.severity.name,
                        "remediation": issue.remediation,
                    }
                    for issue in health_report.issues
                ],
                "metrics": [
                    {
                        "name": metric.name,
                        "value": metric.value,
                        "unit": metric.unit,
                        "component": metric.component,
                    }
                    for metric in health_report.metrics
                ],
            }

            return health_report.is_healthy, health_data

        except Exception as e:
            logger.error(f"Error checking health: {e}", exc_info=True)
            return False, {
                "available": True,
                "status": "ERROR",
                "message": f"Error checking health: {str(e)}",
                "exception": str(e),
            }

    def validate_with_health_check(self) -> Dict[str, Any]:
        """
        Run validation and health checks together.

        This provides a comprehensive assessment of the system's
        deployment readiness, combining validation and health data.

        Returns:
            Dict[str, Any]: Combined validation and health data
        """
        # Run validation
        validation_results = self.validate_all()
        validation_summary = self.get_validation_summary(validation_results)

        # Run health check
        is_healthy, health_data = self.check_health()

        # Combine results
        result = {
            "validation": validation_summary,
            "health": health_data,
            "is_valid": validation_summary["valid"],
            "is_healthy": is_healthy,
            "is_ready": validation_summary["valid"] and is_healthy,
        }

        return result


# Singleton instance
_validation_manager_instance = None


def get_validation_manager() -> ValidationManager:
    """
    Get the singleton validation manager instance.

    Returns:
        ValidationManager: The validation manager instance
    """
    global _validation_manager_instance
    if _validation_manager_instance is None:
        _validation_manager_instance = ValidationManager()
    return _validation_manager_instance


def validate_environment() -> List[ValidationResult]:
    """
    Convenience function to validate the environment.

    Returns:
        List[ValidationResult]: The validation results
    """
    return get_validation_manager().validate("environment")


def validate_resources() -> List[ValidationResult]:
    """
    Convenience function to validate resources.

    Returns:
        List[ValidationResult]: The validation results
    """
    return get_validation_manager().validate("resource")


def validate_security() -> List[ValidationResult]:
    """
    Convenience function to validate security.

    Returns:
        List[ValidationResult]: The validation results
    """
    return get_validation_manager().validate("security")


def validate_all() -> Dict[str, List[ValidationResult]]:
    """
    Convenience function to run all validations.

    Returns:
        Dict[str, List[ValidationResult]]: Dictionary mapping validator names to results
    """
    return get_validation_manager().validate_all()
