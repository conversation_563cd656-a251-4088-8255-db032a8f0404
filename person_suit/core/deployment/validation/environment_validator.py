"""
Person Suit - Environment Validator (DE-1)

This module provides environment-specific validation for the Person Suit framework,
validating hardware, platform, and runtime environment configurations.

Classes:
- EnvironmentValidator: Validator for deployment environments

Related Files:
- models.py: Validation data models and enumerations
- validator.py: Base validator implementation
- resource_validator.py: Resource-specific validation
- security_validator.py: Security-specific validation
- manager.py: Validation orchestration and management

Dependencies:
- person_suit.core.deployment.detection: For environment data to validate
"""

import logging
import os
from typing import List

from ..detection import EnvironmentData
from .models import ValidationIssue
from .models import ValidationResult
from .models import ValidationRule
from .models import ValidationSeverity
from .validator import Validator

# Configure logger
logger = logging.getLogger("person_suit.deployment.validation.environment_validator")


class EnvironmentValidator(Validator):
    """
    Environment validator for deployment environments.

    Validates hardware, platform, and runtime environment configurations
    to ensure they meet the requirements for Person Suit.
    """

    def __init__(self):
        """Initialize the environment validator."""
        super().__init__()

    def _register_rules(self) -> None:
        """Register environment validation rules."""
        # Platform rules
        self.register_rule(
            ValidationRule(
                rule_id="ENV_PLATFORM_SUPPORTED",
                name="Platform Support Check",
                description="Validates that the current platform is supported",
                component="environment.platform",
                is_critical=True,
            )
        )

        self.register_rule(
            ValidationRule(
                rule_id="ENV_PYTHON_VERSION",
                name="Python Version Check",
                description="Validates that the Python version meets requirements",
                component="environment.platform",
                is_critical=True,
                parameters={"min_version": "3.9.0"},
            )
        )

        # Hardware rules
        self.register_rule(
            ValidationRule(
                rule_id="ENV_CPU_CORES",
                name="CPU Core Count Check",
                description="Validates that the system has enough CPU cores",
                component="environment.hardware",
                parameters={"min_cores": 2},
            )
        )

        self.register_rule(
            ValidationRule(
                rule_id="ENV_MEMORY",
                name="Memory Size Check",
                description="Validates that the system has enough memory",
                component="environment.hardware",
                parameters={"min_memory_mb": 4096},  # 4GB
            )
        )

        # Runtime rules
        self.register_rule(
            ValidationRule(
                rule_id="ENV_RUNTIME_ENV",
                name="Runtime Environment Check",
                description="Validates that the runtime environment is properly identified",
                component="environment.runtime",
                is_critical=True,
            )
        )

        self.register_rule(
            ValidationRule(
                rule_id="ENV_ENV_VARS",
                name="Environment Variables Check",
                description="Validates that required environment variables are set",
                component="environment.runtime",
                parameters={"required_vars": ["PERSON_SUIT_HOME"]},
            )
        )

        # M3 Max specific rules
        self.register_rule(
            ValidationRule(
                rule_id="ENV_M3_MAX_OPTIMIZATIONS",
                name="M3 Max Optimizations Check",
                description="Validates M3 Max optimization capabilities if available",
                component="environment.hardware",
                is_enabled=True,
            )
        )

    def validate(self, env_data: EnvironmentData) -> List[ValidationResult]:
        """
        Validate the environment data.

        Args:
            env_data: Environment data to validate

        Returns:
            List[ValidationResult]: Validation results
        """
        results = []

        # Validate all enabled rules
        for rule_id, rule in self._rules.items():
            if rule.is_enabled:
                result = self.validate_rule(rule_id, env_data)
                results.append(result)

                if not result.is_valid and rule.is_critical:
                    logger.error(f"Critical validation rule failed: {rule_id}")

        return results

    def validate_rule(
        self, rule_id: str, env_data: EnvironmentData
    ) -> ValidationResult:
        """
        Validate a specific environment rule.

        Args:
            rule_id: The ID of the rule to validate
            env_data: Environment data to validate

        Returns:
            ValidationResult: The validation result
        """
        # Check if rule exists and is enabled
        rule = self.get_rule(rule_id)
        if not rule or not rule.is_enabled:
            return super().validate_rule(rule_id, env_data)

        # Validate based on rule ID
        if rule_id == "ENV_PLATFORM_SUPPORTED":
            return self._validate_platform_supported(rule, env_data)
        elif rule_id == "ENV_PYTHON_VERSION":
            return self._validate_python_version(rule, env_data)
        elif rule_id == "ENV_CPU_CORES":
            return self._validate_cpu_cores(rule, env_data)
        elif rule_id == "ENV_MEMORY":
            return self._validate_memory(rule, env_data)
        elif rule_id == "ENV_RUNTIME_ENV":
            return self._validate_runtime_env(rule, env_data)
        elif rule_id == "ENV_ENV_VARS":
            return self._validate_env_vars(rule, env_data)
        elif rule_id == "ENV_M3_MAX_OPTIMIZATIONS":
            return self._validate_m3_max_optimizations(rule, env_data)
        else:
            logger.warning(f"Unknown rule ID: {rule_id}")
            return ValidationResult(
                rule_id=rule_id,
                is_valid=False,
                issues=[
                    ValidationIssue(
                        code="UNKNOWN_RULE",
                        message=f"Unknown rule: {rule_id}",
                        severity=ValidationSeverity.ERROR,
                        component="environment_validator",
                    )
                ],
            )

    def _validate_platform_supported(
        self, rule: ValidationRule, env_data: EnvironmentData
    ) -> ValidationResult:
        """Validate that the current platform is supported."""
        supported_platforms = ["Linux", "Darwin", "Windows"]
        is_valid = env_data.platform.system in supported_platforms

        if is_valid:
            return ValidationResult(
                rule_id=rule.rule_id,
                is_valid=True,
                metadata={"platform": env_data.platform.system},
            )
        else:
            return ValidationResult(
                rule_id=rule.rule_id,
                is_valid=False,
                issues=[
                    ValidationIssue(
                        code="UNSUPPORTED_PLATFORM",
                        message=f"Platform {env_data.platform.system} is not supported",
                        severity=ValidationSeverity.ERROR,
                        component="environment.platform",
                        remediation="Please use a supported platform: Linux, macOS, or Windows",
                    )
                ],
                metadata={"platform": env_data.platform.system},
            )

    def _validate_python_version(
        self, rule: ValidationRule, env_data: EnvironmentData
    ) -> ValidationResult:
        """Validate that the Python version meets requirements."""
        import packaging.version

        min_version = rule.parameters.get("min_version", "3.9.0")
        current_version = env_data.platform.python_version

        is_valid = packaging.version.parse(current_version) >= packaging.version.parse(
            min_version
        )

        if is_valid:
            return ValidationResult(
                rule_id=rule.rule_id,
                is_valid=True,
                metadata={
                    "current_version": current_version,
                    "min_version": min_version,
                },
            )
        else:
            return ValidationResult(
                rule_id=rule.rule_id,
                is_valid=False,
                issues=[
                    ValidationIssue(
                        code="PYTHON_VERSION_TOO_LOW",
                        message=f"Python version {current_version} is below the minimum required version {min_version}",
                        severity=ValidationSeverity.ERROR,
                        component="environment.platform",
                        remediation=f"Please upgrade to Python {min_version} or higher",
                    )
                ],
                metadata={
                    "current_version": current_version,
                    "min_version": min_version,
                },
            )

    def _validate_cpu_cores(
        self, rule: ValidationRule, env_data: EnvironmentData
    ) -> ValidationResult:
        """Validate that the system has enough CPU cores."""
        min_cores = rule.parameters.get("min_cores", 2)
        current_cores = env_data.hardware.cpu_count

        is_valid = current_cores >= min_cores

        if is_valid:
            return ValidationResult(
                rule_id=rule.rule_id,
                is_valid=True,
                metadata={"current_cores": current_cores, "min_cores": min_cores},
            )
        else:
            return ValidationResult(
                rule_id=rule.rule_id,
                is_valid=False,
                issues=[
                    ValidationIssue(
                        code="INSUFFICIENT_CPU_CORES",
                        message=f"System has {current_cores} CPU cores, but {min_cores} are required",
                        severity=ValidationSeverity.WARNING,
                        component="environment.hardware",
                        remediation="Performance may be degraded. Consider using a system with more CPU cores.",
                    )
                ],
                metadata={"current_cores": current_cores, "min_cores": min_cores},
            )

    def _validate_memory(
        self, rule: ValidationRule, env_data: EnvironmentData
    ) -> ValidationResult:
        """Validate that the system has enough memory."""
        min_memory_mb = rule.parameters.get("min_memory_mb", 4096)  # 4GB
        current_memory_mb = (
            env_data.hardware.total_memory // (1024 * 1024)
            if env_data.hardware.total_memory
            else 0
        )

        is_valid = current_memory_mb >= min_memory_mb

        if is_valid:
            return ValidationResult(
                rule_id=rule.rule_id,
                is_valid=True,
                metadata={
                    "current_memory_mb": current_memory_mb,
                    "min_memory_mb": min_memory_mb,
                },
            )
        else:
            return ValidationResult(
                rule_id=rule.rule_id,
                is_valid=False,
                issues=[
                    ValidationIssue(
                        code="INSUFFICIENT_MEMORY",
                        message=f"System has {current_memory_mb}MB memory, but {min_memory_mb}MB are required",
                        severity=ValidationSeverity.WARNING,
                        component="environment.hardware",
                        remediation="Performance may be degraded. Consider using a system with more memory.",
                    )
                ],
                metadata={
                    "current_memory_mb": current_memory_mb,
                    "min_memory_mb": min_memory_mb,
                },
            )

    def _validate_runtime_env(
        self, rule: ValidationRule, env_data: EnvironmentData
    ) -> ValidationResult:
        """Validate that the runtime environment is properly identified."""
        is_valid = env_data.runtime.environment is not None

        if is_valid:
            return ValidationResult(
                rule_id=rule.rule_id,
                is_valid=True,
                metadata={"environment": env_data.runtime.environment.name},
            )
        else:
            return ValidationResult(
                rule_id=rule.rule_id,
                is_valid=False,
                issues=[
                    ValidationIssue(
                        code="UNKNOWN_RUNTIME_ENV",
                        message="Runtime environment could not be identified",
                        severity=ValidationSeverity.ERROR,
                        component="environment.runtime",
                        remediation="Check environment configuration and ensure runtime environment can be detected.",
                    )
                ],
            )

    def _validate_env_vars(
        self, rule: ValidationRule, env_data: EnvironmentData
    ) -> ValidationResult:
        """Validate that required environment variables are set."""
        required_vars = rule.parameters.get("required_vars", [])
        missing_vars = [var for var in required_vars if var not in os.environ]

        is_valid = len(missing_vars) == 0

        if is_valid:
            return ValidationResult(
                rule_id=rule.rule_id,
                is_valid=True,
                metadata={"required_vars": required_vars},
            )
        else:
            return ValidationResult(
                rule_id=rule.rule_id,
                is_valid=False,
                issues=[
                    ValidationIssue(
                        code="MISSING_ENV_VARS",
                        message=f"Missing required environment variables: {', '.join(missing_vars)}",
                        severity=ValidationSeverity.ERROR,
                        component="environment.runtime",
                        remediation=f"Set the following environment variables: {', '.join(missing_vars)}",
                    )
                ],
                metadata={"required_vars": required_vars, "missing_vars": missing_vars},
            )

    def _validate_m3_max_optimizations(
        self, rule: ValidationRule, env_data: EnvironmentData
    ) -> ValidationResult:
        """Validate M3 Max optimization capabilities if available."""
        is_m3_max = env_data.hardware.is_m3_max
        has_neural_engine = env_data.hardware.has_neural_engine
        supports_metal = env_data.hardware.supports_metal

        # Not a failure if not an M3 Max, but we want to check optimizations if it is
        if not is_m3_max:
            return ValidationResult(
                rule_id=rule.rule_id,
                is_valid=True,
                metadata={
                    "is_m3_max": False,
                    "message": "System is not an M3 Max, skipping optimization checks",
                },
            )

        # If it is an M3 Max, validate that optimizations are available
        is_valid = has_neural_engine and supports_metal

        if is_valid:
            return ValidationResult(
                rule_id=rule.rule_id,
                is_valid=True,
                metadata={
                    "is_m3_max": True,
                    "has_neural_engine": has_neural_engine,
                    "supports_metal": supports_metal,
                },
            )
        else:
            missing_features = []
            if not has_neural_engine:
                missing_features.append("Neural Engine")
            if not supports_metal:
                missing_features.append("Metal support")

            return ValidationResult(
                rule_id=rule.rule_id,
                is_valid=False,
                issues=[
                    ValidationIssue(
                        code="M3_MAX_OPTIMIZATIONS_UNAVAILABLE",
                        message=f"M3 Max detected but missing optimization features: {', '.join(missing_features)}",
                        severity=ValidationSeverity.WARNING,
                        component="environment.hardware",
                        remediation="Check system configuration for M3 Max optimization support.",
                    )
                ],
                metadata={
                    "is_m3_max": True,
                    "has_neural_engine": has_neural_engine,
                    "supports_metal": supports_metal,
                    "missing_features": missing_features,
                },
            )
