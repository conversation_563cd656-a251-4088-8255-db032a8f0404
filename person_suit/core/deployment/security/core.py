"""
Person Suit - Deployment Security Core (DE-1)

This module provides security boundaries for the Person Suit framework,
enabling secure operation across different deployment environments by
implementing environment-specific security controls and isolation mechanisms.

Components:
- BoundaryType: Types of security boundaries
- SecurityLevel: Security enforcement levels
- SecurityConstraint: Security constraint definition
- SecurityPolicy: Environment-specific security policy
- SecurityBoundary: Security isolation boundary
- SecurityManager: Central security management

Related Files:
- models.py: Security data models and enumerations
- boundary.py: Security boundary implementation
- manager.py: Security manager implementation
- token.py: Security token handling
- policy.py: Security policy definitions

Dependencies:
- person_suit.core.infrastructure.security: For security primitives
- person_suit.core.deployment.detection: For environment detection
"""

# Import from boundary.py
# Import core types from infrastructure
from ...infrastructure.security import AccessLevel
from ...infrastructure.security import SecurityContext
from ...infrastructure.security import SecurityRole
from ...infrastructure.security import TokenType
from ...infrastructure.security.authentication.auth_manager import TokenCredential
from .boundary import SecurityBoundary

# Import from manager.py
from .manager import SecurityManager
from .manager import get_security_manager

# Import from models.py
from .models import BoundaryType
from .models import SecurityConstraint
from .models import SecurityLevel
from .models import SecurityPolicy

# Import policies from policy.py
from .policy import create_development_policy
from .policy import create_production_policy
from .policy import create_staging_policy
from .policy import create_testing_policy

# Import from token.py
from .token import extend_token


# Convenience functions that delegate to the security manager
def get_boundary(name: str, boundary_type: BoundaryType) -> SecurityBoundary:
    """Get a security boundary."""
    return get_security_manager().get_boundary(name, boundary_type)


def create_token(
    token_type: TokenType, access_level: AccessLevel, role: SecurityRole
) -> TokenCredential:
    """Create a security token (represented by TokenCredential)."""
    return get_security_manager().create_token(token_type, access_level, role)


def verify_token(token: TokenCredential) -> bool:
    """Verify a security token."""
    return get_security_manager().verify_token(token)


def revoke_token(token: TokenCredential) -> bool:
    """Revoke a security token."""
    return get_security_manager().revoke_token(token)


def get_security_context() -> SecurityContext:
    """Get the current security context."""
    return get_security_manager().get_security_context()


def get_active_policy() -> SecurityPolicy:
    """Get the active security policy."""
    return get_security_manager().get_active_policy()


# Define exported symbols
__all__ = [
    # Models
    "BoundaryType",
    "SecurityLevel",
    "SecurityConstraint",
    "SecurityPolicy",
    # Security classes
    "SecurityBoundary",
    "SecurityManager",
    "SecurityContext",
    "TokenCredential",
    "TokenType",
    "AccessLevel",
    "SecurityRole",
    # Manager functions
    "get_security_manager",
    # Token functions
    "create_token",
    "verify_token",
    "revoke_token",
    "extend_token",
    # Boundary functions
    "get_boundary",
    # Context and policy functions
    "get_security_context",
    "get_active_policy",
    # Policy creation functions
    "create_development_policy",
    "create_testing_policy",
    "create_staging_policy",
    "create_production_policy",
]
