"""
Person Suit - Security Models (DE-1)

This module provides data models and enumerations for the security system,
defining security boundaries, constraints, policies, and enforcement levels.

Models:
- BoundaryType: Types of security boundaries
- SecurityLevel: Security enforcement levels
- SecurityConstraint: Security constraint definition
- SecurityPolicy: Environment-specific security policy

Related Files:
- boundary.py: Security isolation boundary implementation
- manager.py: Security management and policy enforcement
- token.py: Security token handling and verification
- access.py: Access control mechanisms

Dependencies:
- person_suit.core.infrastructure.security: For core security types
- person_suit.core.infrastructure.configuration.environment: For environment types
"""

from dataclasses import dataclass
from dataclasses import field
from enum import Enum
from enum import auto
from typing import Any
from typing import Dict

# Import from infrastructure for environment types
from ...infrastructure.configuration.environment.handler import Environment

# Import from infrastructure security


class BoundaryType(Enum):
    """Types of security boundaries."""

    PROCESS = auto()
    THREAD = auto()
    MODULE = auto()
    NAMESPACE = auto()
    FILESYSTEM = auto()
    NETWORK = auto()
    MEMORY = auto()
    CUSTOM = auto()


class SecurityLevel(Enum):
    """Security enforcement levels."""

    STRICT = auto()
    STANDARD = auto()
    RELAXED = auto()
    DISABLED = auto()


@dataclass
class SecurityConstraint:
    """Security constraint definition."""

    name: str
    description: str
    boundary_type: BoundaryType
    security_level: SecurityLevel
    enforced: bool = True
    priority: int = 100
    metadata: Dict[str, Any] = field(default_factory=dict)


@dataclass
class SecurityPolicy:
    """
    Environment-specific security policy.

    Defines security constraints and enforcement settings for
    a specific deployment environment.
    """

    name: str
    description: str
    environment: Environment
    constraints: Dict[str, SecurityConstraint]
    default_security_level: SecurityLevel = SecurityLevel.STANDARD
    is_active: bool = False
    metadata: Dict[str, Any] = field(default_factory=dict)

    def to_dict(self) -> Dict[str, Any]:
        """Convert policy to dictionary."""
        return {
            "name": self.name,
            "description": self.description,
            "environment": self.environment.name,
            "constraints": {
                name: {
                    "name": constraint.name,
                    "description": constraint.description,
                    "boundary_type": constraint.boundary_type.name,
                    "security_level": constraint.security_level.name,
                    "enforced": constraint.enforced,
                    "priority": constraint.priority,
                    "metadata": constraint.metadata,
                }
                for name, constraint in self.constraints.items()
            },
            "default_security_level": self.default_security_level.name,
            "is_active": self.is_active,
            "metadata": self.metadata,
        }

    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> "SecurityPolicy":
        """Create policy from dictionary."""
        constraints = {}
        for name, constraint_data in data.get("constraints", {}).items():
            try:
                constraint = SecurityConstraint(
                    name=constraint_data["name"],
                    description=constraint_data["description"],
                    boundary_type=BoundaryType[constraint_data["boundary_type"]],
                    security_level=SecurityLevel[constraint_data["security_level"]],
                    enforced=constraint_data.get("enforced", True),
                    priority=constraint_data.get("priority", 100),
                    metadata=constraint_data.get("metadata", {}),
                )
                constraints[name] = constraint
            except (KeyError, ValueError) as e:
                # Use a logger in a real implementation
                print(f"Warning: Invalid constraint data for {name}: {e}")

        return cls(
            name=data["name"],
            description=data["description"],
            environment=Environment[data["environment"]],
            constraints=constraints,
            default_security_level=SecurityLevel[
                data.get("default_security_level", "STANDARD")
            ],
            is_active=data.get("is_active", False),
            metadata=data.get("metadata", {}),
        )
