"""
Person Suit - Security Boundaries (DE-1)

This module provides security isolation boundaries for the Person Suit framework,
enabling secure isolation between components across different deployment environments.

Classes:
- SecurityBoundary: Security isolation boundary

Related Files:
- models.py: Security data models and enumerations
- manager.py: Security management and policy enforcement
- token.py: Security token handling and verification
- access.py: Access control mechanisms

Dependencies:
- person_suit.core.infrastructure.security: For core security types
"""

import logging
import threading

from ...infrastructure.security import SecurityContext
from ...infrastructure.security.authentication.auth_manager import TokenCredential
from .models import BoundaryType
from .models import SecurityConstraint
from .models import SecurityLevel

# Configure logger
logger = logging.getLogger("person_suit.deployment.security.boundary")


class SecurityBoundary:
    """
    Security isolation boundary.

    Provides isolation between components in the Person Suit framework,
    enforcing security constraints based on the active security policy.
    """

    def __init__(self, name: str, boundary_type: BoundaryType):
        """
        Initialize security boundary.

        Args:
            name: Boundary name
            boundary_type: Boundary type
        """
        self._name = name
        self._boundary_type = boundary_type
        self._security_level = SecurityLevel.STANDARD
        self._constraints = {}
        self._initialized = False
        self._context = None
        self._access_tokens = set()
        self._lock = threading.RLock()

        logger.debug(f"Created security boundary: {name} ({boundary_type.name})")

    @property
    def name(self) -> str:
        """Get boundary name."""
        return self._name

    @property
    def boundary_type(self) -> BoundaryType:
        """Get boundary type."""
        return self._boundary_type

    @property
    def security_level(self) -> SecurityLevel:
        """Get security level."""
        return self._security_level

    def initialize(self, context: SecurityContext) -> bool:
        """
        Initialize the security boundary.

        Args:
            context: Security context

        Returns:
            bool: True if initialization was successful
        """
        if self._initialized:
            return True

        with self._lock:
            # Set up security context
            self._context = context
            self._security_level = context.security_level

            # Apply constraint for boundary type
            constraint_name = f"{self._boundary_type.name.lower()}_boundary"
            if constraint_name in context.constraints:
                constraint = context.constraints[constraint_name]
                self._security_level = constraint.security_level
                self._constraints[constraint_name] = constraint

                logger.debug(
                    f"Applied constraint {constraint_name} with level {constraint.security_level.name}"
                )

            self._initialized = True
            logger.info(
                f"Initialized security boundary: {self._name} ({self._boundary_type.name}) with level {self._security_level.name}"
            )
            return True

    def grant_access(self, token: TokenCredential) -> bool:
        """
        Grant access through this boundary.

        Args:
            token: TokenCredential to grant access to.
                   Assumes the token has already been verified.

        Returns:
            bool: True if access was granted
        """
        with self._lock:
            # Skip if security is disabled
            if self._security_level == SecurityLevel.DISABLED:
                return True

            # TokenCredential has no is_active flag.
            # Verification (including expiration) should happen *before* calling grant_access.
            # logger.debug(f"Granting access assumes token {token.token_value[:8]}... is active and valid.")

            # Register token value
            self._access_tokens.add(token.token_value)
            logger.debug(f"Granted access to token: {token.token_value[:8]}...")
            return True

    def check_access(self, token: TokenCredential) -> bool:
        """
        Check if a token has access through this boundary.

        Args:
            token: TokenCredential to check

        Returns:
            bool: True if access is allowed
        """
        # Skip if security is disabled
        if self._security_level == SecurityLevel.DISABLED:
            return True

        # Check if token value is registered
        has_access = token.token_value in self._access_tokens

        if not has_access:
            logger.warning(f"Access denied for token: {token.token_value[:8]}...")

        return has_access

    def revoke_access(self, token: TokenCredential) -> bool:
        """
        Revoke access through this boundary.

        Args:
            token: TokenCredential whose access to revoke

        Returns:
            bool: True if access was revoked
        """
        with self._lock:
            if token.token_value in self._access_tokens:
                self._access_tokens.remove(token.token_value)
                logger.debug(f"Revoked access for token: {token.token_value[:8]}...")
                return True

            logger.debug(f"Token not found for revocation: {token.token_value[:8]}...")
            return False

    def apply_constraint(self, constraint: SecurityConstraint) -> bool:
        """
        Apply a security constraint to this boundary.

        Args:
            constraint: Security constraint to apply

        Returns:
            bool: True if constraint was applied successfully
        """
        with self._lock:
            # Validate constraint applies to this boundary
            if constraint.boundary_type != self._boundary_type:
                logger.warning(
                    f"Constraint {constraint.name} not applicable to boundary type {self._boundary_type.name}"
                )
                return False

            self._constraints[constraint.name] = constraint

            # Update security level if constraint is higher priority
            if (
                constraint.enforced
                and constraint.security_level.value < self._security_level.value
            ):
                old_level = self._security_level
                self._security_level = constraint.security_level
                logger.info(
                    f"Updated security level from {old_level.name} to {self._security_level.name} due to constraint {constraint.name}"
                )

            logger.debug(f"Applied constraint: {constraint.name}")
            return True
