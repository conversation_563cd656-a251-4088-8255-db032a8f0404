"""
Person Suit - Deployment Security Package (DE-1)

This package provides security boundaries for the Person Suit framework,
enabling secure operation across different deployment environments by
implementing environment-specific security controls and isolation mechanisms.

Components:
- BoundaryType: Types of security boundaries
- SecurityLevel: Security enforcement levels
- SecurityConstraint: Security constraint definition
- SecurityPolicy: Environment-specific security policy
- SecurityBoundary: Security isolation boundary
- SecurityManager: Central security management

Usage:
    # Get the security manager
    manager = get_security_manager()

    # Get a security boundary
    boundary = get_boundary("my_component", BoundaryType.PROCESS)

    # Create a security token
    token = create_token(TokenType.COMPONENT, AccessLevel.STANDARD, SecurityRole.SYSTEM)

    # Grant access through a boundary
    boundary.grant_access(token)
"""

# Import from core module
from .core import (  # Models; Security classes; Manager functions; Token functions; Boundary functions; Context and policy functions; Policy creation functions
    AccessLevel,
)
from .core import (  # Models; Security classes; Manager functions; Token functions; Boundary functions; Context and policy functions; Policy creation functions
    BoundaryType,
)
from .core import (  # Models; Security classes; Manager functions; Token functions; Boundary functions; Context and policy functions; Policy creation functions
    SecurityBoundary,
)
from .core import (  # Models; Security classes; Manager functions; Token functions; Boundary functions; Context and policy functions; Policy creation functions
    SecurityConstraint,
)
from .core import (  # Models; Security classes; Manager functions; Token functions; Boundary functions; Context and policy functions; Policy creation functions
    SecurityContext,
)
from .core import (  # Models; Security classes; Manager functions; Token functions; Boundary functions; Context and policy functions; Policy creation functions
    SecurityLevel,
)
from .core import (  # Models; Security classes; Manager functions; Token functions; Boundary functions; Context and policy functions; Policy creation functions
    SecurityManager,
)
from .core import (  # Models; Security classes; Manager functions; Token functions; Boundary functions; Context and policy functions; Policy creation functions
    SecurityPolicy,
)
from .core import (  # Models; Security classes; Manager functions; Token functions; Boundary functions; Context and policy functions; Policy creation functions
    SecurityRole,
)
from .core import (  # Models; Security classes; Manager functions; Token functions; Boundary functions; Context and policy functions; Policy creation functions
    TokenCredential,
)
from .core import (  # Models; Security classes; Manager functions; Token functions; Boundary functions; Context and policy functions; Policy creation functions
    TokenType,
)
from .core import (  # Models; Security classes; Manager functions; Token functions; Boundary functions; Context and policy functions; Policy creation functions
    create_development_policy,
)
from .core import (  # Models; Security classes; Manager functions; Token functions; Boundary functions; Context and policy functions; Policy creation functions
    create_production_policy,
)
from .core import (  # Models; Security classes; Manager functions; Token functions; Boundary functions; Context and policy functions; Policy creation functions
    create_staging_policy,
)
from .core import (  # Models; Security classes; Manager functions; Token functions; Boundary functions; Context and policy functions; Policy creation functions
    create_testing_policy,
)
from .core import (  # Models; Security classes; Manager functions; Token functions; Boundary functions; Context and policy functions; Policy creation functions
    create_token,
)
from .core import (  # Models; Security classes; Manager functions; Token functions; Boundary functions; Context and policy functions; Policy creation functions
    extend_token,
)
from .core import (  # Models; Security classes; Manager functions; Token functions; Boundary functions; Context and policy functions; Policy creation functions
    get_active_policy,
)
from .core import (  # Models; Security classes; Manager functions; Token functions; Boundary functions; Context and policy functions; Policy creation functions
    get_boundary,
)
from .core import (  # Models; Security classes; Manager functions; Token functions; Boundary functions; Context and policy functions; Policy creation functions
    get_security_context,
)
from .core import (  # Models; Security classes; Manager functions; Token functions; Boundary functions; Context and policy functions; Policy creation functions
    get_security_manager,
)
from .core import (  # Models; Security classes; Manager functions; Token functions; Boundary functions; Context and policy functions; Policy creation functions
    revoke_token,
)
from .core import (  # Models; Security classes; Manager functions; Token functions; Boundary functions; Context and policy functions; Policy creation functions
    verify_token,
)

# All functionality is now imported from the core module

# Define exported symbols
__all__ = [
    # Models
    "BoundaryType",
    "SecurityLevel",
    "SecurityConstraint",
    "SecurityPolicy",
    # Security classes
    "SecurityBoundary",
    "SecurityManager",
    "SecurityContext",
    "TokenCredential",
    "TokenType",
    "AccessLevel",
    "SecurityRole",
    # Manager functions
    "get_security_manager",
    # Token functions
    "create_token",
    "verify_token",
    "revoke_token",
    "extend_token",
    # Boundary functions
    "get_boundary",
    # Context and policy functions
    "get_security_context",
    "get_active_policy",
    # Policy creation functions
    "create_development_policy",
    "create_testing_policy",
    "create_staging_policy",
    "create_production_policy",
]
