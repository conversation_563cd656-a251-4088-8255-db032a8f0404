"""
Person Suit - Platform Detection Module (DE-1)

This module implements platform detection for the Person Suit framework,
identifying operating system, version, architecture, and other platform characteristics.

Components:
- PlatformDetector: Detects and reports platform information

Related Files:
- models.py: Data models for detection results
- hardware_detector.py: Hardware detection implementation
- runtime_detector.py: Runtime environment detection
- __init__.py: Module exports and convenience functions

Dependencies:
- platform: Python standard library for platform information
"""

import logging
import os
import platform
import subprocess
from typing import Any
from typing import Dict

from .models import PlatformInfo

# Configure logger
logger = logging.getLogger("person_suit.deployment.detection.platform")


class PlatformDetector:
    """
    Platform detection for the Person Suit framework.

    Detects and provides information about the operating system platform,
    including OS type, version, architecture, and Python environment.
    """

    def __init__(self):
        """Initialize the platform detector."""
        self._platform_info = None

    def detect_platform(self) -> PlatformInfo:
        """
        Detect platform information.

        Returns:
            PlatformInfo: Platform information
        """
        if self._platform_info is not None:
            return self._platform_info

        try:
            # Get basic platform information
            system = platform.system()
            release = platform.release()
            version = platform.version()
            architecture = platform.architecture()[0]
            machine = platform.machine()
            node = platform.node()
            python_version = platform.python_version()
            python_implementation = platform.python_implementation()

            # Create platform info
            self._platform_info = PlatformInfo(
                system=system,
                release=release,
                version=version,
                architecture=architecture,
                machine=machine,
                node=node,
                python_version=python_version,
                python_implementation=python_implementation,
            )

            # Log platform detection
            logger.debug(f"Detected platform: {system} {release} ({architecture})")

            return self._platform_info
        except Exception as e:
            logger.error(f"Failed to detect platform: {e}")

            # Return default platform info on error
            return PlatformInfo(
                system=platform.system() or "Unknown",
                release="Unknown",
                version="Unknown",
                architecture="Unknown",
                machine="Unknown",
                node="Unknown",
                python_version=platform.python_version() or "Unknown",
                python_implementation="Unknown",
            )

    def get_os_details(self) -> Dict[str, Any]:
        """
        Get detailed operating system information.

        Returns:
            Dict[str, Any]: Operating system details
        """
        if self._platform_info is None:
            self.detect_platform()

        details = {}
        system = self._platform_info.system

        try:
            # Platform-specific OS details
            if system == "Linux":
                details = self._get_linux_details()
            elif system == "Darwin":  # macOS
                details = self._get_macos_details()
            elif system == "Windows":
                details = self._get_windows_details()
            else:
                details = {"name": system}
        except Exception as e:
            logger.warning(f"Failed to get detailed OS information: {e}")

        return details

    def _get_linux_details(self) -> Dict[str, Any]:
        """
        Get detailed Linux information.

        Returns:
            Dict[str, Any]: Linux details
        """
        details = {
            "name": "Linux",
            "distribution": "Unknown",
            "distribution_version": "Unknown",
        }

        # Try to get distribution information
        try:
            # First try /etc/os-release which is standard on newer systems
            if os.path.exists("/etc/os-release"):
                with open("/etc/os-release", "r") as f:
                    for line in f:
                        if line.startswith("NAME="):
                            details["distribution"] = (
                                line.split("=")[1].strip().strip("\"'")
                            )
                        elif line.startswith("VERSION_ID="):
                            details["distribution_version"] = (
                                line.split("=")[1].strip().strip("\"'")
                            )

            # Next, try LSB release command which works on many systems
            elif subprocess.call(["which", "lsb_release"], stdout=subprocess.PIPE) == 0:
                lsb_output = subprocess.check_output(
                    ["lsb_release", "-a"], stderr=subprocess.PIPE
                ).decode()
                for line in lsb_output.splitlines():
                    if line.startswith("Distributor ID:"):
                        details["distribution"] = line.split(":")[1].strip()
                    elif line.startswith("Release:"):
                        details["distribution_version"] = line.split(":")[1].strip()

            # Finally, check specific distribution files
            elif os.path.exists("/etc/redhat-release"):
                with open("/etc/redhat-release", "r") as f:
                    release_info = f.read().strip()
                    details["distribution"] = "Red Hat"
                    details["distribution_version"] = release_info
            elif os.path.exists("/etc/debian_version"):
                with open("/etc/debian_version", "r") as f:
                    details["distribution"] = "Debian"
                    details["distribution_version"] = f.read().strip()
        except Exception as e:
            logger.debug(f"Could not determine Linux distribution details: {e}")

        # Get kernel version
        try:
            kernel_version = subprocess.check_output(["uname", "-r"]).decode().strip()
            details["kernel_version"] = kernel_version
        except Exception:
            pass

        return details

    def _get_macos_details(self) -> Dict[str, Any]:
        """
        Get detailed macOS information.

        Returns:
            Dict[str, Any]: macOS details
        """
        details = {
            "name": "macOS",
            "version": self._platform_info.release,
            "build": "Unknown",
        }

        try:
            # Get macOS version information using system_profiler
            output = subprocess.check_output(["sw_vers"]).decode()
            for line in output.splitlines():
                if line.startswith("ProductVersion:"):
                    details["version"] = line.split(":")[1].strip()
                elif line.startswith("BuildVersion:"):
                    details["build"] = line.split(":")[1].strip()

            # Get more detailed OS information
            system_profiler_output = subprocess.check_output(
                ["system_profiler", "SPSoftwareDataType"], stderr=subprocess.PIPE
            ).decode()

            for line in system_profiler_output.splitlines():
                line = line.strip()
                if line.startswith("System Version:"):
                    details["full_version"] = line.split("System Version:")[1].strip()
                elif line.startswith("Kernel Version:"):
                    details["kernel_version"] = line.split("Kernel Version:")[1].strip()
        except Exception as e:
            logger.debug(f"Could not determine detailed macOS information: {e}")

        return details

    def _get_windows_details(self) -> Dict[str, Any]:
        """
        Get detailed Windows information.

        Returns:
            Dict[str, Any]: Windows details
        """
        details = {
            "name": "Windows",
            "version": self._platform_info.release,
            "edition": "Unknown",
        }

        try:
            # Python provides good Windows version information through platform
            windows_version = platform.version()
            windows_release = platform.release()

            details["version"] = windows_version
            details["release"] = windows_release

            # Try to get more detailed information from registry
            try:
                import winreg

                key = winreg.OpenKey(
                    winreg.HKEY_LOCAL_MACHINE,
                    r"SOFTWARE\Microsoft\Windows NT\CurrentVersion",
                )

                # Get product name
                try:
                    product_name = winreg.QueryValueEx(key, "ProductName")[0]
                    details["edition"] = product_name
                except Exception:
                    pass

                # Get build number
                try:
                    build_number = winreg.QueryValueEx(key, "CurrentBuildNumber")[0]
                    details["build"] = build_number
                except Exception:
                    pass

                # Get update build revision
                try:
                    ubr = winreg.QueryValueEx(key, "UBR")[0]
                    details["update_build_revision"] = str(ubr)
                except Exception:
                    pass

                winreg.CloseKey(key)
            except ImportError:
                logger.debug("winreg module not available for Windows registry access")
        except Exception as e:
            logger.debug(f"Could not determine detailed Windows information: {e}")

        return details


# Singleton instance
_platform_detector_instance = None


def get_platform_detector() -> PlatformDetector:
    """Get the singleton platform detector instance."""
    global _platform_detector_instance

    if _platform_detector_instance is None:
        _platform_detector_instance = PlatformDetector()

    return _platform_detector_instance


# Convenience functions
def detect_platform() -> PlatformInfo:
    """Detect platform information."""
    return get_platform_detector().detect_platform()


def get_os_details() -> Dict[str, Any]:
    """Get detailed operating system information."""
    return get_platform_detector().get_os_details()
