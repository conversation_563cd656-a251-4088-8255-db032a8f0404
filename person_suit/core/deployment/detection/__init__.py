"""
Person Suit - Deployment Environment Detection Module (DE-1)

This module provides environment detection capabilities for the Person Suit framework,
enabling automatic identification of hardware, platform, and runtime environment.

Components:
- PlatformDetector: Detects platform characteristics
- HardwareDetector: Detects hardware capabilities
- RuntimeDetector: Detects runtime environment
- EnvironmentData: Comprehensive environment data

The Environment Detection module enables Person Suit to adapt to different
hardware and platform environments, with special focus on optimizing for
Apple M3 Max hardware.
"""

# Import from core module
from .core import EnvironmentDetector  # Classes; Functions
from .core import detect_environment  # Classes; Functions
from .core import get_environment_detector  # Classes; Functions
from .core import is_container  # Classes; Functions
from .core import is_development  # Classes; Functions
from .core import is_m3_max  # Classes; Functions
from .core import is_production  # Classes; Functions

# Import hardware detection
from .hardware_detector import HardwareDetector
from .hardware_detector import detect_hardware
from .hardware_detector import get_hardware_capabilities
from .hardware_detector import get_hardware_detector
from .hardware_detector import get_optimal_thread_count

# Import models directly
from .models import Environment  # Re-export Environment from models
from .models import EnvironmentData
from .models import HardwareInfo
from .models import PlatformInfo
from .models import RuntimeInfo

# Import platform detection
from .platform_detector import PlatformDetector
from .platform_detector import detect_platform
from .platform_detector import get_os_details
from .platform_detector import get_platform_detector

# Import runtime detection
from .runtime_detector import RuntimeDetector
from .runtime_detector import detect_runtime
from .runtime_detector import get_environment_variables
from .runtime_detector import get_network_info
from .runtime_detector import get_runtime_detector
from .runtime_detector import is_ci

# Version info
__version__ = "0.1.0"
