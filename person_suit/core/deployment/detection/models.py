"""
Person Suit - Deployment Environment Detection Models (DE-1)

This module provides data models for the environment detection system,
defining structures for storing platform, hardware, and runtime information.

Models:
- PlatformInfo: Information about the operating system platform
- HardwareInfo: Information about the hardware capabilities
- RuntimeInfo: Information about the runtime environment
- EnvironmentData: Comprehensive environment data

Related Files:
- platform_detector.py: Platform detection implementation
- hardware_detector.py: Hardware detection implementation
- runtime_detector.py: Runtime environment detection
- __init__.py: Module exports and convenience functions

Dependencies:
- person_suit.core.infrastructure.configuration.environment: For environment types
"""

import json
from dataclasses import dataclass
from dataclasses import field
from typing import Any
from typing import Dict
from typing import Optional

# Import from infrastructure for environment types
from ...infrastructure.configuration.environment.handler import Environment


@dataclass
class PlatformInfo:
    """Information about the platform."""

    system: str
    release: str
    version: str
    architecture: str
    machine: str
    node: str
    python_version: str
    python_implementation: str

    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary representation."""
        return {
            "system": self.system,
            "release": self.release,
            "version": self.version,
            "architecture": self.architecture,
            "machine": self.machine,
            "node": self.node,
            "python_version": self.python_version,
            "python_implementation": self.python_implementation,
        }


@dataclass
class HardwareInfo:
    """Information about the hardware."""

    cpu_count: int
    total_memory: int
    available_memory: Optional[int] = None
    cpu_model: Optional[str] = None
    cpu_brand: Optional[str] = None
    performance_core_count: Optional[int] = None
    efficiency_core_count: Optional[int] = None
    cache_sizes: Dict[str, int] = field(default_factory=dict)

    # Hardware capabilities
    is_m3_max: bool = False
    supports_metal: bool = False
    has_neural_engine: bool = False
    has_gpu: bool = False
    gpu_info: Optional[Dict[str, Any]] = None

    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary representation."""
        return {
            "cpu_count": self.cpu_count,
            "total_memory": self.total_memory,
            "available_memory": self.available_memory,
            "cpu_model": self.cpu_model,
            "cpu_brand": self.cpu_brand,
            "performance_core_count": self.performance_core_count,
            "efficiency_core_count": self.efficiency_core_count,
            "cache_sizes": self.cache_sizes,
            "is_m3_max": self.is_m3_max,
            "supports_metal": self.supports_metal,
            "has_neural_engine": self.has_neural_engine,
            "has_gpu": self.has_gpu,
            "gpu_info": self.gpu_info,
        }


@dataclass
class RuntimeInfo:
    """Information about the runtime environment."""

    environment: Environment
    is_container: bool
    is_ci: bool
    container_id: Optional[str] = None
    container_type: Optional[str] = None
    ci_system: Optional[str] = None
    custom_attributes: Dict[str, Any] = field(default_factory=dict)

    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary representation."""
        return {
            "environment": self.environment.name,
            "is_container": self.is_container,
            "is_ci": self.is_ci,
            "container_id": self.container_id,
            "container_type": self.container_type,
            "ci_system": self.ci_system,
            "custom_attributes": self.custom_attributes,
        }


@dataclass
class EnvironmentData:
    """Comprehensive environment data."""

    platform: PlatformInfo
    hardware: HardwareInfo
    runtime: RuntimeInfo
    detection_time: float

    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary representation."""
        return {
            "platform": self.platform.to_dict(),
            "hardware": self.hardware.to_dict(),
            "runtime": self.runtime.to_dict(),
            "detection_time": self.detection_time,
        }

    def to_json(self) -> str:
        """Convert to JSON representation."""
        return json.dumps(self.to_dict(), indent=2)
