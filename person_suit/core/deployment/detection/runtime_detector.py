"""
Person Suit - Runtime Environment Detection Module (DE-1)

This module implements runtime environment detection for the Person Suit framework,
identifying the execution environment, container status, CI/CD context, and other
runtime characteristics.

Components:
- RuntimeDetector: Detects and reports runtime environment information

Related Files:
- models.py: Data models for detection results
- platform_detector.py: Platform detection implementation
- hardware_detector.py: Hardware detection implementation
- __init__.py: Module exports and convenience functions

Dependencies:
- person_suit.core.infrastructure.configuration.environment.handler: For environment types
"""

import logging
import os
import socket
import subprocess
import sys
from typing import Any
from typing import Dict
from typing import Optional
from typing import Tuple

from ...infrastructure.configuration.environment.handler import Environment
from .models import RuntimeInfo

# Configure logger
logger = logging.getLogger("person_suit.deployment.detection.runtime")


class RuntimeDetector:
    """
    Runtime environment detection for the Person Suit framework.

    Detects and provides information about the runtime environment,
    including execution environment, container status, and CI/CD context.
    """

    def __init__(self, env_prefix: str = "PERSON_SUIT_"):
        """
        Initialize the runtime detector.

        Args:
            env_prefix: Environment variable prefix
        """
        self._runtime_info = None
        self._env_prefix = env_prefix

    def detect_runtime(self) -> RuntimeInfo:
        """
        Detect runtime environment information.

        Returns:
            RuntimeInfo: Runtime environment information
        """
        if self._runtime_info is not None:
            return self._runtime_info

        try:
            # Determine environment type
            environment = self._determine_environment_type()

            # Check for container
            is_container, container_id, container_type = self._detect_container()

            # Check for CI
            is_ci, ci_system = self._detect_ci()

            # Create runtime info
            self._runtime_info = RuntimeInfo(
                environment=environment,
                is_container=is_container,
                is_ci=is_ci,
                container_id=container_id,
                container_type=container_type,
                ci_system=ci_system,
                custom_attributes=self._get_custom_attributes(),
            )

            # Log runtime detection
            logger.debug(f"Detected runtime environment: {environment.name}")
            if is_container:
                logger.debug(f"Running in container: {container_type or 'Unknown'}")
            if is_ci:
                logger.debug(f"Running in CI: {ci_system or 'Unknown'}")

            return self._runtime_info
        except Exception as e:
            logger.error(f"Failed to detect runtime environment: {e}")

            # Return default runtime info on error
            return RuntimeInfo(
                environment=Environment.DEVELOPMENT, is_container=False, is_ci=False
            )

    def _determine_environment_type(self) -> Environment:
        """
        Determine the environment type.

        Returns:
            Environment: Environment type
        """
        # Check for explicit environment setting
        env_name = os.environ.get(f"{self._env_prefix}ENV", "").upper()
        if env_name:
            try:
                return Environment[env_name]
            except KeyError:
                # Custom environment name
                return Environment.CUSTOM

        # Check for common environment indicators
        if "TEST" in os.environ or "PYTEST_CURRENT_TEST" in os.environ:
            return Environment.TESTING

        if any(env in os.environ for env in ["PROD", "PRODUCTION"]):
            return Environment.PRODUCTION

        if any(env in os.environ for env in ["STAGE", "STAGING"]):
            return Environment.STAGING

        # Check for environment indicator files
        if os.path.exists(".development"):
            return Environment.DEVELOPMENT
        elif os.path.exists(".testing"):
            return Environment.TESTING
        elif os.path.exists(".staging"):
            return Environment.STAGING
        elif os.path.exists(".production"):
            return Environment.PRODUCTION

        # Default to development for safety
        return Environment.DEVELOPMENT

    def _detect_container(self) -> Tuple[bool, Optional[str], Optional[str]]:
        """
        Detect if running in a container.

        Returns:
            Tuple[bool, Optional[str], Optional[str]]: Is container, container ID, container type
        """
        is_container = False
        container_id = None
        container_type = None

        # Environment variable override
        if os.environ.get(f"{self._env_prefix}CONTAINER") == "1":
            is_container = True
            container_type = os.environ.get(f"{self._env_prefix}CONTAINER_TYPE")
            container_id = os.environ.get(f"{self._env_prefix}CONTAINER_ID")
            return is_container, container_id, container_type

        # Check for Docker
        if os.path.exists("/.dockerenv"):
            is_container = True
            container_type = "Docker"

            # Try to get container ID
            try:
                with open("/proc/self/cgroup", "r") as f:
                    for line in f:
                        if "/docker/" in line:
                            container_id = line.split("/docker/")[-1].strip()
                            break
            except (FileNotFoundError, OSError):
                pass

        # Check for Kubernetes
        elif "KUBERNETES_SERVICE_HOST" in os.environ:
            is_container = True
            container_type = "Kubernetes"
            container_id = os.environ.get("HOSTNAME")

        # Check for other container types
        elif os.path.exists("/run/.containerenv"):
            is_container = True
            container_type = "Unknown"

        # Check cgroups for containerization
        elif os.path.exists("/proc/self/cgroup"):
            try:
                with open("/proc/self/cgroup", "r") as f:
                    content = f.read()
                    if "/docker/" in content:
                        is_container = True
                        container_type = "Docker"
                    elif "/kubepods/" in content:
                        is_container = True
                        container_type = "Kubernetes"
                    elif any(c in content for c in ["/lxc/", "/garden/"]):
                        is_container = True
                        container_type = "LXC"
            except (FileNotFoundError, OSError):
                pass

        return is_container, container_id, container_type

    def _detect_ci(self) -> Tuple[bool, Optional[str]]:
        """
        Detect if running in a CI environment.

        Returns:
            Tuple[bool, Optional[str]]: Is CI, CI system name
        """
        # Environment variable override
        if os.environ.get(f"{self._env_prefix}CI") == "1":
            return True, os.environ.get(f"{self._env_prefix}CI_SYSTEM")

        ci_env_vars = {
            "GITHUB_ACTIONS": "GitHub Actions",
            "GITLAB_CI": "GitLab CI",
            "TRAVIS": "Travis CI",
            "CIRCLECI": "CircleCI",
            "JENKINS_URL": "Jenkins",
            "TEAMCITY_VERSION": "TeamCity",
            "BUDDY": "Buddy",
            "APPVEYOR": "AppVeyor",
            "TF_BUILD": "Azure DevOps",
        }

        for env_var, name in ci_env_vars.items():
            if env_var in os.environ:
                return True, name

        # Generic CI detection
        if "CI" in os.environ:
            return True, os.environ.get("CI_NAME", "Unknown CI")

        return False, None

    def _get_custom_attributes(self) -> Dict[str, Any]:
        """
        Get custom runtime attributes.

        Returns:
            Dict[str, Any]: Custom runtime attributes
        """
        attributes = {}

        # Hostname
        try:
            attributes["hostname"] = socket.gethostname()
        except:
            pass

        # Username
        try:
            attributes["username"] = os.getenv("USER") or os.getenv("USERNAME")
        except:
            pass

        # Process ID
        attributes["pid"] = os.getpid()

        # Parent Process ID
        attributes["ppid"] = os.getppid()

        # Working directory
        attributes["working_dir"] = os.getcwd()

        # Python executable
        attributes["python_executable"] = sys.executable

        # Python path
        attributes["python_path"] = sys.path

        # Environment variables with prefix
        env_vars = {}
        for key, value in os.environ.items():
            if key.startswith(self._env_prefix):
                env_vars[key] = value
        attributes["environment_variables"] = env_vars

        return attributes

    def get_network_info(self) -> Dict[str, Any]:
        """
        Get network information.

        Returns:
            Dict[str, Any]: Network information
        """
        network_info = {}

        # Get hostname
        try:
            network_info["hostname"] = socket.gethostname()
            network_info["fqdn"] = socket.getfqdn()
        except:
            pass

        # Get IP addresses
        try:
            # Get all addresses
            addresses = []

            # Prefer the socket library approach
            hostname = socket.gethostname()
            addresses.append(socket.gethostbyname(hostname))

            # Alternative approach
            if hasattr(socket, "getaddrinfo"):
                addrinfo = socket.getaddrinfo(hostname, None)
                addresses.extend(
                    [info[4][0] for info in addrinfo if info[4][0] not in addresses]
                )

            network_info["ip_addresses"] = addresses
        except:
            pass

        # Get network interface information on Linux
        if sys.platform.startswith("linux"):
            try:
                interfaces = {}

                # Use ip command if available
                try:
                    output = subprocess.check_output(["ip", "addr", "show"]).decode()

                    # Parse output
                    current_iface = None
                    for line in output.splitlines():
                        line = line.strip()

                        # Interface line
                        if line.startswith(tuple(map(str, range(10)))):
                            parts = line.split(":", 1)
                            if len(parts) >= 2:
                                current_iface = parts[1].strip()
                                interfaces[current_iface] = {"addresses": []}

                        # IP address line
                        elif current_iface and "inet " in line:
                            addr = line.split("inet ")[1].split("/")[0]
                            interfaces[current_iface]["addresses"].append(addr)
                except:
                    pass

                network_info["interfaces"] = interfaces
            except:
                pass

        return network_info

    def get_environment_variables(self) -> Dict[str, str]:
        """
        Get environment variables.

        Returns:
            Dict[str, str]: Environment variables
        """
        # Return filtered environment variables
        # Exclude sensitive information
        sensitive_patterns = ["PASSWORD", "SECRET", "KEY", "TOKEN", "CREDENTIAL"]

        filtered_env = {}
        for key, value in os.environ.items():
            # Skip sensitive variables
            if any(pattern in key.upper() for pattern in sensitive_patterns):
                filtered_env[key] = "[REDACTED]"
            else:
                filtered_env[key] = value

        return filtered_env


# Singleton instance
_runtime_detector_instance = None


def get_runtime_detector() -> RuntimeDetector:
    """Get the singleton runtime detector instance."""
    global _runtime_detector_instance

    if _runtime_detector_instance is None:
        _runtime_detector_instance = RuntimeDetector()

    return _runtime_detector_instance


# Convenience functions
def detect_runtime() -> RuntimeInfo:
    """Detect runtime environment information."""
    return get_runtime_detector().detect_runtime()


def get_network_info() -> Dict[str, Any]:
    """Get network information."""
    return get_runtime_detector().get_network_info()


def get_environment_variables() -> Dict[str, str]:
    """Get environment variables."""
    return get_runtime_detector().get_environment_variables()


def is_container() -> bool:
    """Check if running in a container."""
    runtime_info = detect_runtime()
    return runtime_info.is_container


def is_ci() -> bool:
    """Check if running in a CI environment."""
    runtime_info = detect_runtime()
    return runtime_info.is_ci


def is_development() -> bool:
    """Check if running in development environment."""
    runtime_info = detect_runtime()
    return runtime_info.environment == Environment.DEVELOPMENT


def is_production() -> bool:
    """Check if running in production environment."""
    runtime_info = detect_runtime()
    return runtime_info.environment == Environment.PRODUCTION
