# Health Monitor Migration Notice

## Thread-Based Components Deprecated

The thread-based health monitoring components in this directory have been deprecated and replaced with actor-based implementations that follow the Person Suit architectural principles.

### Deprecated Files:
- `monitor_deprecated.py` - Thread-based HealthMonitor (uses `threading.Thread` with `time.sleep()`)

### New Actor-Based Implementation:
- `person_suit/core/actors/foundation_actors.py`
  - `HealthMonitorActor` - Actor-based health monitoring
  - Uses declarative effects instead of direct I/O
  - No blocking operations (uses `asyncio` instead of `time.sleep`)
  - Follows CAW principles

### Migration Guide:

#### Old Thread-Based Usage:
```python
from person_suit.core.deployment.health.monitor import get_health_monitor

monitor = get_health_monitor()
monitor.start_background_monitoring(interval=60)
report = monitor.check_health()
```

#### New Actor-Based Usage:
```python
from person_suit.core.infrastructure.hybrid_message_bus import get_message_bus
from person_suit.core.infrastructure.hybrid_message import HybridMessage

# Send health check request via message bus
bus = await get_message_bus()
await bus.send(HybridMessage(
    message_type="COMMAND",
    channel="health.check.request",
    payload={"check_id": "system_health"}
))

# Health check results will be published as events
# Subscribe to receive health check results
await bus.subscribe(
    "event.health.check.completed",
    handle_health_check_result
)
```

### Key Differences:

1. **No Direct I/O**: Actors only return Effect descriptions
2. **Message-Based**: All communication via HybridMessageBus
3. **Non-Blocking**: Uses `asyncio` instead of threads
4. **Supervised**: Actors run under supervision with automatic restart
5. **Capability-Based**: All operations require proper capabilities

### Timeline:
- **Deprecated**: 2025-12-24
- **Removal Target**: Q1 2026
- **Migration Support**: Available until removal

For questions or migration assistance, see the [Thread-to-Actor Migration Guide](../../../migration/THREAD_TO_ACTOR_MIGRATION.md). 