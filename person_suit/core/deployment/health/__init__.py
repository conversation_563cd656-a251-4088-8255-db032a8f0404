"""
Person Suit - Health Monitoring Package (DE-1)

This package provides health monitoring capabilities for the Person Suit framework.
It includes components for tracking system health, performing health checks,
and generating health reports.

Components:
- HealthStatus: Status levels for components and the overall system
- HealthSeverity: Severity levels for health issues
- HealthMetric: Metric data point with value and metadata
- HealthIssue: Issue detected during health checks
- HealthReport: Complete health report for the system
- HealthCheck: Base class for health checks
- HealthMonitor: Central health monitoring system

Functions:
- get_health_monitor: Get the singleton health monitor instance
- check_health: Convenience function to check system health
- get_default_health_checks: Get default health checks
- create_health_check: Create a health check of a specific type

Example usage:
```python
from . import check_health, HealthStatus

# Check system health
report = check_health()

# Check if the system is healthy
if report.status in (HealthStatus.HEALTHY, HealthStatus.DEGRADED):
    print(f"System is operational: {report.summary}")
else:
    print(f"System has issues: {report.summary}")
    for issue in report.issues:
        print(f"- {issue.severity.name}: {issue.message}")
```

For background monitoring:
```python
from . import get_health_monitor

# Start background monitoring
monitor = get_health_monitor()
monitor.start_background_monitoring(interval=120)  # Check every 2 minutes

# Get the latest health report
latest_report = monitor.get_current_report()

# Stop monitoring when done
monitor.stop_background_monitoring()
```
"""

# Import from core module
from .core import HealthCheck  # Models; Health checks; Monitor; Convenience functions
from .core import HealthIssue  # Models; Health checks; Monitor; Convenience functions
from .core import HealthMonitor  # Models; Health checks; Monitor; Convenience functions
from .core import HealthReport  # Models; Health checks; Monitor; Convenience functions
from .core import HealthSeverity  # Models; Health checks; Monitor; Convenience functions
from .core import HealthStatus  # Models; Health checks; Monitor; Convenience functions
from .core import ResourceHealthCheck  # Models; Health checks; Monitor; Convenience functions
from .core import SecurityHealthCheck  # Models; Health checks; Monitor; Convenience functions
from .core import SystemHealthCheck  # Models; Health checks; Monitor; Convenience functions
from .core import check_health  # Models; Health checks; Monitor; Convenience functions
from .core import get_health_monitor  # Models; Health checks; Monitor; Convenience functions

# Import additional utilities from models
from .models import HealthMetric
from .models import create_summary_from_status
from .models import determine_status_from_issues

# Define public API
__all__ = [
    # Models
    "HealthStatus",
    "HealthSeverity",
    "HealthMetric",
    "HealthIssue",
    "HealthReport",
    # Health checks
    "HealthCheck",
    "ResourceHealthCheck",
    "SecurityHealthCheck",
    "SystemHealthCheck",
    # Health monitor
    "HealthMonitor",
    "get_health_monitor",
    "check_health",
    # Utility functions
    "determine_status_from_issues",
    "create_summary_from_status",
]
