"""
Person Suit - System Health Check (DE-1)

This module provides the system health check implementation for
monitoring core system components and environment.

Classes:
- SystemHealthCheck: Health check for system components

Related Files:
- base.py: Base health check abstract class
- person_suit.core.deployment.detection: Environment detection capabilities
"""

import logging
import os
import socket
import sys
from datetime import datetime
from typing import List

# Try to import optional dependencies, some of which might not be available
try:
    import psutil

    HAVE_PSUTIL = True
except ImportError:
    HAVE_PSUTIL = False

from ..models import HealthIssue
from ..models import HealthMetric
from ..models import HealthSeverity
from ..models import HealthThreshold
from .base import HealthCheck

# Configure logger
logger = logging.getLogger("person_suit.deployment.health.checks.system")


class SystemHealthCheck(HealthCheck):
    """
    Health check for system components.

    Monitors CPU, memory, disk, network connectivity, and other
    system resources required for Person Suit to operate optimally.
    """

    def __init__(
        self,
        cpu_warning_threshold: float = 80.0,
        cpu_error_threshold: float = 90.0,
        memory_warning_threshold: float = 80.0,
        memory_error_threshold: float = 90.0,
        disk_warning_threshold: float = 80.0,
        disk_error_threshold: float = 90.0,
        enabled: bool = True,
    ):
        """
        Initialize the system health check.

        Args:
            cpu_warning_threshold: CPU usage percentage that triggers a warning
            cpu_error_threshold: CPU usage percentage that triggers an error
            memory_warning_threshold: Memory usage percentage that triggers a warning
            memory_error_threshold: Memory usage percentage that triggers an error
            disk_warning_threshold: Disk usage percentage that triggers a warning
            disk_error_threshold: Disk usage percentage that triggers an error
            enabled: Whether the health check is enabled
        """
        super().__init__(name="system", enabled=enabled)

        # Set up thresholds
        self.register_threshold(
            HealthThreshold(
                metric_name="cpu_usage",
                warning_threshold=cpu_warning_threshold,
                error_threshold=cpu_error_threshold,
                comparison="greater",
            )
        )

        self.register_threshold(
            HealthThreshold(
                metric_name="memory_usage",
                warning_threshold=memory_warning_threshold,
                error_threshold=memory_error_threshold,
                comparison="greater",
            )
        )

        self.register_threshold(
            HealthThreshold(
                metric_name="disk_usage",
                warning_threshold=disk_warning_threshold,
                error_threshold=disk_error_threshold,
                comparison="greater",
            )
        )

        # Initialize health check history
        self.last_check_time = None
        self.consecutive_errors = 0
        self.consecutive_warnings = 0

        # Flag for slow checks
        self.full_check_interval = 120  # seconds
        self.last_full_check_time = None

    def check(self) -> List[HealthIssue]:
        """
        Perform the system health check.

        Returns:
            List[HealthIssue]: List of health issues found
        """
        issues = []
        now = datetime.now()

        # Track check time for frequency tracking
        self.last_check_time = now

        # Determine if we should do a full check
        do_full_check = True
        if self.last_full_check_time:
            time_since_full_check = (now - self.last_full_check_time).total_seconds()
            do_full_check = time_since_full_check >= self.full_check_interval

        if do_full_check:
            self.last_full_check_time = now

        # Basic system info check - always do this
        issues.extend(self._check_system_info())

        # Check CPU
        cpu_issues = self._check_cpu()
        issues.extend(cpu_issues)

        # Check memory
        memory_issues = self._check_memory()
        issues.extend(memory_issues)

        # Full checks - do less frequently
        if do_full_check:
            # Check disk
            disk_issues = self._check_disk()
            issues.extend(disk_issues)

            # Check network
            network_issues = self._check_network()
            issues.extend(network_issues)

        # Update consecutive error/warning counts
        if any(
            issue.severity in (HealthSeverity.ERROR, HealthSeverity.CRITICAL)
            for issue in issues
        ):
            self.consecutive_errors += 1
        else:
            self.consecutive_errors = 0

        if any(issue.severity == HealthSeverity.WARNING for issue in issues):
            self.consecutive_warnings += 1
        else:
            self.consecutive_warnings = 0

        # Add persistent issue if we've had consecutive errors/warnings
        if self.consecutive_errors >= 5:
            issues.append(
                HealthIssue(
                    component="system",
                    message=f"System has had {self.consecutive_errors} consecutive error-level issues",
                    severity=HealthSeverity.CRITICAL,
                    details={"consecutive_errors": self.consecutive_errors},
                    remediation="Review system logs and resolve persistent system issues.",
                )
            )
        elif self.consecutive_warnings >= 10:
            issues.append(
                HealthIssue(
                    component="system",
                    message=f"System has had {self.consecutive_warnings} consecutive warning-level issues",
                    severity=HealthSeverity.ERROR,
                    details={"consecutive_warnings": self.consecutive_warnings},
                    remediation="Review system logs and resolve persistent warning conditions.",
                )
            )

        return issues

    def _check_system_info(self) -> List[HealthIssue]:
        """
        Check basic system information.

        Returns:
            List[HealthIssue]: List of health issues found
        """
        issues = []

        try:
            # Get Python version and check if it's supported
            python_version = sys.version_info
            if python_version.major < 3 or (
                python_version.major == 3 and python_version.minor < 8
            ):
                issues.append(
                    HealthIssue(
                        component="system.python",
                        message=f"Python version {sys.version.split()[0]} is below required version 3.8",
                        severity=HealthSeverity.WARNING,
                        details={
                            "current_version": sys.version.split()[0],
                            "required_version": "3.8",
                        },
                        remediation="Upgrade to Python 3.8 or later for optimal performance and security.",
                    )
                )

            # Check if running in a development environment
            if "PYTEST_CURRENT_TEST" in os.environ or "DEVELOPMENT" in os.environ:
                issues.append(
                    HealthIssue(
                        component="system.environment",
                        message="Running in development/test environment",
                        severity=HealthSeverity.INFO,
                        details={"environment": "development/test"},
                    )
                )

            # Check if psutil is available (recommended but not required)
            if not HAVE_PSUTIL:
                issues.append(
                    HealthIssue(
                        component="system.dependencies",
                        message="psutil package is not available, limiting system health monitoring capabilities",
                        severity=HealthSeverity.INFO,
                        details={"missing_dependency": "psutil"},
                        remediation="Install psutil package for enhanced system monitoring.",
                    )
                )

        except Exception as e:
            logger.error(f"Error checking system info: {e}", exc_info=True)
            issues.append(
                HealthIssue(
                    component="system",
                    message=f"Error checking system info: {str(e)}",
                    severity=HealthSeverity.ERROR,
                    details={"exception": str(e), "exception_type": type(e).__name__},
                )
            )

        return issues

    def _check_cpu(self) -> List[HealthIssue]:
        """
        Check CPU usage and health.

        Returns:
            List[HealthIssue]: List of health issues found
        """
        issues = []

        if not HAVE_PSUTIL:
            return issues

        try:
            # Get CPU usage
            cpu_percent = psutil.cpu_percent(interval=0.5)

            # Create a metric
            cpu_metric = HealthMetric(
                name="cpu_usage", value=cpu_percent, unit="%", component="system.cpu"
            )

            # Evaluate against thresholds
            cpu_issue = self.evaluate_metric(cpu_metric)
            if cpu_issue:
                issues.append(cpu_issue)

            # Check for high load
            try:
                load1, load5, load15 = psutil.getloadavg()
                cpu_count = psutil.cpu_count()

                if cpu_count and load5 > cpu_count * 1.5:
                    issues.append(
                        HealthIssue(
                            component="system.cpu",
                            message=f"High CPU load average: {load5:.2f} (5 min) with {cpu_count} CPUs",
                            severity=HealthSeverity.WARNING,
                            details={
                                "load_avg_1m": load1,
                                "load_avg_5m": load5,
                                "load_avg_15m": load15,
                                "cpu_count": cpu_count,
                            },
                            remediation="Check for CPU-intensive processes and consider scaling resources.",
                        )
                    )
            except (AttributeError, NotImplementedError):
                # Load average not available on all platforms
                pass

        except Exception as e:
            logger.error(f"Error checking CPU: {e}", exc_info=True)
            issues.append(
                HealthIssue(
                    component="system.cpu",
                    message=f"Error checking CPU health: {str(e)}",
                    severity=HealthSeverity.ERROR,
                    details={"exception": str(e), "exception_type": type(e).__name__},
                )
            )

        return issues

    def _check_memory(self) -> List[HealthIssue]:
        """
        Check memory usage and health.

        Returns:
            List[HealthIssue]: List of health issues found
        """
        issues = []

        if not HAVE_PSUTIL:
            return issues

        try:
            # Get memory usage
            memory = psutil.virtual_memory()
            memory_percent = memory.percent

            # Create a metric
            memory_metric = HealthMetric(
                name="memory_usage",
                value=memory_percent,
                unit="%",
                component="system.memory",
            )

            # Evaluate against thresholds
            memory_issue = self.evaluate_metric(memory_metric)
            if memory_issue:
                issues.append(memory_issue)

            # Check for low available memory
            if memory.available < (512 * 1024 * 1024):  # < 512 MB available
                issues.append(
                    HealthIssue(
                        component="system.memory",
                        message=f"Low available memory: {memory.available / (1024 * 1024):.1f} MB",
                        severity=HealthSeverity.WARNING,
                        details={
                            "available_memory_mb": memory.available / (1024 * 1024),
                            "total_memory_mb": memory.total / (1024 * 1024),
                        },
                        remediation="Close unused applications or increase system memory.",
                    )
                )

        except Exception as e:
            logger.error(f"Error checking memory: {e}", exc_info=True)
            issues.append(
                HealthIssue(
                    component="system.memory",
                    message=f"Error checking memory health: {str(e)}",
                    severity=HealthSeverity.ERROR,
                    details={"exception": str(e), "exception_type": type(e).__name__},
                )
            )

        return issues

    def _check_disk(self) -> List[HealthIssue]:
        """
        Check disk usage and health.

        Returns:
            List[HealthIssue]: List of health issues found
        """
        issues = []

        if not HAVE_PSUTIL:
            return issues

        try:
            # Path to check
            paths_to_check = [
                os.path.dirname(os.path.abspath(__file__)),  # Current directory
                os.path.expanduser("~/.person_suit"),  # Home directory for person_suit
            ]

            for path in paths_to_check:
                if os.path.exists(path):
                    # Get disk usage
                    disk_usage = psutil.disk_usage(path)
                    disk_percent = disk_usage.percent

                    # Create a metric
                    disk_metric = HealthMetric(
                        name="disk_usage",
                        value=disk_percent,
                        unit="%",
                        component=f"system.disk.{os.path.basename(path)}",
                    )

                    # Evaluate against thresholds
                    disk_issue = self.evaluate_metric(disk_metric)
                    if disk_issue:
                        issues.append(disk_issue)

                    # Check for low disk space
                    if disk_usage.free < (1 * 1024 * 1024 * 1024):  # < 1 GB free
                        issues.append(
                            HealthIssue(
                                component=f"system.disk.{os.path.basename(path)}",
                                message=f"Low disk space on {path}: {disk_usage.free / (1024 * 1024 * 1024):.1f} GB free",
                                severity=HealthSeverity.WARNING,
                                details={
                                    "free_space_gb": disk_usage.free
                                    / (1024 * 1024 * 1024),
                                    "total_space_gb": disk_usage.total
                                    / (1024 * 1024 * 1024),
                                    "path": path,
                                },
                                remediation="Free up disk space or increase storage capacity.",
                            )
                        )

        except Exception as e:
            logger.error(f"Error checking disk: {e}", exc_info=True)
            issues.append(
                HealthIssue(
                    component="system.disk",
                    message=f"Error checking disk health: {str(e)}",
                    severity=HealthSeverity.ERROR,
                    details={"exception": str(e), "exception_type": type(e).__name__},
                )
            )

        return issues

    def _check_network(self) -> List[HealthIssue]:
        """
        Check network connectivity and health.

        Returns:
            List[HealthIssue]: List of health issues found
        """
        issues = []

        try:
            # Check internet connectivity
            hosts_to_check = [
                ("*******", 53),  # Google DNS
                ("*******", 53),  # Cloudflare DNS
            ]

            connected = False
            connection_errors = []

            for host, port in hosts_to_check:
                try:
                    # Try to create a socket connection
                    socket.setdefaulttimeout(3)
                    socket.socket(socket.AF_INET, socket.SOCK_STREAM).connect(
                        (host, port)
                    )
                    connected = True
                    break
                except Exception as e:
                    connection_errors.append(
                        f"Failed to connect to {host}:{port} - {str(e)}"
                    )

            if not connected:
                issues.append(
                    HealthIssue(
                        component="system.network",
                        message="Network connectivity issues detected",
                        severity=HealthSeverity.WARNING,
                        details={"connection_errors": connection_errors},
                        remediation="Check network connection and DNS settings.",
                    )
                )

        except Exception as e:
            logger.error(f"Error checking network: {e}", exc_info=True)
            issues.append(
                HealthIssue(
                    component="system.network",
                    message=f"Error checking network health: {str(e)}",
                    severity=HealthSeverity.ERROR,
                    details={"exception": str(e), "exception_type": type(e).__name__},
                )
            )

        return issues
