"""
Person Suit - Security Health Check (DE-1)

This module provides security health check implementation for monitoring
security aspects of the Person Suit deployment.

Classes:
- SecurityHealthCheck: Health check for security components

Related Files:
- base.py: Base health check abstract class
- person_suit.core.deployment.security: Security components
"""

import logging
import os
import platform
import ssl
from datetime import datetime
from typing import List

from ..models import HealthIssue
from ..models import HealthSeverity
from .base import HealthCheck

# Configure logger
logger = logging.getLogger("person_suit.deployment.health.checks.security")

# Try to import security components, handling potential circular import
try:
    from person_suit.core.deployment.security import BoundaryType
    from person_suit.core.deployment.security import SecurityLevel
    from person_suit.core.deployment.security import get_active_policy
    from person_suit.core.deployment.security import get_boundary

    HAVE_SECURITY = True
except ImportError:
    logger.warning(
        "Could not import security components, limited health checks will be available"
    )
    HAVE_SECURITY = False


class SecurityHealthCheck(HealthCheck):
    """
    Health check for security components.

    Monitors security configurations, boundaries, and policies to ensure
    that the Person Suit framework is operating within secure parameters.
    """

    def __init__(self, enabled: bool = True):
        """
        Initialize the security health check.

        Args:
            enabled: Whether the health check is enabled
        """
        super().__init__(name="security", enabled=enabled)

        # Initialize check history
        self.last_check_time = None

        # Security-critical paths to check
        self.security_paths = [
            os.path.expanduser("~/.person_suit/security"),
        ]

        # Flag for expensive checks
        self.full_check_interval = 300  # seconds (5 minutes)
        self.last_full_check_time = None

    def check(self) -> List[HealthIssue]:
        """
        Perform the security health check.

        Returns:
            List[HealthIssue]: List of health issues found
        """
        issues = []
        now = datetime.now()

        # Track check time for frequency tracking
        self.last_check_time = now

        # Determine if we should do a full check
        do_full_check = True
        if self.last_full_check_time:
            time_since_full_check = (now - self.last_full_check_time).total_seconds()
            do_full_check = time_since_full_check >= self.full_check_interval

        if do_full_check:
            self.last_full_check_time = now

        # Basic security checks - always do these
        issues.extend(self._check_security_policy())
        issues.extend(self._check_security_boundaries())

        # Full checks - do less frequently
        if do_full_check:
            issues.extend(self._check_security_permissions())
            issues.extend(self._check_tls_security())

        return issues

    def _check_security_policy(self) -> List[HealthIssue]:
        """
        Check that a security policy is active and appropriate.

        Returns:
            List[HealthIssue]: List of health issues found
        """
        issues = []

        if not HAVE_SECURITY:
            issues.append(
                HealthIssue(
                    component="security.policy",
                    message="Security components not available, cannot check security policy",
                    severity=HealthSeverity.WARNING,
                    details={"reason": "import_error"},
                    remediation="Ensure security components are properly installed and configured.",
                )
            )
            return issues

        try:
            # Check if a security policy is active
            active_policy = get_active_policy()

            if active_policy is None:
                issues.append(
                    HealthIssue(
                        component="security.policy",
                        message="No active security policy found",
                        severity=HealthSeverity.ERROR,
                        details={},
                        remediation="Initialize the security manager and activate a security policy.",
                    )
                )
                return issues

            # Check if the security level is appropriate
            # This is a simplified check - in a real implementation,
            # we would get the environment (e.g. PRODUCTION, DEVELOPMENT)
            # and check if the security level is appropriate for that environment

            if active_policy.default_security_level == SecurityLevel.DISABLED:
                issues.append(
                    HealthIssue(
                        component="security.policy",
                        message="Security is disabled in the active security policy",
                        severity=HealthSeverity.ERROR,
                        details={
                            "active_policy": active_policy.name,
                            "security_level": "DISABLED",
                        },
                        remediation="Activate a security policy with security enabled.",
                    )
                )
            elif active_policy.default_security_level == SecurityLevel.RELAXED:
                # This might be appropriate for development but not production
                issues.append(
                    HealthIssue(
                        component="security.policy",
                        message="Security level is set to RELAXED, which may not be appropriate for production",
                        severity=HealthSeverity.WARNING,
                        details={
                            "active_policy": active_policy.name,
                            "security_level": "RELAXED",
                        },
                        remediation="Consider using a higher security level for production environments.",
                    )
                )

        except Exception as e:
            logger.error(f"Error checking security policy: {e}", exc_info=True)
            issues.append(
                HealthIssue(
                    component="security.policy",
                    message=f"Error checking security policy: {str(e)}",
                    severity=HealthSeverity.ERROR,
                    details={"exception": str(e), "exception_type": type(e).__name__},
                )
            )

        return issues

    def _check_security_boundaries(self) -> List[HealthIssue]:
        """
        Check that security boundaries are established.

        Returns:
            List[HealthIssue]: List of health issues found
        """
        issues = []

        if not HAVE_SECURITY:
            return issues  # Already reported in _check_security_policy

        try:
            # Check process boundary
            process_boundary = get_boundary("person_suit", BoundaryType.PROCESS)
            if process_boundary is None:
                issues.append(
                    HealthIssue(
                        component="security.boundary",
                        message="No process isolation boundary found",
                        severity=HealthSeverity.ERROR,
                        details={},
                        remediation="Initialize the security manager and ensure process boundaries are created.",
                    )
                )

            # Check filesystem boundary
            filesystem_boundary = get_boundary("person_suit", BoundaryType.FILESYSTEM)
            if filesystem_boundary is None:
                issues.append(
                    HealthIssue(
                        component="security.boundary",
                        message="No filesystem security boundary found",
                        severity=HealthSeverity.WARNING,
                        details={},
                        remediation="Initialize the security manager and ensure filesystem boundaries are created.",
                    )
                )

            # Check network boundary
            network_boundary = get_boundary("person_suit", BoundaryType.NETWORK)
            if network_boundary is None:
                issues.append(
                    HealthIssue(
                        component="security.boundary",
                        message="No network security boundary found",
                        severity=HealthSeverity.WARNING,
                        details={},
                        remediation="Initialize the security manager and ensure network boundaries are created.",
                    )
                )

        except Exception as e:
            logger.error(f"Error checking security boundaries: {e}", exc_info=True)
            issues.append(
                HealthIssue(
                    component="security.boundary",
                    message=f"Error checking security boundaries: {str(e)}",
                    severity=HealthSeverity.ERROR,
                    details={"exception": str(e), "exception_type": type(e).__name__},
                )
            )

        return issues

    def _check_security_permissions(self) -> List[HealthIssue]:
        """
        Check file permissions for security-critical files.

        Returns:
            List[HealthIssue]: List of health issues found
        """
        issues = []

        # This is most relevant on Unix-like systems
        if platform.system() == "Windows":
            return issues

        try:
            for path in self.security_paths:
                if os.path.exists(path):
                    # Check permissions
                    try:
                        permissions = oct(os.stat(path).st_mode & 0o777)
                        # Check if permissions are too permissive (readable/writable by others)
                        if permissions[-1] in ("6", "7"):  # Others can write
                            issues.append(
                                HealthIssue(
                                    component="security.filesystem",
                                    message=f"Security-critical path {path} has insecure permissions {permissions} (writable by others)",
                                    severity=HealthSeverity.ERROR,
                                    details={"path": path, "permissions": permissions},
                                    remediation=f"Change permissions for {path} to be more restrictive (e.g., using chmod).",
                                )
                            )
                        elif permissions[-1] in ("4", "5"):  # Others can read
                            issues.append(
                                HealthIssue(
                                    component="security.filesystem",
                                    message=f"Security-critical path {path} has permissive permissions {permissions} (readable by others)",
                                    severity=HealthSeverity.WARNING,
                                    details={"path": path, "permissions": permissions},
                                    remediation=f"Consider changing permissions for {path} to be more restrictive.",
                                )
                            )
                    except Exception as e:
                        issues.append(
                            HealthIssue(
                                component="security.filesystem",
                                message=f"Error checking permissions for {path}: {str(e)}",
                                severity=HealthSeverity.ERROR,
                                details={"path": path, "exception": str(e)},
                            )
                        )

        except Exception as e:
            logger.error(f"Error checking security permissions: {e}", exc_info=True)
            issues.append(
                HealthIssue(
                    component="security.filesystem",
                    message=f"Error checking security permissions: {str(e)}",
                    severity=HealthSeverity.ERROR,
                    details={"exception": str(e), "exception_type": type(e).__name__},
                )
            )

        return issues

    def _check_tls_security(self) -> List[HealthIssue]:
        """
        Check TLS security configuration.

        Returns:
            List[HealthIssue]: List of health issues found
        """
        issues = []

        try:
            # Check SSL/TLS version
            context = ssl.create_default_context()

            # Get protocol name
            try:
                protocol_name = ssl.get_protocol_name(context.minimum_version)
            except (AttributeError, ValueError):
                protocol_name = "Unknown"

            # Check if the TLS version is secure
            if protocol_name in ("SSLv2", "SSLv3", "TLSv1", "TLSv1.1"):
                issues.append(
                    HealthIssue(
                        component="security.communication",
                        message=f"Insecure TLS protocol version in use: {protocol_name}",
                        severity=HealthSeverity.ERROR,
                        details={"tls_version": protocol_name},
                        remediation="Update Python or SSL/TLS libraries to use TLSv1.2 or later.",
                    )
                )
            elif protocol_name == "Unknown":
                issues.append(
                    HealthIssue(
                        component="security.communication",
                        message="Could not determine TLS protocol version",
                        severity=HealthSeverity.WARNING,
                        details={},
                        remediation="Verify SSL/TLS configuration and libraries.",
                    )
                )

        except Exception as e:
            logger.error(f"Error checking TLS security: {e}", exc_info=True)
            issues.append(
                HealthIssue(
                    component="security.communication",
                    message=f"Error checking TLS security: {str(e)}",
                    severity=HealthSeverity.ERROR,
                    details={"exception": str(e), "exception_type": type(e).__name__},
                )
            )

        return issues
