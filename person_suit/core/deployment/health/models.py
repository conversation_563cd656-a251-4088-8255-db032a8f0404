"""
Person Suit - Health Models (DE-1)

This module provides data models for health monitoring in the Person Suit framework.

Classes:
- HealthStatus: Enumeration of possible health statuses
- HealthSeverity: Severity levels for health issues
- HealthMetric: Individual measured health metric
- HealthThreshold: Threshold definition for health metrics
- HealthIssue: Representation of a health problem
- HealthReport: Complete health check report

Related Files:
- __init__.py: Package exports
- checks/*.py: Health check implementations
- monitor.py: Health monitoring orchestration

Dependencies:
- datetime: For timestamp handling
- enum: For enumeration types
- typing: For type annotations
- dataclasses: For data model definitions
"""

from dataclasses import dataclass
from dataclasses import field
from datetime import datetime
from enum import Enum
from typing import Any
from typing import Dict
from typing import List
from typing import Optional
from typing import Tuple
from typing import Union

# Forward declarations or constants if needed


def determine_status_from_issues(
    issues: List["HealthIssue"],
) -> "HealthStatus":  # Use forward reference for HealthIssue/HealthStatus
    """
    Determine the overall health status based on a list of issues.

    The status is determined by the most severe issue present.

    Args:
        issues: A list of HealthIssue objects.

    Returns:
        The calculated HealthStatus.
    """
    # Need HealthStatus/HealthSeverity defined *before* this function is called
    # If they are defined later, we might need to import them locally or restructure

    # Temporary assumption: Enums are defined above or imported
    # This might need adjustment if Enums are defined later in this file

    if not issues:
        return HealthStatus.HEALTHY

    issue_statuses = [issue.status for issue in issues if issue.severity.affects_status]

    if not issue_statuses:
        return HealthStatus.HEALTHY

    return max(issue_statuses, key=lambda s: s.value)


def create_summary_from_status(
    status: "HealthStatus", issues: List["HealthIssue"] = None
) -> str:
    """
    Create a human-readable summary string for a given health status and optional issues.

    Args:
        status: The overall HealthStatus.
        issues: Optional list of HealthIssue objects to include details.

    Returns:
        A summary string.
    """
    if status == HealthStatus.HEALTHY:
        return "All systems operational"

    if not issues:
        return f"System status: {status.name.lower()}"

    # Count issues by severity
    severity_counts = {}
    for issue in issues:
        severity_counts[issue.severity] = severity_counts.get(issue.severity, 0) + 1

    # Create summary message
    summary_parts = []
    for severity, count in severity_counts.items():
        if count > 0:
            summary_parts.append(
                f"{count} {severity.name.lower()} issue{'s' if count > 1 else ''}"
            )

    return f"System status: {status.name.lower()}. Issues: " + ", ".join(summary_parts)


class HealthStatus(Enum):
    """
    Health status of a component or the entire system.
    """

    HEALTHY = "healthy"
    DEGRADED = "degraded"
    UNHEALTHY = "unhealthy"
    CRITICAL = "critical"
    UNKNOWN = "unknown"

    @property
    def is_operational(self) -> bool:
        """
        Returns whether the system can continue operating in this status.

        HEALTHY and DEGRADED are considered operational, while
        UNHEALTHY, CRITICAL and UNKNOWN are not.
        """
        return self in (HealthStatus.HEALTHY, HealthStatus.DEGRADED)


class HealthSeverity(Enum):
    """
    Severity level for health issues.
    """

    INFO = "info"
    WARNING = "warning"
    ERROR = "error"
    CRITICAL = "critical"

    @property
    def requires_attention(self) -> bool:
        """Returns whether this severity level requires human attention."""
        return self in (HealthSeverity.ERROR, HealthSeverity.CRITICAL)

    @property
    def affects_status(self) -> bool:
        """Returns whether this severity level affects the overall health status."""
        return self != HealthSeverity.INFO

    def to_health_status(self) -> HealthStatus:
        """Maps severity level to a corresponding health status."""
        if self == HealthSeverity.INFO:
            return HealthStatus.HEALTHY
        elif self == HealthSeverity.WARNING:
            return HealthStatus.DEGRADED
        elif self == HealthSeverity.ERROR:
            return HealthStatus.UNHEALTHY
        elif self == HealthSeverity.CRITICAL:
            return HealthStatus.CRITICAL
        else:
            return HealthStatus.UNKNOWN


@dataclass
class HealthMetric:
    """
    An individual health metric measurement.
    """

    name: str
    value: Union[float, int, str, bool]
    unit: Optional[str] = None
    timestamp: datetime = field(default_factory=datetime.now)
    component: str = "system"

    def __post_init__(self):
        """Ensures timestamp is a datetime object."""
        if isinstance(self.timestamp, (int, float)):
            self.timestamp = datetime.fromtimestamp(self.timestamp)


@dataclass
class HealthThreshold:
    """
    Threshold definition for health metrics.
    """

    metric_name: str
    warning_threshold: Optional[Union[float, int]] = None
    error_threshold: Optional[Union[float, int]] = None
    critical_threshold: Optional[Union[float, int]] = None
    comparison: str = "greater"  # "greater" or "less"

    def evaluate(self, metric: HealthMetric) -> Tuple[bool, Optional[HealthSeverity]]:
        """
        Evaluates a metric against this threshold.

        Args:
            metric: The health metric to evaluate

        Returns:
            Tuple of (is_threshold_exceeded, severity_if_exceeded)
        """
        if metric.name != self.metric_name or not isinstance(
            metric.value, (int, float)
        ):
            return False, None

        value = metric.value

        # Define comparison function based on comparison direction
        if self.comparison == "greater":
            def compare(v, t):
                return v > t
        else:  # "less"
            def compare(v, t):
                return v < t

        # Check thresholds from most severe to least
        if self.critical_threshold is not None and compare(
            value, self.critical_threshold
        ):
            return True, HealthSeverity.CRITICAL

        if self.error_threshold is not None and compare(value, self.error_threshold):
            return True, HealthSeverity.ERROR

        if self.warning_threshold is not None and compare(
            value, self.warning_threshold
        ):
            return True, HealthSeverity.WARNING

        return False, None


@dataclass
class HealthIssue:
    """
    Representation of a health problem.
    """

    component: str
    message: str
    severity: HealthSeverity
    timestamp: datetime = field(default_factory=datetime.now)
    details: Dict[str, Any] = field(default_factory=dict)
    remediation: Optional[str] = None

    def __post_init__(self):
        """Ensures timestamp is a datetime object."""
        if isinstance(self.timestamp, (int, float)):
            self.timestamp = datetime.fromtimestamp(self.timestamp)

    @property
    def status(self) -> HealthStatus:
        """Maps this issue's severity to a health status."""
        return self.severity.to_health_status()


@dataclass
class HealthReport:
    """
    Complete health check report.
    """

    timestamp: datetime = field(default_factory=datetime.now)
    metrics: List[HealthMetric] = field(default_factory=list)
    issues: List[HealthIssue] = field(default_factory=list)
    components: Dict[str, HealthStatus] = field(default_factory=dict)
    execution_time_ms: float = 0

    def __post_init__(self):
        """Initialize the report with calculated fields if needed."""
        if isinstance(self.timestamp, (int, float)):
            self.timestamp = datetime.fromtimestamp(self.timestamp)

    @property
    def status(self) -> HealthStatus:
        """
        Calculate the overall health status based on component statuses and issues.

        The worst status among all components becomes the overall status.
        """
        component_status = HealthStatus.HEALTHY  # Default if no components

        # Find the worst component status (highest enum value)
        if self.components:
            component_status = max(self.components.values(), key=lambda s: s.value)

        # Find the worst issue severity and convert to status
        issue_status = HealthStatus.HEALTHY  # Default if no issues
        if self.issues:
            issue_statuses = [
                issue.status for issue in self.issues if issue.severity.affects_status
            ]
            if issue_statuses:
                issue_status = max(issue_statuses, key=lambda s: s.value)

        # Return the worse of the two
        return max(component_status, issue_status, key=lambda s: s.value)

    @property
    def is_healthy(self) -> bool:
        """Returns whether the system is completely healthy."""
        return self.status == HealthStatus.HEALTHY

    @property
    def is_operational(self) -> bool:
        """Returns whether the system can continue operating."""
        return self.status.is_operational

    @property
    def summary(self) -> str:
        """Provides a summary of the health report."""
        if self.is_healthy:
            return "All systems operational"

        # Count issues by severity
        severity_counts = {}
        for issue in self.issues:
            severity_counts[issue.severity] = severity_counts.get(issue.severity, 0) + 1

        # Create summary message
        summary_parts = []
        for severity, count in severity_counts.items():
            if count > 0:
                summary_parts.append(
                    f"{count} {severity.name.lower()} issue{'s' if count > 1 else ''}"
                )

        return (
            "System health issues: " + ", ".join(summary_parts)
            if summary_parts
            else "Health issues detected"
        )

    def add_issue(
        self,
        component: str,
        message: str,
        severity: HealthSeverity,
        details: Dict[str, Any] = None,
        remediation: str = None,
    ) -> None:
        """
        Add a health issue to the report.

        Args:
            component: The component with the issue
            message: Description of the issue
            severity: Severity level
            details: Additional details about the issue
            remediation: Instructions for fixing the issue
        """
        self.issues.append(
            HealthIssue(
                component=component,
                message=message,
                severity=severity,
                details=details or {},
                remediation=remediation,
            )
        )

    def add_metric(
        self,
        name: str,
        value: Union[float, int, str, bool],
        unit: str = None,
        component: str = "system",
    ) -> None:
        """
        Add a health metric to the report.

        Args:
            name: Name of the metric
            value: Metric value
            unit: Measurement unit
            component: Component the metric is associated with
        """
        self.metrics.append(
            HealthMetric(name=name, value=value, unit=unit, component=component)
        )

    def update_component_status(self, component: str, status: HealthStatus) -> None:
        """
        Update the status of a component in the report.

        Args:
            component: Name of the component
            status: Health status
        """
        self.components[component] = status
