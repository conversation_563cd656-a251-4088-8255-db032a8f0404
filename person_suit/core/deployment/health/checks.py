"""
Person Suit - Health Checks (DE-1)

This module provides health check implementations for the Person Suit framework.
Health checks monitor various aspects of the system and report issues.

Classes:
- HealthCheck: Base class for health checks
- SystemResourceCheck: Monitors system resources like CPU, memory, and disk
- NetworkConnectivityCheck: Checks network connectivity and latency
- ComponentStatusCheck: Monitors the status of framework components
- SecurityHealthCheck: Checks security configuration and health
- PersistenceHealthCheck: Monitors database and storage health
- CacheHealthCheck: Monitors cache performance and health

Related Files:
- models.py: Health monitoring data models
- monitor.py: Main health monitoring functionality
"""

import logging
import os
import socket
import threading
import time
from typing import Any
from typing import Callable
from typing import Dict
from typing import List
from typing import Optional
from typing import Tuple
from typing import Type

import psutil

from .models import HealthIssue
from .models import HealthMetric
from .models import HealthSeverity

# Configure logger
logger = logging.getLogger("person_suit.deployment.health.checks")


class HealthCheck:
    """
    Base class for health checks.

    Health checks monitor aspects of the system and report issues and metrics.
    They can be configured with specific thresholds and parameters.
    """

    def __init__(self, check_id: str, name: str, description: str):
        """
        Initialize the health check.

        Args:
            check_id: Unique identifier for the check
            name: Human-readable name
            description: Description of what the check monitors
        """
        self.check_id = check_id
        self.name = name
        self.description = description
        self.enabled = True
        self.check_interval = 60  # Seconds between checks
        self.last_check_time = 0
        self.metadata = {}

        # Configuration parameters with defaults
        self.config = {}

        # Check execution lock
        self._lock = threading.RLock()

    def configure(self, **kwargs) -> None:
        """
        Configure the health check with parameters.

        Args:
            **kwargs: Configuration parameters
        """
        with self._lock:
            self.config.update(kwargs)

            # Update check_interval if provided
            if "check_interval" in kwargs:
                self.check_interval = int(kwargs["check_interval"])

            # Update enabled status if provided
            if "enabled" in kwargs:
                self.enabled = bool(kwargs["enabled"])

    def should_check(self) -> bool:
        """
        Determine if the check should run now.

        Returns:
            bool: True if the check should run
        """
        if not self.enabled:
            return False

        current_time = time.time()
        return (current_time - self.last_check_time) >= self.check_interval

    def check(self) -> Tuple[List[HealthIssue], List[HealthMetric]]:
        """
        Run the health check.

        Returns:
            Tuple[List[HealthIssue], List[HealthMetric]]: Issues and metrics
        """
        if not self.enabled:
            return [], []

        with self._lock:
            self.last_check_time = time.time()

            try:
                # Call the implementation method
                issues, metrics = self._check_impl()
                return issues, metrics
            except Exception as e:
                logger.error(
                    f"Error in health check {self.check_id}: {e}", exc_info=True
                )

                # Return an issue for the check failure
                issue = HealthIssue(
                    component=self.name,
                    message=f"Health check failed: {str(e)}",
                    severity=HealthSeverity.ERROR,
                    check_id=self.check_id,
                    remediation="Check logs for more information and fix the underlying issue.",
                )

                return [issue], []

    def _check_impl(self) -> Tuple[List[HealthIssue], List[HealthMetric]]:
        """
        Implementation of the health check logic.

        Returns:
            Tuple[List[HealthIssue], List[HealthMetric]]: Issues and metrics
        """
        # Base implementation does nothing
        # Subclasses should override this method
        return [], []


class SystemResourceCheck(HealthCheck):
    """Health check for system resources like CPU, memory, and disk."""

    def __init__(self):
        """Initialize the system resource health check."""
        super().__init__(
            check_id="system_resources",
            name="System Resources",
            description="Monitors CPU, memory, disk space, and system load.",
        )

        # Set default thresholds
        self.configure(
            cpu_warning_threshold=80.0,  # %
            cpu_error_threshold=95.0,  # %
            memory_warning_threshold=85.0,  # %
            memory_error_threshold=95.0,  # %
            disk_warning_threshold=90.0,  # %
            disk_error_threshold=95.0,  # %
            check_interval=30,  # Check every 30 seconds
        )

    def _check_impl(self) -> Tuple[List[HealthIssue], List[HealthMetric]]:
        """Implement the system resource health check."""
        issues = []
        metrics = []

        # Get current CPU usage
        try:
            cpu_percent = psutil.cpu_percent(interval=0.5)

            # Add CPU metric
            metrics.append(
                HealthMetric(
                    name="cpu_usage",
                    value=cpu_percent,
                    component="system",
                    unit="%",
                    threshold_warning=self.config.get("cpu_warning_threshold"),
                    threshold_error=self.config.get("cpu_error_threshold"),
                )
            )

            # Check CPU threshold and add issue if necessary
            if cpu_percent >= self.config.get("cpu_error_threshold", 95.0):
                issues.append(
                    HealthIssue(
                        component="system",
                        message=f"CPU usage is critically high: {cpu_percent}%",
                        severity=HealthSeverity.ERROR,
                        check_id=self.check_id,
                        remediation="Reduce system load or scale up computing resources.",
                    )
                )
            elif cpu_percent >= self.config.get("cpu_warning_threshold", 80.0):
                issues.append(
                    HealthIssue(
                        component="system",
                        message=f"CPU usage is high: {cpu_percent}%",
                        severity=HealthSeverity.WARNING,
                        check_id=self.check_id,
                        remediation="Monitor system performance and consider reducing load.",
                    )
                )
        except Exception as e:
            logger.error(f"Error checking CPU usage: {e}")
            issues.append(
                HealthIssue(
                    component="system",
                    message=f"Unable to check CPU usage: {str(e)}",
                    severity=HealthSeverity.WARNING,
                    check_id=self.check_id,
                )
            )

        # Get current memory usage
        try:
            memory = psutil.virtual_memory()
            memory_percent = memory.percent

            # Add memory metric
            metrics.append(
                HealthMetric(
                    name="memory_usage",
                    value=memory_percent,
                    component="system",
                    unit="%",
                    threshold_warning=self.config.get("memory_warning_threshold"),
                    threshold_error=self.config.get("memory_error_threshold"),
                    metadata={"total": memory.total, "available": memory.available},
                )
            )

            # Check memory threshold and add issue if necessary
            if memory_percent >= self.config.get("memory_error_threshold", 95.0):
                issues.append(
                    HealthIssue(
                        component="system",
                        message=f"Memory usage is critically high: {memory_percent}%",
                        severity=HealthSeverity.ERROR,
                        check_id=self.check_id,
                        remediation="Free up memory resources or scale up system memory.",
                    )
                )
            elif memory_percent >= self.config.get("memory_warning_threshold", 85.0):
                issues.append(
                    HealthIssue(
                        component="system",
                        message=f"Memory usage is high: {memory_percent}%",
                        severity=HealthSeverity.WARNING,
                        check_id=self.check_id,
                        remediation="Monitor memory usage and consider freeing up resources.",
                    )
                )
        except Exception as e:
            logger.error(f"Error checking memory usage: {e}")
            issues.append(
                HealthIssue(
                    component="system",
                    message=f"Unable to check memory usage: {str(e)}",
                    severity=HealthSeverity.WARNING,
                    check_id=self.check_id,
                )
            )

        # Get disk usage for the main partition
        try:
            disk_path = os.path.abspath(os.sep)  # Root directory
            disk = psutil.disk_usage(disk_path)
            disk_percent = disk.percent

            # Add disk metric
            metrics.append(
                HealthMetric(
                    name="disk_usage",
                    value=disk_percent,
                    component="system",
                    unit="%",
                    threshold_warning=self.config.get("disk_warning_threshold"),
                    threshold_error=self.config.get("disk_error_threshold"),
                    metadata={
                        "path": disk_path,
                        "total": disk.total,
                        "free": disk.free,
                    },
                )
            )

            # Check disk threshold and add issue if necessary
            if disk_percent >= self.config.get("disk_error_threshold", 95.0):
                issues.append(
                    HealthIssue(
                        component="system",
                        message=f"Disk usage is critically high: {disk_percent}%",
                        severity=HealthSeverity.ERROR,
                        check_id=self.check_id,
                        remediation="Free up disk space or add more storage.",
                    )
                )
            elif disk_percent >= self.config.get("disk_warning_threshold", 90.0):
                issues.append(
                    HealthIssue(
                        component="system",
                        message=f"Disk usage is high: {disk_percent}%",
                        severity=HealthSeverity.WARNING,
                        check_id=self.check_id,
                        remediation="Monitor disk usage and consider cleaning up files.",
                    )
                )
        except Exception as e:
            logger.error(f"Error checking disk usage: {e}")
            issues.append(
                HealthIssue(
                    component="system",
                    message=f"Unable to check disk usage: {str(e)}",
                    severity=HealthSeverity.WARNING,
                    check_id=self.check_id,
                )
            )

        return issues, metrics


class NetworkConnectivityCheck(HealthCheck):
    """Health check for network connectivity and latency."""

    def __init__(self):
        """Initialize the network connectivity health check."""
        super().__init__(
            check_id="network_connectivity",
            name="Network Connectivity",
            description="Checks network connectivity and latency to important hosts.",
        )

        # Set default thresholds and hosts
        self.configure(
            hosts=[
                "*******",
                "*******",
            ],  # Default hosts to check (Google & Cloudflare DNS)
            port=53,  # DNS port
            timeout=2.0,  # Seconds
            latency_warning_threshold=200.0,  # ms
            latency_error_threshold=500.0,  # ms
            check_interval=60,  # Check every 60 seconds
        )

    def _check_impl(self) -> Tuple[List[HealthIssue], List[HealthMetric]]:
        """Implement the network connectivity health check."""
        issues = []
        metrics = []

        # Test connectivity to configured hosts
        hosts = self.config.get("hosts", ["*******", "*******"])
        port = self.config.get("port", 53)
        timeout = self.config.get("timeout", 2.0)

        for host in hosts:
            try:
                # Measure connection latency
                start_time = time.time()

                # Create socket and try to connect
                s = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
                s.settimeout(timeout)

                try:
                    s.connect((host, port))
                    connected = True
                except (socket.timeout, ConnectionRefusedError):
                    connected = False

                s.close()

                # Calculate latency in milliseconds
                latency = (time.time() - start_time) * 1000.0

                # Add metric
                metrics.append(
                    HealthMetric(
                        name="network_latency",
                        value=latency if connected else -1.0,
                        component="network",
                        unit="ms",
                        threshold_warning=self.config.get("latency_warning_threshold"),
                        threshold_error=self.config.get("latency_error_threshold"),
                        metadata={"host": host, "port": port, "connected": connected},
                    )
                )

                if not connected:
                    issues.append(
                        HealthIssue(
                            component="network",
                            message=f"Unable to connect to {host}:{port}",
                            severity=HealthSeverity.ERROR,
                            check_id=self.check_id,
                            remediation="Check network connectivity and firewall settings.",
                        )
                    )
                else:
                    # Check latency thresholds
                    if latency >= self.config.get("latency_error_threshold", 500.0):
                        issues.append(
                            HealthIssue(
                                component="network",
                                message=f"Very high latency to {host}: {latency:.1f}ms",
                                severity=HealthSeverity.ERROR,
                                check_id=self.check_id,
                                remediation="Check network quality and connection stability.",
                            )
                        )
                    elif latency >= self.config.get("latency_warning_threshold", 200.0):
                        issues.append(
                            HealthIssue(
                                component="network",
                                message=f"High latency to {host}: {latency:.1f}ms",
                                severity=HealthSeverity.WARNING,
                                check_id=self.check_id,
                                remediation="Monitor network performance for degradation.",
                            )
                        )

            except Exception as e:
                logger.error(f"Error checking network connectivity to {host}: {e}")
                issues.append(
                    HealthIssue(
                        component="network",
                        message=f"Error checking connectivity to {host}: {str(e)}",
                        severity=HealthSeverity.WARNING,
                        check_id=self.check_id,
                    )
                )

        return issues, metrics


class ComponentStatusCheck(HealthCheck):
    """Health check for Person Suit component status."""

    def __init__(self):
        """Initialize the component status health check."""
        super().__init__(
            check_id="component_status",
            name="Component Status",
            description="Monitors the status of key Person Suit components.",
        )

        # Set default configuration
        self.configure(
            components=["core", "deployment", "adapters", "resources", "security"],
            check_interval=60,  # Check every 60 seconds
        )

        # Component status check functions
        self._check_functions = {}

    def register_component_check(
        self,
        component_name: str,
        check_function: Callable[
            [], Tuple[bool, Optional[str], Optional[Dict[str, Any]]]
        ],
    ) -> None:
        """
        Register a check function for a component.

        Args:
            component_name: Name of the component to check
            check_function: Function that returns (is_healthy, message, metadata)
        """
        with self._lock:
            self._check_functions[component_name] = check_function

    def _check_impl(self) -> Tuple[List[HealthIssue], List[HealthMetric]]:
        """Implement the component status health check."""
        issues = []
        metrics = []

        components = self.config.get("components", [])

        for component in components:
            try:
                # Get status value (0-100, higher is better)
                status_value = 100  # Default is healthy
                check_func = self._check_functions.get(component)

                if check_func:
                    # Call the registered check function
                    is_healthy, message, metadata = check_func()
                    status_value = 100 if is_healthy else 0

                    if not is_healthy and message:
                        issues.append(
                            HealthIssue(
                                component=component,
                                message=message,
                                severity=HealthSeverity.ERROR,
                                check_id=self.check_id,
                                metadata=metadata or {},
                            )
                        )
                else:
                    # Default check based on component type
                    if component == "core":
                        # Check core modules
                        try:
                            # Try to import and check core functionality
                            from person_suit.core import version

                            # If we get here, the import succeeded
                        except ImportError:
                            status_value = 0
                            issues.append(
                                HealthIssue(
                                    component="core",
                                    message="Core modules are not available",
                                    severity=HealthSeverity.ERROR,
                                    check_id=self.check_id,
                                    remediation="Check Person Suit installation and core dependencies.",
                                )
                            )

                    elif component == "deployment":
                        # Check deployment environment
                        try:
                            from person_suit.core.deployment.detection import detect_environment

                            detect_environment()  # If this succeeds, environment detection works
                        except Exception as e:
                            status_value = 0
                            issues.append(
                                HealthIssue(
                                    component="deployment",
                                    message=f"Deployment environment detection failed: {str(e)}",
                                    severity=HealthSeverity.ERROR,
                                    check_id=self.check_id,
                                    remediation="Check deployment configuration and dependencies.",
                                )
                            )

                # Add component status metric
                metrics.append(
                    HealthMetric(
                        name="component_status",
                        value=status_value,
                        component=component,
                        unit="%",
                        threshold_warning=50,  # Below 50% is a warning
                        threshold_error=10,  # Below 10% is an error
                    )
                )

            except Exception as e:
                logger.error(f"Error checking component {component}: {e}")
                issues.append(
                    HealthIssue(
                        component=component,
                        message=f"Error checking component status: {str(e)}",
                        severity=HealthSeverity.WARNING,
                        check_id=self.check_id,
                    )
                )

        return issues, metrics


# Dictionary of available health check classes
AVAILABLE_HEALTH_CHECKS: Dict[str, Type[HealthCheck]] = {
    "system_resources": SystemResourceCheck,
    "network_connectivity": NetworkConnectivityCheck,
    "component_status": ComponentStatusCheck,
}


def create_health_check(check_type: str) -> Optional[HealthCheck]:
    """
    Create a health check of the specified type.

    Args:
        check_type: Type of health check to create

    Returns:
        Optional[HealthCheck]: Created health check or None if type is unknown
    """
    check_class = AVAILABLE_HEALTH_CHECKS.get(check_type)
    if check_class:
        return check_class()
    return None


def get_default_health_checks() -> List[HealthCheck]:
    """
    Get a list of default health checks.

    Returns:
        List[HealthCheck]: List of default health checks
    """
    return [SystemResourceCheck(), NetworkConnectivityCheck(), ComponentStatusCheck()]
