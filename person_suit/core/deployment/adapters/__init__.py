"""
Person Suit - Deployment Adapters Package (DE-1)

This package provides deployment adapters for the Person Suit framework,
enabling integration with different deployment environments including
cloud providers, container orchestration platforms, and local development.

Components:
- AdapterType: Types of deployment adapters
- AdapterCapability: Capabilities provided by deployment adapters
- AdapterInfo: Information about a deployment adapter
- DeploymentAdapter: Base class for all deployment adapters
- CloudAdapter: Base class for cloud adapters
- ContainerAdapter: Base class for container adapters
- LocalAdapter: Adapter for local environments
- AdapterRegistry: Central registry for deployment adapters

Architecture:
The adapters package follows a modular design where each adapter type
has its own implementation file. The AdapterRegistry provides discovery
and management capabilities for all adapters.

Usage:
    # Get adapter registry and initialize
    registry = get_adapter_registry()
    registry.initialize()

    # Get active adapters
    active_adapters = registry.get_active_adapters()

    # Get a capability from the most suitable adapter
    storage = registry.get_capability(AdapterCapability.STORAGE)

    # Register a custom adapter
    registry.register_adapter("MyAdapter", my_adapter)
"""

# Import from core module
from .core import (  # Models; Base adapter; Cloud adapters; Container adapters; Local adapter; Registry
    AdapterCapability,
)
from .core import (  # Models; Base adapter; Cloud adapters; Container adapters; Local adapter; Registry
    AdapterInfo,
)
from .core import (  # Models; Base adapter; Cloud adapters; Container adapters; Local adapter; Registry
    AdapterRegistry,
)
from .core import (  # Models; Base adapter; Cloud adapters; Container adapters; Local adapter; Registry
    AdapterType,
)
from .core import (  # Models; Base adapter; Cloud adapters; Container adapters; Local adapter; Registry
    AWSAdapter,
)
from .core import (  # Models; Base adapter; Cloud adapters; Container adapters; Local adapter; Registry
    CloudAdapter,
)
from .core import (  # Models; Base adapter; Cloud adapters; Container adapters; Local adapter; Registry
    ContainerAdapter,
)
from .core import (  # Models; Base adapter; Cloud adapters; Container adapters; Local adapter; Registry
    DeploymentAdapter,
)
from .core import (  # Models; Base adapter; Cloud adapters; Container adapters; Local adapter; Registry
    KubernetesAdapter,
)
from .core import (  # Models; Base adapter; Cloud adapters; Container adapters; Local adapter; Registry
    LocalAdapter,
)
from .core import (  # Models; Base adapter; Cloud adapters; Container adapters; Local adapter; Registry
    get_active_adapters,
)
from .core import (  # Models; Base adapter; Cloud adapters; Container adapters; Local adapter; Registry
    get_adapter,
)
from .core import (  # Models; Base adapter; Cloud adapters; Container adapters; Local adapter; Registry
    get_adapter_registry,
)
from .core import (  # Models; Base adapter; Cloud adapters; Container adapters; Local adapter; Registry
    get_capability,
)
from .core import (  # Models; Base adapter; Cloud adapters; Container adapters; Local adapter; Registry
    register_adapter,
)

# Define exported symbols
__all__ = [
    # Models
    "AdapterType",
    "AdapterCapability",
    "AdapterInfo",
    # Base adapter
    "DeploymentAdapter",
    # Cloud adapters
    "CloudAdapter",
    "AWSAdapter",
    # Container adapters
    "ContainerAdapter",
    "KubernetesAdapter",
    # Local adapter
    "LocalAdapter",
    # Registry
    "AdapterRegistry",
    "get_adapter_registry",
    "get_adapter",
    "get_active_adapters",
    "get_capability",
    "register_adapter",
]
