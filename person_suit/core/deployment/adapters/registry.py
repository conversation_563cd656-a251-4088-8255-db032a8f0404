"""
Person Suit - Deployment Adapter Registry (DE-1)

This module provides a registry for deployment adapters in the Person Suit framework,
enabling discovery, management, and access to adapters for different environments.

Classes:
- AdapterRegistry: Central registry for deployment adapters

Related Files:
- models.py: Adapter data models and enumerations
- base.py: Base adapter classes and interfaces
- cloud.py: Cloud service adapters
- container.py: Container environment adapters
- local.py: Local environment adapter

Dependencies:
- person_suit.core.deployment.detection: For environment detection
"""

import logging
import threading
from typing import Any
from typing import Dict
from typing import List
from typing import Optional
from typing import Type

from ..detection import detect_environment
from .base import DeploymentAdapter
from .cloud import AWSAdapter
from .container import KubernetesAdapter
from .local import LocalAdapter
from .models import AdapterCapability
from .models import AdapterType

# Configure logger
logger = logging.getLogger("person_suit.deployment.adapters.registry")


class AdapterRegistry:
    """
    Central registry for deployment adapters.

    Manages registration, discovery, and access to deployment adapters
    for different environments.
    """

    _instance = None
    _lock = threading.RLock()

    def __new__(cls):
        """Singleton implementation."""
        with cls._lock:
            if not cls._instance:
                cls._instance = super(AdapterRegistry, cls).__new__(cls)
                cls._instance._initialized = False
            return cls._instance

    def __init__(self):
        """Initialize the adapter registry."""
        if self._initialized:
            return

        with self._lock:
            if not self._initialized:
                self._adapters: Dict[str, DeploymentAdapter] = {}
                self._active_adapters: Dict[str, DeploymentAdapter] = {}
                self._adapter_types: Dict[
                    AdapterType, List[Type[DeploymentAdapter]]
                ] = {}
                self._environment_data = None

                # Register adapter types
                self._register_adapter_types()

                self._initialized = True
                logger.debug("Adapter registry initialized")

    def initialize(self) -> bool:
        """
        Initialize the adapter registry.

        This method detects the environment and registers applicable adapters.

        Returns:
            bool: True if initialization was successful
        """
        with self._lock:
            try:
                # Detect environment
                self._environment_data = detect_environment()

                # Register built-in adapters
                self._register_built_in_adapters()

                # Discover and register applicable adapters
                self._discover_adapters()

                logger.info(
                    f"Adapter registry initialized with {len(self._adapters)} adapters"
                )
                logger.info(
                    f"Active adapters: {', '.join(self._active_adapters.keys())}"
                )

                return True
            except Exception as e:
                logger.error(f"Failed to initialize adapter registry: {e}")
                return False

    def _register_adapter_types(self) -> None:
        """Register adapter types."""
        self._adapter_types = {
            AdapterType.CLOUD: [],
            AdapterType.CONTAINER: [],
            AdapterType.LOCAL: [],
            AdapterType.EDGE: [],
            AdapterType.CI: [],
            AdapterType.CUSTOM: [],
        }

        # Register built-in adapter types
        self._adapter_types[AdapterType.CLOUD].append(AWSAdapter)
        self._adapter_types[AdapterType.CONTAINER].append(KubernetesAdapter)
        self._adapter_types[AdapterType.LOCAL].append(LocalAdapter)

    def _register_built_in_adapters(self) -> None:
        """Register built-in adapters."""
        # Create and register AWS adapter
        aws_adapter = AWSAdapter(self._environment_data)
        self.register_adapter("AWS", aws_adapter)

        # Create and register Kubernetes adapter
        k8s_adapter = KubernetesAdapter(self._environment_data)
        self.register_adapter("Kubernetes", k8s_adapter)

        # Create and register Local adapter
        local_adapter = LocalAdapter(self._environment_data)
        self.register_adapter("Local", local_adapter)

    def _discover_adapters(self) -> None:
        """Discover and initialize applicable adapters."""
        for name, adapter in self._adapters.items():
            if adapter.is_applicable():
                # Initialize the adapter
                if adapter.initialize():
                    # Register as active
                    self._active_adapters[name] = adapter
                    logger.info(f"Activated adapter: {name}")

    def register_adapter(self, name: str, adapter: DeploymentAdapter) -> bool:
        """
        Register a deployment adapter.

        Args:
            name: Name of the adapter
            adapter: Adapter instance

        Returns:
            bool: True if registration was successful
        """
        with self._lock:
            # Check if adapter with same name exists
            if name in self._adapters:
                logger.warning(f"Adapter with name '{name}' already registered")
                return False

            # Register the adapter
            self._adapters[name] = adapter

            # Check if applicable and initialize
            if adapter.is_applicable():
                if adapter.initialize():
                    self._active_adapters[name] = adapter
                    logger.info(f"Registered and activated adapter: {name}")
                else:
                    logger.warning(
                        f"Registered adapter {name}, but initialization failed"
                    )
            else:
                logger.debug(
                    f"Registered adapter {name}, but not applicable to current environment"
                )

            return True

    def get_adapter(self, name: str) -> Optional[DeploymentAdapter]:
        """
        Get a deployment adapter by name.

        Args:
            name: Name of the adapter

        Returns:
            Optional[DeploymentAdapter]: Adapter if found, None otherwise
        """
        with self._lock:
            return self._adapters.get(name)

    def get_active_adapters(self) -> Dict[str, DeploymentAdapter]:
        """
        Get active adapters.

        Returns:
            Dict[str, DeploymentAdapter]: Active adapters
        """
        with self._lock:
            return self._active_adapters.copy()

    def get_adapter_by_type(self, adapter_type: AdapterType) -> List[DeploymentAdapter]:
        """
        Get adapters by type.

        Args:
            adapter_type: Type of adapters to get

        Returns:
            List[DeploymentAdapter]: Adapters of the specified type
        """
        with self._lock:
            result = []

            for name, adapter in self._adapters.items():
                if adapter.adapter_info.adapter_type == adapter_type:
                    result.append(adapter)

            return result

    def get_adapters_by_capability(
        self, capability: AdapterCapability
    ) -> List[DeploymentAdapter]:
        """
        Get adapters by capability.

        Args:
            capability: Capability to look for

        Returns:
            List[DeploymentAdapter]: Adapters supporting the capability
        """
        with self._lock:
            result = []

            for name, adapter in self._active_adapters.items():
                if capability in adapter.adapter_info.capabilities:
                    result.append(adapter)

            return result

    def get_capability(self, capability: AdapterCapability) -> Optional[Any]:
        """
        Get a capability from the most suitable adapter.

        Args:
            capability: Capability to get

        Returns:
            Optional[Any]: Capability implementation if available, None otherwise
        """
        with self._lock:
            # Get adapters supporting this capability
            adapters = self.get_adapters_by_capability(capability)

            if not adapters:
                logger.warning(
                    f"No active adapter supports capability {capability.name}"
                )
                return None

            # Prioritize adapters: first try cloud, then container, then local
            for adapter_type in [
                AdapterType.CLOUD,
                AdapterType.CONTAINER,
                AdapterType.LOCAL,
            ]:
                for adapter in adapters:
                    if adapter.adapter_info.adapter_type == adapter_type:
                        # Try to get the capability
                        capability_impl = adapter.get_capability(capability)
                        if capability_impl:
                            return capability_impl

            # If no specific adapter worked, try any adapter
            for adapter in adapters:
                capability_impl = adapter.get_capability(capability)
                if capability_impl:
                    return capability_impl

            logger.warning(f"No adapter could provide capability {capability.name}")
            return None


# Singleton instance
_adapter_registry_instance = None


def get_adapter_registry() -> AdapterRegistry:
    """Get the singleton adapter registry instance."""
    global _adapter_registry_instance

    if _adapter_registry_instance is None:
        _adapter_registry_instance = AdapterRegistry()

    return _adapter_registry_instance


def get_adapter(name: str) -> Optional[DeploymentAdapter]:
    """Get a deployment adapter by name."""
    return get_adapter_registry().get_adapter(name)


def get_active_adapters() -> Dict[str, DeploymentAdapter]:
    """Get active adapters."""
    return get_adapter_registry().get_active_adapters()


def get_capability(capability: AdapterCapability) -> Optional[Any]:
    """Get a capability from the most suitable adapter."""
    return get_adapter_registry().get_capability(capability)


def register_adapter(name: str, adapter: DeploymentAdapter) -> bool:
    """Register a deployment adapter."""
    return get_adapter_registry().register_adapter(name, adapter)
