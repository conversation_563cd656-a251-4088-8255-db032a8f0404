"""
Person Suit - Deployment Environment Configuration Core (DE-1)

This module provides runtime configuration for the Person Suit framework,
tailoring configuration settings to the specific characteristics of the
detected deployment environment.

Components:
- RuntimeConfiguration: Environment-specific configuration management
- ConfigurationProfile: Environment-specific configuration profile
- ProfileManager: Management of configuration profiles

Related Files:
- detection.py: Environment detection providing hardware/platform info
- resources.py: Resource management based on environment
- optimization.py: Hardware-specific optimizations
- adapters.py: Deployment adapters for different environments
- security.py: Security boundaries based on environment

Dependencies:
- person_suit.core.infrastructure.configuration: For configuration system integration
- person_suit.core.infrastructure.monitoring: For metrics collection
"""

import logging
import threading
from dataclasses import dataclass
from dataclasses import field
from typing import Any
from typing import Dict
from typing import List
from typing import Optional

# Import from configuration system
from ...infrastructure.configuration import ConfigurationError
from ...infrastructure.configuration import Environment
from ...infrastructure.configuration import get_config_manager

# Import from detection module
from ..detection.core import EnvironmentData
from ..detection.core import detect_environment

# Configure logger
logger = logging.getLogger("person_suit.deployment.configuration")


@dataclass
class ConfigurationProfile:
    """
    Environment-specific configuration profile.

    A configuration profile contains environment-specific settings and overrides
    for a particular deployment environment.
    """

    name: str
    environment: Environment
    description: str
    settings: Dict[str, Dict[str, Any]]
    priority: int = 100
    is_active: bool = False
    metadata: Dict[str, Any] = field(default_factory=dict)

    def to_dict(self) -> Dict[str, Any]:
        """Convert profile to dictionary."""
        return {
            "name": self.name,
            "environment": self.environment.name,
            "description": self.description,
            "settings": self.settings,
            "priority": self.priority,
            "is_active": self.is_active,
            "metadata": self.metadata,
        }

    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> "ConfigurationProfile":
        """Create profile from dictionary."""
        try:
            return cls(
                name=data["name"],
                environment=Environment[data["environment"]],
                description=data["description"],
                settings=data["settings"],
                priority=data.get("priority", 100),
                is_active=data.get("is_active", False),
                metadata=data.get("metadata", {}),
            )
        except (KeyError, ValueError) as e:
            logger.error(f"Invalid profile data: {e}")
            raise ValueError(f"Invalid profile data: {e}")


class ProfileManager:
    """
    Management of configuration profiles.

    Handles loading, saving, and selection of environment-specific
    configuration profiles.
    """

    def __init__(self):
        """Initialize profile manager."""
        self._profiles = {}
        self._active_profile = None
        self._initialized = False
        self._lock = threading.RLock()

        # TODO: Initialize profile storage location

    def initialize(self) -> bool:
        """
        Initialize the profile manager.

        Returns:
            bool: True if initialization was successful
        """
        if self._initialized:
            return True

        with self._lock:
            # TODO: Implement initialization
            # 1. Load built-in profiles
            # 2. Load user-defined profiles
            # 3. Determine active profile
            self._initialized = True
            return True

    def get_profiles(self) -> List[ConfigurationProfile]:
        """
        Get all available profiles.

        Returns:
            List[ConfigurationProfile]: Available profiles
        """
        return list(self._profiles.values())

    def get_profile(self, name: str) -> Optional[ConfigurationProfile]:
        """
        Get profile by name.

        Args:
            name: Profile name

        Returns:
            ConfigurationProfile: Profile if found, None otherwise
        """
        return self._profiles.get(name)

    def add_profile(self, profile: ConfigurationProfile) -> bool:
        """
        Add a new profile.

        Args:
            profile: Profile to add

        Returns:
            bool: True if profile was added successfully
        """
        with self._lock:
            # TODO: Implement profile addition
            # 1. Validate profile
            # 2. Add profile to storage
            # 3. Update in-memory profile collection
            self._profiles[profile.name] = profile
            return True

    def remove_profile(self, name: str) -> bool:
        """
        Remove a profile.

        Args:
            name: Profile name

        Returns:
            bool: True if profile was removed successfully
        """
        with self._lock:
            # TODO: Implement profile removal
            # 1. Check if profile exists
            # 2. Remove profile from storage
            # 3. Update in-memory profile collection
            if name in self._profiles:
                del self._profiles[name]
                return True
            return False

    def activate_profile(self, name: str) -> bool:
        """
        Activate a profile.

        Args:
            name: Profile name

        Returns:
            bool: True if profile was activated successfully
        """
        with self._lock:
            # TODO: Implement profile activation
            # 1. Check if profile exists
            # 2. Deactivate current profile
            # 3. Activate new profile
            # 4. Apply profile settings
            profile = self._profiles.get(name)
            if profile:
                if self._active_profile:
                    self._active_profile.is_active = False
                profile.is_active = True
                self._active_profile = profile
                return True
            return False

    def get_active_profile(self) -> Optional[ConfigurationProfile]:
        """
        Get the active profile.

        Returns:
            ConfigurationProfile: Active profile if available, None otherwise
        """
        return self._active_profile

    def _load_builtin_profiles(self) -> None:
        """Load built-in configuration profiles."""
        # TODO: Implement built-in profile loading
        # 1. Load development profile
        # 2. Load testing profile
        # 3. Load staging profile
        # 4. Load production profile
        pass

    def _load_user_profiles(self) -> None:
        """Load user-defined configuration profiles."""
        # TODO: Implement user profile loading
        # 1. Determine profile storage location
        # 2. Load profiles from storage
        # 3. Validate loaded profiles
        pass

    def _save_profiles(self) -> bool:
        """
        Save profiles to storage.

        Returns:
            bool: True if profiles were saved successfully
        """
        # TODO: Implement profile saving
        # 1. Convert profiles to serializable format
        # 2. Save to storage location
        return True

    def _select_profile_for_environment(
        self, environment: Environment
    ) -> Optional[ConfigurationProfile]:
        """
        Select appropriate profile for environment.

        Args:
            environment: Target environment

        Returns:
            ConfigurationProfile: Selected profile if available, None otherwise
        """
        # TODO: Implement profile selection
        # 1. Find profiles matching environment
        # 2. Select highest priority profile
        # 3. Return selected profile
        matching_profiles = [
            p for p in self._profiles.values() if p.environment == environment
        ]
        if matching_profiles:
            return max(matching_profiles, key=lambda p: p.priority)
        return None


class RuntimeConfiguration:
    """
    Environment-specific configuration management.

    Provides environment-specific configuration settings and adjustments
    based on the detected deployment environment.
    """

    _instance = None
    _lock = threading.RLock()

    def __new__(cls):
        """Singleton implementation."""
        with cls._lock:
            if not cls._instance:
                cls._instance = super(RuntimeConfiguration, cls).__new__(cls)
                cls._instance._initialized = False
            return cls._instance

    def __init__(self):
        """Initialize runtime configuration."""
        if self._initialized:
            return

        with self._lock:
            if not self._initialized:
                self._env_prefix = "PERSON_SUIT_"
                self._environment_data = None
                self._config_manager = None
                self._profile_manager = None
                self._active_profile = None
                self._initialized = False

                logger.debug("Runtime configuration created (not initialized)")

    def initialize(self) -> bool:
        """
        Initialize runtime configuration.

        Returns:
            bool: True if initialization was successful
        """
        if self._initialized:
            logger.warning("Runtime configuration already initialized")
            return True

        try:
            # Detect environment
            self._environment_data = detect_environment()

            # Get configuration manager
            self._config_manager = get_config_manager()

            # Initialize profile manager
            self._profile_manager = ProfileManager()
            self._profile_manager.initialize()

            # Select and activate profile for current environment
            self._select_environment_profile()

            # Apply environment-specific optimizations
            self._apply_hardware_optimizations()

            self._initialized = True
            logger.info("Runtime configuration initialized")
            return True
        except Exception as e:
            logger.error(f"Failed to initialize runtime configuration: {e}")
            return False

    def _select_environment_profile(self) -> None:
        """Select and activate environment-specific profile."""
        # TODO: Implement profile selection
        # 1. Get current environment
        # 2. Select appropriate profile
        # 3. Activate selected profile
        # 4. Apply profile settings
        environment = self._environment_data.runtime.environment
        profile = self._profile_manager._select_profile_for_environment(environment)
        if profile:
            self._profile_manager.activate_profile(profile.name)
            self._active_profile = profile
            logger.info(f"Activated configuration profile: {profile.name}")
        else:
            logger.warning(
                f"No configuration profile available for environment: {environment.name}"
            )

    def _apply_hardware_optimizations(self) -> None:
        """Apply hardware-specific optimizations to configuration."""
        # TODO: Implement hardware optimizations
        # 1. Check for M3 Max
        # 2. Apply hardware-specific settings
        # 3. Update configuration
        if self._environment_data.hardware.is_m3_max:
            logger.info("Applying M3 Max optimizations to configuration")
            # TODO: Add M3 Max specific settings

    def get_environment_setting(
        self, section: str, key: str, default: Any = None
    ) -> Any:
        """
        Get environment-specific setting.

        Args:
            section: Configuration section
            key: Configuration key
            default: Default value if not found

        Returns:
            Setting value
        """
        if not self._initialized:
            self.initialize()

        # Check active profile first
        if self._active_profile:
            section_settings = self._active_profile.settings.get(section, {})
            if key in section_settings:
                return section_settings[key]

        # Fall back to configuration manager
        try:
            return self._config_manager.get_value(section, key)
        except ConfigurationError:
            return default

    def set_environment_setting(self, section: str, key: str, value: Any) -> bool:
        """
        Set environment-specific setting.

        Args:
            section: Configuration section
            key: Configuration key
            value: Setting value

        Returns:
            bool: True if setting was updated successfully
        """
        if not self._initialized:
            self.initialize()

        # TODO: Implement setting update
        # 1. Update active profile if available
        # 2. Update configuration manager
        # 3. Log change
        if self._active_profile:
            if section not in self._active_profile.settings:
                self._active_profile.settings[section] = {}
            self._active_profile.settings[section][key] = value

        # Update configuration manager
        try:
            self._config_manager.set_value(section, key, value)
            return True
        except ConfigurationError as e:
            logger.error(f"Failed to update configuration: {e}")
            return False

    def get_active_profile(self) -> Optional[ConfigurationProfile]:
        """
        Get active configuration profile.

        Returns:
            ConfigurationProfile: Active profile if available, None otherwise
        """
        if not self._initialized:
            self.initialize()

        return self._active_profile

    def get_environment_data(self) -> EnvironmentData:
        """
        Get current environment data.

        Returns:
            EnvironmentData: Current environment data
        """
        if not self._initialized:
            self.initialize()

        return self._environment_data

    def refresh(self) -> bool:
        """
        Refresh runtime configuration.

        Returns:
            bool: True if refresh was successful
        """
        if not self._initialized:
            return self.initialize()

        try:
            # Refresh environment data
            self._environment_data = detect_environment(force_refresh=True)

            # Re-select environment profile
            self._select_environment_profile()

            # Re-apply hardware optimizations
            self._apply_hardware_optimizations()

            logger.info("Runtime configuration refreshed")
            return True
        except Exception as e:
            logger.error(f"Failed to refresh runtime configuration: {e}")
            return False


# Singleton instance
_runtime_configuration_instance = None


def get_runtime_configuration() -> RuntimeConfiguration:
    """Get the singleton runtime configuration instance."""
    global _runtime_configuration_instance

    if _runtime_configuration_instance is None:
        _runtime_configuration_instance = RuntimeConfiguration()

    return _runtime_configuration_instance


# Convenience functions
def get_environment_setting(section: str, key: str, default: Any = None) -> Any:
    """Get environment-specific setting."""
    return get_runtime_configuration().get_environment_setting(section, key, default)


def set_environment_setting(section: str, key: str, value: Any) -> bool:
    """Set environment-specific setting."""
    return get_runtime_configuration().set_environment_setting(section, key, value)


def get_active_profile() -> Optional[ConfigurationProfile]:
    """Get active configuration profile."""
    return get_runtime_configuration().get_active_profile()


def refresh_configuration() -> bool:
    """Refresh runtime configuration."""
    return get_runtime_configuration().refresh()
