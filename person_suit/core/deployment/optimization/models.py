"""
Person Suit - Optimization Models (DE-1)

This module provides data models and enumerations for the hardware optimization system,
defining optimization types, settings, and profiles.

Models:
- OptimizationType: Types of hardware optimizations
- OptimizationSetting: Individual optimization setting
- OptimizationProfile: Hardware-specific optimization profile

Related Files:
- strategies/base.py: Base class for optimization strategies
- strategies/memory.py: Memory optimization strategy
- strategies/compute.py: Compute optimization strategy
- strategies/m3_max.py: M3 Max-specific optimization strategy
- manager.py: Hardware-specific optimization management

Dependencies:
- person_suit.core.deployment.detection: For hardware information
"""

import logging
from dataclasses import dataclass
from dataclasses import field
from enum import Enum
from enum import auto
from typing import Any
from typing import Dict

# Configure logger
logger = logging.getLogger("person_suit.deployment.optimization.models")


class OptimizationType(Enum):
    """Types of hardware optimizations."""

    MEMORY = auto()
    COMPUTE = auto()
    PARALLEL = auto()
    VECTORIZATION = auto()
    NEURAL_ENGINE = auto()
    METAL = auto()
    CACHING = auto()
    I_O = auto()
    CUSTOM = auto()


@dataclass
class OptimizationSetting:
    """Individual optimization setting."""

    name: str
    description: str
    enabled: bool
    optimization_type: OptimizationType
    parameters: Dict[str, Any] = field(default_factory=dict)
    impact_level: int = 1  # 1-5 scale of impact


@dataclass
class OptimizationProfile:
    """
    Hardware-specific optimization profile.

    Contains a collection of optimization settings tuned for
    specific hardware configurations.
    """

    name: str
    description: str
    target_hardware: str
    optimizations: Dict[str, OptimizationSetting]
    is_active: bool = False
    impact_score: int = 0
    metadata: Dict[str, Any] = field(default_factory=dict)

    def calculate_impact_score(self) -> int:
        """Calculate the overall impact score of this profile."""
        enabled_opts = [opt for opt in self.optimizations.values() if opt.enabled]
        if not enabled_opts:
            return 0
        return sum(opt.impact_level for opt in enabled_opts)

    def to_dict(self) -> Dict[str, Any]:
        """Convert profile to dictionary."""
        return {
            "name": self.name,
            "description": self.description,
            "target_hardware": self.target_hardware,
            "optimizations": {
                name: {
                    "name": opt.name,
                    "description": opt.description,
                    "enabled": opt.enabled,
                    "optimization_type": opt.optimization_type.name,
                    "parameters": opt.parameters,
                    "impact_level": opt.impact_level,
                }
                for name, opt in self.optimizations.items()
            },
            "is_active": self.is_active,
            "impact_score": self.calculate_impact_score(),
            "metadata": self.metadata,
        }

    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> "OptimizationProfile":
        """Create profile from dictionary."""
        optimizations = {}
        for name, opt_data in data.get("optimizations", {}).items():
            try:
                opt = OptimizationSetting(
                    name=opt_data["name"],
                    description=opt_data["description"],
                    enabled=opt_data["enabled"],
                    optimization_type=OptimizationType[opt_data["optimization_type"]],
                    parameters=opt_data.get("parameters", {}),
                    impact_level=opt_data.get("impact_level", 1),
                )
                optimizations[name] = opt
            except (KeyError, ValueError) as e:
                logger.warning(f"Invalid optimization data for {name}: {e}")

        return cls(
            name=data["name"],
            description=data["description"],
            target_hardware=data["target_hardware"],
            optimizations=optimizations,
            is_active=data.get("is_active", False),
            metadata=data.get("metadata", {}),
        )
