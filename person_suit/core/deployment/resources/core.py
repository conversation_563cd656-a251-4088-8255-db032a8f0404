"""Deployment Resources – Core

Stub module created to satisfy import test.  Real implementation will arrive in
Sprint-4 when Security Enforcement integrates resource provisioning.  Provides
minimal `DeploymentResource` base class used by other packages.
"""

from __future__ import annotations

from dataclasses import dataclass
from typing import Any
from typing import Dict


@dataclass
class DeploymentResource:  # noqa: D101 – simple placeholder
    name: str
    metadata: Dict[str, Any] | None = None

    def provision(self) -> None:  # noqa: D401 – placeholder method
        """Provision the resource (stub)."""
        # In real implementation this would allocate hardware/cloud resources
        pass

    def deprovision(self) -> None:  # noqa: D401 – placeholder
        """Deprovision the resource (stub)."""
        pass 