"""Dependency Injection Registration for Application Subsystem.

Registers application configuration, factory, and related services with the DI container.
Aligns with v0.3 unified paradigm and design documentation for modular, context-aware application orchestration.

References:
- docs/future/unified_paradigm/v0.3/unified_paradigm_v0.3.md
- docs/design/Context_Management_Design.md
"""

from ..infrastructure.dependency_injection import ServiceCollection
from .config import DefaultApplicationConfig
from .factory import create_application_config
from .interfaces.config import ApplicationConfig


def register_with_container(container: ServiceCollection) -> None:
    """Register application subsystem components with the DI container.

    Args:
        container: The DI service collection/container.
    """
    # Register ApplicationConfig (singleton)
    container.add_singleton(
        ApplicationConfig,
        DefaultApplicationConfig,
    )
    # Register application configuration factory (singleton)
    container.add_singleton(
        create_application_config,
        implementation_factory=lambda _: create_application_config,
    )
    # TODO: Register additional application-level services as needed
    # TODO: Support context/capability injection for advanced CAW scenarios
