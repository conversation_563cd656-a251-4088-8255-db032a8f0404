"""
from person_suit.core.constants.fixed_point_scale import <PERSON><PERSON>_HIGH, PRIO_CRITICAL
Handler for Security Alerts

This module provides handlers for security alert events, integrating
with the event bus to respond to security incidents. It demonstrates
context-aware processing and adaptive behavior.
"""
from __future__ import annotations

# -*- coding: utf-8 -*-
import asyncio
import logging
from typing import TYPE_CHECKING
from typing import Optional

from ..interfaces.events_interface import Event
from ..interfaces.events_interface import EventTopic
from ..interfaces.events_interface import IEventManager

# Forward reference import for UnifiedContext
if TYPE_CHECKING:
    from person_suit.core.context.unified import UnifiedContext

from ...infrastructure.contextual.core import ContextRegistry

# Infrastructure imports
from ...infrastructure.dependency_injection.decorators import inject
from ...infrastructure.dependency_injection.decorators import singleton
from ...infrastructure.monitoring.interfaces import AlertData
from ...infrastructure.monitoring.interfaces import AlertManagerInterface
from ...infrastructure.monitoring.interfaces import AlertSeverity
from ...infrastructure.monitoring.interfaces import AlertThresholdData

# Adjust import path for interfaces (now in sibling directory)


# Placeholder for removed CAW infrastructure (Processor, Adapter, Decorators)
# These functionalities need to be re-implemented using core CAW concepts or removed.
class Processor:
    pass  # Placeholder


class Adapter:
    pass  # Placeholder


def processor(topic):
    return lambda func: func  # Dummy decorator


def adaptive(threshold):
    return lambda func: func  # Dummy decorator


logger = logging.getLogger(__name__)

# We'll create the contexts at runtime to avoid circular imports
NORMAL_SECURITY_CONTEXT: Optional['UnifiedContext'] = None
ELEVATED_SECURITY_CONTEXT: Optional['UnifiedContext'] = None  
CRITICAL_SECURITY_CONTEXT: Optional['UnifiedContext'] = None

def _get_security_contexts():
    """Get or create security contexts at runtime."""
    global NORMAL_SECURITY_CONTEXT, ELEVATED_SECURITY_CONTEXT, CRITICAL_SECURITY_CONTEXT
    
    if NORMAL_SECURITY_CONTEXT is None:
        from person_suit.core.context.unified import UnifiedContext
        
        NORMAL_SECURITY_CONTEXT = UnifiedContext.create_default(
            domain="security_normal",
            priority=PRIO_HIGH,
            properties={
                "security_level": "normal",
                "alert_threshold": 3,
                "constraints": ["authenticated"],
                "tags": ["security", "alert"]
            }
        )

        ELEVATED_SECURITY_CONTEXT = UnifiedContext.create_default(
            domain="security_elevated",
            priority=PRIO_CRITICAL,
            properties={
                "security_level": "elevated",
                "alert_threshold": 2,
                "constraints": ["privileged", "authenticated"],
                "tags": ["security", "alert", "critical_incident"]
            }
        )

        CRITICAL_SECURITY_CONTEXT = UnifiedContext.create_default(
            domain="security_critical",
            priority=PRIO_CRITICAL,
            properties={
                "security_level": "critical", 
                "alert_threshold": 1,
                "constraints": ["immediate_action", "privileged"],
                "tags": ["security", "alert", "critical_incident", "compromise"]
            }
        )
    
    return NORMAL_SECURITY_CONTEXT, ELEVATED_SECURITY_CONTEXT, CRITICAL_SECURITY_CONTEXT


@singleton
class SecurityAlertHandler:
    """
    Context-aware handler for SecurityEvents that triggers alerts for severe ones.

    Uses context propagation and adaptive behavior to respond to security events
    based on the current security context, enabling nuanced and effective security responses.
    """

    # Different thresholds for different contexts
    SEVERITY_THRESHOLDS = {
        "security_normal": 3,  # Alert on severity 3 (ERROR) and 4 (CRITICAL) in normal context
        "security_elevated": 2,  # Alert on severity 2 (WARNING) and above in elevated context
        "security_critical": 1,  # Alert on all severities in critical context
    }

    @inject
    def __init__(
        self,
        event_manager: IEventManager,
        alert_manager: AlertManagerInterface,
        context_registry: Optional[ContextRegistry] = None,
    ):
        """
        Initialize the handler and subscribe to events.

        Args:
            event_manager: The main event manager/bus
            alert_manager: The alert manager to notify
            context_registry: Optional registry of contexts to use
        """
        self._event_manager = event_manager
        self._alert_manager = alert_manager
        self._context_registry = context_registry or ContextRegistry()
        self._processor = Processor()  # Using placeholder
        self._adapter = Adapter()  # Using placeholder
        self._subscribed = False
        
        # Initialize contexts at runtime
        normal_ctx, elevated_ctx, critical_ctx = _get_security_contexts()
        self._current_context = normal_ctx

        # Register contexts
        self._context_registry.register_context("security_normal", normal_ctx)
        self._context_registry.register_context("security_elevated", elevated_ctx)
        self._context_registry.register_context("security_critical", critical_ctx)
        self._context_registry.set_default_context(normal_ctx)

        logger.info("SecurityAlertHandler initialized (with placeholders).")

        # Defer subscription to an explicit start/initialize method if needed
        asyncio.create_task(self.subscribe())

    def _switch_to_context(self, context: 'UnifiedContext') -> None:
        """
        Switch to a different security context.

        Args:
            context: The context to switch to
        """
        if self._current_context.domain != context.domain:
            logger.info(
                f"Switching security context from {self._current_context.domain} to {context.domain}"
            )
            self._current_context = context

    async def subscribe(self) -> None:
        """Subscribe to SECURITY_EVENT topic on the event bus."""
        if not self._event_manager:
            logger.error(
                "EventManager not available, cannot subscribe SecurityAlertHandler."
            )
            return
        if self._subscribed:
            return

        try:
            # Subscribe the async handler method to the specific EventTopic
            # NOTE: The original subscribe call included context domain, which might not
            #       be supported by the IEventManager interface. Adjusting to standard subscribe.
            await self._event_manager.subscribe(
                EventTopic.SECURITY_EVENT,
                self._handle_security_event,
                # self._current_context.domain # Removed context from subscribe call
            )
            self._subscribed = True
            logger.info("SecurityAlertHandler subscribed to SECURITY_EVENT.")
        except Exception as e:
            logger.exception(f"Failed to subscribe SecurityAlertHandler: {e}")

    async def unsubscribe(self) -> None:
        """Unsubscribe from events."""
        if not self._event_manager or not self._subscribed:
            return
        try:
            await self._event_manager.unsubscribe(
                EventTopic.SECURITY_EVENT,
                self._handle_security_event,
                # self._current_context.domain # Removed context from unsubscribe call
            )
            self._subscribed = False
            logger.info("SecurityAlertHandler unsubscribed from SECURITY_EVENT.")
        except Exception as e:
            logger.exception(f"Failed to unsubscribe SecurityAlertHandler: {e}")

    # Remove @caw_processor decorator (using placeholder)
    async def _handle_security_event(self, event: Event) -> None:
        """
        Handle incoming security events.

        Args:
            event: The security event to handle
        """
        # The payload is now inside event.data.infom.particle.data
        if not hasattr(event.data, 'infom') or not hasattr(event.data.infom, 'particle'):
             logger.warning(
                "Received event on SECURITY_EVENT topic with malformed data structure."
            )
             return
        
        security_payload = event.data.infom.particle.data
        if not isinstance(security_payload, dict):
            logger.warning(
                f"Received event on SECURITY_EVENT topic with unexpected payload type: {type(security_payload)}"
            )
            return

        severity = security_payload.get("severity", 0)
        event_type_str = security_payload.get("event_type", "unknown")
        details = security_payload.get("details", {})

        logger.debug(
            f"Received Security Event: {event.event_id} ({event_type_str}), Severity: {severity}"
        )

        if not self._alert_manager:
            logger.error(
                "AlertManager not available, cannot process security event for alerting."
            )
            return

        # Get the threshold for the current internal context
        threshold = self.SEVERITY_THRESHOLDS.get(self._current_context.domain, 3)

        if severity >= threshold:
            logger.warning(
                f"Security Event detected ({severity}) in context {self._current_context.domain}, "
                f"generating alert: {event.event_id}"
            )
            try:
                alert_severity = AlertSeverity(severity)
            except ValueError:
                logger.warning(
                    f"Could not map security severity {severity} to AlertSeverity. Defaulting to ERROR."
                )
                alert_severity = AlertSeverity.ERROR

            threshold_info = AlertThresholdData(
                metric_name=f"security.alert.{event_type_str}",
                operator="EVENT",
                threshold_value=severity,
                severity=alert_severity,
                description_template="Security Event: {details[message]}",
            )

            alert_data = AlertData(
                id=f"sec_{event.event_id}",
                metric_name=threshold_info.metric_name,
                value=severity,
                threshold=threshold_info,
                timestamp=event.timestamp,
                status="active",
                severity=alert_severity,
                description=details.get(
                    "message", f"Security Event {event_type_str} occurred."
                ),
                details={
                    **details,
                    "context": self._current_context.domain,
                    "threshold": threshold,
                },
            )
            try:
                self._alert_manager.notify_alert(alert_data)
                logger.info(
                    f"Notified AlertManager about Security Event {event.event_id} in context {self._current_context.domain}."
                )
            except Exception as e:
                logger.exception(
                    f"Failed to notify AlertManager for Security Event {event.event_id}: {e}"
                )
        else:
            logger.debug(
                f"Ignoring Security Event {event.event_id} with severity {severity} "
                f"(below threshold {threshold} for context {self._current_context.domain})."
            )

        # Adapt the handler's internal context based on the event
        await self._adapt_internal_context(severity)

    # Remove @caw_adaptive decorator (using placeholder)
    async def _adapt_internal_context(self, severity: int) -> None:
        """
        Adapt the handler's internal context based on event severity.

        Args:
            severity: The severity of the triggering event.
        """
        # Get contexts at runtime
        normal_ctx, elevated_ctx, critical_ctx = _get_security_contexts()
        
        # Determine new context based on severity
        if severity >= 4:  # CRITICAL
            new_context = critical_ctx
        elif severity >= 3:  # ERROR
            new_context = elevated_ctx
        else:
            new_context = normal_ctx

        # Switch internal context state if needed
        if new_context.domain != self._current_context.domain:
            self._switch_to_context(new_context)
            logger.info(
                f"Adapted internal security context to {new_context.domain} "
                f"based on event severity {severity}. Alert threshold is now {self.SEVERITY_THRESHOLDS.get(new_context.domain, 3)}."
            )
