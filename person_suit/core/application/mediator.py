# -*- coding: utf-8 -*-
"""
Mediator Pattern Implementation.

Facilitates communication between components without direct coupling,
often used for handling commands or requests that have a single handler.
"""

import asyncio
import logging
from typing import Any
from typing import Callable
from typing import Coroutine
from typing import Dict
from typing import Optional
from typing import Type
from typing import TypeVar

logger = logging.getLogger(__name__)

# Type Variables and Aliases
T_Request = TypeVar("T_Request")
T_Response = TypeVar("T_Response")
RequestHandler = Callable[[T_Request], Coroutine[Any, Any, T_Response]]


class Mediator:
    """Handles routing requests/commands to their appropriate handlers."""

    def __init__(self):
        self._handlers: Dict[Type, RequestHandler] = {}
        logger.info("Mediator initialized.")

    async def register_handler(
        self,
        request_type: Type[T_Request],
        handler: RequestHandler[T_Request, T_Response],
    ):
        """Register a handler for a specific request type."""
        if not asyncio.iscoroutinefunction(handler):
            raise TypeError("Handler must be an async function")
        if request_type in self._handlers:
            logger.warning(
                f"Handler for request type {request_type.__name__} is being overwritten."
            )
        self._handlers[request_type] = handler
        logger.debug(
            f"Handler {handler.__name__} registered for request type {request_type.__name__}"
        )

    async def send(self, request: T_Request) -> T_Response:
        """Sends a request to its registered handler."""
        request_type = type(request)
        handler = self._handlers.get(request_type)

        if handler:
            logger.debug(
                f"Sending request {request_type.__name__} to handler {handler.__name__}"
            )
            try:
                response = await handler(request)
                return response
            except Exception as e:
                logger.error(
                    f"Error executing handler {handler.__name__} for request {request_type.__name__}: {e}",
                    exc_info=True,
                )
                # Re-raise or return an error response
                raise  # Re-raise for now
        else:
            logger.error(
                f"No handler registered for request type {request_type.__name__}"
            )
            raise LookupError(f"No handler found for {request_type.__name__}")


# Singleton pattern (optional, DI is often preferred)
_mediator_instance: Optional[Mediator] = None


def get_mediator() -> Mediator:
    """Provides access to the singleton Mediator instance."""
    global _mediator_instance
    if _mediator_instance is None:
        _mediator_instance = Mediator()
    return _mediator_instance
