"""
File: person_suit/core/application/messages.py
Purpose: Person Suit - Toolbox Communication Messages (TB-1.2)

NOTE: This file was moved from core/application/interfaces/communication/ during flattening.
      Imports may need adjustment in files that used the old path.

This module defines the message formats and protocols for cross-toolbox communication.
It provides standardized message structures, serialization, and validation for all
communication between toolbox components.
"""

import json
import logging
import uuid
from dataclasses import dataclass
from dataclasses import field
from datetime import datetime
from enum import Enum
from enum import auto
from typing import Any
from typing import Dict
from typing import Generic
from typing import Optional
from typing import Type
from typing import TypeVar

from person_suit.core.constants.fixed_point_scale import PRIO_HIGH
from person_suit.core.constants.fixed_point_scale import PRIO_NORMAL

# Setup logging
logger = logging.getLogger(__name__)

# Type variables
T = TypeVar("T")
P = TypeVar("P")
R = TypeVar("R")


class MessageType(Enum):
    """Defines the types of messages for toolbox communication."""

    REQUEST = auto()  # Request to perform an operation
    RESPONSE = auto()  # Response to a request
    EVENT = auto()  # Asynchronous event notification
    ERROR = auto()  # Error notification
    CONTROL = auto()  # Control message for communication management


@dataclass
class MessageHeader:
    """
    Header information for a toolbox communication message.

    Contains metadata about the message, its routing, and security information.
    """

    message_id: str = field(default_factory=lambda: str(uuid.uuid4()))
    message_type: MessageType = MessageType.REQUEST
    sender_id: str = ""
    receiver_id: Optional[str] = None  # None for broadcasts
    correlation_id: Optional[str] = None
    timestamp: datetime = field(default_factory=datetime.now)
    priority: int = PRIO_NORMAL  # Changed from MessagePriority to int
    ttl: int = 30  # Time-to-live in seconds
    secure: bool = False
    headers: Dict[str, Any] = field(default_factory=dict)

    def to_dict(self) -> Dict[str, Any]:
        """Convert the header to a dictionary representation."""
        return {
            "message_id": self.message_id,
            "message_type": self.message_type.name,
            "sender_id": self.sender_id,
            "receiver_id": self.receiver_id,
            "correlation_id": self.correlation_id,
            "timestamp": self.timestamp.isoformat(),
            "priority": self.priority,
            "ttl": self.ttl,
            "secure": self.secure,
            "headers": self.headers,
        }

    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> "MessageHeader":
        """Create a header from a dictionary representation."""
        # Convert string representations back to enums
        message_type = MessageType[data["message_type"]]
        # Priority is always an integer
        priority = data.get("priority", PRIO_NORMAL)
        if not isinstance(priority, int):
            priority = PRIO_NORMAL

        # Parse timestamp
        timestamp = datetime.fromisoformat(data["timestamp"])

        return cls(
            message_id=data["message_id"],
            message_type=message_type,
            sender_id=data["sender_id"],
            receiver_id=data["receiver_id"],
            correlation_id=data["correlation_id"],
            timestamp=timestamp,
            priority=priority,
            ttl=data["ttl"],
            secure=data["secure"],
            headers=data["headers"],
        )


@dataclass
class Message(Generic[P]):
    """
    Base class for all toolbox communication messages.

    Provides common functionality for message creation, serialization,
    and validation.
    """

    header: MessageHeader
    payload: Optional[P] = None

    def to_dict(self) -> Dict[str, Any]:
        """Convert the message to a dictionary representation."""
        result: Dict[str, Any] = {"header": self.header.to_dict()}

        # Include payload if present
        if self.payload is not None:
            if hasattr(self.payload, "to_dict") and callable(getattr(self.payload, "to_dict", None)):
                result["payload"] = self.payload.to_dict()  # type: ignore[attr-defined]
            else:
                result["payload"] = self.payload

        return result

    def to_json(self) -> str:
        """Serialize the message to JSON."""
        return json.dumps(self.to_dict())

    @classmethod
    def from_dict(
        cls, data: Dict[str, Any], payload_type: Optional[Type] = None
    ) -> "Message":
        """Create a message from a dictionary representation."""
        header = MessageHeader.from_dict(data["header"])

        # Process payload if present
        payload = None
        if "payload" in data and data["payload"] is not None:
            if payload_type is not None and hasattr(payload_type, "from_dict"):
                # Use the payload_type's from_dict method if available
                payload = payload_type.from_dict(data["payload"])
            else:
                # Otherwise, use the raw payload
                payload = data["payload"]

        return cls(header=header, payload=payload)

    @classmethod
    def from_json(cls, json_str: str, payload_type: Optional[Type] = None) -> "Message":
        """Create a message from a JSON string."""
        data = json.loads(json_str)
        return cls.from_dict(data, payload_type)

    def create_response(self, payload: Any = None) -> "Message":
        """
        Create a response message for this message.

        Args:
            payload: The payload for the response

        Returns:
            A new message with appropriate correlation
        """
        return Message(
            header=MessageHeader(
                message_type=MessageType.RESPONSE,
                sender_id=self.header.receiver_id or "",
                receiver_id=self.header.sender_id,
                correlation_id=self.header.message_id,
                priority=self.header.priority,
            ),
            payload=payload,
        )

    def create_error_response(
        self, error_message: str, error_code: str = "UNKNOWN"
    ) -> "Message":
        """
        Create an error response for this message.

        Args:
            error_message: Description of the error
            error_code: Error code identifier

        Returns:
            A new error message with appropriate correlation
        """
        return Message(
            header=MessageHeader(
                message_type=MessageType.ERROR,
                sender_id=self.header.receiver_id or "",
                receiver_id=self.header.sender_id,
                correlation_id=self.header.message_id,
                priority=self.header.priority,
            ),
            payload={"error_code": error_code, "error_message": error_message},
        )


@dataclass
class RequestMessage(Message[P]):
    """
    A message representing a request to perform an operation.

    Carries a payload with the request parameters and metadata
    for tracking and correlation.
    """

    def __post_init__(self):
        """Ensure request-specific attributes are set."""
        self.header.message_type = MessageType.REQUEST


@dataclass
class ResponseMessage(Message[R]):
    """
    A message representing a response to a request.

    Carries the response data and maintains correlation with
    the original request.
    """

    def __post_init__(self):
        """Ensure response-specific attributes are set."""
        self.header.message_type = MessageType.RESPONSE


@dataclass
class EventMessage(Message[Any]):
    """
    A message representing an asynchronous event notification.

    Used for broadcasting events to interested components without
    expecting a response.
    """

    event_type: str = ""

    def __post_init__(self):
        """Ensure event-specific attributes are set."""
        self.header.message_type = MessageType.EVENT

        # Store event type in header for filtering
        if self.event_type:
            self.header.headers["event_type"] = self.event_type


@dataclass
class ErrorMessage(Message[Any]):
    """
    A message representing an error condition.

    Used to communicate errors during operations or communication.
    """

    error_code: str = "UNKNOWN"
    error_message: str = ""

    def __post_init__(self):
        """Ensure error-specific attributes are set."""
        self.header.message_type = MessageType.ERROR

        # Initialize payload with error information
        if not self.payload:
            self.payload = {
                "error_code": self.error_code,
                "error_message": self.error_message,
            }
        elif isinstance(self.payload, dict):
            # Extract error info from payload if present
            self.error_code = self.payload.get("error_code", self.error_code)
            self.error_message = self.payload.get("error_message", self.error_message)


def create_request(
    sender_id: str,
    receiver_id: str,
    payload: Any = None,
    priority: int = PRIO_NORMAL,
) -> RequestMessage:
    """
    Create a new request message.

    Args:
        sender_id: ID of the sending component
        receiver_id: ID of the receiving component
        payload: Request payload
        priority: Message priority

    Returns:
        A new request message
    """
    header = MessageHeader(
        message_type=MessageType.REQUEST,
        sender_id=sender_id,
        receiver_id=receiver_id,
        priority=priority,
    )
    return RequestMessage(header=header, payload=payload)


def create_event(
    sender_id: str,
    event_type: str,
    payload: Any = None,
    priority: int = PRIO_NORMAL,
) -> EventMessage:
    """
    Create a new event message.

    Args:
        sender_id: ID of the sending component
        event_type: Type of event being published
        payload: Event data
        priority: Message priority

    Returns:
        A new event message
    """
    header = MessageHeader(
        message_type=MessageType.EVENT,
        sender_id=sender_id,
        receiver_id=None,  # Events are broadcast
        priority=priority,
        headers={"event_type": event_type},
    )
    return EventMessage(header=header, payload=payload, event_type=event_type)


def create_error(
    sender_id: str,
    receiver_id: Optional[str],
    error_code: str,
    error_message: str,
    correlation_id: Optional[str] = None,
    priority: int = PRIO_HIGH,
) -> ErrorMessage:
    """
    Create a new error message.

    Args:
        sender_id: ID of the sending component
        receiver_id: ID of the receiving component, or None for broadcast
        error_code: Error code identifier
        error_message: Description of the error
        correlation_id: ID of the related message, if any
        priority: Message priority

    Returns:
        A new error message
    """
    header = MessageHeader(
        message_type=MessageType.ERROR,
        sender_id=sender_id,
        receiver_id=receiver_id,
        correlation_id=correlation_id,
        priority=priority,
    )
    return ErrorMessage(
        header=header, error_code=error_code, error_message=error_message
    )
