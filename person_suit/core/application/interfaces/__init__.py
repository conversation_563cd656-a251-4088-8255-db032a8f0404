# -*- coding: utf-8 -*-
"""
Core Application Interfaces.

This package provides core application-level interfaces for the Person Suit framework.
All interfaces here support the CAW architecture and dependency inversion principles.
"""

# Communication interfaces
from .communication_interface import IChannelManager
from .communication_interface import ICommunicator

# Context interfaces
from .config import ApplicationConfig

# Configuration interfaces  
from .config_interface import IConfigManager

# Error handling interfaces
from .error_handling_interface import ErrorManagerInterface

# Event provider interfaces
from .event_provider_interfaces import IEventCapabilityProvider
from .event_provider_interfaces import IEventCryptoProvider
from .event_provider_interfaces import IEventPersistenceProvider

# Event interfaces
from .events_interface import BaseEvent
from .events_interface import EventCallback
from .events_interface import EventTopic
from .events_interface import IEventManager

# Registration interfaces
from .registration_interface import IRegistry

__all__ = [
    # Communication
    "IChannelManager",
    "ICommunicator",
    
    # Configuration
    "IConfigManager",
    
    # Error handling
    "ErrorManagerInterface",
    
    # Events
    "IEventManager",
    "BaseEvent",
    "EventCallback",
    "EventTopic",
    
    # Event providers
    "IEventCapabilityProvider", 
    "IEventCryptoProvider",
    "IEventPersistenceProvider",
    
    # Registration
    "IRegistry",
    
    # Context
    "ApplicationConfig",
] 