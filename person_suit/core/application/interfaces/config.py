"""Configuration Management Interface.

This module provides the IConfigManager interface for dependency injection.
This is the main interface that should be imported for configuration management.
"""

from abc import ABC
from abc import abstractmethod
from typing import Any
from typing import Dict

from .config_interface import IConfigManager


class ApplicationConfig(ABC):
    """Abstract base class for application configuration."""
    
    @abstractmethod
    def get_setting(self, key: str, default: Any = None) -> Any:
        """Get a configuration setting by key."""
        pass
    
    @abstractmethod
    def get_all_settings(self) -> Dict[str, Any]:
        """Get all configuration settings."""
        pass
    
    @abstractmethod
    def update_setting(self, key: str, value: Any) -> None:
        """Update a configuration setting."""
        pass


# Re-export the interface for easier imports
__all__ = ["IConfigManager", "ApplicationConfig"]
