"""
File: person_suit/core/application/interfaces/error_handling_interface.py
Purpose: Interfaces for the Error Handling System.

NOTE: This file was moved from core/application/ during de-flattening.
"""

from abc import ABC
from abc import abstractmethod
from dataclasses import dataclass
from dataclasses import field
from datetime import datetime
from enum import Enum
from typing import Any
from typing import Callable
from typing import Dict
from typing import List
from typing import Optional

# --- Forward References / Data Structures --- #
# Define data structures used by the interface methods
# These should ideally be defined centrally if used across modules,
# but defining here for interface clarity.


class ErrorSeverity(Enum):  # Placeholder
    DEBUG = 1
    INFO = 2
    WARNING = 3
    ERROR = 4
    CRITICAL = 5


class ErrorCategory(Enum):  # Placeholder
    UNKNOWN = 1


class RecoveryStrategy(Enum):  # Placeholder
    PROPAGATE = 1


@dataclass
class ErrorState:
    """Rich state information for errors."""

    timestamp: datetime = field(default_factory=datetime.now)
    operation: str = "unknown"
    component: str = "unknown"
    user_id: Optional[str] = None
    request_id: Optional[str] = None
    correlation_id: Optional[str] = None
    parameters: Dict[str, Any] = field(default_factory=dict)
    system_state: Dict[str, Any] = field(default_factory=dict)
    stack_trace: Optional[str] = None
    related_errors: List["Error"] = field(default_factory=list)


@dataclass
class Error:
    """Standardized error representation."""

    code: str
    message: str
    exception: Optional[Exception] = None
    category: ErrorCategory = ErrorCategory.UNKNOWN
    severity: ErrorSeverity = ErrorSeverity.INFO
    context: ErrorState = field(default_factory=ErrorState)
    recovery_strategy: RecoveryStrategy = RecoveryStrategy.PROPAGATE
    is_expected: bool = False
    is_recoverable: bool = True
    is_reported: bool = False
    is_handled: bool = False
    handler_id: Optional[str] = None


class ErrorInfo:
    # Simplified placeholder based on ErrorManager usage
    exception: Exception
    context: ErrorState
    category: ErrorCategory
    severity: ErrorSeverity
    recovery_strategy: RecoveryStrategy
    traceback: str
    error_id: str


# Type alias for subscribers
ErrorSubscriberCallable = Callable[[ErrorInfo], None]

# --- Interface Definition --- #


class ErrorManagerInterface(ABC):
    """Interface for the central error management component."""

    @abstractmethod
    def register_subscriber(self, subscriber: ErrorSubscriberCallable) -> None:
        """Register a function to be called when an error is handled."""
        pass

    @abstractmethod
    def unregister_subscriber(self, subscriber: ErrorSubscriberCallable) -> None:
        """Unregister an error subscriber."""
        pass

    @abstractmethod
    def handle_error(
        self,
        exception: Exception,
        context: Optional[ErrorState] = None,
        parent_error_id: Optional[str] = None,
        translate_to_domain: Optional[str] = None,
        **context_kwargs,
    ) -> ErrorInfo:
        """Handle, classify, log, and store an error, returning error details."""
        pass

    @abstractmethod
    async def recover(
        self,
        error_info: ErrorInfo,
        retry_func: Optional[Callable] = None,
        fallback_value: Any = None,
    ) -> Any:
        """Attempt to recover from a handled error based on its configured strategy."""
        pass

    @abstractmethod
    def get_error(self, error_id: str) -> Optional[ErrorInfo]:
        """Retrieve detailed information about a specific error by its ID."""
        pass

    @abstractmethod
    def get_recent_errors(self, limit: int = 100) -> List[ErrorInfo]:
        """Get a list of the most recently handled errors."""
        pass

    # Context managers might be harder to define abstractly, but can be done.
    # Alternatively, leave them on the concrete class if they are mainly convenience.
    # @abstractmethod
    # @contextmanager
    # def capture_errors(self, operation: str, parent_error_id: Optional[str] = None, **context_kwargs):
    #     """Context manager for capturing synchronous errors."""
    #     pass
    #
    # @abstractmethod
    # @asynccontextmanager
    # async def capture_errors_async(self, operation: str, parent_error_id: Optional[str] = None, **context_kwargs):
    #     """Async context manager for capturing asynchronous errors."""
    #     pass

    # Configuration methods - potentially omit from interface if config is done centrally
    # @abstractmethod
    # def register_type(...)
    # @abstractmethod
    # def register_pattern(...)
    # @abstractmethod
    # def register_strategy(...)

    # Translation - Include if consumers need it, otherwise maybe internal detail
    # @abstractmethod
    # def translate_error(self, error_id: str, target_domain: str) -> Optional[str]:
    #     pass
