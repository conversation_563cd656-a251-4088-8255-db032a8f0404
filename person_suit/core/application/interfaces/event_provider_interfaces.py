"""
File: person_suit/core/application/interfaces/event_provider_interfaces.py
Purpose: Defines interfaces for pluggable providers used by the Event Bus.

NOTE: This file was moved from core/application/ during de-flattening.

This module specifies the contracts for components providing services like
event persistence, cryptographic operations, and capability management,
allowing the Event Bus implementation to be extended via Dependency Injection.

Related Files:
- events_interface.py: Defines the core event structures.
- ../../meta_systems/persona_core/folded_mind/event_bus_impl.py: Consumes these interfaces.

Dependencies:
- typing
- .events_interface (BaseEvent, Capability, CryptoMetadata)
"""

from __future__ import annotations

import abc
from typing import TYPE_CHECKING
from typing import Any
from typing import AsyncGenerator
from typing import Dict
from typing import List
from typing import Optional
from typing import Protocol
from typing import runtime_checkable

# Adjust import path after flattening/renaming
if TYPE_CHECKING:
    # Assuming events_interface.py is in the same interfaces/ directory
    from .events_interface import BaseEvent
    from .events_interface import Capability
    from .events_interface import CryptoMetadata


@runtime_checkable
class IEventPersistenceProvider(Protocol):
    """Interface for providers that handle event persistence and retrieval."""

    @abc.abstractmethod
    async def initialize(self, config: Optional[Dict[str, Any]] = None) -> None:
        """Initialize the persistence provider (e.g., connect to DB)."""
        raise NotImplementedError

    @abc.abstractmethod
    async def save_event(self, event: "BaseEvent") -> None:
        """Persists a single event."""
        raise NotImplementedError

    @abc.abstractmethod
    async def save_events_batch(self, events: List["BaseEvent"]) -> None:
        """Persists a batch of events efficiently."""
        raise NotImplementedError

    @abc.abstractmethod
    async def retrieve_events(
        self,
        topic: Optional[str] = None,
        event_type: Optional[str] = None,
        start_time: Optional[float] = None,  # Unix timestamp
        end_time: Optional[float] = None,  # Unix timestamp
        limit: Optional[int] = None,
        correlation_id: Optional[str] = None,
    ) -> AsyncGenerator["BaseEvent", None]:
        """Retrieves persisted events based on criteria."""
        # Must be an async generator
        raise NotImplementedError
        yield  # Required for async generator protocol check

    @abc.abstractmethod
    async def shutdown(self) -> None:
        """Clean up resources (e.g., close DB connection)."""
        raise NotImplementedError


@runtime_checkable
class IEventCryptoProvider(Protocol):
    """Interface for providers handling cryptographic operations on events."""

    @abc.abstractmethod
    async def initialize(self, config: Optional[Dict[str, Any]] = None) -> None:
        """Initialize the crypto provider (e.g., load keys)."""
        raise NotImplementedError

    @abc.abstractmethod
    async def sign_event_data(self, event_data: Any) -> "CryptoMetadata":
        """
        Generates a signature for the event data payload.
        Returns metadata including the algorithm used and the signature.
        """
        raise NotImplementedError

    @abc.abstractmethod
    async def verify_event_data(
        self, event_data: Any, crypto_metadata: "CryptoMetadata"
    ) -> bool:
        """
        Verifies the signature of the event data using the provided metadata.
        Returns True if the signature is valid, False otherwise.
        """
        raise NotImplementedError

    @abc.abstractmethod
    async def get_available_algorithms(self) -> List[str]:
        """Returns a list of supported signing/verification algorithms."""
        raise NotImplementedError

    @abc.abstractmethod
    async def shutdown(self) -> None:
        """Clean up crypto resources if necessary."""
        raise NotImplementedError


@runtime_checkable
class IEventCapabilityProvider(Protocol):
    """Interface for providers managing and verifying capabilities for events."""

    @abc.abstractmethod
    async def initialize(self, config: Optional[Dict[str, Any]] = None) -> None:
        """Initialize the capability provider."""
        raise NotImplementedError

    @abc.abstractmethod
    async def verify_publish_capability(
        self, capability: Optional["Capability"]
    ) -> bool:
        """Checks if the provided capability grants permission to publish events."""
        raise NotImplementedError

    @abc.abstractmethod
    async def verify_handle_capability(
        self, handler_identity: Any, required_capabilities: Optional[List["Capability"]]
    ) -> bool:
        """
        Checks if the handler (identified by handler_identity) possesses
        the required capabilities to handle an event.
        Returns True if requirements are met or if no capabilities are required.
        """
        raise NotImplementedError

    @abc.abstractmethod
    async def shutdown(self) -> None:
        """Clean up capability provider resources if necessary."""
        raise NotImplementedError
