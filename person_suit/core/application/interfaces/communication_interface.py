"""
File: person_suit/core/application/interfaces/communication_interface.py
Purpose: Person Suit - Core Application Communication Interfaces
-----------------------------------------------------

Defines the abstract interfaces and data structures for inter-component
communication within the Person Suit application layer.

NOTE: This file was moved from core/application/ during de-flattening.

This module implements a secure, capability-based communication system with
effect tracking and telemetry. It follows the zero-trust architecture principles
and provides cryptographic verification for all messages.

This includes:
- Message structures (Headers, Payloads, Types)
- Channel management interfaces with security capabilities
- Event bus interfaces with audit trails
- Secure communication patterns

These interfaces define the contracts that concrete communication implementations
(e.g., in the infrastructure layer) must adhere to.

Security Features:
- End-to-end encryption for all messages
- Capability-based access control for channels
- Message authentication and integrity verification
- Audit logging for all communication operations
- Protection against replay attacks

Related Files:
- person_suit/core/infrastructure/communication/manager.py: Implementation
- person_suit/core/infrastructure/security/communication/: Security components
- person_suit/core/infrastructure/effects/: Effect system integration
"""

import base64
import hashlib
import time
import uuid
from concurrent.futures import Future
from dataclasses import dataclass
from dataclasses import field
from enum import Enum
from enum import auto
from typing import Any
from typing import Callable
from typing import Coroutine
from typing import Dict
from typing import Generic
from typing import List
from typing import Optional
from typing import Protocol
from typing import TypeVar

from person_suit.core.constants.fixed_point_scale import PRIO_NORMAL

# --- Message Structures ---


class MessageType(Enum):
    """Defines the type of a message."""

    REQUEST = auto()
    RESPONSE = auto()
    EVENT = auto()
    ERROR = auto()
    COMMAND = auto()  # Example: For command pattern
    NOTIFICATION = auto()  # Example: One-way info


# ---------------------------------------------------------------------------
# Priority Handling – fixed-point buckets only
# ---------------------------------------------------------------------------
# Historically the application layer exposed a ``MessagePriority`` Enum.  The
# entire PersonSuit stack now standardises on the µ-resolution buckets defined
# in ``core/constants/fixed_point_scale.py`` (``PRIO_LOW``, ``PRIO_NORMAL``,
# ``PRIO_HIGH``, ``PRIO_CRITICAL``).  Any enum abstractions have been removed
# from this interface to avoid dual representations.  Callers MUST pass one of
# those integer constants when specifying priority.


@dataclass(frozen=True)
class MessageHeader:
    """Standard header for all messages with security features."""

    message_id: str = field(default_factory=lambda: str(uuid.uuid4()))
    message_type: MessageType = MessageType.NOTIFICATION
    source_id: str = ""  # ID of the sending component/service
    target_id: Optional[str] = None  # ID of the target (None for broadcast/events)
    correlation_id: Optional[str] = (
        None  # To correlate requests/responses or related events
    )
    timestamp: float = field(default_factory=time.time)  # Current time
    priority: int = PRIO_NORMAL
    version: str = "1.0"  # Protocol version

    # Security fields
    signature: Optional[str] = None  # Cryptographic signature of the message
    encryption_id: Optional[str] = None  # ID of the encryption key/certificate used
    auth_token: Optional[str] = None  # Authentication token for zero-trust verification
    capability_id: Optional[str] = (
        None  # ID of the capability token authorizing this message
    )
    nonce: Optional[str] = None  # Unique value to prevent replay attacks
    ttl: Optional[float] = None  # Time-to-live in seconds for message validity


# Generic Type Variable for message payloads
PayloadType = TypeVar("PayloadType")


@dataclass(frozen=True)
class Message(Generic[PayloadType]):
    """Generic base message structure."""

    header: MessageHeader
    payload: PayloadType


@dataclass(frozen=True)
class RequestMessage(Message[PayloadType]):
    """Specific structure for request messages."""

    header: MessageHeader  # Ensure header.message_type is REQUEST
    # Add request-specific fields if needed (e.g., timeout_hint)


@dataclass(frozen=True)
class ResponseMessage(Message[PayloadType]):
    """Specific structure for response messages."""

    header: MessageHeader  # Ensure header.message_type is RESPONSE
    # Add response-specific fields (e.g., status_code)


@dataclass(frozen=True)
class ErrorMessage(Message[PayloadType]):
    """Specific structure for error messages."""

    header: MessageHeader  # Ensure header.message_type is ERROR
    error_code: Optional[str] = None
    error_details: Optional[str] = None


@dataclass(frozen=True)
class EventMessage(Message[PayloadType]):
    """Specific structure for event messages."""

    header: MessageHeader  # Ensure header.message_type is EVENT
    event_type: str = ""  # Type of event being broadcast

    def verify_signature(self) -> bool:
        """Verify the cryptographic signature of this message."""
        if not self.header.signature:
            return False

        # In a real implementation, this would use proper cryptographic verification
        # For this example, we'll use a simple hash verification
        h = hashlib.sha256()
        h.update(self.header.message_id.encode())
        h.update(self.header.message_type.name.encode())
        h.update(self.header.source_id.encode())
        if self.header.target_id:
            h.update(self.header.target_id.encode())
        h.update(str(self.header.timestamp).encode())
        h.update(str(self.payload).encode())
        h.update(self.event_type.encode())

        expected = base64.b64encode(h.digest()).decode()
        return self.header.signature == expected


# --- Channel Interfaces ---


class ChannelType(Enum):
    """Defines the types of communication channels."""

    DIRECT = auto()
    BROADCAST = auto()
    REQUEST_REPLY = auto()
    PUBSUB = auto()


class ChannelState(Enum):
    """Defines the possible states of a communication channel."""

    INIT = auto()
    OPEN = auto()
    CLOSING = auto()
    CLOSED = auto()
    ERROR = auto()


@dataclass
class ChannelMetadata:
    """Abstract representation of channel metadata."""

    channel_id: str
    channel_type: ChannelType
    source_id: str
    target_id: Optional[str]
    state: ChannelState
    # Add other relevant metadata fields as needed


T = TypeVar("T")  # Generic type for channel messages


class IChannel(Protocol[T]):
    """Interface for a secure communication channel."""

    @property
    def metadata(self) -> ChannelMetadata:
        """Get the channel's metadata."""
        ...

    def open(self) -> None:
        """Open the channel for communication."""
        ...

    def close(self) -> None:
        """Close the channel."""
        ...

    def send(self, message: Message[T]) -> None:
        """Send a message through the channel (asynchronous by nature)."""
        ...

    async def send_async(self, message: Message[T]) -> None:
        """Explicitly asynchronous send operation."""
        ...

    def send_request(self, message: RequestMessage[T]) -> Future:
        """Send a request message and return a future for the response."""
        ...

    def authenticate(self) -> bool:
        """Authenticate the channel with the peer."""
        ...

    def verify_peer(self) -> bool:
        """Verify the identity of the peer."""
        ...

    def is_secure(self) -> bool:
        """Check if the channel is secure (encrypted and authenticated)."""
        ...

    def encrypt_data(self, data: Any) -> bytes:
        """Encrypt data for transmission."""
        ...

    def decrypt_data(self, encrypted_data: bytes) -> Any:
        """Decrypt received data."""
        ...

    def refresh_security(self) -> bool:
        """Refresh security parameters (e.g., rotate session keys)."""
        ...


class IChannelManager(Protocol):
    """Interface for managing secure communication channels."""

    def create_channel(
        self,
        source_id: str,
        target_id: Optional[str] = None,
        channel_type: ChannelType = ChannelType.DIRECT,
        auto_open: bool = True,
        secure: bool = True,  # Default to secure channels
        encryption_level: Optional[str] = None,  # Encryption level (e.g., 'AES256')
        capability_token: Optional[str] = None,  # Capability token for authorization
        **properties,
    ) -> IChannel:
        """Create and return a new secure communication channel."""
        ...

    def get_channel(self, channel_id: str) -> Optional[IChannel]:
        """Get a channel by its ID."""
        ...

    def get_channels_for_component(self, component_id: str) -> List[IChannel]:
        """Get all channels associated with a component."""
        ...

    def close_channel(self, channel_id: str) -> None:
        """Close a specific channel."""
        ...

    def close_component_channels(self, component_id: str) -> None:
        """Close all channels associated with a component."""
        ...

    def get_telemetry(self) -> Dict[str, Any]:
        """Get telemetry data for the channel manager."""
        ...

    def verify_channel_security(self, channel_id: str) -> bool:
        """Verify the security of a channel."""
        ...

    def rotate_channel_keys(self, channel_id: str) -> bool:
        """Rotate encryption keys for a channel."""
        ...

    def audit_channel_activity(self, channel_id: str) -> List[Dict[str, Any]]:
        """Get audit logs for a channel."""
        ...

    def create_secure_group(self, group_name: str, component_ids: List[str]) -> str:
        """Create a secure communication group."""
        ...


# --- Event Bus Interface (Potentially overlaps/integrates with IEventManager) ---

# Reusing EventMessage defined above
EventBusCallback = Callable[[EventMessage], Coroutine[Any, Any, None]]

# --- Communication API Abstraction (Optional) ---


class ICommunicator(Protocol):
    """High-level interface abstracting secure communication patterns."""

    async def send_message_async(
        self,
        target_id: str,
        message_payload: Any,
        source_id: Optional[str] = None,  # Can be inferred in implementation
        priority: int = PRIO_NORMAL,
        message_type: MessageType = MessageType.NOTIFICATION,
        secure: bool = True,  # Default to secure communication
        encryption_level: Optional[str] = None,  # Encryption level (e.g., 'AES256')
        capability_token: Optional[str] = None,  # Capability token for authorization
        ttl: Optional[float] = None,  # Time-to-live in seconds
    ) -> None:
        """Send a one-way message asynchronously with security features."""
        ...

    async def send_request_async(
        self,
        target_id: str,
        request_payload: Any,
        source_id: Optional[str] = None,
        timeout: float = 30.0,
        priority: int = PRIO_NORMAL,
        secure: bool = True,  # Default to secure communication
        encryption_level: Optional[str] = None,  # Encryption level (e.g., 'AES256')
        capability_token: Optional[str] = None,  # Capability token for authorization
        verify_response: bool = True,  # Verify the authenticity of the response
    ) -> Any:
        """Send a secure request and await the response payload asynchronously."""
        ...

    async def subscribe_secure(
        self,
        event_type: str,
        callback: EventBusCallback,
        capability_token: Optional[str] = None,  # Capability token for authorization
        verify_events: bool = True,  # Verify the authenticity of received events
    ) -> str:
        """Subscribe to events with security verification."""
        ...

    async def publish_secure(
        self,
        event_type: str,
        event_payload: Any,
        source_id: Optional[str] = None,
        priority: int = PRIO_NORMAL,
        capability_token: Optional[str] = None,  # Capability token for authorization
        ttl: Optional[float] = None,  # Time-to-live in seconds
    ) -> None:
        """Publish an event securely."""
        ...

    def get_security_status(self) -> Dict[str, Any]:
        """Get the security status of the communicator."""
        ...

    def rotate_keys(self) -> bool:
        """Rotate encryption keys for all channels."""
        ...
