from __future__ import annotations

# -*- coding: utf-8 -*-
# Message-based services initialization
from person_suit.core.infrastructure.message_based_imports import ensure_services_initialized


async def _ensure_message_services():
    """Ensure message-based services are initialized."""
    if not await ensure_services_initialized():
        raise RuntimeError("Failed to initialize message-based services")


"""
File: person_suit/core/application/interfaces/events_interface.py
Purpose: Core interfaces for the event management system.

NOTE: This file was moved from core/application/ during de-flattening.

Defines standard event structures, topics, data types, and the main
IEventManager interface.

# TODO: Refactoring Needed
# (List of TODOs from original file...)
"""

import sys
import uuid
from dataclasses import dataclass
from datetime import datetime
from datetime import timezone
from enum import Enum
from enum import auto
from typing import Any
from typing import Callable
from typing import Coroutine
from typing import Dict
from typing import Protocol
from typing import TypeAlias
from typing import TypeVar

from pydantic import BaseModel
from pydantic import Field
from pydantic import field_validator

from person_suit.core.constants.fixed_point_scale import PRIO_CRITICAL
from person_suit.core.constants.fixed_point_scale import PRIO_HIGH

# Import fixed-point scale constants for consistent priority handling
from person_suit.core.constants.fixed_point_scale import PRIO_LOW
from person_suit.core.constants.fixed_point_scale import PRIO_NORMAL
from person_suit.core.infrastructure.message_based_imports import UnifiedContext as UnifiedContext
from person_suit.shared.information.core import Infom
from person_suit.shared.information.translator import InformationTranslator

_information_translator = InformationTranslator()

if sys.version_info >= (3, 11):
    pass  # pylint: disable=no-name-in-module
else:
    pass

# --- Forward References & Placeholders ---
Capability = TypeVar("Capability")
Effect = TypeVar("Effect")
CryptoMetadata: TypeAlias = Dict[str, Any]
TraceContext: TypeAlias = Dict[str, str]

# --- Event Topics ---


class EventTopic(Enum):
    """Standard event topics for inter-component communication."""

    SYSTEM_INITIALIZED = auto()
    SYSTEM_UPDATED = auto()
    SYSTEM_STARTUP = auto()
    SYSTEM_SHUTDOWN = auto()
    SYSTEM_ERROR = auto()
    SYSTEM_HEALTH_CHANGE = auto()
    SYSTEM_STATE_CHANGE = auto()
    CONFIGURATION_LOADED = auto()
    CONFIG_CHANGED = auto()
    SERVICE_REGISTERED = auto()
    SERVICE_UNREGISTERED = auto()
    RESOURCE_THRESHOLD_EXCEEDED = auto()
    THROTTLING_ACTIVATED = auto()
    THROTTLING_DEACTIVATED = auto()
    ALERT_TRIGGERED = auto()
    SECURITY_EVENT = auto()
    LOG_MESSAGE = auto()
    PERFORMANCE_METRIC = auto()
    INPUT_RECEIVED = auto()
    USER_INTERACTION = auto()
    GENERIC_EVENT = auto()
    CUSTOM_EVENT = auto()
    MEMORY = "pc.memory"
    MEMORY_OPERATION_REQUESTED = "pc.memory.operation.requested"
    MEMORY_OPERATION_COMPLETED = "pc.memory.operation.completed"
    MEMORY_OPERATION_FAILED = "pc.memory.operation.failed"
    MEMORY_STATE_CHANGED = "pc.memory.state.changed"
    SEM = "pc.folded_mind.sem"
    SEM_STATE_CHANGED = "pc.folded_mind.sem.state.changed"
    SEM_COGNITIVE_EVENT = "pc.folded_mind.sem.cognitive.event"
    SEM_EMOTIONAL_EVENT = "pc.folded_mind.sem.emotional.event"
    NEUROCHEMICAL = "pc.folded_mind.sem.neurochemical"
    NEUROCHEMICAL_LEVEL_CHANGED = "pc.folded_mind.sem.neurochemical.level.changed"
    NEUROCHEMICAL_STATE_EFFECT = "pc.folded_mind.sem.neurochemical.state.effect"
    SYNC = "pc.folded_mind.sync"
    SYNC_STATE_UPDATE = "pc.folded_mind.sync.state.update"
    SYNC_REQUESTED = "pc.folded_mind.sync.requested"
    ANALYSIS_REQUESTED = "an.analysis.requested"
    ANALYSIS_COMPLETED = "an.analysis.completed"
    PATTERN_DETECTED = "an.pattern.detected"
    PREDICTION_REQUESTED = "pr.prediction.requested"
    PREDICTION_GENERATED = "pr.prediction.generated"
    OUTPUT_SENT = "sio.output.sent"
    EXTERNAL_SYSTEM_EVENT = "sio.external.event"
    CONSISTENCY_CHECK_REQUESTED = "sync.consistency.check.requested"
    CONSISTENCY_INCONSISTENCY_DETECTED = "sync.consistency.inconsistency.detected"
    CONSISTENCY_REPAIRED = "sync.consistency.repaired"
    CONSISTENCY_REPAIR_FAILED = "sync.consistency.repair.failed"
    STATE_UPDATED = "sync.state.updated"
    EMOTION_FEEL_REQUESTED = "pc.emotion.feel.requested"
    EMOTION_GET_STATE_REQUESTED = "pc.emotion.get_state.requested"
    EMOTION_STATE_REPORTED = "pc.emotion.state.reported"
    EMOTION_APPRAISAL_COMPLETED = "pc.emotion.appraisal.completed"
    EXPRESSION_REQUESTED = "pc.expression.requested"
    EXPRESSION_PERFORMED = "pc.expression.performed"
    DISPLAYED_EMOTION_INFO = "pc.expression.displayed_info"
    SOCIAL_CONTEXT_UPDATE = "pc.social.context.update"
    PHYSIOLOGICAL_STATE_UPDATE = "pc.physiology.state.update"
    PERCEPTION_RESULT_PROCESSED = "app.perception.result.processed"

    def __str__(self) -> str:
        return str(self.value) if isinstance(self.value, str) else self.name


# --- Event Priority ---


class EventPriority(Enum):
    """Priority levels for event processing (using fixed-point scale)."""
    LOW = PRIO_LOW        # 200_000
    NORMAL = PRIO_NORMAL  # 500_000  
    HIGH = PRIO_HIGH      # 800_000
    CRITICAL = PRIO_CRITICAL  # 1_000_000


# ---------------------------------------------------------------------------
# Compatibility aliases (legacy *EventPRIO_* constants)
# ---------------------------------------------------------------------------
# Legacy modules still refer to `EventPRIO_*` symbols. These aliases map
# directly to the fixed-point scale constants for consistency across the
# entire system. They are injected into `builtins` so bare references resolve.
#
# NOTE: These are temporary compatibility shims and will be removed once all
# callers migrate to the canonical constants or the EventPriority enum.
# ---------------------------------------------------------------------------

EventPRIO_LOW: int = PRIO_LOW        # 200_000
EventPRIO_NORMAL: int = PRIO_NORMAL  # 500_000
EventPRIO_HIGH: int = PRIO_HIGH      # 800_000
EventPRIO_CRITICAL: int = PRIO_CRITICAL  # 1_000_000

# Make aliases visible to modules that *don't* import from this file but still
# reference the names as globals – this includes several files under
# `meta_systems/` and `core/pathway/`.

import builtins as _ps_builtins  # noqa: WPS433 (runtime patch)

_ps_builtins.EventPRIO_LOW = EventPRIO_LOW  # type: ignore[attr-defined]
_ps_builtins.EventPRIO_NORMAL = EventPRIO_NORMAL  # type: ignore[attr-defined]
_ps_builtins.EventPRIO_HIGH = EventPRIO_HIGH  # type: ignore[attr-defined]
_ps_builtins.EventPRIO_CRITICAL = EventPRIO_CRITICAL  # type: ignore[attr-defined]

# --- Event Manager Interface ---

EventType = TypeVar("EventType", bound="BaseEvent")
EventCallback = Callable[[EventType], Coroutine[Any, Any, None]]


@dataclass
class EventOptimizationConfig:
    """Configuration for event manager optimization."""

    batch_size: int = 1
    enable_m3: bool = False
    priority_queuing: bool = False


class IEventManager(Protocol[EventType]):
    """Interface for the event management system."""

    def subscribe(
        self, event_topic: EventTopic, callback: EventCallback[EventType]
    ) -> None:
        """Subscribe a callback function..."""
        ...

    async def publish(self, event: EventType) -> None:
        """Publish an event..."""
        ...

    def configure_optimization(self, config: EventOptimizationConfig) -> None:
        """Configure optimization settings..."""
        ...

    def unsubscribe(
        self, event_topic: EventTopic, callback: EventCallback[EventType]
    ) -> None:
        """Unsubscribe a callback..."""
        ...


# --- Example Concrete Event Definition ---


class EventData(BaseModel):
    """
    The "what" of an event, containing the core information payload.
    This class now standardizes all incoming data into an Infom object.
    """
    source: str
    infom: Infom = Field(description="The unified information payload of the event.")

    @field_validator('infom', mode='before')
    @classmethod
    def _validate_and_convert_to_infom(cls, v: Any) -> Infom:
        """
        Pydantic validator to automatically convert any input data into an Infom object.
        This is the core of the new unified information flow.
        """
        if isinstance(v, Infom):
            return v
        # Use the translator for any other data type (dict, other objects)
        return _information_translator.translate(v, to_type=Infom)

    class Config:
        arbitrary_types_allowed = True


class BaseEvent(BaseModel):
    """
    Base class for all events, defining the common structure (the "envelope").
    It ensures that every event has a standardized data payload.
    """
    event_id: str = Field(default_factory=lambda: str(uuid.uuid4()))
    timestamp: datetime = Field(default_factory=lambda: datetime.now(timezone.utc))
    data: EventData

    @field_validator('data', mode='before')
    @classmethod
    def _validate_and_wrap_data(cls, v: Any) -> EventData:
        """
        Ensures the payload is always a compliant EventData object,
        converting Infom or other types as needed.
        """
        if isinstance(v, EventData):
            return v

        # If raw Infom is passed, wrap it in EventData
        if isinstance(v, Infom):
            return EventData(source="unknown_wrapped", infom=v)

        # If a dictionary or other object is passed, let EventData's validator handle it
        if isinstance(v, dict) and 'source' in v and ('infom' in v or 'data' in v):
             # This handles the case where a dict representing EventData is passed
            infom_payload = v.get('infom', v.get('data'))
            return EventData(source=v['source'], infom=infom_payload)

        # Fallback for simple data types that need to be translated
        return EventData(source="unknown_translated", infom=v)


class Event(BaseEvent):
    """
    A generic, concrete event class that can be used for most purposes.
    Inherits the validation and structure from BaseEvent.
    """
    pass


# Example of a more specific event type
class SystemEvent(BaseEvent):
    """An event specifically related to system operations."""
    pass
