# -*- coding: utf-8 -*-
"""
File: person_suit/core/application/interfaces/config_interface.py
Purpose: Defines the interface for configuration management.

NOTE: This file was moved from core/application/ during de-flattening.
"""

from typing import Any
from typing import Dict
from typing import Optional
from typing import Protocol


class IConfigManager(Protocol):
    """Interface for accessing application configuration."""

    def load_config(self, config_source: Any) -> None:
        """Loads configuration from a specified source (e.g., file path)."""
        ...

    def get_section(self, section_name: str) -> Optional[Dict[str, Any]]:
        """Gets an entire configuration section as a dictionary."""
        ...

    def get_setting(self, key: str, default: Optional[Any] = None) -> Optional[Any]:
        """
        Gets a specific setting using a dot-separated key (e.g., 'logging.level').
        Returns the default value if the key is not found.
        """
        ...

    def get_full_config(self) -> Dict[str, Any]:
        """Returns the entire loaded configuration dictionary."""
        ...


__all__ = ["IConfigManager"]
