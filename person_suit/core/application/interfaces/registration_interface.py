"""
File: person_suit/core/application/interfaces/registration_interface.py
Purpose: Interfaces for Secure Component Registration and Discovery.

Defines the contracts for registering components (services, toolboxes, etc.)
and discovering them based on capabilities or identifiers.

NOTE: This file was moved from core/application/ during de-flattening.

This module implements a secure, capability-based registry system with
effect tracking and telemetry. It follows the zero-trust architecture principles
and provides cryptographic verification for all registry operations.

Security Features:
- Capability-based access control for registry operations
- Cryptographic verification of component identity
- Audit logging for all registry operations
- Telemetry for registry access patterns

Related Files:
- person_suit/core/infrastructure/registration.py: Implementation
- person_suit/core/infrastructure/security/registry/: Security components
- person_suit/core/infrastructure/effects/: Effect system integration
"""

import uuid
from dataclasses import dataclass
from dataclasses import field
from enum import Enum
from enum import auto
from typing import Any
from typing import Dict
from typing import List
from typing import Optional
from typing import Protocol
from typing import TypeVar

# --- Data Structures ---


class CapabilityScope(Enum):
    """Defines the scope of a registry capability."""

    READ = auto()  # Can read component information
    INVOKE = auto()  # Can invoke component methods
    MODIFY = auto()  # Can modify component state
    ADMIN = auto()  # Full administrative access


@dataclass(frozen=True)
class RegistryCapability:
    """Unforgeable capability token for registry access."""

    # --- Fields without defaults first ---
    resource_id: str
    scope: CapabilityScope
    issuer: str
    subject: str
    expiration: float
    # --- Fields with defaults last ---
    capability_id: str = field(default_factory=lambda: str(uuid.uuid4()))
    parent_id: Optional[str] = None  # For delegated capabilities
    signature: Optional[str] = None  # Cryptographic signature


@dataclass(frozen=True)
class ComponentCapability:
    """Represents a capability offered by a component."""

    name: str
    version: str
    # Add other relevant fields, e.g., parameters, schema


@dataclass(frozen=True)
class ComponentMetadata:
    """Standard metadata for a registered component."""

    component_id: str
    component_type: str  # e.g., 'service', 'toolbox', 'adapter'
    version: str
    capabilities: List[ComponentCapability] = field(default_factory=list)
    properties: Dict[str, Any] = field(default_factory=dict)
    # Add other relevant fields, e.g., dependencies, status


# Generic TypeVar for component instance type
ComponentType = TypeVar("ComponentType")

# --- Interface Definition ---


class IRegistry(Protocol[ComponentType]):
    """Interface for a secure component registry with capability-based access control."""

    def issue_capability(
        self,
        resource_id: str,
        scope: CapabilityScope,
        issuer: str,
        subject: str,
        ttl_seconds: int = 3600,
    ) -> RegistryCapability:
        """Issue a new capability for registry access."""
        ...

    def verify_access(
        self,
        capability: RegistryCapability,
        resource_id: str,
        required_scope: CapabilityScope,
    ) -> bool:
        """Verify if a capability grants access to a resource with the required scope."""
        ...

    def register_component(
        self,
        component: ComponentType,
        metadata: ComponentMetadata,
        capability: Optional[RegistryCapability] = None,
    ) -> None:
        """Register a component instance with its metadata."""
        ...

    def unregister_component(
        self, component_id: str, capability: Optional[RegistryCapability] = None
    ) -> None:
        """Unregister a component by its ID."""
        ...

    def get_component(
        self, component_id: str, capability: Optional[RegistryCapability] = None
    ) -> Optional[ComponentType]:
        """Get a component instance by its ID."""
        ...

    def get_metadata(
        self, component_id: str, capability: Optional[RegistryCapability] = None
    ) -> Optional[ComponentMetadata]:
        """Get the metadata for a component by its ID."""
        ...

    def find_components(
        self,
        capability_name: Optional[str] = None,
        component_type: Optional[str] = None,
        registry_capability: Optional[RegistryCapability] = None,
    ) -> List[ComponentMetadata]:
        """Find components based on criteria (e.g., capability, type)."""
        ...

    def list_all_components(
        self, registry_capability: Optional[RegistryCapability] = None
    ) -> List[ComponentMetadata]:
        """List metadata for all registered components."""
        ...

    def get_telemetry(self) -> Dict[str, Any]:
        """Get telemetry data for the registry."""
        ...

    def delegate_capability(
        self,
        parent_capability: RegistryCapability,
        new_subject: str,
        new_scope: Optional[CapabilityScope] = None,
        new_resource_id: Optional[str] = None,
        ttl_seconds: Optional[int] = None,
    ) -> RegistryCapability:
        """Delegate a capability with potentially reduced permissions."""
        ...

    def revoke_capability(self, capability_id: str) -> bool:
        """Revoke a capability."""
        ...
