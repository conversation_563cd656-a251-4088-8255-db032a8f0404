"""Application Configuration Factory.

This module provides factory functions for creating application configurations.
"""

import logging
from typing import Optional
from typing import Type

from .config import DefaultApplicationConfig
from .interfaces.config import ApplicationConfig

logger = logging.getLogger(__name__)


def create_application_config(
    config_class: Optional[Type[ApplicationConfig]] = None,
) -> ApplicationConfig:
    """Create an application configuration instance.

    Args:
        config_class: Optional custom application configuration class.
            If None, uses DefaultApplicationConfig.

    Returns:
        An initialized ApplicationConfig instance.

    Raises:
        ValueError: If config_class is not a subclass of ApplicationConfig.
    """
    if config_class is None:
        config_class = DefaultApplicationConfig
    elif not issubclass(config_class, ApplicationConfig):
        raise ValueError(
            f"Configuration class must be a subclass of ApplicationConfig, got {config_class}"
        )

    try:
        config = config_class()
        logger.info(f"Created application configuration: {config.__class__.__name__}")
        return config

    except Exception as e:
        logger.error(f"Failed to create application configuration: {e}")
        raise
