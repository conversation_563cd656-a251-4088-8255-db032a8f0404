# -*- coding: utf-8 -*-
"""
Core Application Lifecycle, Communication, and Support Components.

This package provides fundamental components for managing application state,
inter-component communication (Events, Mediation), service registration/resolution,
and common application-level utilities and interfaces.
These are framework-level utilities intended for use by all meta-systems.

NOTE: This directory was restructured ('de-flattened') to improve organization.
      Interfaces are now in 'interfaces/', handlers in 'handlers/'.
"""

# Handlers (moved to handlers/)
# Export specific handlers if needed directly, otherwise let users import from handlers
from .handlers.security_alert_handler import Security<PERSON>lertHandler
from .interfaces.communication_interface import IChannelManager
from .interfaces.communication_interface import ICommunicator
from .interfaces.config_interface import IConfigManager
from .interfaces.error_handling_interface import ErrorManagerInterface
from .interfaces.event_provider_interfaces import IEventCapabilityProvider
from .interfaces.event_provider_interfaces import IEventCryptoProvider
from .interfaces.event_provider_interfaces import IEventPersistenceProvider

# Interfaces (moved to interfaces/)
from .interfaces.events_interface import (  # Add other event data structures if commonly used as types; e.g., ErrorEventData, ConfigChangedEventData
    BaseEvent,
)
from .interfaces.events_interface import (  # Add other event data structures if commonly used as types; e.g., ErrorEventData, ConfigChangedEventData
    EventCallback,
)
from .interfaces.events_interface import (  # Add other event data structures if commonly used as types; e.g., ErrorEventData, ConfigChangedEventData
    EventData,
)
from .interfaces.events_interface import (  # Add other event data structures if commonly used as types; e.g., ErrorEventData, ConfigChangedEventData
    EventTopic,
)
from .interfaces.events_interface import (  # Add other event data structures if commonly used as types; e.g., ErrorEventData, ConfigChangedEventData
    IEventManager,
)
from .interfaces.registration_interface import IRegistry

# Standard Mediator
from .mediator import Mediator  # Expose singleton getter too
from .mediator import get_mediator  # Expose singleton getter too

# Messages
from .messages import Message
from .messages import MessageHeader
from .messages import MessageType

# Add exports for other handlers if they exist and are commonly needed


__all__ = [
    # Mediator
    "Mediator",
    "get_mediator",
    # Interfaces
    "IEventManager",
    "BaseEvent",
    "EventData",
    "EventTopic",
    "EventCallback",
    "IEventPersistenceProvider",
    "IEventCryptoProvider",
    "IEventCapabilityProvider",
    "ErrorManagerInterface",
    "IRegistry",
    "IChannelManager",
    "ICommunicator",
    "IConfigManager",
    # Handlers
    "SecurityAlertHandler",
    # Messages
    "Message",
    "MessageHeader",
    "MessageType",
]

# Note: The original ServiceRegistry might be equivalent to the DependencyContainer
# in core.infrastructure.dependency_injection. If so, this package might only
# need EventManager and Mediator, and ServiceRegistry/setup should be removed
# in favor of the DI system. For now, assuming they co-exist or are distinct.
# A review of core.infrastructure.dependency_injection vs these files is needed.
