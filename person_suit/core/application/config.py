"""Default Application Configuration.

This module implements the default application configuration that manages
the core Person Suit system components and lifecycle.

CAW-Aligned with dependency inversion to avoid circular dependencies.
"""

import logging
from typing import Protocol
from typing import runtime_checkable

from ..deployment import get_deployment_manager
from ..infrastructure import configuration as config
from .interfaces.config import ApplicationConfig

logger = logging.getLogger(__name__)


# ====== DEPENDENCY INVERSION INTERFACES ======

@runtime_checkable
class IPersonaCore(Protocol):
    """Protocol for PersonaCore dependency injection."""
    
    async def initialize(self) -> None:
        """Initialize the persona core."""
        ...
    
    async def shutdown(self) -> None:
        """Shutdown the persona core."""
        ...


@runtime_checkable
class IAnalyst(Protocol):
    """Protocol for Analyst dependency injection."""
    
    async def initialize(self) -> None:
        """Initialize the analyst."""
        ...
    
    async def shutdown(self) -> None:
        """Shutdown the analyst."""
        ...


@runtime_checkable
class IPredictor(Protocol):
    """Protocol for Predictor dependency injection."""
    
    async def initialize(self) -> None:
        """Initialize the predictor."""
        ...
    
    async def shutdown(self) -> None:
        """Shutdown the predictor."""
        ...


@runtime_checkable
class IIOLayer(Protocol):
    """Protocol for IO Layer dependency injection."""
    
    async def initialize(self) -> None:
        """Initialize the IO layer."""
        ...
    
    async def shutdown(self) -> None:
        """Shutdown the IO layer."""
        ...


class DefaultApplicationConfig(ApplicationConfig):
    """Default implementation of application configuration.

    Manages:
    - Core system components
    - Meta-system initialization
    - Component lifecycle
    
    Uses dependency inversion to avoid circular dependencies.
    """

    def __init__(self) -> None:
        """Initialize the configuration."""
        super().__init__()
        self._deployment = get_deployment_manager()
        self._config = config.get_config_manager()

    async def start(self) -> None:
        """Start the application.

        Initializes and starts:
        1. Core infrastructure
        2. Effect system
        3. Meta-systems (PC, AN, PR) via dependency injection
        4. I/O Layer
        """
        try:
            # Initialize meta-systems via dependency injection
            await self._initialize_meta_systems_via_di()

            # Initialize I/O layer via dependency injection
            await self._initialize_io_layer_via_di()

            # Start monitoring
            await self._monitoring.start_collection()

            logger.info("Application started successfully with dependency injection")

        except Exception as e:
            logger.error(f"Failed to start application: {e}")
            raise

    async def stop(self) -> None:
        """Stop the application.

        Shuts down:
        1. I/O Layer
        2. Meta-systems
        3. Effect system
        4. Core infrastructure
        """
        try:
            # Stop monitoring
            await self._monitoring.stop_collection()

            # Shutdown I/O layer via DI
            await self._shutdown_io_layer_via_di()

            # Shutdown meta-systems via DI
            await self._shutdown_meta_systems_via_di()

            logger.info("Application stopped successfully")

        except Exception as e:
            logger.error(f"Error during application shutdown: {e}")
            raise

    async def _initialize_meta_systems_via_di(self) -> None:
        """Initialize meta-systems using dependency injection to avoid circular imports."""
        try:
            # Resolve meta-systems from DI container using protocols
            # The concrete implementations will be registered by the bootstrap process
            persona_core = self._container.get_service(IPersonaCore)
            analyst = self._container.get_service(IAnalyst)
            predictor = self._container.get_service(IPredictor)

            # Initialize components if available
            if persona_core:
                await persona_core.initialize()
                logger.info("PersonaCore initialized via DI")
            else:
                logger.warning("PersonaCore not available in DI container")

            if analyst:
                await analyst.initialize()
                logger.info("Analyst initialized via DI")
            else:
                logger.warning("Analyst not available in DI container")

            if predictor:
                await predictor.initialize()
                logger.info("Predictor initialized via DI")
            else:
                logger.warning("Predictor not available in DI container")

            logger.info("Meta-systems initialized successfully via dependency injection")

        except Exception as e:
            logger.error(f"Failed to initialize meta-systems via DI: {e}")
            # Try fallback initialization with lazy imports
            await self._fallback_meta_system_initialization()

    async def _fallback_meta_system_initialization(self) -> None:
        """Fallback initialization - graceful degradation without meta-systems."""
        try:
            logger.info("Meta-systems not available in DI container - graceful degradation")
            logger.info("System will operate with core infrastructure only")
            
            # No direct imports from meta_systems to avoid circular dependencies
            # Bootstrap code is responsible for registering concrete implementations

        except Exception as e:
            logger.error(f"Fallback handling failed: {e}")
            # Continue without meta-systems - graceful degradation
            logger.info("Continuing with core infrastructure only")

    async def _shutdown_meta_systems_via_di(self) -> None:
        """Shutdown meta-systems using dependency injection."""
        try:
            # Get components from DI container using protocols
            predictor = self._container.get_service(IPredictor)
            analyst = self._container.get_service(IAnalyst)
            persona_core = self._container.get_service(IPersonaCore)

            # Shutdown components if available
            if predictor:
                await predictor.shutdown()
                logger.info("Predictor shutdown via DI")

            if analyst:
                await analyst.shutdown()
                logger.info("Analyst shutdown via DI")

            if persona_core:
                await persona_core.shutdown()
                logger.info("PersonaCore shutdown via DI")

            logger.info("Meta-systems shutdown successfully via dependency injection")

        except Exception as e:
            logger.error(f"Error during meta-systems shutdown via DI: {e}")

    async def _initialize_io_layer_via_di(self) -> None:
        """Initialize I/O layer using dependency injection."""
        try:
            # Resolve IO layer from DI container
            io_layer = self._container.get_service(IIOLayer)

            if io_layer:
                await io_layer.initialize()
                logger.info("I/O layer initialized via DI")
            else:
                logger.warning("I/O layer not available in DI container")
                logger.info("System will operate without I/O layer - graceful degradation")

        except Exception as e:
            logger.error(f"Failed to initialize I/O layer via DI: {e}")

    async def _shutdown_io_layer_via_di(self) -> None:
        """Shutdown I/O layer using dependency injection."""
        try:
            # Get component from DI container
            io_layer = self._container.get_service(IIOLayer)

            if io_layer:
                await io_layer.shutdown()
                logger.info("I/O layer shutdown via DI")

        except Exception as e:
            logger.error(f"Error during I/O layer shutdown: {e}") 