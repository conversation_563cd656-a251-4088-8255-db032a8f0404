"""
System-Wide Constants

This module defines system-wide operational constants for the Person Suit framework.
These constants affect multiple meta-systems and provide consistent configuration
across the entire framework.

Constants defined here include:
- Operation timeouts
- Resource limits
- Platform detection
- System-wide configuration

Usage:
```python
from .system import DEFAULT_OPERATION_TIMEOUT
```
"""

import multiprocessing
import os
import platform
from typing import Dict
from typing import Final

# System-wide operation timeouts (in seconds)
DEFAULT_OPERATION_TIMEOUT: Final[float] = 5.0
EXTENDED_OPERATION_TIMEOUT: Final[float] = 30.0
VECTOR_SEARCH_TIMEOUT: Final[float] = 10.0

# Resource limits
MAX_CONCURRENT_OPERATIONS: Final[int] = 10
MAX_VECTOR_OPERATIONS_PER_SECOND: Final[int] = 100
MAX_DISK_OPERATIONS_PER_SECOND: Final[int] = 500

# Platform detection
IS_APPLE_SILICON: Final[bool] = (
    platform.system() == "Darwin" and platform.processor() == "arm"
)
IS_WINDOWS: Final[bool] = platform.system() == "Windows"
IS_LINUX: Final[bool] = platform.system() == "Linux"
IS_MACOS: Final[bool] = platform.system() == "Darwin"

# M3 processor architecture constants
M3_ARCHITECTURE: Final[Dict[str, Dict[str, int]]] = {
    "M3": {"performance_cores": 4, "efficiency_cores": 4, "total_cores": 8},
    "M3 Pro": {"performance_cores": 6, "efficiency_cores": 6, "total_cores": 12},
    "M3 Max": {"performance_cores": 12, "efficiency_cores": 4, "total_cores": 16},
    "M3 Ultra": {"performance_cores": 24, "efficiency_cores": 8, "total_cores": 32},
}

# System-wide configuration
DEFAULT_ENCODING: Final[str] = "utf-8"
DEFAULT_LOCALE: Final[str] = "en_US.UTF-8"
DEFAULT_TIMEZONE: Final[str] = "UTC"
DEFAULT_LOG_LEVEL: Final[str] = "INFO"

# File system constants
MAX_PATH_LENGTH: Final[int] = 260 if IS_WINDOWS else 4096
DEFAULT_FILE_PERMISSIONS: Final[int] = 0o644
DEFAULT_DIRECTORY_PERMISSIONS: Final[int] = 0o755
DEFAULT_TEMP_DIRECTORY: Final[str] = os.path.join(
    os.path.expanduser("~"), ".person_suit", "temp"
)

# Network constants
DEFAULT_PORT: Final[int] = 8080
DEFAULT_HOST: Final[str] = "localhost"
DEFAULT_TIMEOUT: Final[float] = 30.0
DEFAULT_RETRIES: Final[int] = 3
DEFAULT_BACKOFF_FACTOR: Final[float] = 0.5

# Process and thread constants
DEFAULT_PROCESS_COUNT: Final[int] = min(multiprocessing.cpu_count(), 8)
DEFAULT_THREAD_COUNT: Final[int] = min(multiprocessing.cpu_count() * 2, 16)
DEFAULT_QUEUE_SIZE: Final[int] = 1000
DEFAULT_BUFFER_SIZE: Final[int] = 8192

# Memory constants
DEFAULT_MEMORY_LIMIT: Final[int] = 1024 * 1024 * 1024  # 1 GB
DEFAULT_CACHE_EXPIRATION: Final[int] = 3600  # 1 hour
DEFAULT_MEMORY_THRESHOLD: Final[float] = 0.8  # 80%
