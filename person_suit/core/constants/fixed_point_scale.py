"""Fixed-point scale constants for integer arithmetic optimization.

This module defines the scaling factor and common bucket values used throughout
the Person Suit system for efficient integer-based calculations of priorities,
fidelities, and other normalized values.

The fixed-point representation uses a scale of 1,000,000 (one million) to provide
microsecond-level precision while maintaining integer arithmetic efficiency.

Related Files:
- infrastructure/hybrid_message.py: Uses these constants for priority buckets
- infrastructure/middleware/acf.py: Uses for fidelity calculations
"""
from __future__ import annotations

# ============================================================================
# Core Scale
# ============================================================================

SCALE = 1_000_000  # One million - provides 6 decimal places of precision

# ============================================================================
# Priority Ranges (Buckets)
# ============================================================================

# Critical Priority: 900,000 - 1,000,000 (top 10%)
PRIO_CRITICAL_MIN = int(0.9 * SCALE)    # 900,000
PRIO_CRITICAL_MAX = SCALE               # 1,000,000
PRIO_CRITICAL = SCALE                   # Default value for critical priority

# High Priority: 700,000 - 899,999 (next 20%)
PRIO_HIGH_MIN = int(0.7 * SCALE)       # 700,000
PRIO_HIGH_MAX = PRIO_CRITICAL_MIN - 1   # 899,999
PRIO_HIGH = int(0.8 * SCALE)           # 800,000 - middle of high range

# Normal Priority: 300,000 - 699,999 (middle 40%)
PRIO_NORMAL_MIN = int(0.3 * SCALE)     # 300,000
PRIO_NORMAL_MAX = PRIO_HIGH_MIN - 1     # 699,999
PRIO_NORMAL = int(0.5 * SCALE)         # 500,000 - middle of normal range

# Low Priority: 100,000 - 299,999 (next 20%)
PRIO_LOW_MIN = int(0.1 * SCALE)        # 100,000
PRIO_LOW_MAX = PRIO_NORMAL_MIN - 1      # 299,999
PRIO_LOW = int(0.2 * SCALE)            # 200,000 - middle of low range

# Deferred Priority: 0 - 99,999 (bottom 10%)
PRIO_DEFERRED_MIN = 0                   # 0
PRIO_DEFERRED_MAX = PRIO_LOW_MIN - 1    # 99,999
PRIO_DEFERRED = int(0.05 * SCALE)      # 50,000 - middle of deferred range

# ============================================================================
# Fidelity Ranges (Buckets)
# ============================================================================

# Maximum Fidelity: 900,000 - 1,000,000 (top 10%)
FIDELITY_MAX_MIN = int(0.9 * SCALE)     # 900,000
FIDELITY_MAX_MAX = SCALE                # 1,000,000
FIDELITY_MAX = SCALE                    # Default value for max fidelity

# High Fidelity: 700,000 - 899,999 (next 20%)
FIDELITY_HIGH_MIN = int(0.7 * SCALE)    # 700,000
FIDELITY_HIGH_MAX = FIDELITY_MAX_MIN - 1  # 899,999
FIDELITY_HIGH = int(0.8 * SCALE)       # 800,000 - middle of high range

# Medium Fidelity: 400,000 - 699,999 (middle 30%)
FIDELITY_MED_MIN = int(0.4 * SCALE)     # 400,000
FIDELITY_MED_MAX = FIDELITY_HIGH_MIN - 1   # 699,999
FIDELITY_MED = int(0.5 * SCALE)        # 500,000 - middle of medium range

# Low Fidelity: 200,000 - 399,999 (next 20%)
FIDELITY_LOW_MIN = int(0.2 * SCALE)     # 200,000
FIDELITY_LOW_MAX = FIDELITY_MED_MIN - 1    # 399,999
FIDELITY_LOW = int(0.3 * SCALE)        # 300,000 - middle of low range

# Minimum Fidelity: 0 - 199,999 (bottom 20%)
FIDELITY_MIN_MIN = 0                    # 0
FIDELITY_MIN_MAX = FIDELITY_LOW_MIN - 1    # 199,999
FIDELITY_MIN = int(0.1 * SCALE)        # 100,000 - middle of minimum range

# ============================================================================
# Conversion Functions
# ============================================================================

def float_to_bucket(value: float) -> int:
    """Convert a float (0.0-1.0) to fixed-point bucket integer.
    
    Args:
        value: Float value between 0.0 and 1.0
        
    Returns:
        Integer bucket value (0 to SCALE)
    """
    return int(round(max(0.0, min(1.0, value)) * SCALE))


def bucket_to_float(bucket: int) -> float:
    """Convert a fixed-point bucket integer to float (0.0-1.0).
    
    Args:
        bucket: Integer bucket value (0 to SCALE)
        
    Returns:
        Float value between 0.0 and 1.0
    """
    return max(0.0, min(1.0, bucket / SCALE))


def fmt_bucket(value: int) -> str:  # noqa: D401
    """Return fixed-point bucket as pretty string with 6-decimals («0.700000»)."""
    return f"{bucket_to_float(value):.6f}"  # up to µ-resolution


def get_priority_name(priority: int) -> str:
    """Get human-readable name for a priority value.
    
    Args:
        priority: Integer priority value
        
    Returns:
        String name of the priority range
    """
    if priority >= PRIO_CRITICAL_MIN:
        return "CRITICAL"
    elif priority >= PRIO_HIGH_MIN:
        return "HIGH"
    elif priority >= PRIO_NORMAL_MIN:
        return "NORMAL"
    elif priority >= PRIO_LOW_MIN:
        return "LOW"
    else:
        return "DEFERRED"


def get_fidelity_name(fidelity: int) -> str:
    """Get human-readable name for a fidelity value.
    
    Args:
        fidelity: Integer fidelity value
        
    Returns:
        String name of the fidelity range
    """
    if fidelity >= FIDELITY_MAX_MIN:
        return "MAXIMUM"
    elif fidelity >= FIDELITY_HIGH_MIN:
        return "HIGH"
    elif fidelity >= FIDELITY_MED_MIN:
        return "MEDIUM"
    elif fidelity >= FIDELITY_LOW_MIN:
        return "LOW"
    else:
        return "MINIMUM"


def is_priority_in_range(priority: int, range_name: str) -> bool:
    """Check if a priority value falls within a named range.
    
    Args:
        priority: Integer priority value to check
        range_name: Name of the range (CRITICAL, HIGH, NORMAL, LOW, DEFERRED)
        
    Returns:
        True if priority is in the specified range
    """
    range_name = range_name.upper()
    if range_name == "CRITICAL":
        return PRIO_CRITICAL_MIN <= priority <= PRIO_CRITICAL_MAX
    elif range_name == "HIGH":
        return PRIO_HIGH_MIN <= priority <= PRIO_HIGH_MAX
    elif range_name == "NORMAL":
        return PRIO_NORMAL_MIN <= priority <= PRIO_NORMAL_MAX
    elif range_name == "LOW":
        return PRIO_LOW_MIN <= priority <= PRIO_LOW_MAX
    elif range_name == "DEFERRED":
        return PRIO_DEFERRED_MIN <= priority <= PRIO_DEFERRED_MAX
    else:
        return False


# ============================================================================
# Legacy Aliases (for backward compatibility)
# ============================================================================

# These map to the default values within each range
PRIORITY_CRITICAL = PRIO_CRITICAL
PRIORITY_HIGH = PRIO_HIGH
PRIORITY_NORMAL = PRIO_NORMAL
PRIORITY_LOW = PRIO_LOW
PRIORITY_DEFERRED = PRIO_DEFERRED 