"""
Security-Related Constants

This module defines security-related constants for the Person Suit framework.
These constants affect security mechanisms across the entire framework and
provide consistent security configuration.

Constants defined here include:
- Cryptographic algorithm defaults
- Security levels and thresholds
- Authentication and authorization defaults
- Capability-based security configuration

Usage:
```python
from .security import DEFAULT_ENCRYPTION_ALGORITHM
```
"""

from enum import Enum
from enum import auto
from typing import Dict
from typing import Final
from typing import List


# Security levels
class SecurityLevel(Enum):
    """Security levels for the Person Suit framework."""

    LOW = auto()
    MEDIUM = auto()
    HIGH = auto()
    VERY_HIGH = auto()
    MILITARY = auto()


# Default security level
SECURITY_LEVEL: Final[SecurityLevel] = SecurityLevel.HIGH

# Cryptographic algorithm defaults
DEFAULT_ENCRYPTION_ALGORITHM: Final[str] = "AES-256-GCM"
DEFAULT_HASH_ALGORITHM: Final[str] = "SHA-256"
DEFAULT_KEY_DERIVATION_ALGORITHM: Final[str] = "PBKDF2"
DEFAULT_SIGNATURE_ALGORITHM: Final[str] = "Ed25519"
DEFAULT_RANDOM_GENERATOR: Final[str] = "CSPRNG"

# Key sizes
DEFAULT_SYMMETRIC_KEY_SIZE: Final[int] = 256
DEFAULT_ASYMMETRIC_KEY_SIZE: Final[int] = 4096
DEFAULT_ELLIPTIC_CURVE: Final[str] = "Curve25519"

# Authentication and authorization defaults
DEFAULT_PASSWORD_MIN_LENGTH: Final[int] = 12
DEFAULT_PASSWORD_MAX_LENGTH: Final[int] = 128
DEFAULT_PASSWORD_COMPLEXITY: Final[Dict[str, bool]] = {
    "uppercase": True,
    "lowercase": True,
    "digits": True,
    "special_chars": True,
    "min_groups": 3,
}
DEFAULT_AUTH_TOKEN_LENGTH: Final[int] = 64
DEFAULT_TOKEN_LIFETIME: Final[int] = 3600  # 1 hour in seconds
DEFAULT_REFRESH_TOKEN_LIFETIME: Final[int] = 86400 * 7  # 7 days in seconds
DEFAULT_SESSION_LIFETIME: Final[int] = 3600  # 1 hour in seconds
DEFAULT_LOGIN_ATTEMPTS: Final[int] = 5
DEFAULT_LOCKOUT_DURATION: Final[int] = 300  # 5 minutes in seconds

# Capability-based security configuration
DEFAULT_CAPABILITY_LIFETIME: Final[int] = 3600  # 1 hour in seconds
DEFAULT_CAPABILITY_SCOPE: Final[str] = "system"
DEFAULT_CAPABILITY_DELEGATION_DEPTH: Final[int] = 3
DEFAULT_CAPABILITY_TOKEN_LENGTH: Final[int] = 64
DEFAULT_CAPABILITY_VERIFICATION_CACHE_SIZE: Final[int] = 1000
DEFAULT_CAPABILITY_VERIFICATION_CACHE_TTL: Final[int] = 60  # 60 seconds

# Zero-trust configuration
ZERO_TRUST_VERIFICATION_INTERVAL: Final[int] = 300  # 5 minutes in seconds
ZERO_TRUST_CONTEXT_FACTORS: Final[List[str]] = [
    "time",
    "location",
    "device",
    "network",
    "behavior",
]
ZERO_TRUST_RISK_THRESHOLD: Final[float] = 0.7  # 70% risk threshold

# Audit and logging
SECURITY_AUDIT_RETENTION_PERIOD: Final[int] = 86400 * 90  # 90 days in seconds
SECURITY_AUDIT_LEVELS: Final[Dict[str, bool]] = {
    "authentication": True,
    "authorization": True,
    "data_access": True,
    "configuration_change": True,
    "security_event": True,
}
SECURITY_ALERT_THRESHOLD: Final[float] = 0.8  # 80% alert threshold

# Quantum-resistant algorithms
QUANTUM_RESISTANT_ALGORITHMS: Final[Dict[str, str]] = {
    "key_exchange": "CRYSTALS-Kyber",
    "digital_signature": "CRYSTALS-Dilithium",
    "encryption": "NTRU",
}

# Formal verification
FORMAL_VERIFICATION_ENABLED: Final[bool] = True
FORMAL_VERIFICATION_PROPERTIES: Final[List[str]] = [
    "authentication",
    "authorization",
    "confidentiality",
    "integrity",
    "non-repudiation",
]

# Secure communication
SECURE_CHANNEL_PROTOCOL: Final[str] = "TLS-1.3"
SECURE_CHANNEL_CIPHER_SUITES: Final[List[str]] = [
    "TLS_AES_256_GCM_SHA384",
    "TLS_CHACHA20_POLY1305_SHA256",
]
SECURE_CHANNEL_PERFECT_FORWARD_SECRECY: Final[bool] = True
SECURE_CHANNEL_CERTIFICATE_VERIFICATION: Final[bool] = True
