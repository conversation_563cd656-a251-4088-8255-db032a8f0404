"""
Core Constants Package
======================

This package defines shared, system-wide constants for the Person Suit framework.
Using these constants ensures consistency and a single source of truth for critical
values like timeouts, security parameters, and hardware settings.

Modules:
- system: General system-wide operational constants.
- security: Constants related to security, encryption, and capabilities.
- hardware: Hardware-specific constants, especially for CPU architectures.
- performance: Constants for performance tuning and resource management.
"""

from . import hardware
from . import performance
from . import security
from . import system

__all__ = [
    "system",
    "security",
    "hardware",
    "performance",
]
