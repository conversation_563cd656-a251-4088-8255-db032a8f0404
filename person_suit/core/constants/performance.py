"""
Performance Tuning Constants

This module defines performance-related constants for the Person Suit framework.
These constants affect performance tuning across the entire framework and
provide consistent performance configuration.

Constants defined here include:
- Batch sizes and chunk sizes
- Cache sizes and TTLs
- Thread and process pool sizes
- Performance thresholds and limits

Usage:
```python
from .performance import DEFAULT_BATCH_SIZE
```
"""

import multiprocessing
from typing import Dict
from typing import Final

# Batch and chunk sizes
DEFAULT_BATCH_SIZE: Final[int] = 100
DEFAULT_CHUNK_SIZE: Final[int] = 1024 * 1024  # 1 MB
DEFAULT_PAGE_SIZE: Final[int] = 50
DEFAULT_BUFFER_SIZE: Final[int] = 8192  # 8 KB

# Cache configuration
DEFAULT_CACHE_SIZE: Final[int] = 1000
DEFAULT_CACHE_TTL: Final[int] = 300  # 5 minutes in seconds
DEFAULT_NEGATIVE_CACHE_TTL: Final[int] = 60  # 1 minute in seconds
DEFAULT_CACHE_CLEANUP_INTERVAL: Final[int] = 60  # 1 minute in seconds
DEFAULT_CACHE_EVICTION_POLICY: Final[str] = "LRU"

# Thread and process pools
DEFAULT_THREAD_POOL_SIZE: Final[int] = min(multiprocessing.cpu_count() * 2, 16)
DEFAULT_PROCESS_POOL_SIZE: Final[int] = min(multiprocessing.cpu_count(), 8)
DEFAULT_TASK_QUEUE_SIZE: Final[int] = 1000
DEFAULT_WORKER_TIMEOUT: Final[int] = 300  # 5 minutes in seconds

# Performance thresholds and limits
DEFAULT_CPU_THRESHOLD: Final[float] = 0.8  # 80% CPU usage
DEFAULT_MEMORY_THRESHOLD: Final[float] = 0.8  # 80% memory usage
DEFAULT_DISK_THRESHOLD: Final[float] = 0.9  # 90% disk usage
DEFAULT_NETWORK_THRESHOLD: Final[float] = 0.7  # 70% network usage
DEFAULT_LATENCY_THRESHOLD: Final[float] = 0.5  # 500 ms

# Timeouts
DEFAULT_OPERATION_TIMEOUT: Final[float] = 5.0  # 5 seconds
DEFAULT_NETWORK_TIMEOUT: Final[float] = 10.0  # 10 seconds
DEFAULT_DATABASE_TIMEOUT: Final[float] = 30.0  # 30 seconds
DEFAULT_LOCK_TIMEOUT: Final[float] = 10.0  # 10 seconds

# Retry configuration
DEFAULT_RETRY_COUNT: Final[int] = 3
DEFAULT_RETRY_DELAY: Final[float] = 1.0  # 1 second
DEFAULT_RETRY_BACKOFF_FACTOR: Final[float] = 2.0
DEFAULT_RETRY_MAX_DELAY: Final[float] = 60.0  # 60 seconds
DEFAULT_RETRY_JITTER: Final[float] = 0.1  # 10% jitter

# Database configuration
DEFAULT_DB_CONNECTION_POOL_SIZE: Final[int] = 10
DEFAULT_DB_CONNECTION_TIMEOUT: Final[float] = 5.0  # 5 seconds
DEFAULT_DB_OPERATION_TIMEOUT: Final[float] = 30.0  # 30 seconds
DEFAULT_DB_IDLE_TIMEOUT: Final[float] = 300.0  # 5 minutes
DEFAULT_DB_MAX_OVERFLOW: Final[int] = 20

# Vector operations
DEFAULT_VECTOR_DIMENSION: Final[int] = 2048
DEFAULT_VECTOR_SIMILARITY_METRIC: Final[str] = "cosine"
DEFAULT_VECTOR_INDEX_TYPE: Final[str] = "HNSW"
DEFAULT_VECTOR_BATCH_SIZE: Final[int] = 100
DEFAULT_VECTOR_SEARCH_LIMIT: Final[int] = 10
DEFAULT_VECTOR_SEARCH_THRESHOLD: Final[float] = 0.7

# Optimization strategies
DEFAULT_OPTIMIZATION_LEVEL: Final[int] = 2  # 0-3, higher is more aggressive
DEFAULT_OPTIMIZATION_STRATEGIES: Final[Dict[str, bool]] = {
    "vectorization": True,
    "parallelization": True,
    "caching": True,
    "lazy_loading": True,
    "batching": True,
    "compression": True,
    "memory_mapping": True,
}

# Hardware-specific optimizations
M3_OPTIMIZATIONS: Final[Dict[str, bool]] = {
    "metal_acceleration": True,
    "neural_engine": True,
    "unified_memory": True,
    "performance_cores": True,
    "efficiency_cores": True,
}

# Differential dataflow configuration
DIFFERENTIAL_BATCH_SIZE: Final[int] = 1000
DIFFERENTIAL_WORKER_COUNT: Final[int] = min(multiprocessing.cpu_count(), 8)
DIFFERENTIAL_MEMORY_LIMIT: Final[int] = 1024 * 1024 * 1024  # 1 GB
DIFFERENTIAL_ARRANGEMENT_THRESHOLD: Final[int] = 1000
