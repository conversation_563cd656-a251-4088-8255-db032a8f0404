# -*- coding: utf-8 -*-
"""
Concrete implementation of the Event/Effect Log Service using ArangoDB.

Persists event history to an ArangoDB collection, providing query capabilities.

Related Files:
- docs/design/Event_Effect_Log_Service_Design.md
- schemas.python_schema.py
- person_suit.core.actors.central_state_actor.py (Uses this service interface)
- person_suit.core.containers.py (Configures and provides this service)
"""

import logging
import time
from dataclasses import asdict
from dataclasses import is_dataclass
from enum import Enum
from typing import Any
from typing import Dict
from typing import List

# Define missing types
from typing import NewType
from typing import Optional

# ArangoDB Driver
from arango import ArangoClient
from arango.exceptions import CollectionCreateError
from arango.exceptions import DocumentInsertError
from arango.exceptions import IndexCreateError

from ..actors.central_state_actor import EventLogServiceInterface

# Schemas and Interfaces
from ..state_models import BaseEffect
from ..state_models import EventID
from ..state_models import StateChangeNotification
from ..state_models import StateRef

EntityID = NewType("EntityID", str)

# Define a structure for the logged document, potentially adding fields
# not present in StateChangeNotification like prev_ref and context.
# For now, we'll store them directly in the ArangoDB document.
LoggedEventSchema = StateChangeNotification  # Alias for now


class ArangoDBEventLogService(EventLogServiceInterface):
    """
    Event Log Service implementation using ArangoDB.

    Handles connection, data serialization, insertion, and querying.
    Assumes ArangoDB connection details are provided via configuration.
    """

    def __init__(
        self,
        db_host: str,
        db_name: str,
        db_user: str,
        db_password: str,
        collection_name: str = "event_log",
        snapshot_meta_collection: str = "snapshot_metadata",
    ):
        """
        Initializes the service, connects to ArangoDB, and ensures collections/indexes exist.

        Args:
            db_host: ArangoDB host URL (e.g., "http://localhost:8529").
            db_name: Database name.
            db_user: Database username.
            db_password: Database password.
            collection_name: Name of the collection for event logs.
            snapshot_meta_collection: Name of the collection for snapshot metadata.
        """
        self.collection_name = collection_name
        self.snapshot_meta_collection = snapshot_meta_collection
        self._db = None
        self._log_collection = None
        self._snapshot_collection = None

        try:
            # Initialize the ArangoDB client.
            client = ArangoClient(hosts=db_host)

            # Connect to the database.
            self._db = client.db(db_name, username=db_user, password=db_password)

            # Ensure the event log collection exists.
            if self._db.has_collection(collection_name):
                self._log_collection = self._db.collection(collection_name)
                logging.info(
                    f"Connected to existing ArangoDB event log collection: '{collection_name}'"
                )
            else:
                logging.info(
                    f"Creating ArangoDB event log collection: '{collection_name}'"
                )
                self._log_collection = self._db.create_collection(collection_name)

            # Ensure the snapshot metadata collection exists.
            if self._db.has_collection(snapshot_meta_collection):
                self._snapshot_collection = self._db.collection(
                    snapshot_meta_collection
                )
                logging.info(
                    f"Connected to existing ArangoDB snapshot metadata collection: '{snapshot_meta_collection}'"
                )
            else:
                logging.info(
                    f"Creating ArangoDB snapshot metadata collection: '{snapshot_meta_collection}'"
                )
                self._snapshot_collection = self._db.create_collection(
                    snapshot_meta_collection
                )

            # Ensure necessary indexes exist for efficient querying.
            self._ensure_indexes()

        except CollectionCreateError as e:
            logging.exception(f"Failed to create ArangoDB collection: {e}")
            raise
        except Exception as e:
            logging.exception(f"Failed to connect or initialize ArangoDB: {e}")
            raise

        if not self._db or not self._log_collection or not self._snapshot_collection:
            raise ConnectionError(
                "Failed to establish connection or access collections in ArangoDB."
            )

    def _ensure_indexes(self):
        """Creates necessary indexes if they don't exist."""
        try:
            # Index for querying by entity_id and sorting/filtering by timestamp
            self._log_collection.add_persistent_index(
                fields=["entity_id", "timestamp"], unique=False, sparse=False
            )
            logging.info(
                f"Ensured index on ['entity_id', 'timestamp'] for '{self.collection_name}'"
            )

            # Unique index on event_id for idempotency (Arango _key could also be used if event_id is suitable)
            self._log_collection.add_persistent_index(
                fields=["event_id"], unique=True, sparse=False
            )
            logging.info(
                f"Ensured unique index on ['event_id'] for '{self.collection_name}'"
            )

            # Index for snapshot metadata lookup
            self._snapshot_collection.add_persistent_index(
                fields=["entity_id", "last_event_timestamp"], unique=False, sparse=False
            )
            logging.info(
                f"Ensured index on ['entity_id', 'last_event_timestamp'] for '{self.snapshot_meta_collection}'"
            )

        except IndexCreateError as e:
            # May happen if index exists with different definition - log warning
            logging.warning(
                f"Could not create index (may already exist with different definition): {e}"
            )
        except Exception as e:
            logging.exception(f"Error ensuring indexes: {e}")
            raise

    def _serialize_dataclass(self, obj: Any) -> Dict[str, Any]:
        """Recursively converts dataclasses to dictionaries for JSON storage."""
        if isinstance(obj, dict):
            return {k: self._serialize_dataclass(v) for k, v in obj.items()}
        elif isinstance(obj, (list, tuple)):
            return [self._serialize_dataclass(item) for item in obj]
        elif is_dataclass(obj):
            # Convert enum values to their string representation or value
            res = {}
            for key, value in asdict(obj).items():
                if isinstance(value, Enum):
                    res[key] = value.name  # or value.value
                else:
                    res[key] = self._serialize_dataclass(value)
            return res
        # Handle basic types that are already JSON serializable
        elif isinstance(obj, (str, int, float, bool, type(None))):
            return obj
        else:
            # Attempt to convert other types to string, might lose info
            logging.warning(
                f"Attempting to serialize non-standard type {type(obj)} to string."
            )
            return str(obj)

    async def log_event(
        self,
        event_id: EventID,
        effect: BaseEffect,
        context: Context,
        previous_state_ref: Optional[StateRef],
        resulting_state_ref: StateRef,
        entity_id: EntityID,
        timestamp: float,
    ) -> bool:
        """
        Persists the event data to ArangoDB.
        Uses event_id as the document key (_key) for idempotency.
        """
        if not self._log_collection:
            logging.error(
                "ArangoDBEventLogService not properly initialized (no collection)."
            )
            return False

        try:
            # Serialize complex objects (Effect, Context)
            serialized_effect = self._serialize_dataclass(effect)
            serialized_context = self._serialize_dataclass(context)

            # Construct the document
            event_doc = {
                "_key": event_id,  # Use event_id for idempotency via unique key constraint
                "event_id": event_id,
                "entity_id": entity_id,
                "timestamp": timestamp,
                "previous_state_ref": previous_state_ref,
                "resulting_state_ref": resulting_state_ref,
                "triggering_effect": serialized_effect,
                "context": serialized_context,
            }

            # Insert the document
            self._log_collection.insert(
                event_doc, overwrite=False
            )  # overwrite=False enforces idempotency on _key
            logging.info(f"Event logged to ArangoDB: ID={event_id}, Entity={entity_id}")
            return True

        except DocumentInsertError as e:
            # Check if it's a unique constraint violation (meaning already logged)
            if e.http_exception and e.http_exception.status_code == 409:
                logging.warning(
                    f"Attempted to log duplicate event_id {event_id} (ArangoDB key conflict). Assuming success (idempotency)."
                )
                return True  # Idempotent: Already exists
            else:
                logging.exception(
                    f"ArangoDB document insert error logging event {event_id}: {e}"
                )
                return False
        except Exception as e:
            logging.exception(f"Error logging event {event_id} to ArangoDB: {e}")
            return False

    async def query_events(
        self,
        entity_id: EntityID,
        since_sequence: Optional[
            int
        ] = None,  # Sequence numbers not directly used here, relying on timestamp
        since_time: Optional[float] = None,
        limit: Optional[int] = None,
    ) -> List[LoggedEventSchema]:
        """
        Retrieves historical events for a specific entity from ArangoDB.
        NOTE: Sequence number is not directly supported by this Arango implementation,
              filtering relies primarily on timestamp.
        """
        if not self._log_collection or not self._db:
            logging.error("ArangoDBEventLogService not properly initialized.")
            return []

        if since_sequence is not None:
            logging.warning(
                "'since_sequence' filter not directly supported by ArangoDB implementation, use 'since_time'."
            )

        try:
            bind_vars = {"entity_id": entity_id}
            filter_clauses = ["FILTER doc.entity_id == @entity_id"]

            if since_time is not None:
                filter_clauses.append("FILTER doc.timestamp >= @since_time")
                bind_vars["since_time"] = since_time

            aql_query = f"""
                FOR doc IN @@collection
                {" ".join(filter_clauses)}
                SORT doc.timestamp ASC
                {f"LIMIT {limit}" if limit is not None else ""}
                RETURN doc
            """
            # Use collection name directly in query string (AQL injection risk if name is user-controlled)
            # Better: Use bind_vars for collection name if driver supports it
            cursor = self._db.aql.execute(
                aql_query.replace(
                    "@@collection", self.collection_name
                ),  # Replace placeholder
                bind_vars=bind_vars,
                count=False,  # Don't need total count usually
            )

            # Deserialize results (simplified - assumes structure matches LoggedEventSchema)
            # A more robust solution would involve dedicated deserialization logic
            results = []
            for doc in cursor:
                try:
                    # Basic deserialization - needs refinement based on actual schema needed
                    # Might need to reconstruct dataclasses properly
                    results.append(
                        LoggedEventSchema(
                            event_id=doc.get("event_id"),
                            entity_id=doc.get("entity_id"),
                            new_state_ref=doc.get("resulting_state_ref"),
                            triggering_effect=doc.get(
                                "triggering_effect"
                            ),  # Will be dict, needs BaseEffect reconstruction
                            timestamp=doc.get("timestamp"),
                        )
                    )
                except Exception as deser_ex:
                    logging.error(
                        f"Failed to deserialize event document {doc.get('_key')}: {deser_ex}"
                    )
                    # Skip problematic documents or handle differently

            logging.debug(
                f"ArangoDB query returned {len(results)} events for entity {entity_id}"
            )
            return results

        except Exception as e:
            logging.exception(
                f"Error querying events for entity {entity_id} from ArangoDB: {e}"
            )
            return []

    async def get_latest_event(
        self, entity_id: EntityID
    ) -> Optional[LoggedEventSchema]:
        """
        Retrieves the most recent event for an entity from ArangoDB.
        """
        if not self._log_collection or not self._db:
            logging.error("ArangoDBEventLogService not properly initialized.")
            return None

        try:
            aql_query = """
                FOR doc IN @@collection
                FILTER doc.entity_id == @entity_id
                SORT doc.timestamp DESC
                LIMIT 1
                RETURN doc
            """
            cursor = self._db.aql.execute(
                aql_query.replace("@@collection", self.collection_name),
                bind_vars={"entity_id": entity_id},
            )

            doc = cursor.next()
            if doc:
                # Basic deserialization
                latest_event = LoggedEventSchema(
                    event_id=doc.get("event_id"),
                    entity_id=doc.get("entity_id"),
                    new_state_ref=doc.get("resulting_state_ref"),
                    triggering_effect=doc.get("triggering_effect"),
                    timestamp=doc.get("timestamp"),
                )
                logging.debug(
                    f"Latest event for entity {entity_id}: ID={latest_event.event_id}"
                )
                return latest_event
            else:
                logging.debug(f"No events found for entity {entity_id}")
                return None

        except Exception as e:
            logging.exception(
                f"Error getting latest event for entity {entity_id} from ArangoDB: {e}"
            )
            return None

    # --- Snapshot Integration --- #
    async def record_snapshot_metadata(
        self,
        entity_id: EntityID,
        snapshot_state_ref: StateRef,
        last_event_timestamp: float,  # Use timestamp for consistency with querying
        storage_location: str,  # e.g., file path, object key
    ) -> bool:
        """
        Stores snapshot metadata in ArangoDB.
        Uses snapshot_state_ref as the document key (_key) for potential lookup.
        """
        if not self._snapshot_collection:
            logging.error(
                "ArangoDBEventLogService not properly initialized (no snapshot collection)."
            )
            return False
        try:
            snapshot_doc = {
                "_key": snapshot_state_ref,
                "entity_id": entity_id,
                "snapshot_state_ref": snapshot_state_ref,
                "last_event_timestamp": last_event_timestamp,
                "storage_location": storage_location,
                "recorded_at": time.time(),
            }
            self._snapshot_collection.insert(
                snapshot_doc, overwrite=True
            )  # Allow overwriting if ref reappears?
            logging.info(
                f"Snapshot metadata recorded for Entity={entity_id}, Ref={snapshot_state_ref}"
            )
            return True
        except Exception as e:
            logging.exception(
                f"Error recording snapshot metadata for entity {entity_id}: {e}"
            )
            return False

    async def get_latest_snapshot_metadata(
        self, entity_id: EntityID, before_time: Optional[float] = None
    ) -> Optional[Dict[str, Any]]:
        """
        Finds the latest snapshot metadata for an entity, optionally before a certain time.
        """
        if not self._snapshot_collection or not self._db:
            logging.error("ArangoDBEventLogService not properly initialized.")
            return None
        try:
            bind_vars = {"entity_id": entity_id}
            filter_clauses = ["FILTER doc.entity_id == @entity_id"]
            if before_time is not None:
                filter_clauses.append("FILTER doc.last_event_timestamp < @before_time")
                bind_vars["before_time"] = before_time

            aql_query = f"""
                 FOR doc IN @@collection
                 {" ".join(filter_clauses)}
                 SORT doc.last_event_timestamp DESC
                 LIMIT 1
                 RETURN doc
             """
            cursor = self._db.aql.execute(
                aql_query.replace("@@collection", self.snapshot_meta_collection),
                bind_vars=bind_vars,
            )
            doc = cursor.next()
            if doc:
                logging.debug(
                    f"Found latest snapshot metadata for {entity_id}: {doc.get('_key')}"
                )
                return doc
            else:
                logging.debug(
                    f"No suitable snapshot metadata found for {entity_id} before {before_time}"
                )
                return None
        except Exception as e:
            logging.exception(
                f"Error getting latest snapshot metadata for entity {entity_id}: {e}"
            )
            return None
