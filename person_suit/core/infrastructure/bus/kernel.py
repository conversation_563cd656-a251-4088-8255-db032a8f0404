"""person_suit.core.infrastructure.bus.kernel
================================================
Light-weight *Kernel* of the Hybrid Message Bus.

Responsibilities (and *only* these):
1.  Accept `HybridMessage` instances and place them on an ACF-aware
    priority queue.
2.  Maintain a subscription registry and route messages to matching
    handlers.
3.  Provide lifecycle control (`start/stop`) and a global singleton
    (`get_message_bus`).
4.  Support middleware integration for enhanced functionality.

All security, provenance, telemetry, ACF-adaptation, etc. live in
**middleware actors** imported by higher-level modules – keeping this
kernel <400 LOC and free of circular-import pain.
"""
from __future__ import annotations

import asyncio
import logging
import uuid
from collections import defaultdict
from typing import TYPE_CHECKING
from typing import Any
from typing import Awaitable
from typing import Callable
from typing import Dict
from typing import List
from typing import Optional

from person_suit.core.constants.fixed_point_scale import PRIO_HIGH
from person_suit.core.constants.fixed_point_scale import PRIO_NORMAL
from person_suit.core.constants.fixed_point_scale import SCALE
from person_suit.core.constants.fixed_point_scale import bucket_to_float
from person_suit.core.context.unified import UnifiedContext
from person_suit.core.infrastructure.channel_registry import ChannelRegistry
from person_suit.core.infrastructure.channel_registry import get_channel_registry
from person_suit.core.infrastructure.deployment_profile import DeploymentProfile
from person_suit.core.infrastructure.deployment_profile import DeploymentProfiles
from person_suit.core.infrastructure.hybrid_message import ChannelSubscription
from person_suit.core.infrastructure.hybrid_message import HybridMessage
from person_suit.core.infrastructure.hybrid_message import MessageResult
from person_suit.core.infrastructure.hybrid_message import MessageType
from person_suit.core.infrastructure.hybrid_message import validate as _validate_envelope
from person_suit.core.infrastructure.time_utils import timer

if TYPE_CHECKING:
    from typing import Protocol

    class _ProvenanceProcessorProtocol(Protocol):  # noqa: D401
        def prepare_message_provenance(self, msg: "HybridMessage") -> None: ...
        def record_message_provenance(self, msg: "HybridMessage", event: str) -> None: ...

    class _BusKernelMixin(Protocol):  # noqa: D401
        def _determine_processing_strategy(self, msg: "HybridMessage") -> Dict[str, Any]: ...
        async def _adapt_message_for_fidelity(self, msg: "HybridMessage", fidelity: int) -> None: ...
        async def _authorize_message(self, msg: "HybridMessage") -> bool: ...
        async def _record_message_provenance(self, msg: "HybridMessage", event: str) -> None: ...
        async def _authorize_subscription(self, pattern: str, capability_token: Optional[str]) -> bool: ...
        _provenance_processor: _ProvenanceProcessorProtocol

logger = logging.getLogger(__name__)

###############################################################################
# Priority Queue
###############################################################################


class PriorityMessageQueue:
    """Simple priority queue aware of `HybridMessage.priority`."""

    def __init__(self, profile: DeploymentProfile):
        self._queue: asyncio.PriorityQueue[tuple[int, float, HybridMessage]] = (
            asyncio.PriorityQueue(maxsize=profile.max_queue_depth)
        )
        self._dropped = 0
        self._expired = 0

    async def put(self, message: HybridMessage) -> bool:  # noqa: D401
        """Enqueue *message* with back-pressure when approaching full capacity."""
        # Integer queue utilization calculation (eliminates float arithmetic from hot path)
        if self._queue.maxsize > 0:
            queue_utilization_int = (self._queue.qsize() * SCALE) // self._queue.maxsize
        else:
            queue_utilization_int = 0
        
        # Apply back-pressure when queue is 90% full (0.9 * SCALE = 900,000)
        backpressure_threshold_int = (9 * SCALE) // 10  # 900,000 for 90%
        if queue_utilization_int >= backpressure_threshold_int and not self._queue.full():
            # Wait up to 100ms for space, giving consumers time to process
            try:
                await asyncio.wait_for(
                    self._queue.put((-message.priority_int, message.timestamp, message)),
                    timeout=0.1
                )
                return True
            except asyncio.TimeoutError:
                # Still full after waiting, drop the message
                self._dropped += 1
                return False
        elif self._queue.full():
            # Queue completely full, immediate drop
            self._dropped += 1
            return False
        else:
            # Normal operation - queue has space
            await self._queue.put((-message.priority_int, message.timestamp, message))
            return True

    async def get(self) -> Optional[HybridMessage]:
        try:
            _, _, msg = await asyncio.wait_for(self._queue.get(), timeout=1.0)
            return msg
        except asyncio.TimeoutError:
            return None

    # ---------- metrics helpers ------------------------------------------------
    @property
    def depth(self) -> int:  # noqa: D401
        return self._queue.qsize()

    @property
    def dropped(self) -> int:  # noqa: D401
        return self._dropped

    @property
    def expired(self) -> int:  # noqa: D401
        return self._expired
    
    @property
    def utilization_int(self) -> int:  # noqa: D401
        """Get queue utilization as integer bucket (0 to SCALE)."""
        if self._queue.maxsize > 0:
            return (self._queue.qsize() * SCALE) // self._queue.maxsize
        return 0
    
    @property
    def utilization_float(self) -> float:  # noqa: D401
        """Get queue utilization as float (0.0 to 1.0) for backward compatibility."""
        return bucket_to_float(self.utilization_int)
    
    @property
    def utilization(self) -> float:  # noqa: D401
        """Alias for utilization_float for backward compatibility."""
        return self.utilization_float

###############################################################################
# Channel Router
###############################################################################


class ChannelRouter:
    """Match channels to subscribers using wild-card patterns."""

    def __init__(self, registry: ChannelRegistry):
        self._registry = registry
        self._subs: Dict[str, List[ChannelSubscription]] = defaultdict(list)

    # --------------------------------------------------------------------- API
    def subscribe(self, sub: ChannelSubscription) -> None:
        self._subs[sub.channel_pattern].append(sub)

    def unsubscribe(self, sub_id: str, pattern: str) -> None:
        self._subs[pattern] = [s for s in self._subs[pattern] if s.subscriber_id != sub_id]

    def match(self, channel: str) -> List[ChannelSubscription]:
        """Find all subscriptions that match the given channel."""
        matching_subs: List[ChannelSubscription] = []
        
        # Iterate through all registered patterns
        for pattern, subscriptions in self._subs.items():
            if self._pattern_matches_channel(pattern, channel):
                matching_subs.extend(subscriptions)
        
        # Remove duplicates and sort by priority
        seen = set()
        unique_out = []
        for sub in matching_subs:
            if sub.subscriber_id not in seen:
                seen.add(sub.subscriber_id)
                unique_out.append(sub)
        
        return sorted(unique_out, key=lambda s: s.priority, reverse=True)

    def _pattern_matches_channel(self, pattern: str, channel: str) -> bool:
        """Check if a pattern with wildcards matches a channel."""
        pattern_parts = pattern.split('.')
        channel_parts = channel.split('.')
        
        return self._match_recursive(pattern_parts, channel_parts)

    def _match_recursive(self, pattern_parts: List[str], channel_parts: List[str]) -> bool:
        """Recursive helper for pattern matching."""
        if not pattern_parts:
            return not channel_parts

        if not channel_parts:
            return pattern_parts == ['#'] or (len(pattern_parts) == 1 and pattern_parts[0] == '*')

        p_part = pattern_parts[0]
        c_part = channel_parts[0]

        if p_part == "#":
            return True

        if p_part == "*":
            return self._match_recursive(pattern_parts[1:], channel_parts[1:])

        if p_part == c_part:
            return self._match_recursive(pattern_parts[1:], channel_parts[1:])

        return False

    # ------------------------------------------------------------------
    # Backwards-compat convenience property – several legacy tests expect
    # ``bus.router.registry`` to exist.  We expose it as a read-only alias
    # to the internal ``_registry`` reference.  This does **not** grant
    # write access and therefore maintains encapsulation guarantees.
    # ------------------------------------------------------------------
    @property
    def registry(self) -> ChannelRegistry:  # noqa: D401 – simple alias
        """Return the underlying ChannelRegistry instance (read-only)."""
        return self._registry

###############################################################################
# Bus Kernel
###############################################################################


class BusKernel:
    def __init__(self, profile: Optional[DeploymentProfile] = None):
        self.profile = profile or self._detect_profile()
        self.queue = PriorityMessageQueue(self.profile)
        self.router = ChannelRouter(get_channel_registry())
        self._running = False
        self._workers: List[asyncio.Task] = []
        self._result_waiters: Dict[str, Callable[[MessageResult], None]] = {}
        self._handler_tasks: set[asyncio.Task] = set()
        self._housekeeping_tasks: set[asyncio.Task] = set()
        # Statistics for middleware use
        self._stats = {
            "messages_processed": 0,
            "total_latency_ms": 0.0,
            "retries_attempted": 0,
            "effects_executed": 0,
        }
        # ------------------------------------------------------------------
        # Differential Context Propagation (cache of last seen contexts)
        # ------------------------------------------------------------------
        self._context_cache: Dict[str, Dict[str, Any]] = {}
        self._context_cache_max: int = 10000  # Prevent unbounded growth
        # ------------------------------------------------------------------
        # Ready flag – set once all middleware & registries have finished
        # initialisation inside ``start``.  `send()` waits on this event so
        # the very first message benefits from fully-wired ACF/security/etc.
        # ------------------------------------------------------------------
        self._ready_evt: asyncio.Event = asyncio.Event()
        
        # Define optional middleware processor attributes for type hinting.
        # These are attached by the HybridMessageBus subclass after initialization.
        self.acf_processor: Optional[Any] = None
        self.provenance_processor: Optional[Any] = None
        self.security_processor: Optional[Any] = None

    # ------------------------------------------------------------------ lifecycle
    async def start(self) -> None:
        if self._running:
            return
        self._running = True
        logger.info("BusKernel started (%s)", self.profile.environment.value)
        
        # --------------------------------------------------------------
        # Determine optimal worker count – allow dynamic adjustment for
        # high-core machines such as Apple M-series chips.  When the
        # deployment profile sets ``bus_workers`` to 0 (or negative) we
        # auto-detect.  The feature can also be toggled explicitly via
        # environment variable ``PS_AUTO_WORKERS=1`` which forces an
        # auto-calculation regardless of the profile value.  This keeps
        # backward compatibility with existing tests that rely on a fixed
        # worker count while enabling production instances to scale.
        # --------------------------------------------------------------
        import os  # noqa: WPS433
        auto_flag = os.getenv("PS_AUTO_WORKERS", "0") == "1"
        configured_workers = getattr(self.profile, "bus_workers", 1) or 0

        if auto_flag or configured_workers <= 0:
            cpu_total = os.cpu_count() or 4  # Fallback to 4 if detection fails
            # Empirical sweet-spot: ~0.75× logical CPUs balances context-switch
            # overhead vs. parallelism for the mostly async workload.
            num_workers = max(1, int(cpu_total * 0.75))
        else:
            num_workers = configured_workers

        for i in range(num_workers):
            worker = asyncio.create_task(self._loop())
            self._workers.append(worker)
        logger.info("Started %d bus worker(s)", num_workers)

        # All middleware is initialized by the HybridMessageBus subclass.
        # This kernel remains lean.

        self._ready_evt.set()

        # --------------------------------------------------------------
        # Attach ACF middleware so stand-alone BusKernel instances used
        # by tests (e.g. ``tests/bus/test_acf_dynamic_adaptation.py``)
        # expose strategy helpers without requiring the full
        # ``HybridMessageBus`` wrapper.
        # --------------------------------------------------------------
        if getattr(self, "acf_processor", None) is None:  # type: ignore[attr-defined]
            try:
                from person_suit.core.infrastructure.middleware import (
                    acf as _acf,  # noqa: WPS433 – runtime import avoids heavy deps
                )

                await _acf.initialize(self)  # Injects helper aliases
            except Exception as exc:  # noqa: BLE001 – tests rely on graceful degradation
                logger.debug("ACF middleware initialisation failed: %s", exc)

    async def stop(self) -> None:
        """Stop the bus kernel gracefully."""
        if not self._running:
            return  # Already stopped
            
        logger.info("Stopping BusKernel...")
        self._running = False
        
        # Shutdown provenance processor if available
        if hasattr(self, 'provenance_processor') and self.provenance_processor:
            if hasattr(self.provenance_processor, 'shutdown'):
                try:
                    await self.provenance_processor.shutdown()
                    logger.info("Provenance processor shutdown complete")
                except Exception as e:
                    logger.error(f"Error shutting down provenance processor: {e}")
        
        # Signal shutdown to queue processing
        try:
            if hasattr(self, 'queue') and self.queue:
                await self.queue.put(None)  # type: ignore[arg-type]  # Shutdown signal
        except Exception as e:
            logger.debug("Error signaling queue shutdown: %s", e)
        
        # Cancel all worker tasks gracefully
        if hasattr(self, '_workers'):
            for worker in self._workers:
                if not worker.done():
                    worker.cancel()
            
            # Wait for workers to finish with timeout
            if self._workers:
                try:
                    await asyncio.wait_for(
                        asyncio.gather(*self._workers, return_exceptions=True),
                        timeout=2.0
                    )
                except asyncio.TimeoutError:
                    logger.warning("Some workers did not shut down gracefully")
                except Exception as e:
                    logger.debug("Error during worker shutdown: %s", e)
        
        # Clear worker references
        self._workers = []
        
        logger.info("BusKernel stopped")

    # ------------------------------------------------------------------ API
    async def send(self, msg: HybridMessage, timeout: Optional[float] = None) -> Optional[MessageResult]:
        # Ensure middleware hooks are attached before preparing message –
        # guarantees ACF degradation applies even to the very first message.
        if not self._ready_evt.is_set():
            await self._ready_evt.wait()
        # Prepare message (middleware will enhance this)
        self._prepare_message(msg)
        
        msg_to_queue = msg  # Default reference
        if hasattr(self, "acf_processor") and self.acf_processor and hasattr(self.acf_processor, "_determine_processing_strategy"):
            try:
                strat_pre = self.acf_processor.determine_processing_strategy(msg)
                logger.debug("send pre-queue ACF strat: %s", strat_pre)
                fid_pre = strat_pre.get("fidelity")
                if fid_pre is not None and fid_pre != getattr(msg.acf_metadata, "fidelity", None):
                    await self.acf_processor.adapt_message_for_fidelity(msg, fid_pre)
                    # Clone to ensure queue receives the mutated fidelity (PriorityQueue may keep the object ref)
                    import copy
                    msg_to_queue = copy.deepcopy(msg)
            except Exception as exc:  # noqa: BLE001
                logger.debug("Pre-queue ACF strategy failed: %s", exc)
        
        # Apply middleware hooks if available
        if hasattr(self, "provenance_processor") and self.provenance_processor:
            self.provenance_processor.prepare_message_provenance(msg)
        
        # Authorization check
        if (
            hasattr(self, "security_processor")
            and self.security_processor
            and hasattr(self.security_processor, "authorize_message")
        ):
            authorized = await self.security_processor.authorize_message(msg)
            if not authorized:
                return MessageResult(
                    success=False,
                    message_id=msg.message_id,
                    handler_id="security",
                    processing_time_ms=0,
                    error="Authorization failed"
                )
        
        # Record provenance if enabled
        if hasattr(self, "provenance_processor") and self.provenance_processor:
            await self.provenance_processor.record_message_provenance(msg, "sent")
        
        # ACF strategy applied pre-queue
        
        # Queue the message (use potentially cloned version)
        if not await self.queue.put(msg_to_queue):
            return None  # dropped
            
        if msg.response_expected:
            fut: asyncio.Future[MessageResult] = asyncio.Future()
            self._result_waiters[msg.message_id] = fut.set_result  # type: ignore[arg-type]
            try:
                return await asyncio.wait_for(fut, timeout=timeout)
            except asyncio.TimeoutError:
                self._result_waiters.pop(msg.message_id, None)
                return None
        return None

    async def subscribe(
        self,
        pattern: str | None = None,
        handler: Callable[[HybridMessage], Awaitable[Any]] | None = None,
        *,
        # legacy kw aliases
        channel_pattern: str | None = None,
        subscriber_id: Optional[str] = None,
        handler_priority: int = 0,
        priority: int = 0,
        filter_func: Optional[Callable[[HybridMessage], bool]] = None,
        capability_token: Optional[str] = None,
    ) -> str:
        """Subscribe handler; supports both new and legacy kw names."""
        if pattern is None:
            pattern = channel_pattern  # type: ignore[assignment]
        assert pattern and handler  # simple validation
        
        prio = handler_priority or priority
        sub_id = subscriber_id or f"sub_{timer.timestamp_ms()}"
        
        # Authorization check for subscription (with subscriber_id tracking)
        if (
            hasattr(self, "security_processor")
            and self.security_processor
            and hasattr(self.security_processor, "authorize_subscription")
        ):
            # New signature includes *subscriber_id* kw-only param; tolerate older processors.
            try:
                authorized = await self.security_processor.authorize_subscription(
                    pattern, capability_token, subscriber_id=sub_id
                )
            except TypeError:
                # Fallback to legacy two-parameter signature
                authorized = await self.security_processor.authorize_subscription(
                    pattern, capability_token
                )
            if not authorized:
                raise PermissionError(f"Not authorized to subscribe to {pattern}")
        self.router.subscribe(
            ChannelSubscription(
                subscriber_id=sub_id,
                channel_pattern=pattern,
                handler=handler,  # type: ignore[arg-type]
                priority=prio,
                filter_func=filter_func,
                capability_token=capability_token,
            )
        )
        return sub_id

    def unsubscribe(self, sub_id: str, pattern: str) -> None:
        self.router.unsubscribe(sub_id, pattern)
        # Inform security processor so it can drop capability tracking
        if (
            hasattr(self, "security_processor")
            and self.security_processor
            and hasattr(self.security_processor, "remove_subscriber_capability")
        ):
            try:
                self.security_processor.remove_subscriber_capability(sub_id)
            except Exception:  # noqa: BLE001
                pass

    # ------------------------------------------------------------------ internals
    async def _loop(self) -> None:
        while self._running:
            msg = await self.queue.get()
            if msg is None:
                continue
            await self._dispatch(msg)

    async def _dispatch(self, msg: HybridMessage) -> None:
        """Enhanced dispatch with middleware integration and retry logic."""
        logger.debug(f"Dispatching message {msg.message_id} on channel {msg.channel}")
        start_time_ns = timer.now_ns()
        msg.metadata.setdefault('processing_start_ns', start_time_ns)
        
        # Get matching subscriptions
        initial_subscriptions = self.router.match(msg.channel)
        logger.debug(f"Found {len(initial_subscriptions)} initial subscriptions for {msg.channel}")
        
        # ------------------------------------------------------------------
        # Capability-aware routing (Capability-Aware Routing Rule)
        # ------------------------------------------------------------------
        final_subscriptions = initial_subscriptions
        required_cap = None
        
        # Use self.router.registry to access the channel registry instance
        if hasattr(self.router, '_registry') and self.router._registry:
            try:
                sec_reqs = self.router._registry.get_security_requirements(msg.channel)
                required_cap = sec_reqs.get("min_capability")
            except Exception:  # noqa: BLE001 – registry may not be initialised yet or channel not defined
                pass

        if required_cap:
            final_subscriptions = [
                s for s in initial_subscriptions if s.capability_token == required_cap
            ]
        
        if not final_subscriptions:
            logger.debug("No subscribers for message on channel %s", msg.channel)

            self._notify_waiter(msg, MessageResult(
                success=False,
                message_id=msg.message_id,
                handler_id="router",
                processing_time_ms=0,
                error="No authorized subscribers found" if required_cap else "No subscribers found"
            ))
            return
        
        # --------------------------------------------------------------
        # ACF closed-loop – adjust message fidelity just before handlers
        # --------------------------------------------------------------
        if hasattr(self, "acf_processor") and self.acf_processor:
            logger.debug("ACF processor found for %s", msg.message_id)
            try:
                # Pass live queue depth to the strategy determination
                live_metrics = {"queue_depth": self.queue.depth}
                strat = self.acf_processor.determine_processing_strategy(msg, system_metrics=live_metrics)
                fidelity = strat.get("fidelity", None)
                logger.debug("ACF strategy for %s: %s", msg.channel, strat)
                # Apply adaptation only if it *reduces* fidelity – avoid overwriting
                # earlier degradation decided pre-queue (prevents test oscillation).
                if fidelity is not None and fidelity < msg.acf_metadata.fidelity:
                    logger.debug("ACF adapting message %s from fidelity %s to %s", 
                                msg.message_id, msg.acf_metadata.fidelity, fidelity)
                    await self.acf_processor.adapt_message_for_fidelity(msg, fidelity)
                    logger.debug("ACF adapted message %s to fidelity %s", 
                                msg.message_id, msg.acf_metadata.fidelity)
            except Exception as exc:  # noqa: BLE001
                logger.debug("ACF strategy failed: %s", exc)
        else:
            logger.debug("ACF methods not available: acf_processor=%s", hasattr(self, "acf_processor"))

        # Execute handlers
        results = []
        for sub in final_subscriptions:
            logger.debug(f"Executing handler {sub.subscriber_id} for message {msg.message_id}")
            try:
                # Apply execution constraints
                if hasattr(msg, 'execution_constraints') and msg.execution_constraints.max_latency_ms:
                    result = await asyncio.wait_for(
                        sub.handler(msg),
                        timeout=msg.execution_constraints.max_latency_ms / 1000
                    )
                else:
                    result = await sub.handler(msg)
                
                processing_time_ms = timer.elapsed_ms(start_time_ns)
                results.append(MessageResult(
                    success=True,
                    message_id=msg.message_id,
                    handler_id=sub.subscriber_id,
                    processing_time_ms=processing_time_ms,
                    response=result if isinstance(result, HybridMessage) else None,
                ))
                
            except Exception as exc:  # noqa: BLE001
                logger.error(f"Handler {sub.subscriber_id} for {msg.message_id} failed: {exc}", exc_info=True)
                processing_time_ms = timer.elapsed_ms(start_time_ns)
                results.append(MessageResult(
                    success=False,
                    message_id=msg.message_id,
                    handler_id=sub.subscriber_id,
                    processing_time_ms=processing_time_ms,
                    error=str(exc)
                ))
        
        logger.debug(f"Finished dispatching message {msg.message_id}")
        # Update statistics
        self._stats["messages_processed"] += 1
        self._stats["total_latency_ms"] += timer.elapsed_ms(start_time_ns)
        
        # Handle retry logic if all handlers failed
        if results and all(not r.success for r in results):
            retry_handled = await self._handle_retry_logic(msg, results)
            if retry_handled:
                return  # Message will be requeued
        
        # Notify waiter with best result
        if results:
            successful_result = next((r for r in results if r.success), None)
            result_to_return = successful_result or results[-1]
        else:
            result_to_return = MessageResult(
                success=False,
                message_id=msg.message_id,
                handler_id="dispatcher",
                processing_time_ms=timer.elapsed_ms(start_time_ns),
                error="No handlers executed"
            )
        
        self._notify_waiter(msg, result_to_return)
        
        # Record provenance for completion
        if hasattr(self, "provenance_processor") and self.provenance_processor:
            event_type = "processed" if result_to_return.success else "failed"
            await self.provenance_processor.record_message_provenance(msg, event_type)
        
        # Sprint 2 Enhancement: Publish response messages (e.g., effects) returned by handlers
        # This enables the Command->Effect->Event flow
        for result in results:
            if result.success and result.response and isinstance(result.response, HybridMessage):
                logger.debug(
                    "Handler %s returned response message on channel %s, publishing",
                    result.handler_id,
                    result.response.channel
                )
                # Preserve causality chain
                if not result.response.correlation_id:
                    result.response.correlation_id = msg.correlation_id or msg.message_id
                result.response.causality_chain = msg.causality_chain + [msg.message_id]
                
                # ------------------------------------------------------
                # WaveTrace propagation – copy trace context & parent span
                # ------------------------------------------------------
                if not getattr(result.response, "trace_id", None):
                    # Inherit trace – new child span
                    result.response.trace_id = msg.trace_id
                result.response.parent_span_id = msg.span_id
                # Generate fresh span_id to represent this processing step
                result.response.span_id = uuid.uuid4().hex[:16]
                
                # Enqueue the response message (e.g., an effect to be processed)
                await self._enqueue_internal(result.response)
    
    async def _handle_retry_logic(self, msg: HybridMessage, failed_results: List[MessageResult]) -> bool:
        """Handle retry logic for failed messages."""
        if not hasattr(msg, 'execution_constraints'):
            return False
            
        retry_count = msg.metadata.get("retry_count", 0)
        max_retries = getattr(msg.execution_constraints, 'max_retries', 0)
        
        if retry_count >= max_retries:
            # Emit retry exceeded event
            await self._emit_retry_exceeded_event(msg, retry_count)
            return False
        
        # Calculate retry delay
        delay_ms = self._calculate_retry_delay(retry_count, msg)
        
        # Update retry metadata
        msg.metadata["retry_count"] = retry_count + 1
        self._stats["retries_attempted"] += 1
        
        # Schedule retry
        async def _requeue_after_delay() -> None:
            await asyncio.sleep(delay_ms / 1000)
            await self.queue.put(msg)
        
        asyncio.create_task(_requeue_after_delay())
        logger.debug("Retrying message %s (attempt %d/%d)", 
                    msg.message_id, retry_count + 1, max_retries)
        return True
    
    def _calculate_retry_delay(self, retry_count: int, msg: HybridMessage) -> float:
        """Calculate retry delay using exponential backoff with integer arithmetic."""
        base_delay_ms = 1000  # 1 second base
        max_delay_ms = 30000  # 30 seconds max
        
        # Calculate exponential backoff using bit shifting (faster than pow)
        delay_ms = min(base_delay_ms << retry_count, max_delay_ms)
        
        # Add jitter using integer arithmetic (10-30% of delay)
        import random
        jitter_percent = random.randint(10, 30)  # Integer percentage
        jitter_ms = (delay_ms * jitter_percent) // 100
        
        # Return integer milliseconds converted to float seconds for asyncio.sleep()
        total_delay_ms = delay_ms + jitter_ms
        return total_delay_ms / 1000.0  # Convert to seconds for asyncio.sleep()
    
    async def _emit_retry_exceeded_event(self, msg: HybridMessage, retry_count: int) -> None:
        """Emit an event when retry limit is exceeded."""
        exceeded_evt = HybridMessage(
            message_type=MessageType.EVENT,
            channel="sys.retry.exceeded",
            payload={
                "message_id": msg.message_id,
                "channel": msg.channel,
                "retries": retry_count,
                "reason": "max_retries_exceeded",
            },
            response_expected=False,
            metadata={"record_provenance": False},
        )
        await self.queue.put(exceeded_evt)
    
    def _notify_waiter(self, msg: HybridMessage, result: MessageResult) -> None:
        """Notify any waiting caller with the message result."""
        cb = self._result_waiters.pop(msg.message_id, None)
        if cb:
            cb(result)

    # ------------------------------------------------------------------ helpers
    def _prepare(self, msg: HybridMessage) -> None:
        # Attempt early validation. If it fails, we will augment missing
        # fields (context, dual states, etc.) and re-validate afterwards.
        try:
            _validate_envelope(msg)
            validated = True
        except ValueError:
            validated = False

        # ------------------------------------------------------------------
        # Priority handling – defer default assignment until ChannelRegistry
        # merge runs so we can honour per-channel defaults such as 0.7 for
        # ``pc.memory.encode``.  Only *coerce* if the producer explicitly
        # supplied a float value.
        # ------------------------------------------------------------------
        try:
            # Priority_bucket is already set by HybridMessage.__post_init__
            # We don't need to do anything here
            pass
        except Exception:  # noqa: BLE001 – maintain legacy tolerance
            pass

        # ------------------------------------------------------------------
        # Auto-inject UnifiedContext when absent so every message carries the
        # canonical context required by downstream middleware (CAW principle).
        # ------------------------------------------------------------------
        if not msg.context:
            msg.context = UnifiedContext.create_default(domain=msg.meta_system).to_dict()

        # ------------------------------------------------------------------
        # Ensure dual representation (wave / particle) objects are present –
        # many legacy producers still omit these but validators expect them.
        # ------------------------------------------------------------------
        if msg.wave_state is None or msg.particle_state is None:
            try:
                from person_suit.core.infrastructure.hybrid_message import ParticleState
                from person_suit.core.infrastructure.hybrid_message import WaveState

                if msg.wave_state is None:
                    msg.wave_state = WaveState(metadata={"auto": True})  # type: ignore[arg-type]
                if msg.particle_state is None:
                    msg.particle_state = ParticleState(metadata={"auto": True})  # type: ignore[arg-type]
            except Exception:  # noqa: BLE001
                # If importing fails we still continue; final validation will
                # raise if these objects are truly mandatory.
                pass

        # ------------------------------------------------------------------
        # Final validation (only if the initial attempt failed) – guarantees
        # envelope is now fully compliant without penalising already valid
        # messages with an extra validation pass.
        # ------------------------------------------------------------------
        if not validated:
            _validate_envelope(msg)

        # ------------------------------------------------------------------
        # Differential Context Propagation – emit minimal incremental updates
        # ------------------------------------------------------------------
        try:
            self._process_context_diff(msg)
        except Exception as exc:  # noqa: BLE001
            logger.debug("Context diff processing failed: %s", exc)

    @staticmethod
    def _detect_profile() -> DeploymentProfile:
        import platform  # noqa: WPS433
        machine = platform.machine().lower()
        system = platform.system().lower()

        # Apple Silicon (arm64) or generic desktop
        if machine in {"arm64", "aarch64"} and system == "darwin":
            return DeploymentProfiles.workstation()

        return DeploymentProfiles.server()

    # ------------------------------------------------------------------ middleware helper
    def register_housekeeping_task(self, task: asyncio.Task) -> None:  # noqa: D401
        """Allow middleware to register long-running background tasks."""
        self._housekeeping_tasks.add(task)

    def register_security_manager(self, manager: Any) -> None:  # noqa: D401
        """Dynamically register *manager* as the AdaptiveSecurityManager instance.

        This helper fulfils the Hybrid Message Bus roadmap (Sprint-2) allowing
        deployments to plug a custom security manager at runtime (e.g. a gRPC
        micro-service).

        Args:
            manager: An object implementing the same public API as
                ``security.adaptive_security.AdaptiveSecurityManager``.  The
                exact protocol is intentionally *duck-typed* because the
                adaptive manager evolves independently of the bus.
        """
        if hasattr(self, "security_processor") and self.security_processor:
            try:
                # The processor exposes *_security_manager* attribute for lazy
                # loading.  We simply overwrite it – subsequent authorisation calls
                # will use the newly supplied manager.
                self.security_processor._security_manager = manager  # type: ignore[attr-defined]
                # Advertise change via internal event for observability.
                import time  # noqa: WPS433

                from person_suit.core.infrastructure.hybrid_message import HybridMessage

                evt = HybridMessage(
                    message_type=MessageType.EVENT,
                    channel="sys.security.manager.registered",
                    payload={
                        "timestamp": time.time(),
                        "manager_class": manager.__class__.__name__,
                    },
                    response_expected=False,
                    metadata={"record_provenance": False},
                )
                # Fire-and-forget – enqueue via internal helper
                asyncio.create_task(self._enqueue_internal(evt))
            except Exception as exc:  # noqa: BLE001
                import logging as _lg

                _lg.getLogger(__name__).error("Failed to register security manager: %s", exc)
                raise
        else:
            raise RuntimeError("Security middleware not initialised; cannot register security manager")

    # legacy alias for processing task state in tests
    @property
    def _processing_task(self):  # noqa: D401
        # Return first worker for backward compatibility
        return self._workers[0] if self._workers else None
    
    # Legacy property for tests that expect _worker
    @property
    def _worker(self):  # noqa: D401
        # Return first worker for backward compatibility
        return self._workers[0] if self._workers else None

    # expose registry for legacy tests
    @property
    def registry(self):  # noqa: D401
        return self.router._registry

    # ------------------------------------------------------------------ legacy aliases
    @property
    def _is_running(self):  # noqa: D401 – legacy alias for tests
        """Legacy property expected by older test suites."""
        return self._running

    @property
    def is_running(self) -> bool:  # noqa: D401
        """Return True if the BusKernel workers are active."""
        return self._running

    def get_statistics(self) -> Dict[str, Any]:
        """Get current bus statistics."""
        return {
            "is_running": self._running,
            "queue_depth": self.queue.depth,
            "dropped_messages": self.queue.dropped,
            "expired_messages": self.queue.expired,
            "active_handlers": len(self._handler_tasks),
            "deployment_profile": self.profile.environment.value,
            "messages_sent": self._stats["messages_processed"],  # Backward compatibility
            **self._stats
        }

    def _prepare_message(self, msg: "HybridMessage") -> None:  # noqa: D401
        """Augment *msg* with channel defaults & provenance before queuing.

        This helper back-ports the richer envelope augmentation that used to
        live inside *hybrid_message_bus_legacy* so that middleware and tests
        can rely on it without importing the legacy module.  It deliberately
        re-uses the lighter ``_prepare`` first and then layers extra CAW
        defaults on top.  Validation is rerun only when additional fields
        were added to minimise overhead.
        """
        # Run base preparation (context, dual states, early validation)
        self._prepare(msg)

        # ------------------------------------------------------------------
        # ChannelRegistry defaults (priority, ACF, wave/particle ratio)
        # ------------------------------------------------------------------
        chan_def = None
        try:
            chan_def = self.registry.get_channel(msg.channel)
        except Exception:  # noqa: BLE001 – registry may not be initialised yet
            pass

        if chan_def is not None:
            # Priority default – honour float values (0.7) expected by tests.
            pr_def = chan_def.priority_default
            # pr_def should already be int bucket from ChannelDefinition.__post_init__
            msg._priority_scaled = pr_def  # type: ignore[attr-defined]
            # Update priority float for backward compatibility
            msg.priority = pr_def / SCALE  # type: ignore[assignment]

            # ACF defaults
            if hasattr(msg, "acf_metadata") and getattr(msg.acf_metadata, "fidelity", 1.0) == 1.0:
                for key, value in chan_def.acf_defaults.items():
                    if hasattr(msg.acf_metadata, key):
                        setattr(msg.acf_metadata, key, value)

            # Dual representation ratio
            if (
                getattr(msg, "wave_particle_ratio", 0.5) == 0.5
                and chan_def.default_wave_particle_ratio is not None
            ):
                msg.wave_particle_ratio = chan_def.default_wave_particle_ratio

            # QoS latency budgets
            if (
                hasattr(msg, "execution_constraints")
                and msg.execution_constraints.max_latency_ms is None
                and chan_def.typical_latency_ms
            ):
                msg.execution_constraints.max_latency_ms = chan_def.typical_latency_ms

            # Retry defaults based on QoS
            if (
                hasattr(msg, "execution_constraints")
                and msg.execution_constraints.max_retries == 3  # placeholder default
            ):
                qos_to_retry = {
                    "best_effort": 0,
                    "at_least_once": 3,
                    "exactly_once": 5,
                    "guaranteed": 7,
                }
                try:
                    qos_val = chan_def.qos.value  # enum or str
                except Exception:  # noqa: BLE001
                    qos_val = str(chan_def.qos)
                msg.execution_constraints.max_retries = qos_to_retry.get(qos_val, 3)

        # ------------------------------------------------------------------
        # DualInformation auto-wrap for payloads with raw "data" entries
        # ------------------------------------------------------------------
        if isinstance(getattr(msg, "payload", None), dict) and "data" in msg.payload:
            try:
                from person_suit.core.information.dual import DualInformation  # noqa: WPS433

                if not isinstance(msg.payload["data"], DualInformation):
                    msg.payload["data"] = DualInformation.from_raw(msg.payload["data"])
                    msg.payload.setdefault("_meta", {})["auto_dual_wrap"] = True
            except Exception:  # noqa: BLE001 – optional module
                pass

        # ------------------------------------------------------------------
        # Provenance – ensure deterministic hash & correlation id
        # ------------------------------------------------------------------
        if hasattr(self, "provenance_processor") and self.provenance_processor:
            self.provenance_processor.prepare_message_provenance(msg)

        # Final validation if available (will raise on failure)
        try:
            from person_suit.core.infrastructure.hybrid_message import (
                validate as _validate_envelope,
            )

            _validate_envelope(msg)
        except Exception:  # noqa: BLE001 – keep legacy behaviour (raise later in send)
            pass

    async def _enqueue_internal(self, msg: "HybridMessage") -> None:  # noqa: D401
        """Queue *msg* after applying full envelope preparation.

        Internal helpers (provenance, security, housekeeping) use this to
        publish system events without re-implementing envelope logic.
        """
        self._prepare_message(msg)
        await self.queue.put(msg)

    async def _housekeeping(self) -> None:  # noqa: D401
        """Periodic maintenance – emits bus metrics snapshot.

        Legacy tests expect calling ``bus._housekeeping()`` to produce a
        ``sys.metrics.bus`` event.  We gather the current statistics and
        enqueue the metrics event via the internal helper to guarantee the
        envelope is properly augmented.
        """
        try:
            metrics_payload = {"metrics": self.get_statistics()}
            metrics_msg = HybridMessage(
                message_type=MessageType.EVENT,
                channel="sys.metrics.bus",
                payload=metrics_payload,
                response_expected=False,
                metadata={"record_provenance": False},
            )
            await self._enqueue_internal(metrics_msg)
        except Exception as exc:  # noqa: BLE001
            logger.error("Housekeeping failed: %s", exc)

    # Provide placeholder attribute to mirror legacy naming – some tests
    # patch this value directly to cancel the task.
    _housekeeping_task: Optional[asyncio.Task] = None

    def _process_context_diff(self, msg: HybridMessage) -> None:  # noqa: D401
        """Compare message context with cached version and emit delta event."""
        if not msg.context or not isinstance(msg.context, dict):
            return

        context_id = msg.context.get("context_id")
        if not context_id:
            return

        new_context = msg.context
        old_context = self._context_cache.get(context_id)

        if old_context == new_context:
            return  # No changes

        if old_context is not None:
            from person_suit.core.context.diff_utils import create_diff
            delta = create_diff(new_context, old_context)
            
            if delta:
                delta_event = HybridMessage(
                    message_type=MessageType.EVENT,
                    channel="sys.context.delta",
                    payload={
                        "context_id": context_id,
                        "delta": delta,
                        "old_context_version": old_context.get("version"),
                        "new_context_version": new_context.get("version"),
                    },
                    priority=PRIO_HIGH,
                    context=new_context,
                )
                asyncio.create_task(self.queue.put(delta_event))

        # Update cache
        self._context_cache[context_id] = new_context
        # Evict old entries if cache is too large
        while len(self._context_cache) > self._context_cache_max:
            self._context_cache.pop(next(iter(self._context_cache)))


###############################################################################
# Global Bus Singleton
###############################################################################

_bus_instance: Optional[BusKernel] = None


async def get_message_bus(profile: Optional[DeploymentProfile] = None) -> BusKernel:
    """Get a singleton message bus instance, creating it if needed."""
    global _bus_instance  # noqa: WPS420 – singleton pattern
    if _bus_instance is None:
        _bus_instance = BusKernel(profile)
        await _bus_instance.start()
    return _bus_instance


async def stop_message_bus() -> None:
    """Stop the global message bus instance."""
    global _bus_instance  # noqa: WPS420 – singleton pattern
    if _bus_instance is not None:
        await _bus_instance.stop()
        _bus_instance = None 