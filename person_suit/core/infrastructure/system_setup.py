"""
System Setup Utilities
======================

This module provides initial setup routines that should be called at the very
beginning of the application's lifecycle.

Related Files:
- person_suit/main.py: Primary entry point that uses these utilities.
"""

import asyncio
import logging
import platform
import signal
from typing import Any, Optional

logger = logging.getLogger(__name__)


async def safe_import_with_timeout(module_path: str, timeout: float = 10.0) -> Optional[Any]:
    """
    Import module with timeout to prevent hangs.

    Args:
        module_path: Dotted path to module to import
        timeout: Maximum time to wait for import (seconds)

    Returns:
        Imported module or None if import failed/timed out

    Raises:
        ImportError: If import times out or fails
    """
    try:
        # Use asyncio.to_thread for thread-safe import with timeout
        return await asyncio.wait_for(
            asyncio.to_thread(__import__, module_path),
            timeout=timeout
        )
    except asyncio.TimeoutError:
        logger.error(f"Import timeout for {module_path} after {timeout}s")
        raise ImportError(f"Import timeout: {module_path}")
    except Exception as exc:
        logger.error(f"Import failed for {module_path}: {exc}")
        raise ImportError(f"Import failed: {module_path}") from exc

def install_event_loop_policy():
    """
    Installs uvloop as the default event loop policy on compatible systems.

    This function checks the operating system and attempts to import and install
    uvloop if the system is not Windows. If uvloop is not available or the
    system is Windows, it falls back to the default asyncio event loop.
    This should be called once at application startup.
    """
    if platform.system() != "Windows":
        try:
            import uvloop
            uvloop.install()
            logger.info("uvloop installed as the default event loop.")
        except ImportError:
            logger.info("uvloop not found, using default asyncio event loop.")
    else:
        logger.info("Windows detected, using default asyncio event loop.")

def setup_logging(level: int = logging.INFO):
    """
    Configures basic logging for the application.

    Args:
        level: The logging level to set for the root logger.
    """
    logging.basicConfig(
        level=level,
        format="%(asctime)s - %(name)s - %(levelname)s - %(message)s",
        datefmt="%Y-%m-%d %H:%M:%S",
    )
    logger.info(f"Root logger configured with level {logging.getLevelName(level)}")

# --- Optional Dependency Stubs -------------------------------------------------

def install_optional_torch_stub() -> None:  # noqa: D401
    """Install a lightweight stub for *torch* if it cannot be imported.

    The presence of many *torch* imports in meta-system code paths causes the
    real PyTorch package – and its transitive interactive dependencies like
    ``readline`` – to be imported at *module import* time.  When the core
    infrastructure is started in a background process (e.g. via ``&`` or a
    process supervisor) this can lead to the infamous *"suspended (tty
    output)"* state because one of those transitive imports tries to access
    the controlling TTY.

    To keep the **base infrastructure** independent of heavy ML libraries
    until the meta-systems are actually plugged in, we install a **minimal
    stub** under ``sys.modules['torch']`` *iff*:

    1. The environment variable ``PS_NO_TORCH`` is set to ``"1"`` **OR**
    2. Importing the real *torch* package raises an *ImportError* or any other
       Exception (e.g. incompatible architecture wheels).

    The stub only exposes the minimal attributes required by the currently
    imported core modules.  Accessing advanced functionality (e.g. ``torch.nn``)
    will raise ``AttributeError``, which is acceptable because such calls
    originate from meta-systems that are *intentionally detached* at this
    stage of the bootstrap.
    """

    import os
    import sys
    import types

    # If the real torch is already imported, respect that.
    if 'torch' in sys.modules:
        return

    # Condition (1): explicit opt-out via env flag
    no_torch_env = os.getenv('PS_NO_TORCH', '0') == '1'

    if not no_torch_env:
        # Condition (2): attempt real import – heavy but allows full features
        try:
            return  # Real PyTorch is available – nothing to stub
        except Exception as exc:  # pragma: no cover – broad to catch wheels issues
            logger.debug("Real torch import failed (%s) – falling back to stub", exc)

    # ------------------------------------------------------------------
    # Install stub – *very* minimal API to satisfy attribute look-ups used
    # by core infrastructure (mostly tensor dtype constants and the Tensor
    # type itself).  We do NOT attempt to emulate functionality.
    # ------------------------------------------------------------------
    torch_stub = types.ModuleType('torch')

    # Basic constant placeholders
    class _StubTensor:  # noqa: D401  # simple placeholder class
        """Placeholder for torch.Tensor to satisfy type hints."""

        def __init__(self, *args, **kwargs):  # noqa: D401, WPS110
            raise RuntimeError(
                'Torch stub in use – tensor operations are unavailable. '
                'Ensure meta-systems are detached or unset PS_NO_TORCH to '
                'enable real PyTorch.'
            )

    torch_stub.Tensor = _StubTensor  # type: ignore[attr-defined]

    # Common dtype placeholders used in annotations
    torch_stub.float32 = 'float32'  # type: ignore[attr-defined]
    torch_stub.float64 = 'float64'  # type: ignore[attr-defined]

    # Minimal sub-modules to avoid AttributeError on dotted access
    torch_stub.nn = types.ModuleType('torch.nn')  # type: ignore[attr-defined]
    torch_stub.nn.functional = types.ModuleType('torch.nn.functional')  # type: ignore[attr-defined]

    sys.modules['torch'] = torch_stub
    sys.modules['torch.nn'] = torch_stub.nn
    sys.modules['torch.nn.functional'] = torch_stub.nn.functional
    logger.info('Installed minimal torch stub (PS_NO_TORCH=%s)', no_torch_env) 