from __future__ import annotations

# -*- coding: utf-8 -*-

"""
Person Suit Dependency Injection System.

This module provides a comprehensive dependency injection system for the
Person Suit architecture, including service registration, lifetime management,
and resolution capabilities.

Uses protocol-based dependency injection to avoid circular dependencies.
"""

import logging  # Import logging for warnings
import threading

# Import typing
from typing import Optional
from typing import Protocol
from typing import Type
from typing import TypeVar
from typing import runtime_checkable

# Import events interface
from ...application.interfaces.events_interface import IEventManager

# Example: Core Event Manager
# from person_suit.core.infrastructure.configuration import ConfigManager
# import person_suit.core.infrastructure.effects as effects_module
from ...events import InfrastructureEventManager

# Import configuration-driven registration components
from .configuration import ConfigurationServiceRegistrar
from .configuration import ServiceRegistrationConfig
from .configuration import register_services_from_config
from .configuration import register_services_from_json

# Import container components
from .container import ServiceCollection
from .container import ServiceDescriptor

# Import container access functions
from .container_access import create_service_collection
from .container_access import get_container

# Import decorator components
from .decorators import async_singleton
from .decorators import scoped
from .decorators import transient

# Import dependency graph components
from .dependency_graph import DependencyAnalyzer
from .dependency_graph import DependencyGraph
from .dependency_graph import DependencyNode

# Import factory components
from .factory import ActivatorServiceFactory
from .factory import CompositeServiceFactory
from .factory import DelegateServiceFactory
from .factory import KeyedServiceFactory
from .factory import ServiceFactory
from .factory import add_factory
from .factory import add_keyed_factory

# Import fluent API components
from .fluent import FluentLifetimeRegistration
from .fluent import FluentServiceRegistration
from .fluent import add_service

# Import property and method injection components
from .injection import PropertyInjector
from .injection import inject
from .injection import inject_instance
from .injection import inject_method
from .injection import inject_property

# Import lazy initialization components
from .lazy import LazyActivationTracker
from .lazy import LazyServiceDescriptor
from .lazy import LazyServiceProxy
from .lazy import add_lazy_scoped
from .lazy import add_lazy_singleton
from .lazy import add_lazy_transient

# Import lifetime components
from .lifetime import LifetimeManager
from .lifetime import ScopedLifetime
from .lifetime import ServiceLifetime
from .lifetime import SingletonLifetime
from .lifetime import TransientLifetime

# Import provider components
from .provider import ServiceProvider
from .provider import ServiceScope
from .singleton_decorator import singleton

# ====== DEPENDENCY INVERSION PROTOCOLS ======

@runtime_checkable
class IStateManager(Protocol):
    """Protocol for state manager dependency injection."""
    
    async def initialize(self) -> None:
        """Initialize the state manager."""
        ...


@runtime_checkable
class IConsistencyChecker(Protocol):
    """Protocol for consistency checker dependency injection."""
    
    async def initialize(self) -> None:
        """Initialize the consistency checker."""
        ...


@runtime_checkable
class ISynchronizer(Protocol):
    """Protocol for synchronizer dependency injection."""
    
    async def initialize(self) -> None:
        """Initialize the synchronizer."""
        ...


@runtime_checkable
class INeurochemicalSystem(Protocol):
    """Protocol for neurochemical system dependency injection."""
    
    async def initialize(self) -> None:
        """Initialize the neurochemical system."""
        ...


# Expose key functions and classes
__all__ = [
    # Container
    "ServiceDescriptor",
    "ServiceCollection",
    # Lifetime
    "ServiceLifetime",
    "TransientLifetime",
    "SingletonLifetime",
    "ScopedLifetime",
    "LifetimeManager",
    # Provider
    "ServiceProvider",
    "ServiceScope",
    # Lazy Initialization
    "LazyServiceDescriptor",
    "LazyServiceProxy",
    "LazyActivationTracker",
    "add_lazy_transient",
    "add_lazy_singleton",
    "add_lazy_scoped",
    # Dependency Graph
    "DependencyNode",
    "DependencyGraph",
    "DependencyAnalyzer",
    # Factory
    "ServiceFactory",
    "DelegateServiceFactory",
    "ActivatorServiceFactory",
    "KeyedServiceFactory",
    "CompositeServiceFactory",
    "add_factory",
    "add_keyed_factory",
    # Property and Method Injection
    "inject",
    "inject_property",
    "inject_method",
    "PropertyInjector",
    "inject_instance",
    # Configuration-Driven Registration
    "ServiceRegistrationConfig",
    "ConfigurationServiceRegistrar",
    "register_services_from_config",
    "register_services_from_json",
    # Fluent API
    "FluentServiceRegistration",
    "FluentLifetimeRegistration",
    "add_service",
    # Decorators - Keep these in __all__ so users can still import them
    # from the package, but they won't be imported by __init__ itself.
    "singleton",
    "transient",
    "scoped",
    "inject",
    "async_singleton",
    # Container Access
    "create_service_collection",
    "get_container",
    # Protocols
    "IStateManager",
    "IConsistencyChecker", 
    "ISynchronizer",
    "INeurochemicalSystem",
]


# Convenience function to create a service collection
def create_service_collection() -> ServiceCollection:
    """Create a new service collection."""
    return ServiceCollection()


# Global container instance (managed by get_container)
_global_container_instance: Optional[ServiceProvider] = None
_global_container_lock = threading.RLock()  # Use RLock for reentrancy if needed


def get_container() -> ServiceProvider:
    """Gets the singleton ServiceProvider instance.

    Initializes it with a default ServiceCollection if not already created.
    Uses protocol-based registration to avoid circular dependencies.
    """
    global _global_container_instance
    with _global_container_lock:
        if _global_container_instance is None:
            # Create a default service collection
            collection = create_service_collection()

            # --- Config Manager Registration ---
            from person_suit.core.application.interfaces.config import IConfigManager
            from person_suit.core.infrastructure.config import JsonConfigManager

            # Define path to config file (could come from env var or arg parser later)
            config_file_path = "config.json"
            try:
                config_manager_instance = JsonConfigManager(
                    config_path=config_file_path
                )
                collection.add_singleton(
                    IConfigManager, instance=config_manager_instance
                )
                print(f"[DI] Registered IConfigManager (using {config_file_path}).")
            except Exception as e:
                print(
                    f"[DI] WARNING: Failed to initialize/register IConfigManager: {e}. Using default configs."
                )
            # --- End Config Registration ---

            # --- Add Core Infrastructure Service Registrations ---
            # Import necessary interfaces and implementations
            from person_suit.core.application.interfaces.communication_interface import (
                IChannelManager,
            )
            from person_suit.core.application.interfaces.events_interface import IEventManager
            from person_suit.core.application.interfaces.registration_interface import IRegistry
            from person_suit.core.events import InfrastructureEventManager
            from person_suit.core.infrastructure.communication import ChannelManager
            from person_suit.core.infrastructure.registration import InfrastructureRegistry

            # Register Core Services as Singletons
            collection.add_singleton(
                IRegistry, implementation_type=InfrastructureRegistry
            )
            collection.add_singleton(
                IChannelManager, implementation_type=ChannelManager
            )
            collection.add_singleton(
                IEventManager, implementation_type=InfrastructureEventManager
            )
            print(
                "[DI] Registered core infrastructure services (Registry, ChannelManager, EventManager)."
            )
            # --- End Registrations ---

            # --- Protocol-based Meta-System Service Placeholders ---
            # These will be registered by bootstrap code that has access to concrete implementations
            # We register the protocols here so dependency resolution can work

            log = logging.getLogger(__name__)
            log.info("[DI] Protocol-based service placeholders registered. "
                    "Concrete implementations should be registered by bootstrap code.")
            
            # Meta-system implementations will be registered later via:
            # container.register_implementation(IStateManager, concrete_state_manager)
            # container.register_implementation(IConsistencyChecker, concrete_checker)  
            # etc.
            
            # --- End Protocol Registration ---

            # Build the service provider
            _global_container_instance = collection.build_service_provider()

            print("[DI] Global ServiceProvider created with protocol-based registration.")

    return _global_container_instance


__all__.extend(
    ["create_service_collection", "get_container"]
)  # Add new functions to __all__
