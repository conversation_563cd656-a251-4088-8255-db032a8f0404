"""
singleton_decorator.py

Purpose:
    Provides a minimal @singleton decorator for marking classes as singletons.
    This decorator does not perform registration or container access at import time.
    It simply marks the class for later registration by the DI system or choreography/bootstrapper.

Usage:
    from person_suit.core.infrastructure.dependency_injection.singleton_decorator import singleton

    @singleton
    class MyService:
        ...

Related Files:
    - service_registration.py: Centralized service registration logic.
    - container_access.py: DI container access utilities.

"""

from typing import Type
from typing import TypeVar

T = TypeVar("T")


def singleton(cls: Type[T]) -> Type[T]:
    """
    Mark a class as a singleton for later registration.

    Args:
        cls: The class to mark as a singleton.

    Returns:
        The original class, unmodified.
    """
    cls._is_singleton = True
    return cls
