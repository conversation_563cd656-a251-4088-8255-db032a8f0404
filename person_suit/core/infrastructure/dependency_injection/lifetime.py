"""
Lifetime management for the Person Suit dependency injection system.

This module defines the service lifetime managers that control how services
are created, cached, and disposed of based on their registered lifetime.
"""

import threading
import uuid
from typing import Any
from typing import Dict
from typing import Optional
from typing import Set
from typing import TypeVar

from .container import ServiceDescriptor

# Type variables for generics
T = TypeVar("T")


class ServiceLifetime:
    """Base class for service lifetime managers.

    A service lifetime manager controls how a service is created and cached
    based on its registered lifetime (transient, singleton, or scoped).
    """

    def get_service(
        self, provider: "ServiceProvider", descriptor: ServiceDescriptor
    ) -> Any:
        """
        Get a service instance.

        Args:
            provider: Service provider to resolve dependencies
            descriptor: Service descriptor

        Returns:
            Service instance
        """
        raise NotImplementedError("Subclasses must implement get_service")


class TransientLifetime(ServiceLifetime):
    """Lifetime manager for transient services.

    Transient services are created newly each time they are requested
    and are not cached or tracked by the system.
    """

    def get_service(
        self, provider: "ServiceProvider", descriptor: ServiceDescriptor
    ) -> Any:
        """
        Get a new instance of the service.

        Args:
            provider: Service provider to resolve dependencies
            descriptor: Service descriptor

        Returns:
            New service instance
        """
        return descriptor.get_implementation(provider)


class SingletonLifetime(ServiceLifetime):
    """Lifetime manager for singleton services.

    Singleton services are created once and shared by all consumers
    for the lifetime of the application.
    """

    def __init__(self):
        """Initialize singleton lifetime."""
        self._instances: Dict[ServiceDescriptor, Any] = {}
        self._lock = threading.RLock()

    def get_service(
        self, provider: "ServiceProvider", descriptor: ServiceDescriptor
    ) -> Any:
        """
        Get or create a singleton instance of the service.

        Args:
            provider: Service provider to resolve dependencies
            descriptor: Service descriptor

        Returns:
            Singleton service instance
        """
        # Check if we already have an instance
        with self._lock:
            if descriptor in self._instances:
                return self._instances[descriptor]

            # Create a new instance
            instance = descriptor.get_implementation(provider)
            self._instances[descriptor] = instance
            return instance

    def has_instance(self, descriptor: ServiceDescriptor) -> bool:
        """
        Check if a singleton instance exists.

        Args:
            descriptor: Service descriptor

        Returns:
            True if an instance exists, False otherwise
        """
        with self._lock:
            return descriptor in self._instances


class ScopedLifetime(ServiceLifetime):
    """Lifetime manager for scoped services.

    Scoped services are created once per scope and shared within that scope.
    Each scope has its own set of scoped service instances.
    """

    def __init__(self, scope_id: str):
        """
        Initialize scoped lifetime.

        Args:
            scope_id: Unique identifier for the scope
        """
        self.scope_id = scope_id
        self._instances: Dict[ServiceDescriptor, Any] = {}
        self._lock = threading.RLock()
        self._disposable_instances: Set[Any] = set()

    def get_service(
        self, provider: "ServiceProvider", descriptor: ServiceDescriptor
    ) -> Any:
        """
        Get or create a scoped instance of the service.

        Args:
            provider: Service provider to resolve dependencies
            descriptor: Service descriptor

        Returns:
            Scoped service instance
        """
        # Check if we already have an instance in this scope
        with self._lock:
            if descriptor in self._instances:
                return self._instances[descriptor]

            # Create a new instance
            instance = descriptor.get_implementation(provider)
            self._instances[descriptor] = instance

            # Track disposable instances
            if hasattr(instance, "dispose") and callable(getattr(instance, "dispose")):
                self._disposable_instances.add(instance)

            return instance

    def dispose(self) -> None:
        """Dispose all scoped services."""
        with self._lock:
            # Dispose all disposable instances
            for instance in self._disposable_instances:
                try:
                    instance.dispose()
                except Exception as e:
                    # Log the exception but continue disposing other instances
                    print(f"Error disposing {instance}: {e}")

            # Clear instances
            self._instances.clear()
            self._disposable_instances.clear()


class LifetimeManager:
    """Manager for service lifetimes.

    The lifetime manager creates and tracks service lifetime objects
    for different service lifetimes (transient, singleton, scoped).
    """

    def __init__(self):
        """Initialize lifetime manager."""
        self._transient_lifetime = TransientLifetime()
        self._singleton_lifetime = SingletonLifetime()
        self._scoped_lifetimes: Dict[str, ScopedLifetime] = {}
        self._lock = threading.RLock()

    def get_lifetime(
        self, lifetime_type: str, scope_id: Optional[str] = None
    ) -> ServiceLifetime:
        """
        Get a service lifetime manager.

        Args:
            lifetime_type: Type of lifetime ('transient', 'singleton', or 'scoped')
            scope_id: Scope identifier for scoped lifetimes

        Returns:
            Service lifetime manager

        Raises:
            ValueError: If an invalid lifetime type is provided
        """
        if lifetime_type == "transient":
            return self._transient_lifetime

        if lifetime_type == "singleton":
            return self._singleton_lifetime

        if lifetime_type == "scoped":
            if not scope_id:
                raise ValueError("Scope ID is required for scoped lifetime")

            with self._lock:
                if scope_id not in self._scoped_lifetimes:
                    self._scoped_lifetimes[scope_id] = ScopedLifetime(scope_id)
                return self._scoped_lifetimes[scope_id]

        raise ValueError(f"Invalid lifetime type '{lifetime_type}'")

    def create_scope(self, parent_scope_id: Optional[str] = None) -> str:
        """
        Create a new scope.

        Args:
            parent_scope_id: Optional parent scope ID

        Returns:
            New scope ID
        """
        scope_id = str(uuid.uuid4())

        with self._lock:
            self._scoped_lifetimes[scope_id] = ScopedLifetime(scope_id)

        return scope_id

    def dispose_scope(self, scope_id: str) -> None:
        """
        Dispose a scope and all its scoped services.

        Args:
            scope_id: Scope ID to dispose
        """
        with self._lock:
            if scope_id in self._scoped_lifetimes:
                # Dispose the scope
                self._scoped_lifetimes[scope_id].dispose()
                # Remove it from our tracking
                del self._scoped_lifetimes[scope_id]
