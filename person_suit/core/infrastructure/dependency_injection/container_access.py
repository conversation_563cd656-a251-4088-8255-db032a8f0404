"""
Container Access Utilities for Dependency Injection

Provides global access to the singleton DI container instance and related helpers.
Uses protocol-based dependency injection to avoid circular dependencies.
"""

import logging
import threading
from typing import Optional

from .container import ServiceCollection
from .provider import ServiceProvider


def create_service_collection() -> ServiceCollection:
    """Create a new service collection."""
    return ServiceCollection()


# Global container instance (managed by get_container)
_global_container_instance: Optional[ServiceProvider] = None
_global_container_lock = threading.RLock()  # Use RLock for reentrancy if needed


def get_container() -> ServiceProvider:
    """Gets the singleton ServiceProvider instance.

    Initializes it with a default ServiceCollection if not already created.
    Uses protocol-based registration to avoid circular dependencies.
    """
    global _global_container_instance
    with _global_container_lock:
        if _global_container_instance is None:
            # Create a default service collection
            collection = create_service_collection()
            
            # --- Config Manager Registration ---
            from person_suit.core.application.interfaces.config_interface import IConfigManager
            from person_suit.core.infrastructure.configuration.manager import JsonConfigManager

            config_file_path = "config.json"
            try:
                config_manager_instance = JsonConfigManager(
                    config_path=config_file_path
                )
                collection.add_singleton(
                    IConfigManager, instance=config_manager_instance
                )
                print(f"[DI] Registered IConfigManager (using {config_file_path}).")
            except Exception as e:
                print(
                    f"[DI] WARNING: Failed to initialize/register IConfigManager: {e}. Using default configs."
                )
            # --- End Config Registration ---
            
            # --- Add Core Infrastructure Service Registrations ---
            from person_suit.core.application.interfaces.communication_interface import (
                IChannelManager,
            )
            from person_suit.core.application.interfaces.events_interface import IEventManager
            from person_suit.core.application.interfaces.registration_interface import IRegistry
            from person_suit.core.events import InfrastructureEventManager
            from person_suit.core.infrastructure.communication import ChannelManager
            from person_suit.core.infrastructure.registration import InfrastructureRegistry

            collection.add_singleton(
                IRegistry, implementation_type=InfrastructureRegistry
            )
            collection.add_singleton(
                IChannelManager, implementation_type=ChannelManager
            )
            collection.add_singleton(
                IEventManager, implementation_type=InfrastructureEventManager
            )
            print(
                "[DI] Registered core infrastructure services (Registry, ChannelManager, EventManager)."
            )
            # --- End Core Infrastructure Registration ---

            # --- Protocol-based Meta-System Service Placeholders ---
            # Meta-system implementations will be registered later by bootstrap code that has 
            # access to concrete implementations, avoiding circular dependencies

            log = logging.getLogger(__name__)
            log.info("[DI] Protocol-based service container created. "
                    "Meta-system implementations should be registered by bootstrap code.")
            
            # Bootstrap code will register concrete implementations like:
            # container.register_implementation(IStateManager, concrete_state_manager)
            # container.register_implementation(IConsistencyChecker, concrete_checker)  
            # etc.
            
            # --- End Protocol Registration ---
            
            _global_container_instance = collection.build_service_provider()
            print("[DI] Global ServiceProvider created with protocol-based registration.")
            
    return _global_container_instance
