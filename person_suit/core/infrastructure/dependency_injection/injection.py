"""
Property and method injection support for the Person Suit dependency injection system.

This module extends the dependency injection system with property and method injection
capabilities, providing alternatives to constructor injection for certain scenarios.
"""

import inspect
from typing import Any
from typing import List
from typing import Optional
from typing import Type
from typing import TypeVar
from typing import get_type_hints

from .provider import ServiceProvider

T = TypeVar("T")
TInstance = TypeVar("TInstance")
TResult = TypeVar("TResult")

# Constants for injection targets
INJECT_PROPERTY = "__di_inject_property__"
INJECT_METHOD = "__di_inject_method__"
INJECT_ATTRIBUTE = "__di_inject_attributes__"


def inject_property(property_type: Optional[Type] = None):
    """
    Decorator for marking a property for dependency injection.

    Args:
        property_type: Optional type of the property, will default to the type annotation if not provided

    Returns:
        Decorator function
    """

    def decorator(func):
        # Store the property type and function to inject
        setattr(func, INJECT_PROPERTY, property_type)
        return func

    return decorator


def inject_method(method_types: Optional[List[Type]] = None):
    """
    Decorator for marking a method for dependency injection.

    Args:
        method_types: Optional list of parameter types, will default to type annotations if not provided

    Returns:
        Decorator function
    """

    def decorator(func):
        # Store the method parameter types to inject
        setattr(func, INJECT_METHOD, method_types)
        return func

    return decorator


def inject(cls):
    """
    Class decorator that marks a class for property and method injection.

    This decorator scans the class for properties and methods marked with @inject_property
    and @inject_method and stores their injection requirements.

    Args:
        cls: Class to decorate

    Returns:
        Decorated class
    """
    # Get all attributes of the class
    attributes = {}

    # Scan for properties marked with @inject_property
    for name, member in inspect.getmembers(cls):
        if hasattr(member, INJECT_PROPERTY):
            property_type = getattr(member, INJECT_PROPERTY)

            # If type is not provided, get it from annotation
            if property_type is None:
                # Get type hints for the property
                hints = get_type_hints(
                    member.fget if hasattr(member, "fget") else member
                )
                if "return" in hints:
                    property_type = hints["return"]
                else:
                    raise ValueError(
                        f"Could not determine type for property '{name}' in {cls.__name__}"
                    )

            attributes[name] = property_type

    # Scan for methods marked with @inject_method
    for name, member in inspect.getmembers(cls, inspect.isfunction):
        if hasattr(member, INJECT_METHOD):
            method_types = getattr(member, INJECT_METHOD)

            # If types are not provided, get them from annotations
            if method_types is None:
                sig = inspect.signature(member)
                hints = get_type_hints(member)

                method_types = []
                for param_name, param in sig.parameters.items():
                    # Skip 'self' parameter
                    if param_name == "self":
                        continue

                    # Get type hint for parameter
                    if param_name in hints:
                        method_types.append(hints[param_name])
                    else:
                        raise ValueError(
                            f"Could not determine type for parameter '{param_name}' "
                            f"in method '{name}' of {cls.__name__}"
                        )

            # Store method injection info
            setattr(cls, f"{INJECT_METHOD}_{name}", method_types)

    # Store attributes to inject
    setattr(cls, INJECT_ATTRIBUTE, attributes)

    return cls


class PropertyInjector:
    """Utility for injecting dependencies into properties and methods of an object."""

    @staticmethod
    def inject_properties(instance: Any, provider: ServiceProvider) -> None:
        """
        Inject dependencies into properties of an instance.

        Args:
            instance: Instance to inject properties into
            provider: Service provider to resolve dependencies
        """
        cls = instance.__class__

        # Check if class is marked for injection
        if not hasattr(cls, INJECT_ATTRIBUTE):
            return

        # Get properties to inject
        attributes = getattr(cls, INJECT_ATTRIBUTE)

        # Inject properties
        for name, property_type in attributes.items():
            # Resolve dependency
            dependency = provider.get_required_service(property_type)

            # Set property
            setattr(instance, name, dependency)

    @staticmethod
    def inject_methods(instance: Any, provider: ServiceProvider) -> None:
        """
        Inject dependencies into methods of an instance.

        Args:
            instance: Instance to inject methods into
            provider: Service provider to resolve dependencies
        """
        cls = instance.__class__

        # Scan for methods marked for injection
        for name, _ in inspect.getmembers(cls, inspect.isfunction):
            method_attr = f"{INJECT_METHOD}_{name}"

            # Check if method is marked for injection
            if hasattr(cls, method_attr):
                # Get method parameter types
                method_types = getattr(cls, method_attr)

                # Resolve dependencies
                dependencies = [provider.get_required_service(t) for t in method_types]

                # Get the original method
                method = getattr(instance, name)

                # Call method with dependencies
                method(*dependencies)

    @staticmethod
    def inject(instance: Any, provider: ServiceProvider) -> None:
        """
        Inject dependencies into properties and methods of an instance.

        Args:
            instance: Instance to inject dependencies into
            provider: Service provider to resolve dependencies
        """
        PropertyInjector.inject_properties(instance, provider)
        PropertyInjector.inject_methods(instance, provider)


# Extension for ServiceDescriptor to support property and method injection
def inject_instance(instance: Any, provider: ServiceProvider) -> Any:
    """
    Inject dependencies into an instance created by a service descriptor.

    Args:
        instance: Instance to inject dependencies into
        provider: Service provider to resolve dependencies

    Returns:
        Instance with dependencies injected
    """
    PropertyInjector.inject(instance, provider)
    return instance
