"""
Service container and descriptor classes for the Person Suit dependency injection system.

This module defines the core components for service registration and collection,
providing a flexible dependency management system for all framework components.
"""

import inspect
import threading
from typing import TYPE_CHECKING
from typing import Any
from typing import Callable
from typing import Dict
from typing import List
from typing import Optional
from typing import Type
from typing import TypeVar

# Import ServiceProvider only for type checking to break cycle
if TYPE_CHECKING:
    from .provider import ServiceProvider

# Type variables for generics
T = TypeVar("T")
R = TypeVar("R")


class ServiceDescriptor:
    """Descriptor for a registered service.

    A service descriptor holds information about a registered service, including
    its service type, implementation type or factory, and lifetime.
    """

    def __init__(
        self,
        service_type: Type,
        implementation_type: Optional[Type] = None,
        implementation_factory: Optional[Callable[["ServiceProvider"], Any]] = None,
        lifetime: str = "transient",
        instance: Optional[Any] = None,
    ):
        """
        Initialize service descriptor.

        Args:
            service_type: Type of the service (usually an interface)
            implementation_type: Type that implements the service
            implementation_factory: Factory function to create the service
            lifetime: Service lifetime ('transient', 'singleton', or 'scoped')
            instance: Existing instance to use (for singleton services)

        Raises:
            ValueError: If neither implementation_type, implementation_factory, nor instance is provided,
                        or if both implementation_type and implementation_factory are provided
        """
        if not any([implementation_type, implementation_factory, instance]):
            raise ValueError(
                "Either implementation_type, implementation_factory, or instance must be provided"
            )

        if implementation_type and implementation_factory:
            raise ValueError(
                "Cannot provide both implementation_type and implementation_factory"
            )

        if lifetime not in ["transient", "singleton", "scoped"]:
            raise ValueError(
                f"Invalid lifetime '{lifetime}'. Must be 'transient', 'singleton', or 'scoped'"
            )

        self.service_type = service_type
        self.implementation_type = implementation_type
        self.implementation_factory = implementation_factory
        self.lifetime = lifetime
        self.instance = instance

    def get_implementation(self, provider: "ServiceProvider") -> Any:
        """
        Get the implementation instance for this service.

        Args:
            provider: Service provider to resolve dependencies

        Returns:
            Service implementation instance

        Raises:
            ValueError: If the implementation cannot be created
        """

        # If we have an existing instance, return it
        if self.instance:
            return self.instance

        # If we have a factory, use it
        if self.implementation_factory:
            return self.implementation_factory(provider)

        # Otherwise, create an instance of the implementation type
        if self.implementation_type:
            return self._create_instance(self.implementation_type, provider)

        # We should never get here if the constructor validation is correct
        raise ValueError(
            f"Cannot create implementation for service {self.service_type.__name__}"
        )

    def _create_instance(
        self, implementation_type: Type, provider: "ServiceProvider"
    ) -> Any:
        """
        Create an instance of the implementation type.

        Args:
            implementation_type: Type to create
            provider: Service provider to resolve dependencies

        Returns:
            Created instance

        Raises:
            ValueError: If the implementation cannot be created
        """
        # Get the constructor
        constructor = self._get_constructor(implementation_type)
        if not constructor:
            # If no constructor is found, try to create the instance directly
            return implementation_type()

        # Get constructor parameters
        params = inspect.signature(constructor).parameters
        if not params:
            # If no parameters, create instance directly
            return implementation_type()

        # Resolve constructor dependencies
        args = []
        for param_name, param in params.items():
            # Skip 'self' parameter
            if param_name == "self":
                continue

            # Get parameter type hint if available
            param_type = param.annotation
            if param_type is inspect.Parameter.empty:
                raise ValueError(
                    f"Parameter '{param_name}' of {implementation_type.__name__} constructor has no type hint"
                )

            # Resolve dependency
            dependency = provider.get_required_service(param_type)
            args.append(dependency)

        # Create instance with resolved dependencies
        return implementation_type(*args)

    def _get_constructor(self, implementation_type: Type) -> Optional[Callable]:
        """
        Get the constructor of a type.

        Args:
            implementation_type: Type to get constructor for

        Returns:
            Constructor or None if not found
        """
        # Get __init__ method if it exists
        constructor = getattr(implementation_type, "__init__", None)
        return constructor if constructor is not None else None


class ServiceCollection:
    """Collection of service descriptors.

    Service collection is used to register services and build a service provider
    that can resolve those services.
    """

    def __init__(self):
        """Initialize service collection."""
        self._descriptors: Dict[Type, List[ServiceDescriptor]] = {}
        self._lock = threading.RLock()

    def add(self, descriptor: ServiceDescriptor) -> "ServiceCollection":
        """
        Add a service descriptor to the collection.

        Args:
            descriptor: Service descriptor to add

        Returns:
            Self for method chaining
        """
        with self._lock:
            service_type = descriptor.service_type
            if service_type not in self._descriptors:
                self._descriptors[service_type] = []
            self._descriptors[service_type].append(descriptor)
        return self

    def add_transient(
        self,
        service_type: Type[T],
        implementation_type: Optional[Type] = None,
        implementation_factory: Optional[Callable[["ServiceProvider"], T]] = None,
    ) -> "ServiceCollection":
        """
        Register a transient service.

        Transient services are created newly each time they are requested.

        Args:
            service_type: Type of the service
            implementation_type: Type that implements the service
            implementation_factory: Factory function to create the service

        Returns:
            Self for method chaining
        """
        descriptor = ServiceDescriptor(
            service_type=service_type,
            implementation_type=implementation_type,
            implementation_factory=implementation_factory,
            lifetime="transient",
        )
        return self.add(descriptor)

    def add_singleton(
        self,
        service_type: Type[T],
        implementation_type: Optional[Type] = None,
        implementation_factory: Optional[Callable[["ServiceProvider"], T]] = None,
        instance: Optional[T] = None,
    ) -> "ServiceCollection":
        """
        Register a singleton service.

        Singleton services are created once and shared by all components.

        Args:
            service_type: Type of the service
            implementation_type: Type that implements the service
            implementation_factory: Factory function to create the service
            instance: Existing instance to use

        Returns:
            Self for method chaining
        """
        descriptor = ServiceDescriptor(
            service_type=service_type,
            implementation_type=implementation_type,
            implementation_factory=implementation_factory,
            lifetime="singleton",
            instance=instance,
        )
        return self.add(descriptor)

    def add_scoped(
        self,
        service_type: Type[T],
        implementation_type: Optional[Type] = None,
        implementation_factory: Optional[Callable[["ServiceProvider"], T]] = None,
    ) -> "ServiceCollection":
        """
        Register a scoped service.

        Scoped services are created once per scope and shared within that scope.

        Args:
            service_type: Type of the service
            implementation_type: Type that implements the service
            implementation_factory: Factory function to create the service

        Returns:
            Self for method chaining
        """
        descriptor = ServiceDescriptor(
            service_type=service_type,
            implementation_type=implementation_type,
            implementation_factory=implementation_factory,
            lifetime="scoped",
        )
        return self.add(descriptor)

    def get_descriptors(self, service_type: Type) -> List[ServiceDescriptor]:
        """
        Get service descriptors for a type.

        Args:
            service_type: Type to get descriptors for

        Returns:
            List of service descriptors
        """
        with self._lock:
            return self._descriptors.get(service_type, []).copy()

    def get_all_descriptors(self) -> Dict[Type, List[ServiceDescriptor]]:
        """
        Get all service descriptors.

        Returns:
            Dictionary of service descriptors keyed by service type
        """
        with self._lock:
            return {key: value.copy() for key, value in self._descriptors.items()}

    def build_service_provider(self) -> "ServiceProvider":
        """
        Build a service provider from this collection.

        Returns:
            Service provider
        """
        from .provider import ServiceProvider

        # First, validate dependencies if that feature is available
        # This will be implemented later as part of the circular dependency detection
        if hasattr(self, "validate_dependencies"):
            self.validate_dependencies()

        return ServiceProvider(self)

    def __len__(self) -> int:
        """Get the number of service descriptors."""
        with self._lock:
            return sum(len(descriptors) for descriptors in self._descriptors.values())

    def validate_dependencies(self) -> None:
        """
        Validate dependencies in the service collection.

        Raises:
            ConfigurationError: If circular dependencies are detected
        """
        from person_suit.core.infrastructure.dependency_injection.dependency_graph import (
            DependencyAnalyzer,
        )

        analyzer = DependencyAnalyzer(self)
        analyzer.check_for_cycles()
