"""
Factory-based service registration for the Person Suit dependency injection system.

This module provides advanced factory patterns for complex instantiation scenarios,
enabling more control over the service creation process than basic implementation factories.
"""

import inspect
import threading
from typing import Any
from typing import Callable
from typing import Dict
from typing import Generic
from typing import List
from typing import Optional
from typing import Type
from typing import TypeVar

from .container import ServiceCollection
from .container import ServiceDescriptor
from .provider import ServiceProvider

T = TypeVar("T")
TKey = TypeVar("TKey")
TResult = TypeVar("TResult")


class ServiceFactory(Generic[T]):
    """Base class for service factories."""

    def create(self, provider: ServiceProvider) -> T:
        """
        Create an instance of the service.

        Args:
            provider: Service provider to resolve dependencies

        Returns:
            Service instance
        """
        raise NotImplementedError("Service factories must implement create method")


class DelegateServiceFactory(ServiceFactory[T]):
    """Service factory that delegates creation to a factory function."""

    def __init__(self, factory_func: Callable[[ServiceProvider], T]):
        """
        Initialize delegate service factory.

        Args:
            factory_func: Factory function to create the service
        """
        self.factory_func = factory_func

    def create(self, provider: ServiceProvider) -> T:
        """
        Create an instance of the service using the factory function.

        Args:
            provider: Service provider to resolve dependencies

        Returns:
            Service instance
        """
        return self.factory_func(provider)


class ActivatorServiceFactory(ServiceFactory[T]):
    """Service factory that creates services using a constructor and resolved dependencies."""

    def __init__(
        self, implementation_type: Type[T], constructor_args: Optional[List[Any]] = None
    ):
        """
        Initialize activator service factory.

        Args:
            implementation_type: Type that implements the service
            constructor_args: Optional additional constructor arguments
        """
        self.implementation_type = implementation_type
        self.constructor_args = constructor_args or []

    def create(self, provider: ServiceProvider) -> T:
        """
        Create an instance of the service using its constructor.

        Args:
            provider: Service provider to resolve dependencies

        Returns:
            Service instance
        """
        # Get constructor
        constructor = self._get_constructor(self.implementation_type)

        # If no constructor, create instance directly with any provided args
        if not constructor:
            return self.implementation_type(*self.constructor_args)

        # Get constructor parameters
        params = inspect.signature(constructor).parameters
        if not params:
            return self.implementation_type(*self.constructor_args)

        # Resolve constructor dependencies
        args = list(self.constructor_args)  # Start with any provided args
        for param_name, param in params.items():
            # Skip 'self' parameter
            if param_name == "self":
                continue

            # Skip parameters that already have values in constructor_args
            if len(args) >= len(params) - 1:  # -1 for 'self'
                break

            # Get parameter type hint if available
            param_type = param.annotation
            if param_type is inspect.Parameter.empty:
                # If no type hint and parameter is required, raise an error
                if param.default is inspect.Parameter.empty:
                    raise ValueError(
                        f"Parameter '{param_name}' of {self.implementation_type.__name__} "
                        f"constructor has no type hint and no default value"
                    )
                continue

            # Resolve dependency
            dependency = provider.get_required_service(param_type)
            args.append(dependency)

        # Create instance with resolved dependencies
        return self.implementation_type(*args)

    def _get_constructor(self, implementation_type: Type) -> Optional[Callable]:
        """
        Get the constructor of a type.

        Args:
            implementation_type: Type to get constructor for

        Returns:
            Constructor or None if not found
        """
        # Get __init__ method if it exists
        constructor = getattr(implementation_type, "__init__", None)
        return constructor if constructor is not None else None


class KeyedServiceFactory(Generic[TKey, T]):
    """Factory for creating services based on a key."""

    def __init__(self):
        """Initialize keyed service factory."""
        self._factories: Dict[TKey, ServiceFactory[T]] = {}
        self._lock = threading.RLock()

    def register(
        self, key: TKey, factory: ServiceFactory[T]
    ) -> "KeyedServiceFactory[TKey, T]":
        """
        Register a factory for a specific key.

        Args:
            key: Key to register the factory for
            factory: Factory to register

        Returns:
            Self for method chaining
        """
        with self._lock:
            self._factories[key] = factory
        return self

    def register_type(
        self, key: TKey, implementation_type: Type[T]
    ) -> "KeyedServiceFactory[TKey, T]":
        """
        Register an implementation type for a specific key.

        Args:
            key: Key to register the implementation for
            implementation_type: Type that implements the service

        Returns:
            Self for method chaining
        """
        factory = ActivatorServiceFactory(implementation_type)
        return self.register(key, factory)

    def register_factory(
        self, key: TKey, factory_func: Callable[[ServiceProvider], T]
    ) -> "KeyedServiceFactory[TKey, T]":
        """
        Register a factory function for a specific key.

        Args:
            key: Key to register the factory for
            factory_func: Factory function to create the service

        Returns:
            Self for method chaining
        """
        factory = DelegateServiceFactory(factory_func)
        return self.register(key, factory)

    def register_instance(
        self, key: TKey, instance: T
    ) -> "KeyedServiceFactory[TKey, T]":
        """
        Register an existing instance for a specific key.

        Args:
            key: Key to register the instance for
            instance: Instance to register

        Returns:
            Self for method chaining
        """
        factory = DelegateServiceFactory(lambda _: instance)
        return self.register(key, factory)

    def create(self, key: TKey, provider: ServiceProvider) -> T:
        """
        Create an instance of the service for the specified key.

        Args:
            key: Key to create the service for
            provider: Service provider to resolve dependencies

        Returns:
            Service instance

        Raises:
            KeyError: If no factory is registered for the key
        """
        with self._lock:
            if key not in self._factories:
                raise KeyError(f"No factory registered for key '{key}'")

            factory = self._factories[key]
            return factory.create(provider)

    def get_factory(self, key: TKey) -> Optional[ServiceFactory[T]]:
        """
        Get the factory registered for a specific key.

        Args:
            key: Key to get the factory for

        Returns:
            Registered factory or None if not found
        """
        with self._lock:
            return self._factories.get(key)

    def contains_key(self, key: TKey) -> bool:
        """
        Check if a factory is registered for a specific key.

        Args:
            key: Key to check

        Returns:
            True if a factory is registered for the key, False otherwise
        """
        with self._lock:
            return key in self._factories

    def get_keys(self) -> List[TKey]:
        """
        Get all registered keys.

        Returns:
            List of registered keys
        """
        with self._lock:
            return list(self._factories.keys())


class CompositeServiceFactory(ServiceFactory[T]):
    """Factory that combines multiple factories to create a service."""

    def __init__(self):
        """Initialize composite service factory."""
        self._factories: List[ServiceFactory[T]] = []
        self._lock = threading.RLock()

    def add(self, factory: ServiceFactory[T]) -> "CompositeServiceFactory[T]":
        """
        Add a factory to the composite.

        Args:
            factory: Factory to add

        Returns:
            Self for method chaining
        """
        with self._lock:
            self._factories.append(factory)
        return self

    def create(self, provider: ServiceProvider) -> T:
        """
        Create an instance of the service by trying each factory in sequence.

        Args:
            provider: Service provider to resolve dependencies

        Returns:
            Service instance

        Raises:
            ValueError: If no factory can create the service
        """
        errors = []
        with self._lock:
            for factory in self._factories:
                try:
                    return factory.create(provider)
                except Exception as e:
                    errors.append(str(e))

        raise ValueError(
            f"None of the factories could create the service: {'; '.join(errors)}"
        )


# Extension methods for ServiceCollection
def add_factory(
    service_collection: ServiceCollection,
    service_type: Type[T],
    factory: ServiceFactory[T],
    lifetime: str = "transient",
) -> ServiceCollection:
    """
    Register a service using a factory.

    Args:
        service_collection: Service collection
        service_type: Type of the service
        factory: Factory to create the service
        lifetime: Service lifetime ('transient', 'singleton', or 'scoped')

    Returns:
        Service collection
    """
    # Create a factory function that delegates to the factory
    def factory_func(provider):
        return factory.create(provider)

    # Create a descriptor with the factory function
    descriptor = ServiceDescriptor(
        service_type=service_type,
        implementation_factory=factory_func,
        lifetime=lifetime,
    )

    return service_collection.add(descriptor)


def add_keyed_factory(
    service_collection: ServiceCollection,
    service_type: Type[T],
    keyed_factory: KeyedServiceFactory[TKey, T],
    lifetime: str = "transient",
) -> ServiceCollection:
    """
    Register keyed factory in the service collection.

    Args:
        service_collection: Service collection
        service_type: Base service type
        keyed_factory: Keyed factory
        lifetime: Service lifetime ('transient', 'singleton', or 'scoped')

    Returns:
        Service collection
    """
    # Register keyed factory itself as a singleton
    keyed_factory_descriptor = ServiceDescriptor(
        service_type=KeyedServiceFactory, lifetime="singleton", instance=keyed_factory
    )
    service_collection.add(keyed_factory_descriptor)

    return service_collection
