"""
Dependency Injection Decorators.

Provides decorators for simplified service registration.

IMPORTANT:
- All service and singleton registration MUST occur in the subsystem's
  register_with_container(container) function, NOT at import time or via decorators.
- Use the message-based bootstrap process (see person_suit/main.py) to register all services.
- Decorators here should only mark classes for later registration or provide injection convenience.
"""

import functools
import warnings
from typing import Any
from typing import Callable
from typing import Type
from typing import TypeVar
from typing import cast

# from .container_access import get_container  # REMOVE this top-level import

# Need access to the service collection or registration functions
# This might require passing the collection/container instance or using a global one
# For simplicity, assume a way to access the registration mechanism
# Placeholder: Directly import registration functions (might cause circular issues)
# A better approach would be a central registry or passing the container instance.

T = TypeVar("T")
F = TypeVar("F", bound=Callable[..., Any])


def singleton(cls: Type[T]) -> Type[T]:
    """
    Deprecated singleton decorator. Use singleton_decorator instead.
    This decorator does NOT perform registration or container access at import time.
    All singleton/service registration must occur in the subsystem's
    register_with_container(container) function and be triggered by the DI bootstrap.
    """
    warnings.warn(
        "The old singleton decorator is deprecated. "
        "Use person_suit.core.infrastructure.dependency_injection.singleton_decorator instead. "
        "All registration must occur in register_with_container.",
        DeprecationWarning,
        stacklevel=2,
    )
    cls._is_singleton = True
    return cls


def transient(cls: Type[T]) -> Type[T]:
    """Register a class as a transient service in the global DI container.

    Args:
        cls: The class to register as transient.

    Returns:
        The original class, unmodified.

    Example:
        @transient
        class MyService: ...
    """

    def register_transient():
        container = get_container()
        container._service_collection.add_transient(cls, implementation_type=cls)

    if not hasattr(cls, "_di_registration"):
        cls._di_registration = []
    cls._di_registration.append(register_transient)
    return cls


def scoped(cls: Type[T]) -> Type[T]:
    """Register a class as a scoped service in the global DI container.

    Args:
        cls: The class to register as scoped.

    Returns:
        The original class, unmodified.

    Example:
        @scoped
        class MyService: ...
    """

    def register_scoped():
        container = get_container()
        container._service_collection.add_scoped(cls, implementation_type=cls)

    if not hasattr(cls, "_di_registration"):
        cls._di_registration = []
    cls._di_registration.append(register_scoped)
    return cls


def inject(func: F) -> F:
    """Decorator to inject dependencies into a function or method based on type hints.

    Args:
        func: The function or method to inject dependencies into.

    Returns:
        The wrapped function with dependencies injected from the global DI container.

    Example:
        @inject
        def my_func(dep: IDependency): ...
    """

    @functools.wraps(func)
    def wrapper(*args, **kwargs):
        import inspect

        # Move import here to avoid circular import at module load time
        from person_suit.core.infrastructure.dependency_injection.container_access import (
            get_container,
        )

        container = get_container()
        sig = inspect.signature(func)
        bound_args = sig.bind_partial(*args, **kwargs)
        bound_args.apply_defaults()
        for name, param in sig.parameters.items():
            if name in bound_args.arguments:
                continue
            if param.annotation is inspect.Parameter.empty:
                continue
            # Only inject if not provided
            dependency = container.get_service(param.annotation)
            if dependency is not None:
                bound_args.arguments[name] = dependency
        return func(*bound_args.args, **bound_args.kwargs)

    return cast(F, wrapper)


# TODO: Implement other decorators like @transient, @scoped, @inject etc.
# based on the actual container implementation.


async def async_singleton(cls: Type[T]) -> Type[T]:
    """Decorator to register an async class as a singleton service (Placeholder).

    Args:
        cls: The async class to register as a singleton.

    Returns:
        The original class, unmodified.
    """
    # TODO: Implement async registration logic if needed
    print(f"[DI Decorator] Registering async {cls.__name__} as Singleton (Placeholder)")
    return cls
