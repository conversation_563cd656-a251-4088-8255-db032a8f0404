"""
Service provider for the Person Suit dependency injection system.

This module defines the service provider that resolves services from the
service collection based on their registered lifetime.
"""

import threading
from typing import Any
from typing import List
from typing import Optional
from typing import Type
from typing import TypeVar
from typing import cast

from .container import ServiceCollection
from .container import ServiceDescriptor
from .lifetime import LifetimeManager

# Type variables for generics
T = TypeVar("T")


class ServiceProvider:
    """Provider for resolving and creating services.

    The service provider is responsible for resolving services from the
    service collection and managing their lifetime according to their
    registration.
    """

    def __init__(
        self,
        service_collection: ServiceCollection,
        parent: Optional["ServiceProvider"] = None,
    ):
        """
        Initialize service provider.

        Args:
            service_collection: Collection of service descriptors
            parent: Optional parent provider for scope hierarchies
        """
        # Validate dependencies before building provider
        service_collection.validate_dependencies()

        self._service_collection = service_collection
        self._parent = parent
        self._lifetime_manager = LifetimeManager()
        self._scope_id = self._lifetime_manager.create_scope()
        self._lock = threading.RLock()

        # Register this provider as a service
        self._service_collection.add_singleton(ServiceProvider, instance=self)

    def get_service(self, service_type: Type[T]) -> Optional[T]:
        """
        Get a service of the specified type.

        Args:
            service_type: Type of service to get

        Returns:
            Service instance or None if not registered
        """
        # First try to resolve from this provider
        descriptors = self._service_collection.get_descriptors(service_type)
        if descriptors:
            # Use the last registered descriptor (most recently added)
            descriptor = descriptors[-1]
            return self._resolve_service(descriptor)

        # If not found and we have a parent, try the parent
        if self._parent:
            return self._parent.get_service(service_type)

        # Not found
        return None

    def get_required_service(self, service_type: Type[T]) -> T:
        """
        Get a required service of the specified type.

        Args:
            service_type: Type of service to get

        Returns:
            Service instance

        Raises:
            ValueError: If service is not registered
        """
        service = self.get_service(service_type)
        if service is None:
            raise ValueError(
                f"Service of type {service_type.__name__} is not registered"
            )

        return service

    def get_services(self, service_type: Type[T]) -> List[T]:
        """
        Get all services of the specified type.

        Args:
            service_type: Type of services to get

        Returns:
            List of service instances
        """
        descriptors = self._service_collection.get_descriptors(service_type)
        services = [self._resolve_service(descriptor) for descriptor in descriptors]

        # Add services from parent provider if available
        if self._parent:
            parent_services = self._parent.get_services(service_type)
            services.extend(parent_services)

        return services

    def create_scope(self) -> "ServiceScope":
        """
        Create a new service scope.

        Returns:
            Service scope
        """
        # Create a new provider with this one as parent
        scoped_provider = ServiceProvider(self._service_collection, parent=self)
        return ServiceScope(scoped_provider)

    def _resolve_service(self, descriptor: ServiceDescriptor) -> Any:
        """
        Resolve a service from its descriptor.

        Args:
            descriptor: Service descriptor

        Returns:
            Service instance
        """
        # Get the appropriate lifetime manager
        lifetime = self._lifetime_manager.get_lifetime(
            descriptor.lifetime, self._scope_id
        )

        # Get the service from the lifetime manager
        return lifetime.get_service(self, descriptor)


class ServiceScope:
    """Scope for scoped service lifetimes.

    A service scope provides a context for scoped services, ensuring they
    are properly disposed when the scope is disposed.
    """

    def __init__(self, service_provider: ServiceProvider):
        """
        Initialize service scope.

        Args:
            service_provider: Service provider
        """
        self._service_provider = service_provider
        self._disposed = False

    def get_service_provider(self) -> ServiceProvider:
        """
        Get the service provider for this scope.

        Returns:
            Service provider
        """
        if self._disposed:
            raise ValueError("Cannot use a disposed service scope")

        return self._service_provider

    def dispose(self) -> None:
        """Dispose the scope and all scoped services."""
        if not self._disposed:
            self._disposed = True

            # Get the lifetime manager from the provider
            if hasattr(self._service_provider, "_lifetime_manager") and hasattr(
                self._service_provider, "_scope_id"
            ):
                lifetime_manager = cast(
                    LifetimeManager, self._service_provider._lifetime_manager
                )
                scope_id = cast(str, self._service_provider._scope_id)

                # Dispose the scope
                lifetime_manager.dispose_scope(scope_id)

    def __enter__(self) -> ServiceProvider:
        """Enter the scope context."""
        return self.get_service_provider()

    def __exit__(self, exc_type, exc_val, exc_tb) -> None:
        """Exit the scope context and dispose the scope."""
        self.dispose()
