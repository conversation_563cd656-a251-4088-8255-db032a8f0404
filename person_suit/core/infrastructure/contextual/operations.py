# Message-based services initialization
from person_suit.core.infrastructure.message_based_imports import ensure_services_initialized


async def _ensure_message_services():
    """Ensure message-based services are initialized."""
    if not await ensure_services_initialized():
        raise RuntimeError("Failed to initialize message-based services")


"""
Operations for contextual processing in CAW.

This module provides operations for working with contexts in the
Contextual Adaptive Wave Programming (CAW) paradigm.
"""

import contextvars
import logging
from typing import Any
from typing import Dict
from typing import List
from typing import Optional

from person_suit.core.context.types import ContextConstraint
from person_suit.core.infrastructure.message_based_imports import UnifiedContext

logger = logging.getLogger(__name__)

# Use contextvars instead of threading.local
_current_caw_context: contextvars.ContextVar[Optional[UnifiedContext]] = (
    contextvars.ContextVar("current_caw_context", default=None)
)


def get_current_context() -> Optional[UnifiedContext]:
    """
    Get the current context for the current asyncio task context.

    Returns:
        The current context, or None if not set.
    """
    return _current_caw_context.get()


def set_current_context(context: Optional[UnifiedContext]) -> contextvars.Token:
    """
    Set the current context for the current asyncio task context.

    Use the returned token with `reset_current_context` to restore the previous value.

    Args:
        context: The context to set, or None to unset.

    Returns:
        A token to reset the context variable.
    """
    return _current_caw_context.set(context)


def reset_current_context(token: contextvars.Token) -> None:
    """Reset the context variable to its previous value using the token."""
    _current_caw_context.reset(token)


def switch_context(name: str) -> bool:
    """
    Switch to a named context.

    Args:
        name: The name of the context to switch to

    Returns:
        True if the switch was successful, False otherwise
    """
    from person_suit.core.infrastructure.contextual.core import get_context_registry

    registry = get_context_registry()
    context = registry.get_context(name)
    if context is None:
        return False

    set_current_context(context)
    return True


def match_context(context1: UnifiedContext, context2: UnifiedContext) -> float:
    """
    Compute the match score between two contexts.

    Args:
        context1: The first context
        context2: The second context

    Returns:
        A float between 0.0 and 1.0 representing the match score
    """
    # Use the matches method from UnifiedContext if available
    if hasattr(context1, 'matches'):
        return context1.matches(context2)
    
    # Fallback implementation
    score = 0.0
    if not context1 or not context2:
        return 0.0
    if context1.domain == context2.domain:
        score += 0.5
    if context1.priority == context2.priority:
        score += 0.3
    
    # Constraint matching using constraint types
    constraints1_types = set(c.type for c in context1.constraints or [])
    constraints2_types = set(c.type for c in context2.constraints or [])
    if constraints1_types or constraints2_types:
        intersection = len(constraints1_types.intersection(constraints2_types))
        union = len(constraints1_types.union(constraints2_types))
        if union > 0:
            score += 0.2 * (intersection / union)

    return min(score, 1.0)  # Clamp score to 1.0


def combine_contexts(
    contexts: List[UnifiedContext], weights: Optional[List[float]] = None
) -> UnifiedContext:
    """
    Combine multiple contexts into a single context.

    Args:
        contexts: The contexts to combine
        weights: Optional weights for the contexts

    Returns:
        A combined context

    Raises:
        ValueError: If the contexts list is empty
    """
    if not contexts:
        raise ValueError("Cannot combine empty list of contexts")

    if weights is None:
        weights = [1.0 / len(contexts)] * len(contexts)

    if len(weights) != len(contexts):
        raise ValueError("Number of weights must match number of contexts")

    # Use compose_with method if available, otherwise return first context
    result = contexts[0]
    for i, context in enumerate(contexts[1:], 1):
        if hasattr(result, 'compose_with'):
            result = result.compose_with(context)
        else:
            logger.warning("compose_with not available, using first context only")
            break
    
    return result


def create_context(
    domain: str,
    priority: int,
    constraints: List[ContextConstraint],
    properties: Optional[Dict[str, Any]] = None,
) -> UnifiedContext:
    """
    Create a new context.

    Args:
        domain: The domain of the context
        priority: The priority of the context (float)
        constraints: The constraints of the context
        properties: Optional properties for the context

    Returns:
        A new context
    """
    # Convert float priority to string priority
    priority_str = "low" if priority <= 0.3 else "normal" if priority <= 0.7 else "high" if priority <= 0.9 else "critical"
    
    # Create UnifiedContext using the create_default factory method
    context = UnifiedContext.create_default(
        domain=domain,
        priority=priority_str,
        properties=properties or {}
    )
    
    # Add constraints if provided (would need proper conversion from legacy format)
    # For now, just log that constraints were provided
    if constraints:
        logger.info(f"Creating context with {len(constraints)} constraints (conversion needed)")
    
    return context


def register_context(name: str, context: UnifiedContext) -> None:
    """
    Register a context with the global registry.

    Args:
        name: The name of the context
        context: The context to register
    """
    from person_suit.core.infrastructure.contextual.core import get_context_registry

    registry = get_context_registry()
    registry.register_context(name, context)


def get_context(name: str) -> Optional[UnifiedContext]:
    """
    Get a context from the global registry.

    Args:
        name: The name of the context

    Returns:
        The context, or None if not found
    """
    from person_suit.core.infrastructure.contextual.core import get_context_registry

    registry = get_context_registry()
    return registry.get_context(name)


def set_default_context(context: UnifiedContext) -> None:
    """
    Set the default context in the global registry.

    Args:
        context: The default context
    """
    from person_suit.core.infrastructure.contextual.core import get_context_registry

    registry = get_context_registry()
    registry.set_default_context(context)


def get_default_context() -> Optional[UnifiedContext]:
    """
    Get the default context from the global registry.

    Returns:
        The default context, or None if not set
    """
    from person_suit.core.infrastructure.contextual.core import get_context_registry

    registry = get_context_registry()
    return registry.get_default_context()
