# Message-based services initialization
from person_suit.core.infrastructure.message_based_imports import ensure_services_initialized


async def _ensure_message_services():
    """Ensure message-based services are initialized."""
    if not await ensure_services_initialized():
        raise RuntimeError("Failed to initialize message-based services")


"""
Core components for contextual processing in CAW.

This module provides the fundamental classes for context-sensitive
processing in the Contextual Adaptive Wave Programming (CAW) paradigm.
"""

import abc
import threading
from typing import Any
from typing import Callable
from typing import Dict
from typing import Generic
from typing import List
from typing import Optional
from typing import <PERSON>ple
from typing import TypeVar

# Import the correct Context schema
from person_suit.core.infrastructure.message_based_imports import UnifiedContext

# Import the matching function
from .operations import match_context

# Remove imports related to old wave/dual_wave adapters
# from person_suit.core.infrastructure.dual_wave.adapters import (
#     ContextAdapter as Context,
#     InformationAdapter as Information,
#     InterpretationAdapter as Interpretation
# )

# Remove migration notice if dual_wave is fully removed
# TODO: Fully migrate to use UnifiedContext, DualInformation, and DualInterpretation directly

# Type variable for generic types
T = TypeVar("T")
R = TypeVar("R")

_context_registry_lock = threading.Lock()


class ContextRegistry:
    """
    Central registry for contexts.

    The ContextRegistry maintains a collection of named contexts and
    provides methods for registering, retrieving, and managing contexts.
    """

    def __init__(self) -> None:
        """Initialize the context registry."""
        self.contexts: Dict[str, UnifiedContext] = {}
        self.default_context: Optional[UnifiedContext] = None

    def register_context(self, name: str, context: UnifiedContext) -> None:
        """
        Register a context with the registry.

        Args:
            name: The name of the context
            context: The context to register
        """
        self.contexts[name] = context

    def get_context(self, name: str) -> Optional[UnifiedContext]:
        """
        Get a context by name.

        Args:
            name: The name of the context

        Returns:
            The context, or None if not found
        """
        return self.contexts.get(name)

    def set_default_context(self, context: UnifiedContext) -> None:
        """
        Set the default context.

        Args:
            context: The default context
        """
        self.default_context = context

    def get_default_context(self) -> Optional[UnifiedContext]:
        """
        Get the default context.

        Returns:
            The default context, or None if not set
        """
        return self.default_context

    def get_all_contexts(self) -> Dict[str, UnifiedContext]:
        """
        Get all registered contexts.

        Returns:
            A dictionary of all registered contexts
        """
        return self.contexts.copy()

    def find_matching_contexts(
        self, query: UnifiedContext, threshold: float = 0.5
    ) -> List[Tuple[str, UnifiedContext, float]]:
        """
        Find contexts that match a query context.

        Args:
            query: The query context
            threshold: The minimum match score (0.0 to 1.0)

        Returns:
            A list of tuples (name, context, score) for matching contexts
        """
        matches = []
        for name, context in self.contexts.items():
            # Use the imported match_context function
            score = match_context(query, context)
            if score >= threshold:
                matches.append((name, context, score))

        matches.sort(key=lambda x: x[2], reverse=True)
        return matches


class ContextProvider(abc.ABC):
    """
    Interface for components that provide contexts.

    A ContextProvider is responsible for creating and providing
    contexts based on various inputs and conditions.
    """

    @abc.abstractmethod
    def get_context(self, **kwargs: Any) -> UnifiedContext:
        """
        Get a context based on the provided parameters.

        Args:
            **kwargs: Parameters for context creation

        Returns:
            A context
        """
        pass

    @abc.abstractmethod
    def get_available_contexts(self) -> List[str]:
        """
        Get a list of available context names.

        Returns:
            A list of available context names
        """
        pass

    @abc.abstractmethod
    def supports_context(self, name: str) -> bool:
        """
        Check if the provider supports a specific context.

        Args:
            name: The name of the context

        Returns:
            True if the provider supports the context, False otherwise
        """
        pass


class ContextSwitcher:
    """
    Mechanism for switching between contexts.

    The ContextSwitcher manages the current context and provides
    methods for switching between contexts.
    """

    def __init__(self, registry: ContextRegistry):
        """
        Initialize the context switcher.

        Args:
            registry: The context registry to use
        """
        self.registry = registry
        self.current_context: Optional[UnifiedContext] = None
        self.context_stack: List[UnifiedContext] = []

    def get_current_context(self) -> Optional[UnifiedContext]:
        """
        Get the current context.

        Returns:
            The current context, or None if not set
        """
        if self.current_context is None:
            return self.registry.get_default_context()
        return self.current_context

    def set_current_context(self, context: UnifiedContext) -> None:
        """
        Set the current context.

        Args:
            context: The context to set as current
        """
        self.current_context = context

    def switch_to_context(self, name: str) -> bool:
        """
        Switch to a named context.

        Args:
            name: The name of the context to switch to

        Returns:
            True if the switch was successful, False otherwise
        """
        context = self.registry.get_context(name)
        if context is None:
            return False

        self.current_context = context
        return True

    def push_context(self, context: UnifiedContext) -> None:
        """
        Push a context onto the stack and make it current.

        Args:
            context: The context to push
        """
        if self.current_context is not None:
            self.context_stack.append(self.current_context)
        self.current_context = context

    def pop_context(self) -> Optional[UnifiedContext]:
        """
        Pop the current context and restore the previous one.

        Returns:
            The popped context, or None if the stack was empty
        """
        if not self.context_stack:
            old_context = self.current_context
            self.current_context = None
            return old_context

        old_context = self.current_context
        self.current_context = self.context_stack.pop()
        return old_context


class ContextualProcessor(Generic[T, R]):
    """
    Base class for context-sensitive processors.

    A ContextualProcessor processes information differently based on
    the current context.
    """

    def __init__(self) -> None:
        """Initialize the contextual processor."""
        self.processors: Dict[str, Callable[[T, UnifiedContext], R]] = {}
        self.fallback_processor: Optional[Callable[[T, UnifiedContext], R]] = None

    def register_processor(
        self, context_name: str, processor: Callable[[T, UnifiedContext], R]
    ) -> None:
        """
        Register a processor for a specific context.

        Args:
            context_name: The name of the context
            processor: The processor function
        """
        self.processors[context_name] = processor

    def set_fallback_processor(self, processor: Callable[[T, UnifiedContext], R]) -> None:
        """
        Set the fallback processor.

        Args:
            processor: The fallback processor function
        """
        self.fallback_processor = processor

    def process(self, value: T, context: UnifiedContext) -> R:
        """
        Process a value in a specific context.

        Args:
            value: The value to process
            context: The context in which to process

        Returns:
            The processed value

        Raises:
            ValueError: If no processor is available for the context
        """
        best_match = None
        best_score = -1.0
        registry = get_context_registry()  # Assuming global registry still used

        for name, processor in self.processors.items():
            registered_ctx = registry.get_context(name)
            if registered_ctx is None:
                continue

            # Use the imported match_context function for scoring
            score = match_context(context, registered_ctx)
            if score > best_score:
                best_score = score
                best_match = processor

        if best_match is not None:
            # Pass the *original* target context to the chosen processor
            return best_match(value, context)

        if self.fallback_processor is not None:
            return self.fallback_processor(value, context)

        raise ValueError(f"No processor available for context domain: {context.domain}")


class ContextualRouter(Generic[T]):
    """
    Routes information based on context.

    A ContextualRouter directs information to different destinations
    based on the current context.
    """

    def __init__(self) -> None:
        """Initialize the contextual router."""
        self.routes: Dict[str, Callable[[T, UnifiedContext], None]] = {}
        self.fallback_route: Optional[Callable[[T, UnifiedContext], None]] = None

    def register_route(
        self, context_name: str, route: Callable[[T, UnifiedContext], None]
    ) -> None:
        """
        Register a route for a specific context.

        Args:
            context_name: The name of the context
            route: The route function
        """
        self.routes[context_name] = route

    def set_fallback_route(self, route: Callable[[T, UnifiedContext], None]) -> None:
        """
        Set the fallback route.

        Args:
            route: The fallback route function
        """
        self.fallback_route = route

    def route(self, value: T, context: UnifiedContext) -> bool:
        """
        Route a value based on context.

        Args:
            value: The value to route
            context: The context in which to route

        Returns:
            True if the value was routed, False otherwise
        """
        best_match = None
        best_score = -1.0
        registry = get_context_registry()

        for name, route_func in self.routes.items():
            registered_ctx = registry.get_context(name)
            if registered_ctx is None:
                continue

            # Use the imported match_context function for scoring
            score = match_context(context, registered_ctx)
            if score > best_score:
                best_score = score
                best_match = route_func

        if best_match is not None:
            # Pass the *original* target context to the chosen route
            best_match(value, context)
            return True

        if self.fallback_route is not None:
            self.fallback_route(value, context)
            return True

        return False


# Global context registry
_context_registry = ContextRegistry()


def get_context_registry() -> ContextRegistry:
    """
    Get the global context registry.

    Returns:
        The global context registry
    """
    return _context_registry
