
# Message-based services initialization
from person_suit.core.infrastructure.message_based_imports import ensure_services_initialized


async def _ensure_message_services():
    """Ensure message-based services are initialized."""
    if not await ensure_services_initialized():
        raise RuntimeError("Failed to initialize message-based services")


"""
File: context_determination.py
Purpose: Implements enhanced context determination for the CAW paradigm.

This module provides advanced context determination capabilities using wave
functions and interference patterns. It enables sophisticated context selection
based on multiple factors and wave-based information representation.

Related Files:
- person_suit/core/infrastructure/contextual/core.py: Core contextual components
- person_suit/core/infrastructure/wave/core.py: Wave-based information representation
- person_suit/core/infrastructure/caw/core.py: CAW integration

Dependencies:
- typing>=4.0.0: For type annotations
- logging>=*******: For logging functionality
- numpy>=1.22.0: For numerical computations
"""

import logging

# import numpy as np # No longer needed?
from typing import Any
from typing import Callable
from typing import Dict
from typing import List
from typing import Optional
from typing import TypeVar

# Import UnifiedContext from canonical location
from person_suit.core.infrastructure.message_based_imports import UnifiedContext

# Old effect import removed - effects system migration in progress
# from person_suit.effects.effect_types import Computation, ContextSwitch
from .core import ContextRegistry  # Added get_context_registry

# from ...information.representation import InformationElement # Removed unused import
# from ...memory.state import MemoryStateSnapshot # Removed unused import

# Configure logger
logger = logging.getLogger(__name__)

# Type variables
T = TypeVar("T")
R = TypeVar("R")


class ContextualFeatureExtractor:
    """
    Extracts contextual features from input data.

    The ContextualFeatureExtractor provides methods for extracting common
    contextual features from various types of input data.
    """

    @staticmethod
    def extract_priority(input_data: Any) -> Optional[str]:  # Return Optional
        """
        Extract priority from input data.

        Args:
            input_data: The input data

        Returns:
            The priority as a string
        """
        if hasattr(input_data, "priority"):
            priority = getattr(input_data, "priority")
            if callable(priority):
                return priority()
            return priority

        if hasattr(input_data, "get_priority"):
            return input_data.get_priority()

        if isinstance(input_data, dict) and "priority" in input_data:
            return input_data["priority"]

        return None  # Return None instead of default

    @staticmethod
    def extract_domain(input_data: Any) -> Optional[str]:  # Return Optional
        """
        Extract domain from input data.

        Args:
            input_data: The input data

        Returns:
            The domain as a string
        """
        if hasattr(input_data, "domain"):
            domain = getattr(input_data, "domain")
            if callable(domain):
                return domain()
            return domain

        if hasattr(input_data, "get_domain"):
            return input_data.get_domain()

        if isinstance(input_data, dict) and "domain" in input_data:
            return input_data["domain"]

        return None

    @staticmethod
    def extract_tags(input_data: Any) -> List[str]:  # Keep as list
        """
        Extract tags from input data.

        Args:
            input_data: The input data

        Returns:
            List of tags
        """
        if hasattr(input_data, "tags"):
            tags = getattr(input_data, "tags", [])
            if callable(tags):
                return tags()
            return tags

        if hasattr(input_data, "get_tags"):
            return input_data.get_tags()

        if isinstance(input_data, dict) and "tags" in input_data:
            return input_data.get("tags", [])

        return []

    @staticmethod
    def extract_timestamp(input_data: Any) -> float:
        """
        Extract timestamp from input data.

        Args:
            input_data: The input data

        Returns:
            The timestamp as a float
        """
        # Check for timestamp attribute
        if hasattr(input_data, "timestamp"):
            timestamp = getattr(input_data, "timestamp")
            if callable(timestamp):
                return timestamp()
            return timestamp

        # Check for timestamp method
        if hasattr(input_data, "get_timestamp"):
            return input_data.get_timestamp()

        # Check for timestamp in dictionary
        if isinstance(input_data, dict) and "timestamp" in input_data:
            return input_data["timestamp"]

        # Default timestamp
        import time

        return time.time()

    @staticmethod
    def extract_type(input_data: Any) -> str:
        """
        Extract type from input data.

        Args:
            input_data: The input data

        Returns:
            The type as a string
        """
        # Check for type attribute
        if hasattr(input_data, "type"):
            type_value = getattr(input_data, "type")
            if callable(type_value):
                return type_value()
            return type_value

        # Check for message_type attribute (common in message systems)
        if hasattr(input_data, "message_type"):
            return getattr(input_data, "message_type")

        # Check for type method
        if hasattr(input_data, "get_type"):
            return input_data.get_type()

        # Check for type in dictionary
        if isinstance(input_data, dict) and "type" in input_data:
            return input_data["type"]

        # Use the class name as the type
        return type(input_data).__name__


class ContextDeterminer:
    """
    Determines the most appropriate context for a given input based on features.
    Simplified version after removing old wave/CAW infra dependencies.
    """

    def __init__(self, registry: ContextRegistry):
        """
        Initialize a context determiner.
        Args:
            registry: The context registry to use.
        """
        self.registry = registry
        # Feature extractors can be registered if more complex logic is needed
        self.feature_extractors: Dict[str, Callable[[Any], Any]] = {
            "priority": ContextualFeatureExtractor.extract_priority,
            "domain": ContextualFeatureExtractor.extract_domain,
            "tags": ContextualFeatureExtractor.extract_tags,
            "type": ContextualFeatureExtractor.extract_type,
            # Add more feature extractors as needed
        }
        logger.info("ContextDeterminer initialized (Simplified).")

    # @effect([ContextSwitch, Computation])  # Commented out during migration
    def determine_context(self, input_data: Any) -> UnifiedContext:
        """
        Determine the most appropriate context for input data using registered features.

        Args:
            input_data: The input data.
        Returns:
            The most appropriate context.
        Raises:
            ValueError: If no contexts are available in the registry.
        """
        contexts = list(self.registry.get_all_contexts().values())
        if not contexts:
            logger.error("No contexts available in registry for determination.")
            raise ValueError("No contexts available in registry.")

        # Extract relevant features from input data
        input_features = {}
        for name, extractor in self.feature_extractors.items():
            try:
                value = extractor(input_data)
                if value is not None:
                    input_features[name] = value
            except Exception as e:
                logger.warning(
                    f"Error extracting feature '{name}': {e}", exc_info=False
                )

        if not input_features:
            logger.warning(
                "No features extracted from input data. Returning default context."
            )
            return self.registry.get_default_context() or contexts[0]

        # Score each context based on matching features
        context_scores = []
        for context in contexts:
            score = self._score_context(input_features, context)
            context_scores.append((context, score))
            logger.debug(f"Context {context.domain} score: {score:.4f}")

        # Select the context with the highest score
        # Handle potential ties (e.g., return first max, or apply tie-breaking rule)
        if not context_scores:
            logger.warning("No context scores calculated. Returning default context.")
            return self.registry.get_default_context() or contexts[0]

        best_context, best_score = max(context_scores, key=lambda item: item[1])
        logger.debug(
            f"Determined context {best_context.domain} with score {best_score:.4f}"
        )
        return best_context

    def _score_context(self, input_features: Dict[str, Any], context: UnifiedContext) -> float:
        """
        Calculates a match score between extracted features and a candidate context.
        Placeholder scoring logic - needs refinement based on requirements.
        """
        score = 0.0
        weight_total = 0.0

        # Domain Matching (High weight)
        weight = 1.0
        if "domain" in input_features and context.domain == input_features["domain"]:
            score += 1.0 * weight
        weight_total += weight

        # Priority Matching (Medium weight)
        weight = 0.7
        if "priority" in input_features:
            input_priority = input_features["priority"]
            context_priority = context.priority
            
            # Handle both string and enum priorities
            if hasattr(context_priority, 'name'):
                context_priority_str = context_priority.name.lower()
            else:
                context_priority_str = str(context_priority).lower()
            
            input_priority_str = str(input_priority).lower()
            
            if context_priority_str == input_priority_str:
                score += 1.0 * weight
        weight_total += weight

        # Tags/Constraints Matching (Lower weight - Jaccard Index)
        weight = 0.5
        if "tags" in input_features:
            input_tags = set(input_features["tags"])
            # Extract constraint types from ContextConstraint objects
            context_constraint_types = set(c.type for c in context.constraints or [])
            intersection = len(input_tags.intersection(context_constraint_types))
            union = len(input_tags.union(context_constraint_types))
            if union > 0:
                jaccard = intersection / union
                score += jaccard * weight
            weight_total += weight

        # Normalize score
        if weight_total > 0:
            return score / weight_total
        else:
            return 0.0
