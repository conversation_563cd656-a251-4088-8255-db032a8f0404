"""
Contextual Processing Module for Contextual Adaptive Wave Programming (CAW)

This module implements the contextual processing aspect of CAW,
providing infrastructure for context management and context-sensitive operations.

Key components:
- ContextRegistry: Central registry for contexts.
- ContextProvider: Interface for components that provide contexts.
- ContextSwitcher: Mechanism for switching between contexts (using contextvars).
- ContextualProcessor: Base class for context-sensitive processors.
- ContextualRouter: Routes information based on context.
- Context Operations: Helpers for getting/setting context via contextvars.
- Context Decorators: Decorators for context management (@with_context, etc.).
"""

# Context determination (Simplified)
from .context_determination import ContextDeterminer
from .context_determination import ContextualFeatureExtractor

# Core context management classes
from .core import ContextProvider
from .core import ContextRegistry  # Export getter
from .core import ContextSwitcher
from .core import ContextualProcessor
from .core import ContextualRouter
from .core import get_context_registry

# Context management decorators
from .decorators import context_sensitive
from .decorators import context_switch
from .decorators import contextual_processor
from .decorators import with_context

# Integration helpers
from .integration import StandardContextProvider
from .integration import get_all_context_providers
from .integration import get_context_from_provider
from .integration import get_context_provider
from .integration import initialize_integration
from .integration import register_context_provider

# Context operations (using contextvars)
from .operations import combine_contexts
from .operations import create_context
from .operations import get_context
from .operations import get_current_context
from .operations import get_default_context
from .operations import match_context
from .operations import register_context
from .operations import reset_current_context
from .operations import set_current_context
from .operations import set_default_context

__all__ = [
    # Core components
    "ContextRegistry",
    "ContextProvider",
    "ContextSwitcher",
    "ContextualProcessor",
    "ContextualRouter",
    "get_context_registry",
    # Decorators
    "contextual_processor",
    "with_context",
    "context_sensitive",
    "context_switch",
    # Operations
    "get_current_context",
    "set_current_context",
    "reset_current_context",
    "match_context",
    "combine_contexts",
    "create_context",
    "register_context",
    "get_context",
    "set_default_context",
    "get_default_context",
    # Determination (Simplified)
    "ContextDeterminer",
    "ContextualFeatureExtractor",
    # Integration
    "register_context_provider",
    "get_context_provider",
    "get_all_context_providers",
    "get_context_from_provider",
    "initialize_integration",
    "StandardContextProvider",
]


def initialize():
    """Initialize the contextual processing module (registers default contexts)."""
    # Call the initialize function from integration.py
    initialize_integration()
