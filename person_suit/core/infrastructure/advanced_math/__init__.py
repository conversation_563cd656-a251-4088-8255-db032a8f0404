"""
Advanced Mathematical Structures for PersonSuit
==============================================

This package provides implementations of advanced mathematical structures
beyond vectors, including tensors, symmetric transformations, hypergraphs,
and topological spaces.

These structures enable richer representations for different contexts and
problem domains, enhancing the PersonSuit framework's ability to represent
and process complex information patterns.

Modules:
- tensors: Tensor implementations and operations
- symmetric: Symmetric transformation implementations
- hypergraphs: Hypergraph implementations and algorithms
- topology: Topological space implementations and algorithms
- converters: Conversion utilities between different mathematical structures
- utils: Utility functions for advanced mathematical operations
"""

from .base import MathStructure
from .base import StructureMetadata
from .base import StructureRegistry
from .base import StructureType

__all__ = ["MathStructure", "StructureType", "StructureMetadata", "StructureRegistry"]
