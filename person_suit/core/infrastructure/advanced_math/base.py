"""
Base Interfaces for Advanced Mathematical Structures
==================================================

This module defines the base interfaces and abstract classes for all
advanced mathematical structures in the PersonSuit framework.

These interfaces provide a common foundation for implementing different
mathematical structures like tensors, symmetric transformations, hypergraphs,
and topological spaces.
"""

import abc
import uuid
from enum import Enum
from enum import auto
from typing import Any
from typing import Dict
from typing import List
from typing import Optional
from typing import Type
from typing import TypeVar

from ..effects import EffectType
from ..effects import effects


class StructureType(Enum):
    """Types of mathematical structures supported by the framework."""

    VECTOR = auto()  # Traditional vector representation
    TENSOR = auto()  # Multi-dimensional tensor
    SYMMETRIC_TRANSFORMATION = auto()  # Symmetric transformation
    HYPERGRAPH = auto()  # Hypergraph
    TOPOLOGICAL_SPACE = auto()  # Topological space
    CUSTOM = auto()  # Custom structure type


class StructureMetadata:
    """Metadata for mathematical structures."""

    def __init__(
        self,
        structure_id: Optional[str] = None,
        structure_type: StructureType = StructureType.CUSTOM,
        dimension: Optional[int] = None,
        properties: Optional[Dict[str, Any]] = None,
        tags: Optional[List[str]] = None,
    ):
        """
        Initialize structure metadata.

        Args:
            structure_id: Unique identifier for the structure
            structure_type: Type of mathematical structure
            dimension: Dimension or size of the structure
            properties: Additional properties of the structure
            tags: Tags for categorizing the structure
        """
        self.structure_id = structure_id or str(uuid.uuid4())
        self.structure_type = structure_type
        self.dimension = dimension
        self.properties = properties or {}
        self.tags = tags or []

    def to_dict(self) -> Dict[str, Any]:
        """
        Convert metadata to a dictionary.

        Returns:
            Dictionary representation of the metadata
        """
        return {
            "structure_id": self.structure_id,
            "structure_type": self.structure_type.name,
            "dimension": self.dimension,
            "properties": self.properties,
            "tags": self.tags,
        }

    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> "StructureMetadata":
        """
        Create metadata from a dictionary.

        Args:
            data: Dictionary representation of the metadata

        Returns:
            Metadata created from the dictionary
        """
        structure_type = StructureType[data.get("structure_type", "CUSTOM")]
        return cls(
            structure_id=data.get("structure_id"),
            structure_type=structure_type,
            dimension=data.get("dimension"),
            properties=data.get("properties", {}),
            tags=data.get("tags", []),
        )


T = TypeVar("T", bound="MathStructure")


class MathStructure(abc.ABC):
    """
    Base interface for all mathematical structures.

    This abstract class defines the common interface that all mathematical
    structures must implement, providing a foundation for interoperability
    between different structure types.
    """

    def __init__(self, metadata: Optional[StructureMetadata] = None):
        """
        Initialize the mathematical structure.

        Args:
            metadata: Metadata for the structure
        """
        self.metadata = metadata or StructureMetadata()

    @abc.abstractmethod
    def dimension(self) -> int:
        """
        Get the dimension or size of the structure.

        Returns:
            The dimension or size
        """
        pass

    @abc.abstractmethod
    def to_serializable(self) -> Dict[str, Any]:
        """
        Convert the structure to a serializable format.

        Returns:
            Serializable representation of the structure
        """
        pass

    @classmethod
    @abc.abstractmethod
    def from_serializable(cls: Type[T], data: Dict[str, Any]) -> T:
        """
        Create a structure from a serializable format.

        Args:
            data: Serializable representation of the structure

        Returns:
            Structure created from the serializable representation
        """
        pass

    @abc.abstractmethod
    def to_vector(self) -> List[float]:
        """
        Convert the structure to a vector representation.

        This method enables interoperability with existing vector-based systems.

        Returns:
            Vector representation of the structure
        """
        pass

    @classmethod
    @abc.abstractmethod
    def from_vector(
        cls: Type[T], vector: List[float], metadata: Optional[StructureMetadata] = None
    ) -> T:
        """
        Create a structure from a vector representation.

        Args:
            vector: Vector representation of the structure
            metadata: Metadata for the structure

        Returns:
            Structure created from the vector representation
        """
        pass

    @effects([EffectType.COMPUTATION])
    @abc.abstractmethod
    def transform(self, transformation: Any) -> "MathStructure":
        """
        Apply a transformation to the structure.

        Args:
            transformation: The transformation to apply

        Returns:
            The transformed structure
        """
        pass

    @effects([EffectType.COMPUTATION])
    @abc.abstractmethod
    def combine(self, other: "MathStructure") -> "MathStructure":
        """
        Combine this structure with another structure.

        Args:
            other: The other structure to combine with

        Returns:
            The combined structure
        """
        pass

    @effects([EffectType.COMPUTATION])
    @abc.abstractmethod
    def distance(self, other: "MathStructure") -> float:
        """
        Calculate the distance between this structure and another structure.

        Args:
            other: The other structure

        Returns:
            The distance between the structures
        """
        pass

    @effects([EffectType.COMPUTATION])
    @abc.abstractmethod
    def similarity(self, other: "MathStructure") -> float:
        """
        Calculate the similarity between this structure and another structure.

        Args:
            other: The other structure

        Returns:
            The similarity between the structures (0.0 to 1.0)
        """
        pass


class StructureRegistry:
    """
    Registry for mathematical structure types.

    This class provides a central registry for all mathematical structure types,
    enabling dynamic discovery and instantiation of different structure types.
    """

    _instance = None
    _structure_types: Dict[StructureType, Type[MathStructure]] = {}

    def __new__(cls):
        """Create a singleton instance."""
        if cls._instance is None:
            cls._instance = super(StructureRegistry, cls).__new__(cls)
        return cls._instance

    @classmethod
    def register(
        cls, structure_type: StructureType, structure_class: Type[MathStructure]
    ) -> None:
        """
        Register a structure type.

        Args:
            structure_type: The structure type
            structure_class: The structure class
        """
        cls._structure_types[structure_type] = structure_class

    @classmethod
    def get_class(cls, structure_type: StructureType) -> Optional[Type[MathStructure]]:
        """
        Get the class for a structure type.

        Args:
            structure_type: The structure type

        Returns:
            The structure class, or None if not registered
        """
        return cls._structure_types.get(structure_type)

    @classmethod
    def create_structure(
        cls, structure_type: StructureType, **kwargs
    ) -> Optional[MathStructure]:
        """
        Create a structure of the specified type.

        Args:
            structure_type: The structure type
            **kwargs: Additional arguments for the structure constructor

        Returns:
            The created structure, or None if the type is not registered
        """
        structure_class = cls.get_class(structure_type)
        if structure_class is None:
            return None
        return structure_class(**kwargs)

    @classmethod
    def get_registered_types(cls) -> List[StructureType]:
        """
        Get all registered structure types.

        Returns:
            List of registered structure types
        """
        return list(cls._structure_types.keys())
