# -*- coding: utf-8 -*-
"""
File: person_suit/core/caw/force_analogs.py
Purpose: Placeholder implementations for CAW Physics-Inspired Dynamics.

NOTE: This file was moved from core/caw/dynamics/ during flattening.
      Imports may need adjustment in files that used the old path.

This module will contain functions that simulate the effects of various
'forces' and 'fields' within the CAW paradigm, operating primarily on the
ParticleState (hypergraph) and modulated by Context.

These functions are intended to be called by the StateTransformationLogic
when processing specific effects or during simulation steps.

Related Files:
- docs/CAW_REPRESENTATION_PLAN.md (Sec 3.1)
- person_suit.core.caw.schemas.py (Defines ParticleState, Context, Nodes, Edges)
- person_suit.core.effects.transformation_logic.py (Calls these functions)
"""

import itertools  # For iterating through pairs
import logging
import random  # For probabilistic weak force example
import uuid  # To generate new edge IDs
from typing import Any
from typing import Dict
from typing import List
from typing import Optional

# ACF Logic
from .acf_logic import ACFManager  # Import from flattened location

# Concrete types are needed for actual operation
from .particle_state import ConcreteParticleState

# Core CAW Types
# Adjust imports based on new location
from .schemas import BaseEffect  # Might return effects to apply
from .schemas import Context
from .schemas import EdgeID
from .schemas import EffectCategory  # Added
from .schemas import FlexMetadata  # To create metadata update
from .schemas import InfonFlavor  # To cycle flavors
from .schemas import InfonNode  # To access charge & flavor
from .schemas import NodeID
from .schemas import RelationalEdge
from .schemas import RelationalForceType

# Effect Type constants for clarity
# TODO: Define these centrally
UPDATE_EDGE_METADATA_EFFECT = "UPDATE_EDGE_METADATA"
ADD_EDGE_EFFECT = "ADD_EDGE"
UPDATE_NODE_METADATA_EFFECT = "UPDATE_NODE_METADATA"

# Default mass for nodes without explicit computational_mass
DEFAULT_NODE_MASS = 0.1

# Instantiate ACFManager (can be passed via DI later if needed)
_acf_manager = ACFManager()

logger = logging.getLogger(__name__)


async def calculate_em_force_effect(
    particle_state: ConcreteParticleState,
    interacting_nodes: List[NodeID],
    context: Context,
    acf_params: Dict[str, Any],  # Receive interpreted params
) -> Optional[List[BaseEffect]]:
    """
    Calculates EM force effects, modulated by context and ACF params.
    """
    logging.debug(f"Calculating em_force_effect with ACF: {acf_params}")
    generated_effects: List[BaseEffect] = []

    # Example: Use interpreted force computation level
    if acf_params.get("force_computation_level") == "none":
        logging.debug("EM force calculation skipped due to ACF level 'none'.")
        return None

    base_coupling = float(context.custom_context.get("em_coupling_constant", 0.1))
    emotional_intensity = (
        float(context.emotional_state_analog.get("intensity", 1.0))
        if isinstance(context.emotional_state_analog, dict)
        else 1.0
    )
    contextual_coupling = base_coupling * max(0.1, min(2.0, emotional_intensity))

    if acf_params.get("force_computation_level") == "simplified":
        contextual_coupling *= 0.5  # Example simplification
        logging.debug("EM force using simplified coupling due to ACF.")

    for node_id_a, node_id_b in itertools.combinations(interacting_nodes, 2):
        node_a = particle_state.get_node(node_id_a)
        node_b = particle_state.get_node(node_id_b)

        if not node_a or not node_b:
            logging.warning(
                f"Skipping EM interaction: Node {node_id_a if not node_a else node_id_b} not found."
            )
            continue

        charge_a = (
            getattr(node_a, "lepton_charge", 0.0)
            if isinstance(node_a, InfonNode)
            else 0.0
        )
        charge_b = (
            getattr(node_b, "lepton_charge", 0.0)
            if isinstance(node_b, InfonNode)
            else 0.0
        )

        if charge_a is None:
            charge_a = 0.0  # Handle optional case
        if charge_b is None:
            charge_b = 0.0

        if abs(charge_a) < 1e-9 and abs(charge_b) < 1e-9:
            continue

        # Use ParticleState method if available
        common_edge_ids = set()
        if hasattr(particle_state, "get_edges_between"):
            common_edge_ids = particle_state.get_edges_between(node_id_a, node_id_b)
        elif hasattr(particle_state, "get_incident_edges"):  # Fallback
            edges_a = particle_state.get_incident_edges(node_id_a)
            edges_b = particle_state.get_incident_edges(node_id_b)
            common_edge_ids = edges_a.intersection(edges_b)
        else:
            logging.warning(
                "ParticleState lacks methods to find edges between nodes.", once=True
            )

        for edge_id in common_edge_ids:
            edge = particle_state.get_edge(edge_id)
            if (
                isinstance(edge, RelationalEdge)
                and edge.force_type == RelationalForceType.EM_INFO_FORCE
            ):
                delta_strength = contextual_coupling * (-1.0 * charge_a * charge_b)
                current_strength = float(edge.metadata.get("strength", 0.0))
                new_strength = max(0.0, current_strength + delta_strength)

                if abs(new_strength - current_strength) > 1e-9:
                    logging.debug(
                        f"EM effect: Edge {edge_id} strength change {current_strength:.3f} -> {new_strength:.3f} (Delta: {delta_strength:.3f}) between {node_id_a} ({charge_a}) and {node_id_b} ({charge_b})"
                    )
                    update_effect = BaseEffect(
                        effect_type=UPDATE_EDGE_METADATA_EFFECT,
                        category=EffectCategory.STATE_MANIPULATION,
                        target_entity_id=str(edge_id),
                        parameters={
                            "edge_id": edge_id,
                            "metadata_update": {"strength": new_strength},
                        },
                        intent=f"Update EM force strength between {node_id_a} and {node_id_b}",
                    )
                    generated_effects.append(update_effect)
                else:
                    logging.debug(
                        f"EM effect: Edge {edge_id} strength change negligible for {node_id_a}/{node_id_b}. Skipping effect."
                    )

    return generated_effects if generated_effects else None


async def calculate_gravity_force_effect(
    particle_state: ConcreteParticleState,
    interacting_nodes: List[NodeID],
    context: Context,
    acf_params: Dict[str, Any],
) -> Optional[List[BaseEffect]]:
    """
    Calculates Gravity force effects, modulated by context and ACF params.
    """
    logging.debug(f"Calculating gravity_force_effect with ACF: {acf_params}")
    generated_effects: List[BaseEffect] = []

    if acf_params.get("force_computation_level") == "none":
        logging.debug("Gravity force calculation skipped due to ACF level 'none'.")
        return None

    gravity_coupling = float(
        context.custom_context.get("gravity_coupling_constant", 0.05)
    )
    allow_new_edges = acf_params.get("allow_new_gravity_edges", True)

    if acf_params.get("force_computation_level") == "simplified":
        gravity_coupling *= 0.5
        logging.debug("Gravity force using simplified coupling due to ACF.")

    for node_id_a, node_id_b in itertools.combinations(interacting_nodes, 2):
        node_a = particle_state.get_node(node_id_a)
        node_b = particle_state.get_node(node_id_b)

        if not node_a or not node_b:
            logging.warning(
                f"Skipping Gravity interaction: Node {node_id_a if not node_a else node_id_b} not found."
            )
            continue

        mass_a = getattr(node_a, "computational_mass", DEFAULT_NODE_MASS)
        mass_b = getattr(node_b, "computational_mass", DEFAULT_NODE_MASS)

        interaction_strength = gravity_coupling * mass_a * mass_b

        if interaction_strength < 1e-9:
            continue

        found_existing_edge = False
        common_edge_ids = set()
        if hasattr(particle_state, "get_edges_between"):
            common_edge_ids = particle_state.get_edges_between(node_id_a, node_id_b)
        elif hasattr(particle_state, "get_incident_edges"):  # Fallback
            edges_a = particle_state.get_incident_edges(node_id_a)
            edges_b = particle_state.get_incident_edges(node_id_b)
            common_edge_ids = edges_a.intersection(edges_b)
        else:
            logging.warning(
                "ParticleState lacks methods to find edges between nodes.", once=True
            )

        for edge_id in common_edge_ids:
            edge = particle_state.get_edge(edge_id)
            if (
                isinstance(edge, RelationalEdge)
                and edge.force_type == RelationalForceType.GRAVITY_INFO_FORCE
            ):
                found_existing_edge = True
                current_strength = float(edge.metadata.get("strength", 0.0))
                new_strength = current_strength + interaction_strength

                logging.debug(
                    f"Gravity effect: Update Edge {edge_id} strength {current_strength:.3f} -> {new_strength:.3f} between {node_id_a} ({mass_a:.2f}) and {node_id_b} ({mass_b:.2f})"
                )
                update_effect = BaseEffect(
                    effect_type=UPDATE_EDGE_METADATA_EFFECT,
                    category=EffectCategory.STATE_MANIPULATION,
                    target_entity_id=str(edge_id),
                    parameters={
                        "edge_id": edge_id,
                        "metadata_update": {"strength": new_strength},
                    },
                    intent=f"Update Gravity force strength between {node_id_a} and {node_id_b}",
                )
                generated_effects.append(update_effect)
                break

        if not found_existing_edge and allow_new_edges:
            new_edge_id_str = f"grav_edge_{uuid.uuid4().hex[:8]}"
            new_edge_id = EdgeID(new_edge_id_str)
            initial_strength = interaction_strength
            logging.debug(
                f"Gravity effect: Add Edge {new_edge_id} strength {initial_strength:.3f} between {node_id_a} ({mass_a:.2f}) and {node_id_b} ({mass_b:.2f})"
            )

            add_edge = RelationalEdge(
                edge_id=new_edge_id,
                force_type=RelationalForceType.GRAVITY_INFO_FORCE,
                connected_nodes=[node_id_a, node_id_b],
                # directionality='UNDIRECTED', # Assuming schema allows omission for undirected
                strength=initial_strength,
                metadata=FlexMetadata({"strength": initial_strength}),
            )
            add_effect = BaseEffect(
                effect_type=ADD_EDGE_EFFECT,
                category=EffectCategory.STATE_MANIPULATION,
                target_entity_id=context.custom_context.get(
                    "entity_id", str(node_id_a)
                ),
                parameters={"edge": add_edge},
                intent=f"Add Gravity force edge between {node_id_a} and {node_id_b}",
            )
            generated_effects.append(add_effect)
        elif not found_existing_edge and not allow_new_edges:
            logging.debug(
                f"Gravity effect: Skipping ADD Edge between {node_id_a} and {node_id_b} due to ACF derived setting (allow_new_gravity_edges=False)."
            )

    return generated_effects if generated_effects else None


async def calculate_weak_force_effect(
    particle_state: ConcreteParticleState,
    target_node_id: NodeID,
    context: Context,
    acf_params: Dict[str, Any],
) -> Optional[BaseEffect]:
    """
    Calculates weak force effect, modulated by context and ACF params.
    """
    logging.debug(
        f"Calculating weak_force_effect for node {target_node_id} with ACF: {acf_params}"
    )

    if acf_params.get("force_computation_level") == "none":
        logging.debug("Weak force calculation skipped due to ACF level 'none'.")
        return None

    if not context.custom_context.get("allow_weak_transform", True):
        logging.debug("Weak force: allow_weak_transform is false in context.")
        return None

    target_node = particle_state.get_node(target_node_id)
    if not isinstance(target_node, InfonNode):
        return None

    transformation_probability = 0.1
    target_flavor = None

    if "ANALYZE_TEXT" in context.active_goals:
        transformation_probability = 0.3
        target_flavor = InfonFlavor.LINGUISTIC
        logging.debug(
            "Weak force context: ANALYZE_TEXT goal increases probability and biases towards LINGUISTIC."
        )
    elif "PROCESS_IMAGE" in context.active_goals:
        transformation_probability = 0.2
        target_flavor = InfonFlavor.VISUAL
        logging.debug("Weak force context: PROCESS_IMAGE goal biases towards VISUAL.")

    if acf_params.get("force_computation_level") == "simplified":
        transformation_probability *= 0.5
        logging.debug("Weak force using reduced probability due to ACF.")

    if random.random() >= transformation_probability:
        logging.debug(
            f"Weak force: Transformation check failed (Prob: {transformation_probability:.2f})."
        )
        return None

    current_flavor = target_node.flavor
    new_flavor = current_flavor

    if target_flavor and target_flavor != current_flavor:
        new_flavor = target_flavor
    elif target_flavor == current_flavor:
        logging.debug(
            "Weak force: Node is already target flavor biased by context. No change."
        )
        return None
    else:
        flavors = list(InfonFlavor)
        try:
            current_index = flavors.index(current_flavor)
            next_index = (current_index + 1) % len(flavors)
            new_flavor = flavors[next_index]
        except ValueError:
            logging.warning(
                f"Weak force: Current flavor {current_flavor} not found? Defaulting."
            )
            new_flavor = flavors[0]

    if new_flavor == current_flavor:
        logging.debug(
            "Weak force: Calculated new flavor is same as current. No effect."
        )
        return None

    logging.info(
        f"Weak effect: Proposing flavor change for InfonNode {target_node_id} from {current_flavor.name} to {new_flavor.name}"
    )

    update_effect = BaseEffect(
        effect_type=UPDATE_NODE_METADATA_EFFECT,
        category=EffectCategory.STATE_MANIPULATION,
        target_entity_id=str(target_node_id),
        parameters={
            "node_id": target_node_id,
            "metadata_update": {"intended_new_flavor": new_flavor.name},
        },
        intent=f"Propose weak force flavor change for InfonNode {target_node_id} to {new_flavor.name}",
    )

    return update_effect


async def calculate_higgs_field_effect(
    particle_state: ConcreteParticleState,
    target_nodes: List[NodeID],
    context: Context,
    acf_params: Dict[str, Any],
) -> Optional[List[BaseEffect]]:
    """
    Calculates Higgs field effect, modulated by context and ACF params.
    """
    logging.debug(
        f"Calculating higgs_field_effect for {len(target_nodes)} nodes with ACF: {acf_params}"
    )
    generated_effects: List[BaseEffect] = []

    if acf_params.get("force_computation_level") == "none":
        logging.debug("Higgs effect calculation skipped due to ACF level 'none'.")
        return None

    if not context.custom_context.get("allow_mass_change", True):
        logging.debug("Higgs effect: allow_mass_change is false in context. Skipping.")
        return None

    higgs_level = float(context.custom_context.get("higgs_field_level", 1.0))
    if higgs_level < 0:
        higgs_level = 0.0

    if acf_params.get("force_computation_level") == "simplified":
        higgs_level = 1.0 + (higgs_level - 1.0) * 0.5
        logging.debug(
            f"Higgs effect using moderated level {higgs_level:.3f} due to ACF."
        )

    for node_id in target_nodes:
        node = particle_state.get_node(node_id)

        if not node:
            logging.warning(f"Higgs effect: Node {node_id} not found. Skipping.")
            continue
        if not hasattr(node, "computational_mass"):
            logging.debug(
                f"Higgs effect: Node {node_id} (type {node.node_type}) does not have computational_mass. Skipping."
            )
            continue

        current_mass = float(getattr(node, "computational_mass", 0.0))
        calculated_new_mass = max(0.0, current_mass * higgs_level)
        calculated_new_mass = max(0.0, calculated_new_mass)

        if abs(calculated_new_mass - current_mass) > 1e-9:
            logging.info(
                f"Higgs effect: Proposing mass change for Node {node_id} from {current_mass:.3f} to {calculated_new_mass:.3f} (Higgs Level: {higgs_level:.3f})"
            )
            update_effect = BaseEffect(
                effect_type=UPDATE_NODE_METADATA_EFFECT,
                category=EffectCategory.STATE_MANIPULATION,
                target_entity_id=str(node_id),
                parameters={
                    "node_id": node_id,
                    "metadata_update": {"intended_new_mass": calculated_new_mass},
                },
                intent=f"Propose Higgs field mass change for Node {node_id} to {calculated_new_mass:.3f}",
            )
            generated_effects.append(update_effect)
        else:
            logging.debug(
                f"Higgs effect: Mass change negligible for Node {node_id}. Skipping effect."
            )

    return generated_effects if generated_effects else None
