"""
Tensor Implementations for PersonSuit
===================================

This package provides tensor implementations and operations for the
PersonSuit framework, enabling multi-dimensional data representation
and transformation.

Tensors generalize vectors and matrices to higher dimensions, making
them particularly useful for representing complex, multi-dimensional
relationships and transformations.

Modules:
- base: Base tensor interfaces and abstract classes
- dense: Dense tensor implementations
- sparse: Sparse tensor implementations
- operations: Tensor operations (contraction, product, etc.)
- decomposition: Tensor decomposition methods
"""

from .base import Tensor
from .base import TensorMetadata
from .base import TensorOrder
from .base import TensorShape
from .dense import DenseTensor

__all__ = ["Tensor", "TensorOrder", "TensorShape", "TensorMetadata", "DenseTensor"]
