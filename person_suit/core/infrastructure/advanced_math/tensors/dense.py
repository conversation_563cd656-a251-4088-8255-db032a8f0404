"""
Dense Tensor Implementation for PersonSuit
========================================

This module provides a dense tensor implementation for the PersonSuit
framework, using NumPy arrays as the underlying storage.

Dense tensors store all elements explicitly, making them efficient for
dense data but potentially inefficient for sparse data.
"""

from typing import Any
from typing import Dict
from typing import List
from typing import Optional
from typing import Sequence
from typing import Tuple

import numpy as np

from person_suit.shared.utils.effects import EffectType
from person_suit.shared.utils.effects import effects

from .base import Tensor
from .base import TensorMetadata
from .base import TensorOrder
from .base import TensorShape


class DenseTensor(Tensor):
    """
    Dense tensor implementation using NumPy arrays.

    This class provides a concrete implementation of the Tensor interface
    using NumPy arrays as the underlying storage, making it efficient for
    dense data but potentially inefficient for sparse data.
    """

    def __init__(self, data: np.ndarray, metadata: Optional[TensorMetadata] = None):
        """
        Initialize the dense tensor.

        Args:
            data: NumPy array containing the tensor data
            metadata: Metadata for the tensor
        """
        # Create metadata if not provided
        if metadata is None:
            order = (
                TensorOrder.SCALAR
                if data.ndim == 0
                else (
                    TensorOrder.VECTOR
                    if data.ndim == 1
                    else (
                        TensorOrder.MATRIX
                        if data.ndim == 2
                        else (
                            TensorOrder.THIRD_ORDER
                            if data.ndim == 3
                            else (
                                TensorOrder.FOURTH_ORDER
                                if data.ndim == 4
                                else TensorOrder.HIGHER_ORDER
                            )
                        )
                    )
                )
            )

            metadata = TensorMetadata(shape=data.shape, order=order)

        super().__init__(metadata)
        self._data = data

    def shape(self) -> TensorShape:
        """
        Get the shape of the tensor.

        Returns:
            The shape of the tensor
        """
        return self._data.shape

    def order(self) -> TensorOrder:
        """
        Get the order of the tensor.

        Returns:
            The order of the tensor
        """
        ndim = self._data.ndim
        if ndim == 0:
            return TensorOrder.SCALAR
        elif ndim == 1:
            return TensorOrder.VECTOR
        elif ndim == 2:
            return TensorOrder.MATRIX
        elif ndim == 3:
            return TensorOrder.THIRD_ORDER
        elif ndim == 4:
            return TensorOrder.FOURTH_ORDER
        else:
            return TensorOrder.HIGHER_ORDER

    def get_value(self, indices: Sequence[int]) -> float:
        """
        Get the value at the specified indices.

        Args:
            indices: The indices

        Returns:
            The value at the indices
        """
        return float(self._data[tuple(indices)])

    def set_value(self, indices: Sequence[int], value: float) -> None:
        """
        Set the value at the specified indices.

        Args:
            indices: The indices
            value: The value to set
        """
        self._data[tuple(indices)] = value

    def to_numpy(self) -> np.ndarray:
        """
        Convert the tensor to a NumPy array.

        Returns:
            NumPy array representation of the tensor
        """
        return self._data

    @classmethod
    def from_numpy(
        cls, array: np.ndarray, metadata: Optional[TensorMetadata] = None
    ) -> "DenseTensor":
        """
        Create a tensor from a NumPy array.

        Args:
            array: NumPy array representation of the tensor
            metadata: Metadata for the tensor

        Returns:
            Tensor created from the NumPy array
        """
        return cls(array, metadata)

    def to_serializable(self) -> Dict[str, Any]:
        """
        Convert the tensor to a serializable format.

        Returns:
            Serializable representation of the tensor
        """
        return {"data": self._data.tolist(), "metadata": self.tensor_metadata.to_dict()}

    @classmethod
    def from_serializable(cls, data: Dict[str, Any]) -> "DenseTensor":
        """
        Create a tensor from a serializable format.

        Args:
            data: Serializable representation of the tensor

        Returns:
            Tensor created from the serializable representation
        """
        array = np.array(data["data"])
        metadata = TensorMetadata.from_dict(data["metadata"])
        return cls(array, metadata)

    @classmethod
    def from_vector(
        cls,
        vector: List[float],
        shape: TensorShape,
        metadata: Optional[TensorMetadata] = None,
    ) -> "DenseTensor":
        """
        Create a tensor from a vector representation.

        Args:
            vector: Vector representation of the tensor
            shape: Shape of the tensor
            metadata: Metadata for the tensor

        Returns:
            Tensor created from the vector representation
        """
        array = np.array(vector).reshape(shape)
        return cls(array, metadata)

    @effects([EffectType.COMPUTATION])
    def contract(
        self, other: Tensor, axes: Optional[Tuple[List[int], List[int]]] = None
    ) -> Tensor:
        """
        Contract this tensor with another tensor.

        Args:
            other: The other tensor
            axes: The axes to contract (if None, contract the last axis of this tensor with the first axis of the other tensor)

        Returns:
            The contracted tensor
        """
        if not isinstance(other, DenseTensor):
            raise ValueError(f"Cannot contract DenseTensor with {type(other)}")

        other_data = other.to_numpy()

        if axes is None:
            # Contract the last axis of this tensor with the first axis of the other tensor
            result = np.tensordot(self._data, other_data, axes=1)
        else:
            # Contract the specified axes
            result = np.tensordot(self._data, other_data, axes=axes)

        return DenseTensor(result)

    @effects([EffectType.COMPUTATION])
    def transpose(self, axes: Optional[Sequence[int]] = None) -> Tensor:
        """
        Transpose the tensor.

        Args:
            axes: The new order of axes (if None, reverse the axes)

        Returns:
            The transposed tensor
        """
        if axes is None:
            # Reverse the axes
            axes = list(range(self._data.ndim))[::-1]

        result = np.transpose(self._data, axes)
        return DenseTensor(result)

    @effects([EffectType.COMPUTATION])
    def reshape(self, shape: TensorShape) -> Tensor:
        """
        Reshape the tensor.

        Args:
            shape: The new shape

        Returns:
            The reshaped tensor
        """
        result = np.reshape(self._data, shape)
        return DenseTensor(result)

    @effects([EffectType.COMPUTATION])
    def outer_product(self, other: Tensor) -> Tensor:
        """
        Compute the outer product of this tensor with another tensor.

        Args:
            other: The other tensor

        Returns:
            The outer product tensor
        """
        if not isinstance(other, DenseTensor):
            raise ValueError(
                f"Cannot compute outer product of DenseTensor with {type(other)}"
            )

        other_data = other.to_numpy()

        # Reshape for broadcasting
        shape_a = list(self._data.shape) + [1] * other_data.ndim
        shape_b = [1] * self._data.ndim + list(other_data.shape)

        a_reshaped = self._data.reshape(shape_a)
        b_reshaped = other_data.reshape(shape_b)

        result = a_reshaped * b_reshaped
        return DenseTensor(result)

    @effects([EffectType.COMPUTATION])
    def add(self, other: Tensor) -> Tensor:
        """
        Add another tensor to this tensor.

        Args:
            other: The other tensor

        Returns:
            The sum tensor
        """
        if not isinstance(other, DenseTensor):
            raise ValueError(f"Cannot add DenseTensor and {type(other)}")

        if self.shape() != other.shape():
            raise ValueError(
                f"Cannot add tensors with shapes {self.shape()} and {other.shape()}"
            )

        result = self._data + other.to_numpy()
        return DenseTensor(result)

    @effects([EffectType.COMPUTATION])
    def subtract(self, other: Tensor) -> Tensor:
        """
        Subtract another tensor from this tensor.

        Args:
            other: The other tensor

        Returns:
            The difference tensor
        """
        if not isinstance(other, DenseTensor):
            raise ValueError(f"Cannot subtract {type(other)} from DenseTensor")

        if self.shape() != other.shape():
            raise ValueError(
                f"Cannot subtract tensors with shapes {self.shape()} and {other.shape()}"
            )

        result = self._data - other.to_numpy()
        return DenseTensor(result)

    @effects([EffectType.COMPUTATION])
    def multiply(self, scalar: float) -> Tensor:
        """
        Multiply this tensor by a scalar.

        Args:
            scalar: The scalar

        Returns:
            The product tensor
        """
        result = self._data * scalar
        return DenseTensor(result)

    @effects([EffectType.COMPUTATION])
    def element_wise_multiply(self, other: Tensor) -> Tensor:
        """
        Multiply this tensor element-wise by another tensor.

        Args:
            other: The other tensor

        Returns:
            The element-wise product tensor
        """
        if not isinstance(other, DenseTensor):
            raise ValueError(
                f"Cannot element-wise multiply DenseTensor and {type(other)}"
            )

        if self.shape() != other.shape():
            raise ValueError(
                f"Cannot element-wise multiply tensors with shapes {self.shape()} and {other.shape()}"
            )

        result = self._data * other.to_numpy()
        return DenseTensor(result)

    @effects([EffectType.COMPUTATION])
    def norm(self) -> float:
        """
        Calculate the Frobenius norm of the tensor.

        Returns:
            The Frobenius norm
        """
        return float(np.sqrt(np.sum(self._data**2)))

    @classmethod
    def zeros(
        cls, shape: TensorShape, metadata: Optional[TensorMetadata] = None
    ) -> "DenseTensor":
        """
        Create a tensor of zeros.

        Args:
            shape: Shape of the tensor
            metadata: Metadata for the tensor

        Returns:
            Tensor of zeros
        """
        data = np.zeros(shape)
        return cls(data, metadata)

    @classmethod
    def ones(
        cls, shape: TensorShape, metadata: Optional[TensorMetadata] = None
    ) -> "DenseTensor":
        """
        Create a tensor of ones.

        Args:
            shape: Shape of the tensor
            metadata: Metadata for the tensor

        Returns:
            Tensor of ones
        """
        data = np.ones(shape)
        return cls(data, metadata)

    @classmethod
    def random(
        cls, shape: TensorShape, metadata: Optional[TensorMetadata] = None
    ) -> "DenseTensor":
        """
        Create a tensor of random values.

        Args:
            shape: Shape of the tensor
            metadata: Metadata for the tensor

        Returns:
            Tensor of random values
        """
        data = np.random.rand(*shape)
        return cls(data, metadata)

    @classmethod
    def identity(
        cls, size: int, metadata: Optional[TensorMetadata] = None
    ) -> "DenseTensor":
        """
        Create an identity matrix.

        Args:
            size: Size of the matrix
            metadata: Metadata for the tensor

        Returns:
            Identity matrix
        """
        data = np.eye(size)
        return cls(data, metadata)

    def __str__(self) -> str:
        """
        Get a string representation of the tensor.

        Returns:
            String representation of the tensor
        """
        return f"DenseTensor(shape={self.shape()}, order={self.order().name})"

    def __repr__(self) -> str:
        """
        Get a detailed string representation of the tensor.

        Returns:
            Detailed string representation of the tensor
        """
        return f"DenseTensor(shape={self.shape()}, order={self.order().name}, data={self._data})"
