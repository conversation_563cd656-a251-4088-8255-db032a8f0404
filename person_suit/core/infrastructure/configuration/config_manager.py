"""
Person Suit - Configuration Manager

This module provides the core configuration management functionality for the Person Suit framework,
implementing a hierarchical configuration system with schema validation, environment awareness,
and dynamic update capabilities.

The configuration manager serves as the central coordination point for all configuration-related
operations, ensuring consistent access to configuration across the system.
"""

import asyncio
import json
import logging
import threading
from pathlib import Path
from typing import Any
from typing import Callable
from typing import Dict
from typing import List
from typing import Optional
from typing import Union

# Configure logging
logger = logging.getLogger("person_suit.configuration")

# Forward declarations for types from other modules
# These will be properly imported in the actual implementation
ConfigSchema = Any
SchemaType = Any
ConfigValidator = Any
Environment = Any
EnvironmentHandler = Any
ConfigProvider = Any
ConfigChangeEvent = Any
ChangeType = Any
DynamicConfig = Any


class ConfigurationError(Exception):
    """Base exception for configuration-related errors."""

    pass


class SchemaValidationError(ConfigurationError):
    """Exception raised when configuration fails schema validation."""

    def __init__(self, section: str, errors: List[str]):
        self.section = section
        self.errors = errors
        message = f"Configuration validation failed for section '{section}': {'; '.join(errors)}"
        super().__init__(message)


class ConfigurationNotFoundError(ConfigurationError):
    """Exception raised when a requested configuration section or key is not found."""

    pass


class ConfigManager:
    """Central manager for configuration handling."""

    _instance = None
    _lock = threading.RLock()

    def __new__(cls):
        """Singleton pattern implementation."""
        with cls._lock:
            if cls._instance is None:
                cls._instance = super(ConfigManager, cls).__new__(cls)
                cls._instance._initialized = False
            return cls._instance

    def __init__(self):
        """Initialize the configuration manager."""
        if self._initialized:
            return

        with self._lock:
            if not self._initialized:
                # Import dependencies
                from person_suit.core.infrastructure.configuration.dynamic.dynamic_config import (
                    get_dynamic_config,
                )
                from person_suit.core.infrastructure.configuration.environment.handler import (
                    get_environment_handler,
                )
                from person_suit.core.infrastructure.configuration.provider.provider import (
                    get_config_provider,
                )
                from person_suit.core.infrastructure.configuration.schema.validator import (
                    get_validator,
                )

                self._validator = get_validator()
                self._environment_handler = get_environment_handler()
                self._provider = get_config_provider()
                self._dynamic_config = get_dynamic_config()

                # Initialize configuration containers
                self._config = {}
                self._defaults = {}
                self._schemas = {}
                self._change_handlers = {}
                self._initialized = True

                logger.info("Configuration manager initialized")

    async def initialize(
        self,
        config_paths: List[str] = None,
        env_prefix: str = "PERSON_SUIT",
        defaults: Dict[str, Any] = None,
    ) -> bool:
        """Initialize the configuration system."""
        try:
            # Load defaults if provided
            if defaults:
                self._defaults = defaults.copy()

            # Set up environment variables
            await self._environment_handler.initialize(env_prefix)

            # Load configuration files
            if config_paths:
                await self._load_configuration_files(config_paths)

            # Validate the configuration against registered schemas
            validation_errors = self._validate_configuration()
            if validation_errors:
                for section, errors in validation_errors.items():
                    logger.error(f"Validation errors in section {section}: {errors}")
                return False

            # Set up initial configuration
            await self._apply_environment_overrides()

            logger.info("Configuration successfully initialized")
            return True

        except Exception as e:
            logger.error(f"Failed to initialize configuration: {e}")
            return False

    async def _load_configuration_files(self, config_paths: List[str]) -> None:
        """Load configuration from files.

        Supports both JSON and YAML formats, with automatic detection based on file extension.

        Args:
            config_paths: List of paths to configuration files
        """
        # Check for YAML support
        try:
            import yaml

            yaml_available = True
        except ImportError:
            yaml_available = False
            logger.warning(
                "PyYAML not installed. YAML configuration files will not be supported."
            )

        for path in config_paths:
            try:
                config_path = Path(path)
                if not config_path.exists():
                    logger.warning(f"Configuration file not found: {path}")
                    continue

                # Determine file type and load
                file_ext = config_path.suffix.lower()

                if file_ext == ".json":
                    with open(config_path, "r") as f:
                        config_data = json.load(f)
                elif file_ext in (".yaml", ".yml"):
                    if not yaml_available:
                        logger.warning(
                            f"Cannot load YAML file {path}: PyYAML not installed"
                        )
                        continue

                    with open(config_path, "r") as f:
                        config_data = yaml.safe_load(f)
                else:
                    # Try to detect format based on content
                    with open(config_path, "r") as f:
                        content = f.read()

                    try:
                        config_data = json.loads(content)
                        logger.info(f"Loaded file as JSON: {path}")
                    except json.JSONDecodeError:
                        if yaml_available:
                            try:
                                config_data = yaml.safe_load(content)
                                logger.info(f"Loaded file as YAML: {path}")
                            except yaml.YAMLError as e:
                                logger.error(f"Error parsing file as YAML: {e}")
                                continue
                        else:
                            logger.warning(
                                f"Unsupported file format and PyYAML not available: {path}"
                            )
                            continue

                # Merge with existing configuration
                self._merge_configuration(config_data)
                logger.info(f"Loaded configuration from {path}")

            except json.JSONDecodeError as e:
                logger.error(f"Error decoding JSON configuration file {path}: {e}")
            except Exception as e:
                logger.error(f"Error loading configuration from {path}: {e}")

    def _merge_configuration(self, config_data: Dict[str, Any]) -> None:
        """Merge new configuration data with existing configuration."""
        for section, values in config_data.items():
            if section not in self._config:
                self._config[section] = {}

            if isinstance(values, dict):
                if section not in self._config:
                    self._config[section] = {}

                # Deep merge for dictionary values
                for key, value in values.items():
                    self._config[section][key] = value
            else:
                # Direct assignment for non-dictionary values
                self._config[section] = values

    async def _apply_environment_overrides(self) -> None:
        """Apply environment variable overrides to configuration."""
        env_overrides = await self._environment_handler.get_environment_overrides()

        for override in env_overrides:
            # Each override is a tuple of (section, key, value)
            section, key, value = override

            if section not in self._config:
                self._config[section] = {}

            if key is not None:
                self._config[section][key] = value
            else:
                # If key is None, the value replaces the entire section
                self._config[section] = value

        logger.debug(f"Applied {len(env_overrides)} environment overrides")

    def _validate_configuration(self) -> Dict[str, List[str]]:
        """Validate the configuration against registered schemas."""
        validation_errors = {}

        for section, schema in self._schemas.items():
            # Skip sections without schema
            if not schema:
                continue

            # Get section data, with defaults if missing
            section_data = self._config.get(section, self._defaults.get(section, {}))

            # Validate the section
            errors = self._validator.validate(section, section_data)
            if errors:
                validation_errors[section] = errors

        return validation_errors

    def register_schema(self, section: str, schema: ConfigSchema) -> bool:
        """Register a schema for validating a configuration section.

        Args:
            section: The configuration section to validate
            schema: The schema definition

        Returns:
            True if the schema was registered successfully, False otherwise
        """
        try:
            self._schemas[section] = schema
            self._validator.register_schema(schema)
            logger.debug(f"Registered schema for section {section}")
            return True
        except Exception as e:
            logger.error(f"Failed to register schema for section {section}: {e}")
            return False

    def register_migration(
        self,
        section: str,
        from_version: str,
        to_version: str,
        migration_func: Callable[[Dict[str, Any]], Dict[str, Any]],
    ) -> bool:
        """Register a migration function for a schema.

        Args:
            section: The configuration section
            from_version: The source version
            to_version: The target version
            migration_func: The migration function that transforms the data

        Returns:
            True if the migration was registered successfully, False otherwise
        """
        return self._validator.register_migration(
            section, from_version, to_version, migration_func
        )

    def get_config(
        self, section: str = None, key: str = None, default: Any = None
    ) -> Union[Dict[str, Any], Any]:
        """Get configuration value(s)."""
        try:
            # Get entire configuration
            if section is None:
                return self._config.copy()

            # Get section
            if section not in self._config:
                # Check defaults
                if section in self._defaults:
                    section_config = self._defaults[section]
                else:
                    if key is None:
                        return default
                    raise ConfigurationNotFoundError(
                        f"Configuration section not found: {section}"
                    )
            else:
                section_config = self._config[section]

            # Get specific key if provided
            if key is not None:
                if isinstance(section_config, dict) and key in section_config:
                    return section_config[key]

                # Check defaults
                if (
                    section in self._defaults
                    and isinstance(self._defaults[section], dict)
                    and key in self._defaults[section]
                ):
                    return self._defaults[section][key]

                return default

            return section_config

        except Exception as e:
            logger.error(f"Error retrieving configuration: {e}")
            return default

    async def update_config(
        self,
        section: str,
        key: Optional[str] = None,
        value: Any = None,
        validate: bool = True,
        user_id: Optional[str] = None,
    ) -> bool:
        """Update configuration with new values."""
        try:
            # Determine change type
            if section not in self._config:
                change_type = ChangeType.ADD
                self._config[section] = {}
            else:
                change_type = ChangeType.MODIFY

            # Get old value for event
            if key is not None:
                old_value = (
                    self._config[section].get(key)
                    if isinstance(self._config[section], dict)
                    else None
                )

                # Update the value
                if isinstance(self._config[section], dict):
                    self._config[section][key] = value
                else:
                    # Convert to dict if needed
                    self._config[section] = {key: value}
            else:
                # Update entire section
                old_value = self._config.get(section)
                self._config[section] = value

            # Validate if requested
            if validate and section in self._schemas:
                section_data = self._config[section]
                errors = self._validator.validate(section, section_data)
                if errors:
                    # Revert the change
                    if key is not None and isinstance(self._config[section], dict):
                        if old_value is not None:
                            self._config[section][key] = old_value
                        else:
                            del self._config[section][key]
                    else:
                        if old_value is not None:
                            self._config[section] = old_value
                        else:
                            del self._config[section]

                    logger.error(f"Configuration update validation failed: {errors}")
                    return False

            # Create change event
            change_event = ConfigChangeEvent(
                section=section,
                key=key,
                old_value=old_value,
                new_value=value,
                change_type=change_type,
                user_id=user_id,
            )

            # Notify about the change
            await self._dynamic_config.notify_change(change_event, validate_first=False)

            logger.info(f"Updated configuration: {section}.{key if key else ''}")
            return True

        except Exception as e:
            logger.error(f"Failed to update configuration: {e}")
            return False

    async def delete_config(
        self, section: str, key: Optional[str] = None, user_id: Optional[str] = None
    ) -> bool:
        """Delete configuration section or key."""
        try:
            # Check if section exists
            if section not in self._config:
                logger.warning(f"Cannot delete non-existent section: {section}")
                return False

            # Get old value for event
            if key is not None:
                if (
                    not isinstance(self._config[section], dict)
                    or key not in self._config[section]
                ):
                    logger.warning(f"Cannot delete non-existent key: {section}.{key}")
                    return False

                old_value = self._config[section][key]

                # Delete the key
                del self._config[section][key]

                # If section is empty, delete it too
                if not self._config[section]:
                    del self._config[section]

            else:
                # Delete entire section
                old_value = self._config[section]
                del self._config[section]

            # Create change event
            change_event = ConfigChangeEvent(
                section=section,
                key=key,
                old_value=old_value,
                new_value=None,
                change_type=ChangeType.DELETE,
                user_id=user_id,
            )

            # Notify about the change
            await self._dynamic_config.notify_change(change_event, validate_first=False)

            logger.info(f"Deleted configuration: {section}.{key if key else ''}")
            return True

        except Exception as e:
            logger.error(f"Failed to delete configuration: {e}")
            return False

    async def reset_config(self, user_id: Optional[str] = None) -> bool:
        """Reset configuration to defaults."""
        try:
            # Store old configuration for event
            old_config = self._config.copy()

            # Reset to defaults
            self._config = self._defaults.copy()

            # Reapply environment overrides
            await self._apply_environment_overrides()

            # Create change event
            change_event = ConfigChangeEvent(
                section="*",
                key=None,
                old_value=old_config,
                new_value=self._config,
                change_type=ChangeType.RESET,
                user_id=user_id,
            )

            # Notify about the change
            await self._dynamic_config.notify_change(change_event, validate_first=False)

            logger.info("Reset configuration to defaults")
            return True

        except Exception as e:
            logger.error(f"Failed to reset configuration: {e}")
            return False

    def register_change_handler(
        self, handler: Callable[[ConfigChangeEvent], None], section: str = "*"
    ) -> str:
        """Register a handler for configuration changes."""
        # Generate a unique ID for the handler
        handler_id = f"handler_{id(handler)}"

        # Store the handler
        if section not in self._change_handlers:
            self._change_handlers[section] = {}

        self._change_handlers[section][handler_id] = handler

        logger.debug(f"Registered change handler for section {section}")
        return handler_id

    def unregister_change_handler(self, handler_id: str) -> bool:
        """Unregister a configuration change handler."""
        for section, handlers in self._change_handlers.items():
            if handler_id in handlers:
                del handlers[handler_id]
                logger.debug(f"Unregistered change handler {handler_id}")

                # Clean up empty sections
                if not handlers:
                    del self._change_handlers[section]

                return True

        logger.warning(f"Handler not found: {handler_id}")
        return False

    async def notify_handlers(self, event: ConfigChangeEvent) -> None:
        """Notify handlers of a configuration change."""
        # Get handlers for the specific section
        section_handlers = self._change_handlers.get(event.section, {})

        # Get general handlers (*)
        general_handlers = self._change_handlers.get("*", {})

        # Combine handlers
        all_handlers = {**section_handlers, **general_handlers}

        # Call handlers
        for handler_id, handler in all_handlers.items():
            try:
                if asyncio.iscoroutinefunction(handler):
                    await handler(event)
                else:
                    handler(event)
            except Exception as e:
                logger.error(f"Error in configuration change handler {handler_id}: {e}")

    def export_config(self, include_defaults: bool = False) -> Dict[str, Any]:
        """Export the current configuration."""
        result = self._config.copy()

        # Include defaults if requested
        if include_defaults:
            for section, values in self._defaults.items():
                if section not in result:
                    result[section] = values.copy()
                elif isinstance(values, dict) and isinstance(result[section], dict):
                    # Deep merge defaults
                    for key, value in values.items():
                        if key not in result[section]:
                            result[section][key] = value

        return result

    async def import_config(
        self,
        config_data: Dict[str, Any],
        validate: bool = True,
        user_id: Optional[str] = None,
    ) -> bool:
        """Import configuration from dictionary."""
        try:
            # Store old configuration
            old_config = self._config.copy()

            # Validate if requested
            if validate:
                validation_errors = {}

                for section, data in config_data.items():
                    if section in self._schemas:
                        errors = self._validator.validate(section, data)
                        if errors:
                            validation_errors[section] = errors

                if validation_errors:
                    for section, errors in validation_errors.items():
                        logger.error(
                            f"Validation errors in section {section}: {errors}"
                        )
                    return False

            # Apply the new configuration
            self._config = config_data.copy()

            # Create change event
            change_event = ConfigChangeEvent(
                section="*",
                key=None,
                old_value=old_config,
                new_value=self._config,
                change_type=ChangeType.RESET,
                user_id=user_id,
            )

            # Notify about the change
            await self._dynamic_config.notify_change(change_event, validate_first=False)

            logger.info("Imported configuration")
            return True

        except Exception as e:
            logger.error(f"Failed to import configuration: {e}")
            return False

    def get_schema(self, section: str) -> Optional[ConfigSchema]:
        """Get the schema for a configuration section."""
        return self._schemas.get(section)

    def get_all_schemas(self) -> Dict[str, ConfigSchema]:
        """Get all registered schemas."""
        return self._schemas.copy()


# Singleton accessor
def get_config_manager() -> ConfigManager:
    """Get the singleton configuration manager instance."""
    return ConfigManager()


def register_with_container(container: Any) -> None:
    """Register ConfigManager with the DI container.

    Args:
        container: The DI container to register services with.
    """
    container.register(ConfigManager, ConfigManager())
