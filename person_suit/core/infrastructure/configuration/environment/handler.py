"""
Person Suit - Environment Handler

This module provides environment-aware configuration capabilities for the Person Suit framework,
handling environment-specific configuration overrides and environment detection.

The environment handler manages environment variable mapping to configuration values
and environment-specific configuration defaults.
"""

import json
import logging
import os
import platform
import threading
from dataclasses import dataclass
from dataclasses import field
from enum import Enum
from enum import auto
from typing import Any
from typing import Dict
from typing import List
from typing import Optional
from typing import Tuple

# Configure logging
logger = logging.getLogger("person_suit.configuration.environment")


class Environment(Enum):
    """Environment types supported by the system."""

    DEVELOPMENT = auto()
    TESTING = auto()
    STAGING = auto()
    PRODUCTION = auto()
    CUSTOM = auto()


@dataclass
class EnvironmentInfo:
    """Information about the current execution environment."""

    environment_type: Environment
    name: str
    platform: str
    os_name: str
    os_version: str
    python_version: str
    cpu_count: int
    memory_info: Dict[str, Any]
    is_container: bool
    is_ci: bool
    custom_attributes: Dict[str, Any] = field(default_factory=dict)


class EnvironmentHandler:
    """Handler for environment-specific configuration and detection."""

    _instance = None
    _lock = threading.RLock()

    def __new__(cls):
        """Singleton pattern implementation."""
        with cls._lock:
            if cls._instance is None:
                cls._instance = super(EnvironmentHandler, cls).__new__(cls)
                cls._instance._initialized = False
            return cls._instance

    def __init__(self):
        """Initialize the environment handler."""
        if self._initialized:
            return

        with self._lock:
            if not self._initialized:
                self._env_prefix = "PERSON_SUIT"
                self._current_environment = self._detect_environment()
                self._environment_overrides = {}
                self._initialized = True

                logger.info(
                    f"Environment handler initialized: {self._current_environment.name}"
                )

    def _detect_environment(self) -> EnvironmentInfo:
        """Detect the current execution environment."""
        # Get basic system information
        os_name = platform.system()
        os_version = platform.release()
        python_version = platform.python_version()
        cpu_count = os.cpu_count() or 0

        # Determine if running in container (simple heuristic)
        is_container = os.path.exists("/.dockerenv") or os.path.exists(
            "/run/.containerenv"
        )

        # Determine if running in CI environment
        is_ci = any(
            env in os.environ
            for env in ["CI", "TRAVIS", "GITHUB_ACTIONS", "GITLAB_CI", "JENKINS_URL"]
        )

        # Get memory information
        memory_info = self._get_memory_info()

        # Determine environment type
        env_type = self._determine_environment_type()
        env_name = os.environ.get(
            f"{self._env_prefix}_ENVIRONMENT", env_type.name.lower()
        )

        # Create environment info
        return EnvironmentInfo(
            environment_type=env_type,
            name=env_name,
            platform=platform.platform(),
            os_name=os_name,
            os_version=os_version,
            python_version=python_version,
            cpu_count=cpu_count,
            memory_info=memory_info,
            is_container=is_container,
            is_ci=is_ci,
        )

    def _determine_environment_type(self) -> Environment:
        """Determine the environment type from environment variables or heuristics."""
        # Check for explicit environment setting
        env_name = os.environ.get(f"{self._env_prefix}_ENVIRONMENT", "").upper()
        if env_name:
            try:
                return Environment[env_name]
            except KeyError:
                # Custom environment name
                return Environment.CUSTOM

        # Check for common environment indicators
        if "TEST" in os.environ or "PYTEST_CURRENT_TEST" in os.environ:
            return Environment.TESTING

        if any(env in os.environ for env in ["PROD", "PRODUCTION"]):
            return Environment.PRODUCTION

        if any(env in os.environ for env in ["STAGE", "STAGING"]):
            return Environment.STAGING

        # Default to development
        return Environment.DEVELOPMENT

    def _get_memory_info(self) -> Dict[str, Any]:
        """Get system memory information."""
        memory_info = {}

        try:
            # Platform-specific memory info
            if platform.system() == "Linux":
                with open("/proc/meminfo", "r") as f:
                    for line in f:
                        if "MemTotal" in line:
                            memory_info["total"] = (
                                int(line.split()[1]) * 1024
                            )  # KB to bytes
                        elif "MemAvailable" in line:
                            memory_info["available"] = (
                                int(line.split()[1]) * 1024
                            )  # KB to bytes
            elif platform.system() == "Darwin":  # macOS
                import subprocess

                output = subprocess.check_output(["sysctl", "-n", "hw.memsize"]).strip()
                memory_info["total"] = int(output)
            elif platform.system() == "Windows":
                import ctypes

                kernel32 = ctypes.windll.kernel32
                memory_status = ctypes.c_ulonglong * 5
                memory_data = memory_status()
                memory_data[0] = ctypes.sizeof(memory_status)
                kernel32.GlobalMemoryStatusEx(ctypes.byref(memory_data))
                memory_info["total"] = memory_data[1]
                memory_info["available"] = memory_data[2]
        except Exception as e:
            logger.warning(f"Failed to get detailed memory info: {e}")

        return memory_info

    async def initialize(self, env_prefix: str = "PERSON_SUIT") -> bool:
        """Initialize the environment handler with configuration."""
        try:
            self._env_prefix = env_prefix

            # Re-detect environment with updated prefix
            self._current_environment = self._detect_environment()

            # Parse environment variables for configuration overrides
            self._environment_overrides = self._parse_environment_variables()

            logger.info(f"Environment handler initialized with prefix: {env_prefix}")
            return True
        except Exception as e:
            logger.error(f"Failed to initialize environment handler: {e}")
            return False

    def _parse_environment_variables(self) -> Dict[str, Dict[str, Any]]:
        """Parse environment variables for configuration overrides."""
        overrides = {}
        prefix = f"{self._env_prefix}_CONFIG_"

        for key, value in os.environ.items():
            # Check if this is a configuration variable
            if key.startswith(prefix):
                # Extract section and key
                config_path = key[len(prefix) :].lower()

                # Parse path (format: SECTION__KEY)
                parts = config_path.split("__")
                section = parts[0]

                if len(parts) > 1:
                    # Section and key
                    config_key = parts[1]

                    # Initialize section if needed
                    if section not in overrides:
                        overrides[section] = {}

                    # Parse the value (try as JSON, fall back to string)
                    try:
                        parsed_value = json.loads(value)
                        overrides[section][config_key] = parsed_value
                    except json.JSONDecodeError:
                        overrides[section][config_key] = value
                else:
                    # Section only (should be a JSON object)
                    try:
                        section_config = json.loads(value)
                        if isinstance(section_config, dict):
                            overrides[section] = section_config
                        else:
                            logger.warning(
                                f"Invalid section config for {section}: not a dictionary"
                            )
                    except json.JSONDecodeError:
                        logger.warning(f"Invalid JSON for section {section}: {value}")

        return overrides

    async def get_environment_overrides(self) -> List[Tuple[str, Optional[str], Any]]:
        """Get environment variable overrides for configuration."""
        result = []

        # Convert the dictionary structure to list of tuples for easier processing
        for section, values in self._environment_overrides.items():
            if isinstance(values, dict):
                for key, value in values.items():
                    result.append((section, key, value))
            else:
                # Entire section override
                result.append((section, None, values))

        return result

    def get_current_environment(self) -> EnvironmentInfo:
        """Get information about the current execution environment."""
        return self._current_environment

    def is_development(self) -> bool:
        """Check if running in development environment."""
        return self._current_environment.environment_type == Environment.DEVELOPMENT

    def is_testing(self) -> bool:
        """Check if running in testing environment."""
        return self._current_environment.environment_type == Environment.TESTING

    def is_staging(self) -> bool:
        """Check if running in staging environment."""
        return self._current_environment.environment_type == Environment.STAGING

    def is_production(self) -> bool:
        """Check if running in production environment."""
        return self._current_environment.environment_type == Environment.PRODUCTION

    def is_container(self) -> bool:
        """Check if running in a container."""
        return self._current_environment.is_container

    def is_ci(self) -> bool:
        """Check if running in a CI environment."""
        return self._current_environment.is_ci

    def get_environment_name(self) -> str:
        """Get the name of the current environment."""
        return self._current_environment.name

    def get_environment_type(self) -> Environment:
        """Get the type of the current environment."""
        return self._current_environment.environment_type

    def get_platform_info(self) -> Dict[str, Any]:
        """Get information about the platform."""
        return {
            "platform": self._current_environment.platform,
            "os_name": self._current_environment.os_name,
            "os_version": self._current_environment.os_version,
            "python_version": self._current_environment.python_version,
            "cpu_count": self._current_environment.cpu_count,
            "memory_info": self._current_environment.memory_info,
            "is_container": self._current_environment.is_container,
            "is_ci": self._current_environment.is_ci,
        }

    def set_custom_attribute(self, key: str, value: Any) -> None:
        """Set a custom attribute for the environment."""
        self._current_environment.custom_attributes[key] = value

    def get_custom_attribute(self, key: str, default: Any = None) -> Any:
        """Get a custom attribute for the environment."""
        return self._current_environment.custom_attributes.get(key, default)

    def get_all_custom_attributes(self) -> Dict[str, Any]:
        """Get all custom attributes for the environment."""
        return self._current_environment.custom_attributes.copy()


# Singleton accessor
def get_environment_handler() -> EnvironmentHandler:
    """Get the singleton environment handler instance."""
    return EnvironmentHandler()
