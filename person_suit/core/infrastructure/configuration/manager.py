# -*- coding: utf-8 -*-
"""
File: person_suit/core/infrastructure/configuration/manager.py
Purpose: Implements the core configuration manager for Person Suit.

NOTE: This file was moved from core/infrastructure/config.py during refactoring.

Handles loading, accessing, and saving configuration from various sources
(JSON, YAML), supporting hierarchical structures, type safety, and optional
dependencies like PyYAML.

Related Files:
- person_suit/core/application/interfaces/config_interface.py: Defines the IConfigManager interface.
- config/config.yaml: Example primary configuration file.

Dependencies:
- PyYAML (optional): For YAML file support.
- Python Standard Library (json, logging, pathlib, os, typing)
"""

import json
import logging
import os
from pathlib import Path
from typing import Any
from typing import Dict
from typing import Optional
from typing import Union

# Adjust import path for interface
from ...application.interfaces.config_interface import IConfigManager

logger = logging.getLogger(__name__)

# Optional YAML support
try:
    import yaml

    YAML_AVAILABLE = True
except ImportError:
    YAML_AVAILABLE = False
    logger.warning(
        "PyYAML not installed. YAML configuration files will not be supported."
    )


class ConfigManager(IConfigManager):
    """Manages configuration loaded from JSON or YAML files.

    This implementation supports both JSON and YAML configuration formats,
    with automatic format detection based on file extension. It provides
    comprehensive error handling and graceful fallbacks.

    Attributes:
        _config: The loaded configuration dictionary
    """

    def __init__(self, config_path: Optional[Union[str, Path]] = None):
        """Initialize the configuration manager.

        Args:
            config_path: Optional path to a configuration file to load at initialization
        """
        self._config: Dict[str, Any] = {}
        if config_path:
            self.load_config(config_path)

    def load_config(self, config_source: Any) -> None:
        """Load configuration from a file.

        Supports both JSON and YAML formats, with automatic detection based on file extension.
        For YAML files, requires PyYAML to be installed.

        Args:
            config_source: Path to the configuration file (str or Path)

        Raises:
            TypeError: If config_source is not a string or Path
        """
        if not isinstance(config_source, (str, Path)):
            raise TypeError(
                "config_source must be a file path (str or Path) for ConfigManager"
            )

        config_file = Path(config_source)
        if not config_file.is_file():
            logger.warning(
                f"Configuration file not found: {config_file}. Using empty config."
            )
            self._config = {}
            return

        try:
            file_ext = config_file.suffix.lower()
            with open(config_file, "r", encoding="utf-8") as f:
                if file_ext in (".yaml", ".yml"):
                    if not YAML_AVAILABLE:
                        logger.error("Cannot load YAML file: PyYAML not installed")
                        self._config = {}
                        return
                    self._config = yaml.safe_load(f)
                elif file_ext == ".json":
                    self._config = json.load(f)
                else:
                    content = f.read()
                    try:
                        self._config = json.loads(content)
                        logger.info(f"Loaded file as JSON: {config_file}")
                    except json.JSONDecodeError:
                        if YAML_AVAILABLE:
                            try:
                                self._config = yaml.safe_load(content)
                                logger.info(f"Loaded file as YAML: {config_file}")
                            except yaml.YAMLError as e:
                                logger.error(f"Error parsing file as YAML: {e}")
                                self._config = {}
                        else:
                            logger.error(
                                f"Unsupported file format and PyYAML not available: {config_file}"
                            )
                            self._config = {}

            logger.info(f"Successfully loaded configuration from {config_file}")
        except json.JSONDecodeError as e:
            logger.error(f"Error decoding JSON configuration file {config_file}: {e}")
            self._config = {}
        except yaml.YAMLError as e:
            logger.error(f"Error decoding YAML configuration file {config_file}: {e}")
            self._config = {}
        except Exception as e:
            logger.error(f"Error reading configuration file {config_file}: {e}")
            self._config = {}

    def get_section(self, section_name: str) -> Optional[Dict[str, Any]]:
        """Gets an entire configuration section as a dictionary."""
        section = self._config.get(section_name)
        if isinstance(section, dict):
            return section.copy()
        if section is not None:
            logger.warning(f"Config section '{section_name}' is not a dictionary.")
        return None

    def get_setting(self, key: str, default: Optional[Any] = None) -> Optional[Any]:
        """Gets a specific setting using a dot-separated key."""
        try:
            value = self._config
            for part in key.split("."):
                if isinstance(value, dict):
                    value = value[part]
                else:
                    return default
            return value
        except (KeyError, TypeError):
            return default

    def get_full_config(self) -> Dict[str, Any]:
        """Returns the entire loaded configuration dictionary."""
        return self._config.copy()

    def save_config(self, file_path: Union[str, Path], format: str = "auto") -> bool:
        """Save the current configuration to a file."""
        try:
            path = Path(file_path)
            os.makedirs(path.parent, exist_ok=True)
            if format == "auto":
                ext = path.suffix.lower()
                format = "yaml" if ext in (".yaml", ".yml") else "json"

            with open(path, "w", encoding="utf-8") as f:
                if format == "yaml":
                    if not YAML_AVAILABLE:
                        logger.error("Cannot save as YAML: PyYAML not installed")
                        return False
                    yaml.dump(self._config, f, default_flow_style=False)
                else:
                    json.dump(self._config, f, indent=2)
            logger.info(f"Successfully saved configuration to {path}")
            return True
        except Exception as e:
            logger.error(f"Error saving configuration to {file_path}: {e}")
            return False

    def update_config(self, updates: Dict[str, Any]) -> None:
        """Update the configuration with new values."""
        self._deep_update(self._config, updates)
        logger.debug("Configuration updated")

    def _deep_update(self, target: Dict[str, Any], source: Dict[str, Any]) -> None:
        """Recursively update a dictionary with another dictionary."""
        for key, value in source.items():
            if (
                isinstance(value, dict)
                and key in target
                and isinstance(target[key], dict)
            ):
                self._deep_update(target[key], value)
            else:
                target[key] = value


# Keep backward compatibility alias for now if needed, but prefer ConfigManager
JsonConfigManager = ConfigManager

__all__ = ["ConfigManager", "JsonConfigManager"]
