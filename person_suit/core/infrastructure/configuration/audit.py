"""
Person Suit - Configuration Audit

This module provides auditing capabilities for the configuration system,
tracking all configuration changes and providing a comprehensive audit trail.
"""

import json
import logging
import os
import threading
import uuid
from abc import ABC
from abc import abstractmethod
from datetime import datetime
from functools import wraps
from pathlib import Path
from typing import Any
from typing import Callable
from typing import Dict
from typing import Optional

logger = logging.getLogger("person_suit.configuration.audit")

# Try to import audit system
try:
    from person_suit.security.core.audit.audit_service import AuditCategory
    from person_suit.security.core.audit.audit_service import AuditSeverity
    from person_suit.security.core.audit.audit_service import get_audit_service

    AUDIT_AVAILABLE = True
except ImportError:
    AUDIT_AVAILABLE = False
    logger.warning("Audit system not available. Using local audit logging.")


class ConfigurationAuditEvent:
    """Configuration audit event."""

    def __init__(
        self,
        event_id: str,
        timestamp: float,
        user_id: Optional[str],
        action: str,
        section: Optional[str],
        key: Optional[str],
        old_value: Optional[Any],
        new_value: Optional[Any],
        source: Optional[str] = None,
        metadata: Optional[Dict[str, Any]] = None,
    ):
        """Initialize a configuration audit event.

        Args:
            event_id: Unique event ID
            timestamp: Event timestamp
            user_id: ID of the user who made the change
            action: Action performed (create, update, delete)
            section: Configuration section
            key: Configuration key
            old_value: Previous value
            new_value: New value
            source: Source of the change (e.g., component name)
            metadata: Additional event metadata
        """
        self.event_id = event_id
        self.timestamp = timestamp
        self.user_id = user_id
        self.action = action
        self.section = section
        self.key = key
        self.old_value = old_value
        self.new_value = new_value
        self.source = source
        self.metadata = metadata or {}

    def to_dict(self) -> Dict[str, Any]:
        """Convert to a dictionary.

        Returns:
            Dictionary representation of the event
        """
        return {
            "event_id": self.event_id,
            "timestamp": self.timestamp,
            "user_id": self.user_id,
            "action": self.action,
            "section": self.section,
            "key": self.key,
            "old_value": self.old_value,
            "new_value": self.new_value,
            "source": self.source,
            "metadata": self.metadata,
        }

    def to_json(self) -> str:
        """Convert to JSON.

        Returns:
            JSON representation of the event
        """
        return json.dumps(self.to_dict())

    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> "ConfigurationAuditEvent":
        """Create from a dictionary.

        Args:
            data: Dictionary representation of the event

        Returns:
            ConfigurationAuditEvent instance
        """
        return cls(
            event_id=data["event_id"],
            timestamp=data["timestamp"],
            user_id=data["user_id"],
            action=data["action"],
            section=data["section"],
            key=data["key"],
            old_value=data["old_value"],
            new_value=data["new_value"],
            source=data.get("source"),
            metadata=data.get("metadata", {}),
        )

    @classmethod
    def from_json(cls, json_str: str) -> "ConfigurationAuditEvent":
        """Create from JSON.

        Args:
            json_str: JSON representation of the event

        Returns:
            ConfigurationAuditEvent instance
        """
        return cls.from_dict(json.loads(json_str))


class ConfigurationAuditHandler(ABC):
    """Abstract base class for configuration audit handlers."""

    @abstractmethod
    def handle_event(self, event: ConfigurationAuditEvent) -> None:
        """Handle a configuration audit event.

        Args:
            event: The audit event to handle
        """
        pass


class FileAuditHandler(ConfigurationAuditHandler):
    """File-based audit handler."""

    def __init__(self, file_path: str, append: bool = True):
        """Initialize with file path.

        Args:
            file_path: Path to the audit log file
            append: Whether to append to the file or overwrite
        """
        self._file_path = Path(file_path)
        self._append = append
        self._lock = threading.RLock()

        # Create directory if it doesn't exist
        os.makedirs(self._file_path.parent, exist_ok=True)

        # Create or truncate the file if not appending
        if not append and self._file_path.exists():
            with open(self._file_path, "w"):
                pass

    def handle_event(self, event: ConfigurationAuditEvent) -> None:
        """Handle a configuration audit event.

        Args:
            event: The audit event to handle
        """
        with self._lock:
            try:
                # Open the file in append mode
                with open(self._file_path, "a") as f:
                    # Write the event as JSON
                    f.write(event.to_json() + "\n")
            except Exception as e:
                logger.error(f"Failed to write audit event to file: {e}")


class SystemAuditHandler(ConfigurationAuditHandler):
    """System audit service handler."""

    def __init__(self):
        """Initialize the system audit handler."""
        if not AUDIT_AVAILABLE:
            raise ValueError("Audit system not available")

        self._audit_service = get_audit_service()

    def handle_event(self, event: ConfigurationAuditEvent) -> None:
        """Handle a configuration audit event.

        Args:
            event: The audit event to handle
        """
        try:
            # Map action to severity
            if event.action == "create":
                severity = AuditSeverity.INFO
            elif event.action == "update":
                severity = AuditSeverity.INFO
            elif event.action == "delete":
                severity = AuditSeverity.WARNING
            else:
                severity = AuditSeverity.INFO

            # Create details
            details = {
                "section": event.section,
                "key": event.key,
                "source": event.source,
            }

            # Add old and new values if not sensitive
            if not _is_sensitive_key(event.key):
                details["old_value"] = event.old_value
                details["new_value"] = event.new_value

            # Add metadata
            details.update(event.metadata)

            # Record the audit event
            self._audit_service.record(
                category=AuditCategory.CONFIGURATION,
                severity=severity,
                event_type=f"configuration.{event.action}",
                source=event.source or "configuration",
                user_id=event.user_id,
                resource_id=(
                    f"{event.section}.{event.key}" if event.key else event.section
                ),
                resource_type="configuration",
                action=event.action,
                outcome="success",
                details=details,
            )
        except Exception as e:
            logger.error(f"Failed to record audit event: {e}")


def _is_sensitive_key(key: Optional[str]) -> bool:
    """Check if a key is sensitive.

    Args:
        key: Configuration key

    Returns:
        True if sensitive, False otherwise
    """
    if not key:
        return False

    sensitive_patterns = [
        "password",
        "secret",
        "key",
        "token",
        "credential",
        "auth",
        "private",
    ]

    return any(pattern in key.lower() for pattern in sensitive_patterns)


class ConfigurationAudit:
    """Configuration audit system."""

    _instance = None
    _lock = threading.RLock()

    def __new__(cls):
        """Singleton pattern implementation."""
        with cls._lock:
            if cls._instance is None:
                cls._instance = super(ConfigurationAudit, cls).__new__(cls)
                cls._instance._initialized = False
            return cls._instance

    def __init__(self):
        """Initialize the configuration audit system."""
        if self._initialized:
            return

        with self._lock:
            if not self._initialized:
                self._handlers = []
                self._enabled = True
                self._initialized = True

                logger.info("Configuration audit system initialized")

    def enable(self) -> None:
        """Enable auditing."""
        with self._lock:
            self._enabled = True
            logger.info("Configuration auditing enabled")

    def disable(self) -> None:
        """Disable auditing."""
        with self._lock:
            self._enabled = False
            logger.info("Configuration auditing disabled")

    def is_enabled(self) -> bool:
        """Check if auditing is enabled.

        Returns:
            True if enabled, False otherwise
        """
        return self._enabled

    def add_handler(self, handler: ConfigurationAuditHandler) -> None:
        """Add an audit handler.

        Args:
            handler: The handler to add
        """
        with self._lock:
            self._handlers.append(handler)
            logger.info(f"Added audit handler: {handler.__class__.__name__}")

    def remove_handler(self, handler: ConfigurationAuditHandler) -> None:
        """Remove an audit handler.

        Args:
            handler: The handler to remove
        """
        with self._lock:
            if handler in self._handlers:
                self._handlers.remove(handler)
                logger.info(f"Removed audit handler: {handler.__class__.__name__}")

    def record_event(
        self,
        user_id: Optional[str],
        action: str,
        section: Optional[str],
        key: Optional[str],
        old_value: Optional[Any],
        new_value: Optional[Any],
        source: Optional[str] = None,
        metadata: Optional[Dict[str, Any]] = None,
    ) -> str:
        """Record a configuration audit event.

        Args:
            user_id: ID of the user who made the change
            action: Action performed (create, update, delete)
            section: Configuration section
            key: Configuration key
            old_value: Previous value
            new_value: New value
            source: Source of the change (e.g., component name)
            metadata: Additional event metadata

        Returns:
            Event ID
        """
        if not self._enabled:
            return ""

        # Create the event
        event_id = str(uuid.uuid4())
        event = ConfigurationAuditEvent(
            event_id=event_id,
            timestamp=datetime.now().timestamp(),
            user_id=user_id,
            action=action,
            section=section,
            key=key,
            old_value=old_value,
            new_value=new_value,
            source=source,
            metadata=metadata,
        )

        # Process the event through handlers
        for handler in self._handlers:
            try:
                handler.handle_event(event)
            except Exception as e:
                logger.error(f"Error in audit handler: {e}")

        return event_id


# Singleton accessor
def get_config_audit() -> ConfigurationAudit:
    """Get the singleton configuration audit instance."""
    return ConfigurationAudit()


# Decorator for auditing configuration changes
def audit_config_change(
    action: str,
    section_arg: Optional[int] = None,
    key_arg: Optional[int] = None,
    source: Optional[str] = None,
):
    """Decorator for auditing configuration changes.

    Args:
        action: Action being performed (create, update, delete)
        section_arg: Index of the section argument (or None)
        key_arg: Index of the key argument (or None)
        source: Source of the change (e.g., component name)

    Returns:
        Decorator function
    """

    def decorator(func: Callable) -> Callable:
        @wraps(func)
        def wrapper(*args, **kwargs):
            # Skip if auditing is not enabled
            audit = get_config_audit()
            if not audit.is_enabled():
                return func(*args, **kwargs)

            # Get section and key from args or kwargs
            section = None
            if section_arg is not None:
                if section_arg < len(args):
                    section = args[section_arg]
                elif "section" in kwargs:
                    section = kwargs["section"]

            key = None
            if key_arg is not None:
                if key_arg < len(args):
                    key = args[key_arg]
                elif "key" in kwargs:
                    key = kwargs["key"]

            # Get user ID from kwargs
            user_id = kwargs.get("user_id")

            # Get old value if updating or deleting
            old_value = None
            if action in ["update", "delete"] and hasattr(args[0], "get_config"):
                try:
                    if key:
                        old_value = args[0].get_config(section, key)
                    else:
                        old_value = args[0].get_config(section)
                except Exception:
                    pass

            # Get new value if creating or updating
            new_value = None
            if action in ["create", "update"]:
                if "value" in kwargs:
                    new_value = kwargs["value"]
                elif "updates" in kwargs:
                    new_value = kwargs["updates"]

            # Call the function
            result = func(*args, **kwargs)

            # Record the audit event
            audit.record_event(
                user_id=user_id,
                action=action,
                section=section,
                key=key,
                old_value=old_value,
                new_value=new_value,
                source=source,
            )

            return result

        return wrapper

    return decorator
