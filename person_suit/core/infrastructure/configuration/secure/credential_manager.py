"""
Person Suit - Secure Credential Manager

This module provides secure credential handling for the Person Suit framework,
enabling secure storage and retrieval of sensitive configuration values.

The credential manager supports multiple storage backends, including environment
variables, secure credential stores, and encrypted files, with appropriate
security measures for each.
"""

import base64
import json
import logging
import os
import threading
from abc import ABC
from abc import abstractmethod
from pathlib import Path
from typing import Any
from typing import Dict
from typing import List
from typing import Optional

# Configure logging
logger = logging.getLogger("person_suit.configuration.secure")

# Try to import advanced encryption
try:
    from person_suit.core.infrastructure.configuration.secure.advanced_encryption import (
        EncryptionAlgorithm,
    )
    from person_suit.core.infrastructure.configuration.secure.advanced_encryption import (
        get_encryption_manager,
    )

    ADVANCED_ENCRYPTION_AVAILABLE = True
except ImportError:
    ADVANCED_ENCRYPTION_AVAILABLE = False
    logger.warning("Advanced encryption not available. Using basic encryption.")

# Try to import HSM support
try:
    from person_suit.core.infrastructure.configuration.secure.hsm import HSMCredentialBackend
    from person_suit.core.infrastructure.configuration.secure.hsm import HSMError
    from person_suit.core.infrastructure.configuration.secure.hsm import HSMInterface
    from person_suit.core.infrastructure.configuration.secure.hsm import create_hsm_interface

    HSM_AVAILABLE = True
except ImportError:
    HSM_AVAILABLE = False
    logger.warning("HSM support not available.")

# Try to import cryptography for encryption support
try:
    from cryptography.fernet import Fernet
    from cryptography.hazmat.primitives import hashes
    from cryptography.hazmat.primitives.kdf.pbkdf2 import PBKDF2HMAC

    ENCRYPTION_AVAILABLE = True
except ImportError:
    ENCRYPTION_AVAILABLE = False
    logger.warning(
        "Cryptography package not installed. Encrypted credential storage will not be available."
    )


class CredentialError(Exception):
    """Base exception for credential-related errors."""

    pass


class CredentialStorageBackend(ABC):
    """Abstract base class for credential storage backends."""

    @abstractmethod
    def get_credential(self, key: str) -> Optional[str]:
        """Get a credential by key."""
        pass

    @abstractmethod
    def set_credential(self, key: str, value: str) -> bool:
        """Set a credential value."""
        pass

    @abstractmethod
    def delete_credential(self, key: str) -> bool:
        """Delete a credential."""
        pass

    @abstractmethod
    def list_credentials(self) -> List[str]:
        """List available credential keys."""
        pass


class EnvironmentCredentialBackend(CredentialStorageBackend):
    """Environment variable-based credential storage."""

    def __init__(self, prefix: str = "PERSON_SUIT_CRED_"):
        """Initialize with environment variable prefix."""
        self._prefix = prefix

    def get_credential(self, key: str) -> Optional[str]:
        """Get a credential from environment variables."""
        env_key = f"{self._prefix}{key.upper()}"
        return os.environ.get(env_key)

    def set_credential(self, key: str, value: str) -> bool:
        """Set a credential in environment variables.

        Note: This modifies the current process environment only,
        not the system environment.
        """
        env_key = f"{self._prefix}{key.upper()}"
        os.environ[env_key] = value
        return True

    def delete_credential(self, key: str) -> bool:
        """Delete a credential from environment variables."""
        env_key = f"{self._prefix}{key.upper()}"
        if env_key in os.environ:
            del os.environ[env_key]
            return True
        return False

    def list_credentials(self) -> List[str]:
        """List available credential keys from environment variables."""
        credentials = []
        for env_key in os.environ:
            if env_key.startswith(self._prefix):
                # Remove prefix to get the actual key
                key = env_key[len(self._prefix) :]
                credentials.append(key.lower())
        return credentials


class EncryptedFileCredentialBackend(CredentialStorageBackend):
    """Encrypted file-based credential storage."""

    def __init__(self, file_path: str, master_password: str = None):
        """Initialize with file path and optional master password."""
        if not ENCRYPTION_AVAILABLE:
            raise CredentialError(
                "Cryptography package not installed. Encrypted credential storage is not available."
            )

        self._file_path = Path(file_path)
        self._credentials = {}
        self._master_password = master_password or os.environ.get(
            "PERSON_SUIT_MASTER_PASSWORD"
        )

        if not self._master_password:
            logger.warning(
                "No master password provided. Using default password (INSECURE)."
            )
            self._master_password = "default_password_change_me"

        # Generate encryption key from master password
        self._encryption_key = self._generate_key(self._master_password)

        # Load credentials if file exists
        if self._file_path.exists():
            self._load_credentials()

    def _generate_key(self, password: str) -> bytes:
        """Generate encryption key from password."""
        # Use a static salt (not ideal, but better than nothing)
        salt = b"person_suit_salt"

        # Generate key using PBKDF2
        kdf = PBKDF2HMAC(
            algorithm=hashes.SHA256(),
            length=32,
            salt=salt,
            iterations=100000,
        )

        # Derive key from password
        key = base64.urlsafe_b64encode(kdf.derive(password.encode()))
        return key

    def _load_credentials(self) -> None:
        """Load credentials from encrypted file."""
        try:
            # Read encrypted data
            with open(self._file_path, "rb") as f:
                encrypted_data = f.read()

            # Decrypt data
            fernet = Fernet(self._encryption_key)
            decrypted_data = fernet.decrypt(encrypted_data).decode("utf-8")

            # Parse JSON
            self._credentials = json.loads(decrypted_data)

            logger.debug(
                f"Loaded {len(self._credentials)} credentials from {self._file_path}"
            )
        except Exception as e:
            logger.error(f"Failed to load credentials from {self._file_path}: {e}")
            self._credentials = {}

    def _save_credentials(self) -> bool:
        """Save credentials to encrypted file."""
        try:
            # Ensure directory exists
            os.makedirs(self._file_path.parent, exist_ok=True)

            # Convert credentials to JSON
            data = json.dumps(self._credentials)

            # Encrypt data
            fernet = Fernet(self._encryption_key)
            encrypted_data = fernet.encrypt(data.encode("utf-8"))

            # Write to file
            with open(self._file_path, "wb") as f:
                f.write(encrypted_data)

            logger.debug(
                f"Saved {len(self._credentials)} credentials to {self._file_path}"
            )
            return True
        except Exception as e:
            logger.error(f"Failed to save credentials to {self._file_path}: {e}")
            return False

    def get_credential(self, key: str) -> Optional[str]:
        """Get a credential from encrypted storage."""
        return self._credentials.get(key)

    def set_credential(self, key: str, value: str) -> bool:
        """Set a credential in encrypted storage."""
        self._credentials[key] = value
        return self._save_credentials()

    def delete_credential(self, key: str) -> bool:
        """Delete a credential from encrypted storage."""
        if key in self._credentials:
            del self._credentials[key]
            return self._save_credentials()
        return False

    def list_credentials(self) -> List[str]:
        """List available credential keys from encrypted storage."""
        return list(self._credentials.keys())


class AdvancedEncryptedCredentialBackend(CredentialStorageBackend):
    """Advanced encrypted credential storage using military-grade encryption.

    This backend uses the advanced encryption system to provide stronger security
    for credential storage, including support for multiple encryption algorithms,
    key rotation, and quantum-resistant options.
    """

    def __init__(
        self,
        file_path: str,
        algorithm: str = "AES-GCM",
        quantum_resistant: bool = False,
    ):
        """Initialize with file path and encryption options.

        Args:
            file_path: Path to the credential file
            algorithm: Encryption algorithm to use
            quantum_resistant: Whether to use quantum-resistant encryption

        Raises:
            CredentialError: If advanced encryption is not available
        """
        if not ADVANCED_ENCRYPTION_AVAILABLE:
            raise CredentialError(
                "Advanced encryption not available. Cannot create advanced encrypted credential backend."
            )

        self._file_path = Path(file_path)
        self._credentials = {}

        # Get the encryption manager
        self._encryption_manager = get_encryption_manager()

        # Determine the algorithm to use
        if quantum_resistant:
            self._algorithm = EncryptionAlgorithm.QUANTUM_RESISTANT
        elif algorithm == "AES-GCM":
            self._algorithm = EncryptionAlgorithm.AES_GCM
        elif algorithm == "ChaCha20-Poly1305":
            self._algorithm = EncryptionAlgorithm.CHACHA20_POLY1305
        elif algorithm == "Fernet":
            self._algorithm = EncryptionAlgorithm.FERNET
        else:
            logger.warning(f"Unknown algorithm: {algorithm}. Using AES-GCM.")
            self._algorithm = EncryptionAlgorithm.AES_GCM

        # Load credentials if file exists
        if self._file_path.exists():
            self._load_credentials()

    def _load_credentials(self) -> None:
        """Load credentials from encrypted file."""
        try:
            # Read encrypted data
            with open(self._file_path, "r") as f:
                data = json.load(f)

            # Decrypt each credential
            self._credentials = {}
            for key, encrypted_value in data.items():
                try:
                    # Decrypt the value
                    decrypted_value = self._encryption_manager.decrypt_value(
                        encrypted_value
                    )
                    self._credentials[key] = decrypted_value
                except Exception as e:
                    logger.error(f"Failed to decrypt credential {key}: {e}")

            logger.debug(
                f"Loaded {len(self._credentials)} credentials from {self._file_path}"
            )
        except Exception as e:
            logger.error(f"Failed to load credentials from {self._file_path}: {e}")
            self._credentials = {}

    def _save_credentials(self) -> bool:
        """Save credentials to encrypted file."""
        try:
            # Ensure directory exists
            os.makedirs(self._file_path.parent, exist_ok=True)

            # Encrypt each credential
            encrypted_data = {}
            for key, value in self._credentials.items():
                try:
                    # Encrypt the value
                    encrypted_value = self._encryption_manager.encrypt_value(
                        value, algorithm=self._algorithm
                    )
                    encrypted_data[key] = encrypted_value
                except Exception as e:
                    logger.error(f"Failed to encrypt credential {key}: {e}")
                    return False

            # Write to file
            with open(self._file_path, "w") as f:
                json.dump(encrypted_data, f, indent=2)

            logger.debug(
                f"Saved {len(self._credentials)} credentials to {self._file_path}"
            )
            return True
        except Exception as e:
            logger.error(f"Failed to save credentials to {self._file_path}: {e}")
            return False

    def get_credential(self, key: str) -> Optional[str]:
        """Get a credential from encrypted storage."""
        value = self._credentials.get(key)
        if isinstance(value, str):
            return value
        elif value is not None:
            # Convert non-string values to string
            return str(value)
        return None

    def set_credential(self, key: str, value: str) -> bool:
        """Set a credential in encrypted storage."""
        self._credentials[key] = value
        return self._save_credentials()

    def delete_credential(self, key: str) -> bool:
        """Delete a credential from encrypted storage."""
        if key in self._credentials:
            del self._credentials[key]
            return self._save_credentials()
        return False

    def list_credentials(self) -> List[str]:
        """List available credential keys from encrypted storage."""
        return list(self._credentials.keys())

    def rotate_keys(self) -> bool:
        """Rotate encryption keys.

        This re-encrypts all credentials with new keys.

        Returns:
            True if successful, False otherwise
        """
        try:
            # Rotate keys in the encryption manager
            self._encryption_manager.rotate_keys()

            # Re-save credentials to use the new keys
            return self._save_credentials()
        except Exception as e:
            logger.error(f"Failed to rotate keys: {e}")
            return False


class CredentialManager:
    """Manager for secure credential handling."""

    _instance = None
    _lock = threading.RLock()

    def __new__(cls):
        """Singleton pattern implementation."""
        with cls._lock:
            if cls._instance is None:
                cls._instance = super(CredentialManager, cls).__new__(cls)
                cls._instance._initialized = False
            return cls._instance

    def __init__(self):
        """Initialize the credential manager."""
        if self._initialized:
            return

        with self._lock:
            if not self._initialized:
                # Default to environment variable backend
                self._backend = EnvironmentCredentialBackend()
                self._initialized = True

                logger.info("Credential manager initialized")

    def set_backend(self, backend: CredentialStorageBackend) -> None:
        """Set the storage backend for credentials."""
        with self._lock:
            self._backend = backend
            logger.info(f"Changed credential backend to {backend.__class__.__name__}")

    def get_backend(self) -> CredentialStorageBackend:
        """Get the current storage backend."""
        return self._backend

    def use_environment_backend(self, prefix: str = "PERSON_SUIT_CRED_") -> None:
        """Use environment variable-based credential storage."""
        self.set_backend(EnvironmentCredentialBackend(prefix))

    def use_encrypted_file_backend(
        self, file_path: str, master_password: str = None
    ) -> bool:
        """Use encrypted file-based credential storage."""
        try:
            if not ENCRYPTION_AVAILABLE:
                logger.error(
                    "Cryptography package not installed. Encrypted credential storage is not available."
                )
                return False

            backend = EncryptedFileCredentialBackend(file_path, master_password)
            self.set_backend(backend)
            return True
        except Exception as e:
            logger.error(f"Failed to initialize encrypted file backend: {e}")
            return False

    def use_advanced_encrypted_backend(
        self,
        file_path: str,
        algorithm: str = "AES-GCM",
        quantum_resistant: bool = False,
    ) -> bool:
        """Use advanced encrypted credential storage.

        Args:
            file_path: Path to the credential file
            algorithm: Encryption algorithm to use (AES-GCM, ChaCha20-Poly1305, Fernet)
            quantum_resistant: Whether to use quantum-resistant encryption

        Returns:
            True if successful, False otherwise
        """
        try:
            if not ADVANCED_ENCRYPTION_AVAILABLE:
                logger.error(
                    "Advanced encryption not available. Cannot use advanced encrypted credential backend."
                )
                return False

            backend = AdvancedEncryptedCredentialBackend(
                file_path, algorithm, quantum_resistant
            )
            self.set_backend(backend)
            logger.info(
                f"Using advanced encrypted credential backend with {algorithm} algorithm"
            )
            return True
        except Exception as e:
            logger.error(f"Failed to initialize advanced encrypted backend: {e}")
            return False

    def use_hsm_backend(
        self,
        hsm_type: str = "software",
        metadata_file: Optional[str] = None,
        master_key_id: Optional[str] = None,
        **hsm_kwargs,
    ) -> bool:
        """Use HSM-based credential storage.

        Args:
            hsm_type: Type of HSM to use ("software" for simulator)
            metadata_file: Path to store credential metadata
            master_key_id: ID of the master key to use (generated if not provided)
            **hsm_kwargs: Additional arguments for the HSM interface

        Returns:
            True if successful, False otherwise
        """
        try:
            if not HSM_AVAILABLE:
                logger.error(
                    "HSM support not available. Cannot use HSM credential backend."
                )
                return False

            # Create the HSM interface
            hsm = create_hsm_interface(hsm_type, **hsm_kwargs)

            # Create the HSM credential backend
            backend = HSMCredentialBackend(hsm, master_key_id)

            # Set metadata file if provided
            if metadata_file:
                backend.set_metadata_file(metadata_file)

            # Set the backend
            self.set_backend(backend)

            logger.info(f"Using HSM credential backend with {hsm_type} HSM")
            return True
        except Exception as e:
            logger.error(f"Failed to initialize HSM backend: {e}")
            return False

    def get_credential(self, key: str, default: str = None) -> Optional[str]:
        """Get a credential by key."""
        value = self._backend.get_credential(key)
        return value if value is not None else default

    def set_credential(self, key: str, value: str) -> bool:
        """Set a credential value."""
        return self._backend.set_credential(key, value)

    def delete_credential(self, key: str) -> bool:
        """Delete a credential."""
        return self._backend.delete_credential(key)

    def list_credentials(self) -> List[str]:
        """List available credential keys."""
        return self._backend.list_credentials()

    def resolve_credentials(
        self, config: Dict[str, Any], credential_prefix: str = "${cred:"
    ) -> Dict[str, Any]:
        """Resolve credential references in configuration.

        This method searches for credential references in the configuration
        (e.g., "${cred:database_password}") and replaces them with the actual
        credential values.

        Args:
            config: The configuration dictionary to process
            credential_prefix: The prefix that identifies credential references

        Returns:
            A new configuration dictionary with resolved credentials
        """
        if not isinstance(config, dict):
            return config

        result = {}

        for key, value in config.items():
            if isinstance(value, str) and credential_prefix in value:
                # Extract credential key
                start = value.find(credential_prefix) + len(credential_prefix)
                end = value.find("}", start)

                if end > start:
                    cred_key = value[start:end]
                    cred_value = self.get_credential(cred_key)

                    if cred_value is not None:
                        # Replace credential reference with actual value
                        result[key] = value.replace(
                            f"{credential_prefix}{cred_key}}}", cred_value
                        )
                    else:
                        # Credential not found, keep original value
                        logger.warning(f"Credential not found: {cred_key}")
                        result[key] = value
                else:
                    # Invalid credential reference format
                    result[key] = value
            elif isinstance(value, dict):
                # Recursively process nested dictionaries
                result[key] = self.resolve_credentials(value, credential_prefix)
            elif isinstance(value, list):
                # Process lists
                result[key] = [
                    (
                        self.resolve_credentials(item, credential_prefix)
                        if isinstance(item, dict)
                        else item
                    )
                    for item in value
                ]
            else:
                # Keep other values as is
                result[key] = value

        return result


# Singleton accessor
def get_credential_manager() -> CredentialManager:
    """Get the singleton credential manager instance."""
    return CredentialManager()
