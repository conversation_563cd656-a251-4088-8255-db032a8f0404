"""
Person Suit - Advanced Encryption for Configuration

This module provides advanced encryption capabilities for sensitive configuration values,
supporting multiple encryption algorithms, key rotation, and quantum-resistant options.

The encryption system integrates with the existing security framework and provides
a clean interface for encrypting and decrypting configuration values.
"""

import base64
import json
import logging
import os
import threading
import time
from datetime import datetime
from datetime import <PERSON><PERSON><PERSON>
from enum import Enum
from pathlib import Path
from typing import Any
from typing import Dict
from typing import Optional
from typing import Union

logger = logging.getLogger("person_suit.configuration.secure.encryption")

# Try to import cryptography for encryption support
try:
    from cryptography.fernet import Fernet
    from cryptography.hazmat.backends import default_backend
    from cryptography.hazmat.primitives import hashes
    from cryptography.hazmat.primitives import hmac
    from cryptography.hazmat.primitives.ciphers import Cipher
    from cryptography.hazmat.primitives.ciphers import algorithms
    from cryptography.hazmat.primitives.ciphers import modes
    from cryptography.hazmat.primitives.kdf.hkdf import HKDF
    from cryptography.hazmat.primitives.kdf.pbkdf2 import PBKDF2HMAC

    ENCRYPTION_AVAILABLE = True
except ImportError:
    ENCRYPTION_AVAILABLE = False
    logger.warning(
        "Cryptography package not installed. Advanced encryption will not be available."
    )

# Try to import quantum-resistant cryptography
try:
    # This is a placeholder - in a real implementation, you would import an actual
    # quantum-resistant cryptography library
    QUANTUM_RESISTANT_AVAILABLE = False
    logger.info(
        "Quantum-resistant cryptography not available. Using classical algorithms."
    )
except ImportError:
    QUANTUM_RESISTANT_AVAILABLE = False


class EncryptionAlgorithm(Enum):
    """Supported encryption algorithms."""

    AES_GCM = "AES-GCM"
    CHACHA20_POLY1305 = "ChaCha20-Poly1305"
    FERNET = "Fernet"
    QUANTUM_RESISTANT = "Quantum-Resistant"


class EncryptionKey:
    """Encryption key with metadata."""

    def __init__(
        self,
        key_id: str,
        key_material: bytes,
        algorithm: EncryptionAlgorithm,
        created_at: Optional[datetime] = None,
        expires_at: Optional[datetime] = None,
        metadata: Optional[Dict[str, Any]] = None,
    ):
        """Initialize an encryption key.

        Args:
            key_id: Unique identifier for the key
            key_material: The actual key bytes
            algorithm: The algorithm this key is for
            created_at: When the key was created
            expires_at: When the key expires
            metadata: Additional key metadata
        """
        self.key_id = key_id
        self.key_material = key_material
        self.algorithm = algorithm
        self.created_at = created_at or datetime.now()
        self.expires_at = expires_at
        self.metadata = metadata or {}

    def is_expired(self) -> bool:
        """Check if the key is expired.

        Returns:
            True if the key is expired, False otherwise
        """
        if self.expires_at is None:
            return False
        return datetime.now() > self.expires_at

    def to_dict(self) -> Dict[str, Any]:
        """Convert the key to a dictionary for storage.

        Returns:
            Dictionary representation of the key
        """
        return {
            "key_id": self.key_id,
            "key_material": base64.b64encode(self.key_material).decode("utf-8"),
            "algorithm": self.algorithm.value,
            "created_at": self.created_at.isoformat(),
            "expires_at": self.expires_at.isoformat() if self.expires_at else None,
            "metadata": self.metadata,
        }

    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> "EncryptionKey":
        """Create a key from a dictionary.

        Args:
            data: Dictionary representation of the key

        Returns:
            EncryptionKey instance
        """
        return cls(
            key_id=data["key_id"],
            key_material=base64.b64decode(data["key_material"]),
            algorithm=EncryptionAlgorithm(data["algorithm"]),
            created_at=datetime.fromisoformat(data["created_at"]),
            expires_at=(
                datetime.fromisoformat(data["expires_at"])
                if data.get("expires_at")
                else None
            ),
            metadata=data.get("metadata", {}),
        )


class EncryptedData:
    """Encrypted data with metadata."""

    def __init__(
        self,
        ciphertext: bytes,
        algorithm: EncryptionAlgorithm,
        key_id: str,
        metadata: Dict[str, Any],
    ):
        """Initialize encrypted data.

        Args:
            ciphertext: The encrypted data
            algorithm: The algorithm used for encryption
            key_id: ID of the key used for encryption
            metadata: Additional metadata (e.g., IV, tag)
        """
        self.ciphertext = ciphertext
        self.algorithm = algorithm
        self.key_id = key_id
        self.metadata = metadata

    def to_dict(self) -> Dict[str, Any]:
        """Convert to a dictionary for storage.

        Returns:
            Dictionary representation of the encrypted data
        """
        return {
            "ciphertext": base64.b64encode(self.ciphertext).decode("utf-8"),
            "algorithm": self.algorithm.value,
            "key_id": self.key_id,
            "metadata": self.metadata,
        }

    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> "EncryptedData":
        """Create from a dictionary.

        Args:
            data: Dictionary representation of the encrypted data

        Returns:
            EncryptedData instance
        """
        return cls(
            ciphertext=base64.b64decode(data["ciphertext"]),
            algorithm=EncryptionAlgorithm(data["algorithm"]),
            key_id=data["key_id"],
            metadata=data["metadata"],
        )

    def to_storage_format(self) -> str:
        """Convert to a string format for storage.

        Returns:
            String representation of the encrypted data
        """
        data = self.to_dict()
        return base64.b64encode(json.dumps(data).encode("utf-8")).decode("utf-8")

    @classmethod
    def from_storage_format(cls, data_str: str) -> "EncryptedData":
        """Create from a storage format string.

        Args:
            data_str: String representation of the encrypted data

        Returns:
            EncryptedData instance
        """
        data = json.loads(base64.b64decode(data_str).decode("utf-8"))
        return cls.from_dict(data)


class ConfigurationEncryptionManager:
    """Manager for encrypting and decrypting configuration values."""

    _instance = None
    _lock = threading.RLock()

    def __new__(cls):
        """Singleton pattern implementation."""
        with cls._lock:
            if cls._instance is None:
                cls._instance = super(ConfigurationEncryptionManager, cls).__new__(cls)
                cls._instance._initialized = False
            return cls._instance

    def __init__(self):
        """Initialize the encryption manager."""
        if self._initialized:
            return

        with self._lock:
            if not self._initialized:
                if not ENCRYPTION_AVAILABLE:
                    logger.warning(
                        "Cryptography package not installed. Encryption will not be available."
                    )

                self._keys: Dict[str, EncryptionKey] = {}
                self._default_algorithm = EncryptionAlgorithm.AES_GCM
                self._key_rotation_days = 30
                self._initialized = True

                logger.info("Configuration encryption manager initialized")

    def set_key_rotation_period(self, days: int) -> None:
        """Set the key rotation period.

        Args:
            days: Number of days before keys expire
        """
        with self._lock:
            self._key_rotation_days = days
            logger.info(f"Key rotation period set to {days} days")

    def generate_key(
        self,
        algorithm: Optional[EncryptionAlgorithm] = None,
        key_id: Optional[str] = None,
        metadata: Optional[Dict[str, Any]] = None,
    ) -> EncryptionKey:
        """Generate a new encryption key.

        Args:
            algorithm: The algorithm to generate a key for
            key_id: Optional key ID (generated if not provided)
            metadata: Additional key metadata

        Returns:
            The generated key

        Raises:
            RuntimeError: If encryption is not available
        """
        if not ENCRYPTION_AVAILABLE:
            raise RuntimeError(
                "Cryptography package not installed. Encryption is not available."
            )

        # Use default algorithm if not specified
        algorithm = algorithm or self._default_algorithm

        # Generate key ID if not provided
        if key_id is None:
            key_id = f"key-{int(time.time())}-{os.urandom(4).hex()}"

        # Generate key material based on algorithm
        if algorithm == EncryptionAlgorithm.AES_GCM:
            # AES-256 key
            key_material = os.urandom(32)
        elif algorithm == EncryptionAlgorithm.CHACHA20_POLY1305:
            # ChaCha20 key
            key_material = os.urandom(32)
        elif algorithm == EncryptionAlgorithm.FERNET:
            # Fernet key
            key_material = Fernet.generate_key()
        elif algorithm == EncryptionAlgorithm.QUANTUM_RESISTANT:
            if QUANTUM_RESISTANT_AVAILABLE:
                # Use quantum-resistant algorithm
                # This is a placeholder - in a real implementation, you would use
                # an actual quantum-resistant key generation function
                key_material = os.urandom(32)
            else:
                # Fall back to AES-GCM
                logger.warning(
                    "Quantum-resistant cryptography not available. Using AES-GCM instead."
                )
                algorithm = EncryptionAlgorithm.AES_GCM
                key_material = os.urandom(32)
        else:
            raise ValueError(f"Unsupported encryption algorithm: {algorithm}")

        # Calculate expiration date
        expires_at = datetime.now() + timedelta(days=self._key_rotation_days)

        # Create the key
        key = EncryptionKey(
            key_id=key_id,
            key_material=key_material,
            algorithm=algorithm,
            created_at=datetime.now(),
            expires_at=expires_at,
            metadata=metadata or {},
        )

        # Store the key
        with self._lock:
            self._keys[key_id] = key

        logger.info(f"Generated {algorithm.value} key: {key_id}")

        return key

    def get_key(self, key_id: str) -> Optional[EncryptionKey]:
        """Get a key by ID.

        Args:
            key_id: Key ID

        Returns:
            Key or None if not found or expired
        """
        with self._lock:
            key = self._keys.get(key_id)

            if key and key.is_expired():
                logger.warning(f"Attempted to access expired key: {key_id}")
                return None

            return key

    def encrypt(
        self,
        data: Union[str, bytes],
        key: Optional[EncryptionKey] = None,
        algorithm: Optional[EncryptionAlgorithm] = None,
    ) -> EncryptedData:
        """Encrypt data.

        Args:
            data: Data to encrypt
            key: Key to use (generated if not provided)
            algorithm: Algorithm to use (used only if key is not provided)

        Returns:
            Encrypted data

        Raises:
            RuntimeError: If encryption is not available
            ValueError: If the algorithm is not supported
        """
        if not ENCRYPTION_AVAILABLE:
            raise RuntimeError(
                "Cryptography package not installed. Encryption is not available."
            )

        # Convert string to bytes if needed
        data_bytes = data.encode("utf-8") if isinstance(data, str) else data

        # Generate a key if not provided
        if key is None:
            key = self.generate_key(algorithm)

        # Prepare metadata
        metadata = {
            "algorithm": key.algorithm.value,
            "timestamp": datetime.now().isoformat(),
        }

        # Encrypt based on algorithm
        if key.algorithm == EncryptionAlgorithm.AES_GCM:
            # Generate a random IV
            iv = os.urandom(12)  # 12 bytes for GCM
            metadata["iv"] = base64.b64encode(iv).decode("utf-8")

            # Create an encryptor
            encryptor = Cipher(
                algorithms.AES(key.key_material),
                modes.GCM(iv),
                backend=default_backend(),
            ).encryptor()

            # Encrypt the data
            ciphertext = encryptor.update(data_bytes) + encryptor.finalize()

            # Store the authentication tag
            metadata["tag"] = base64.b64encode(encryptor.tag).decode("utf-8")

            encrypted_data = EncryptedData(
                ciphertext, key.algorithm, key.key_id, metadata
            )

        elif key.algorithm == EncryptionAlgorithm.CHACHA20_POLY1305:
            # Generate a random nonce
            nonce = os.urandom(12)  # 12 bytes for ChaCha20-Poly1305
            metadata["nonce"] = base64.b64encode(nonce).decode("utf-8")

            # Create an encryptor
            encryptor = Cipher(
                algorithms.ChaCha20(key.key_material, nonce),
                modes.Poly1305(key.key_material),
                backend=default_backend(),
            ).encryptor()

            # Encrypt the data
            ciphertext = encryptor.update(data_bytes) + encryptor.finalize()

            # Store the authentication tag
            metadata["tag"] = base64.b64encode(encryptor.tag).decode("utf-8")

            encrypted_data = EncryptedData(
                ciphertext, key.algorithm, key.key_id, metadata
            )

        elif key.algorithm == EncryptionAlgorithm.FERNET:
            # Fernet is a high-level symmetric encryption recipe
            # It handles key derivation, rotation, etc.

            # Create a Fernet instance
            f = Fernet(key.key_material)

            # Encrypt
            ciphertext = f.encrypt(data_bytes)

            encrypted_data = EncryptedData(
                ciphertext, key.algorithm, key.key_id, metadata
            )

        elif key.algorithm == EncryptionAlgorithm.QUANTUM_RESISTANT:
            if QUANTUM_RESISTANT_AVAILABLE:
                # Use quantum-resistant algorithm
                # This is a placeholder - in a real implementation, you would use
                # an actual quantum-resistant encryption function

                # For now, fall back to AES-GCM
                logger.warning(
                    "Quantum-resistant encryption not implemented. Using AES-GCM instead."
                )

                # Generate a random IV
                iv = os.urandom(12)  # 12 bytes for GCM
                metadata["iv"] = base64.b64encode(iv).decode("utf-8")

                # Create an encryptor
                encryptor = Cipher(
                    algorithms.AES(key.key_material),
                    modes.GCM(iv),
                    backend=default_backend(),
                ).encryptor()

                # Encrypt the data
                ciphertext = encryptor.update(data_bytes) + encryptor.finalize()

                # Store the authentication tag
                metadata["tag"] = base64.b64encode(encryptor.tag).decode("utf-8")

                encrypted_data = EncryptedData(
                    ciphertext, key.algorithm, key.key_id, metadata
                )
            else:
                # Fall back to AES-GCM
                logger.warning(
                    "Quantum-resistant cryptography not available. Using AES-GCM instead."
                )

                # Generate a random IV
                iv = os.urandom(12)  # 12 bytes for GCM
                metadata["iv"] = base64.b64encode(iv).decode("utf-8")

                # Create an encryptor
                encryptor = Cipher(
                    algorithms.AES(key.key_material),
                    modes.GCM(iv),
                    backend=default_backend(),
                ).encryptor()

                # Encrypt the data
                ciphertext = encryptor.update(data_bytes) + encryptor.finalize()

                # Store the authentication tag
                metadata["tag"] = base64.b64encode(encryptor.tag).decode("utf-8")

                encrypted_data = EncryptedData(
                    ciphertext, key.algorithm, key.key_id, metadata
                )
        else:
            raise ValueError(f"Unsupported encryption algorithm: {key.algorithm.value}")

        logger.debug(
            f"Encrypted data using algorithm {key.algorithm.value} and key {key.key_id}"
        )

        return encrypted_data

    def decrypt(self, encrypted_data: EncryptedData) -> bytes:
        """Decrypt data.

        Args:
            encrypted_data: Data to decrypt

        Returns:
            Decrypted data

        Raises:
            RuntimeError: If encryption is not available
            ValueError: If the algorithm is not supported or the key is not found
        """
        if not ENCRYPTION_AVAILABLE:
            raise RuntimeError(
                "Cryptography package not installed. Encryption is not available."
            )

        # Get the key
        key = self.get_key(encrypted_data.key_id)
        if key is None:
            raise ValueError(f"Key not found: {encrypted_data.key_id}")

        # Decrypt based on algorithm
        if encrypted_data.algorithm == EncryptionAlgorithm.AES_GCM:
            # Get the IV and tag
            iv = base64.b64decode(encrypted_data.metadata["iv"])
            tag = base64.b64decode(encrypted_data.metadata["tag"])

            # Create a decryptor
            decryptor = Cipher(
                algorithms.AES(key.key_material),
                modes.GCM(iv, tag),
                backend=default_backend(),
            ).decryptor()

            # Decrypt the data
            plaintext = (
                decryptor.update(encrypted_data.ciphertext) + decryptor.finalize()
            )

            return plaintext

        elif encrypted_data.algorithm == EncryptionAlgorithm.CHACHA20_POLY1305:
            # Get the nonce and tag
            nonce = base64.b64decode(encrypted_data.metadata["nonce"])
            tag = base64.b64decode(encrypted_data.metadata["tag"])

            # Create a decryptor
            decryptor = Cipher(
                algorithms.ChaCha20(key.key_material, nonce),
                modes.Poly1305(key.key_material),
                backend=default_backend(),
            ).decryptor()

            # Decrypt the data
            plaintext = (
                decryptor.update(encrypted_data.ciphertext) + decryptor.finalize()
            )

            return plaintext

        elif encrypted_data.algorithm == EncryptionAlgorithm.FERNET:
            # Create a Fernet instance
            f = Fernet(key.key_material)

            # Decrypt
            plaintext = f.decrypt(encrypted_data.ciphertext)

            return plaintext

        elif encrypted_data.algorithm == EncryptionAlgorithm.QUANTUM_RESISTANT:
            if QUANTUM_RESISTANT_AVAILABLE:
                # Use quantum-resistant algorithm
                # This is a placeholder - in a real implementation, you would use
                # an actual quantum-resistant decryption function

                # For now, fall back to AES-GCM
                logger.warning(
                    "Quantum-resistant decryption not implemented. Using AES-GCM instead."
                )

                # Get the IV and tag
                iv = base64.b64decode(encrypted_data.metadata["iv"])
                tag = base64.b64decode(encrypted_data.metadata["tag"])

                # Create a decryptor
                decryptor = Cipher(
                    algorithms.AES(key.key_material),
                    modes.GCM(iv, tag),
                    backend=default_backend(),
                ).decryptor()

                # Decrypt the data
                plaintext = (
                    decryptor.update(encrypted_data.ciphertext) + decryptor.finalize()
                )

                return plaintext
            else:
                # Fall back to AES-GCM
                logger.warning(
                    "Quantum-resistant cryptography not available. Using AES-GCM instead."
                )

                # Get the IV and tag
                iv = base64.b64decode(encrypted_data.metadata["iv"])
                tag = base64.b64decode(encrypted_data.metadata["tag"])

                # Create a decryptor
                decryptor = Cipher(
                    algorithms.AES(key.key_material),
                    modes.GCM(iv, tag),
                    backend=default_backend(),
                ).decryptor()

                # Decrypt the data
                plaintext = (
                    decryptor.update(encrypted_data.ciphertext) + decryptor.finalize()
                )

                return plaintext
        else:
            raise ValueError(
                f"Unsupported encryption algorithm: {encrypted_data.algorithm.value}"
            )

    def encrypt_value(
        self, value: Any, algorithm: Optional[EncryptionAlgorithm] = None
    ) -> str:
        """Encrypt a configuration value.

        Args:
            value: Value to encrypt
            algorithm: Algorithm to use

        Returns:
            Encrypted value in storage format

        Raises:
            RuntimeError: If encryption is not available
        """
        if not ENCRYPTION_AVAILABLE:
            raise RuntimeError(
                "Cryptography package not installed. Encryption is not available."
            )

        # Convert value to JSON
        value_json = json.dumps(value)

        # Encrypt the value
        encrypted_data = self.encrypt(value_json, algorithm=algorithm)

        # Convert to storage format
        return encrypted_data.to_storage_format()

    def decrypt_value(self, encrypted_value: str) -> Any:
        """Decrypt a configuration value.

        Args:
            encrypted_value: Encrypted value in storage format

        Returns:
            Decrypted value

        Raises:
            RuntimeError: If encryption is not available
            ValueError: If the value cannot be decrypted
        """
        if not ENCRYPTION_AVAILABLE:
            raise RuntimeError(
                "Cryptography package not installed. Encryption is not available."
            )

        # Parse the encrypted data
        encrypted_data = EncryptedData.from_storage_format(encrypted_value)

        # Decrypt the value
        decrypted_bytes = self.decrypt(encrypted_data)

        # Parse the JSON
        return json.loads(decrypted_bytes.decode("utf-8"))

    def rotate_keys(self) -> Dict[str, str]:
        """Rotate expired keys.

        Returns:
            Dictionary mapping old key IDs to new key IDs
        """
        with self._lock:
            # Find expired keys
            expired_keys = [
                key_id for key_id, key in self._keys.items() if key.is_expired()
            ]

            # Generate new keys for each expired key
            key_mapping = {}
            for old_key_id in expired_keys:
                old_key = self._keys[old_key_id]

                # Generate a new key with the same algorithm
                new_key = self.generate_key(old_key.algorithm)

                # Store the mapping
                key_mapping[old_key_id] = new_key.key_id

                logger.info(f"Rotated key {old_key_id} to {new_key.key_id}")

            return key_mapping

    def export_keys(self, file_path: Union[str, Path], password: str) -> bool:
        """Export keys to a file.

        Args:
            file_path: Path to export to
            password: Password to encrypt the keys with

        Returns:
            True if successful, False otherwise
        """
        if not ENCRYPTION_AVAILABLE:
            logger.error("Cryptography package not installed. Cannot export keys.")
            return False

        try:
            # Convert keys to dictionaries
            keys_data = {key_id: key.to_dict() for key_id, key in self._keys.items()}

            # Convert to JSON
            keys_json = json.dumps(keys_data)

            # Generate a key from the password
            salt = os.urandom(16)
            kdf = PBKDF2HMAC(
                algorithm=hashes.SHA256(),
                length=32,
                salt=salt,
                iterations=100000,
                backend=default_backend(),
            )
            key = base64.urlsafe_b64encode(kdf.derive(password.encode("utf-8")))

            # Encrypt the keys
            f = Fernet(key)
            encrypted_keys = f.encrypt(keys_json.encode("utf-8"))

            # Create the export data
            export_data = {
                "salt": base64.b64encode(salt).decode("utf-8"),
                "data": base64.b64encode(encrypted_keys).decode("utf-8"),
            }

            # Write to file
            with open(file_path, "w") as f:
                json.dump(export_data, f)

            logger.info(f"Exported {len(self._keys)} keys to {file_path}")
            return True

        except Exception as e:
            logger.error(f"Failed to export keys: {e}")
            return False

    def import_keys(self, file_path: Union[str, Path], password: str) -> bool:
        """Import keys from a file.

        Args:
            file_path: Path to import from
            password: Password to decrypt the keys with

        Returns:
            True if successful, False otherwise
        """
        if not ENCRYPTION_AVAILABLE:
            logger.error("Cryptography package not installed. Cannot import keys.")
            return False

        try:
            # Read the file
            with open(file_path, "r") as f:
                export_data = json.load(f)

            # Get the salt and encrypted data
            salt = base64.b64decode(export_data["salt"])
            encrypted_keys = base64.b64decode(export_data["data"])

            # Generate a key from the password
            kdf = PBKDF2HMAC(
                algorithm=hashes.SHA256(),
                length=32,
                salt=salt,
                iterations=100000,
                backend=default_backend(),
            )
            key = base64.urlsafe_b64encode(kdf.derive(password.encode("utf-8")))

            # Decrypt the keys
            f = Fernet(key)
            keys_json = f.decrypt(encrypted_keys).decode("utf-8")

            # Parse the keys
            keys_data = json.loads(keys_json)

            # Import the keys
            with self._lock:
                for key_id, key_data in keys_data.items():
                    self._keys[key_id] = EncryptionKey.from_dict(key_data)

            logger.info(f"Imported {len(keys_data)} keys from {file_path}")
            return True

        except Exception as e:
            logger.error(f"Failed to import keys: {e}")
            return False


# Singleton accessor
def get_encryption_manager() -> ConfigurationEncryptionManager:
    """Get the singleton encryption manager instance."""
    return ConfigurationEncryptionManager()
