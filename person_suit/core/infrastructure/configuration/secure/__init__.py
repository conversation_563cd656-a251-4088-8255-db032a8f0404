"""
Person Suit - Secure Configuration

This package provides secure configuration handling for the Person Suit framework,
including secure credential storage and retrieval.
"""

from .credential_manager import CredentialError
from .credential_manager import CredentialManager
from .credential_manager import CredentialStorageBackend
from .credential_manager import EncryptedFileCredentialBackend
from .credential_manager import EnvironmentCredentialBackend
from .credential_manager import get_credential_manager

__all__ = [
    "CredentialManager",
    "CredentialStorageBackend",
    "EnvironmentCredentialBackend",
    "EncryptedFileCredentialBackend",
    "CredentialError",
    "get_credential_manager",
]
