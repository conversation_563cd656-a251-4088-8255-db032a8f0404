"""
Person Suit - Configuration Schema Validator

This module provides schema validation functionality for the Person Suit framework's
configuration system, ensuring that configuration values adhere to defined schemas.

The validator supports multiple schema types, including JSON Schema, Python type
validation, and custom validation functions.
"""

import logging
import re
import threading
from dataclasses import dataclass
from enum import Enum
from enum import auto
from typing import Any
from typing import Callable
from typing import Dict
from typing import List
from typing import Optional

# Configure logging
logger = logging.getLogger("person_suit.configuration.schema")

# Optional semver support
try:
    import semver

    SEMVER_AVAILABLE = True
except ImportError:
    SEMVER_AVAILABLE = False
    logger.warning(
        "semver package not installed. Version comparison will use string comparison."
    )

try:
    import jsonschema

    JSONSCHEMA_AVAILABLE = True
except ImportError:
    JSONSCHEMA_AVAILABLE = False

# Configure logging
logger = logging.getLogger("person_suit.configuration.schema")


class SchemaType(Enum):
    """Schema type to determine validation approach."""

    JSON_SCHEMA = auto()
    PYTHON_TYPE = auto()
    CUSTOM = auto()


@dataclass
class ConfigSchema:
    """Configuration schema definition.

    Attributes:
        name: The name of the schema (usually matches the configuration section)
        schema_type: The type of schema (JSON_SCHEMA, PYTHON_TYPE, or CUSTOM)
        schema: The actual schema definition
        description: Optional description of the schema
        version: Optional version number for the schema
        migrations: Optional dictionary of migration functions for version upgrades
    """

    name: str
    schema_type: SchemaType
    schema: Any
    description: str = ""
    version: Optional[str] = None
    migrations: Optional[Dict[str, Callable[[Dict[str, Any]], Dict[str, Any]]]] = None

    def __post_init__(self):
        """Validate schema upon initialization."""
        if self.schema_type == SchemaType.JSON_SCHEMA and not JSONSCHEMA_AVAILABLE:
            logger.warning(
                "JSON Schema validation requested but jsonschema package is not available."
            )

        if not self.description:
            self.description = f"Schema for {self.name} configuration section"

        # Initialize migrations dictionary if not provided
        if self.migrations is None:
            self.migrations = {}

        # Ensure version is a string if provided
        if self.version is not None and not isinstance(self.version, str):
            self.version = str(self.version)


class ValidationError:
    """Represents a schema validation error."""

    def __init__(self, path: str, message: str, value: Any = None):
        self.path = path
        self.message = message
        self.value = value

    def __str__(self):
        if self.value is not None:
            return f"{self.path}: {self.message} (got: {self.value})"
        return f"{self.path}: {self.message}"


class ConfigValidator:
    """Configuration validator for schema validation."""

    _instance = None
    _lock = threading.RLock()

    def __new__(cls):
        """Singleton pattern implementation."""
        with cls._lock:
            if cls._instance is None:
                cls._instance = super(ConfigValidator, cls).__new__(cls)
                cls._instance._initialized = False
            return cls._instance

    def __init__(self):
        """Initialize the config validator."""
        if self._initialized:
            return

        with self._lock:
            if not self._initialized:
                self._schemas = {}
                self._custom_validators = {}
                self._initialized = True

                # Register built-in validators
                self._register_builtin_validators()

                logger.info("Configuration validator initialized")

    def _register_builtin_validators(self):
        """Register built-in custom validators."""
        # Register common validators
        self.register_custom_validator("positive_integer", validate_positive_integer)
        self.register_custom_validator("port_number", validate_port_number)
        self.register_custom_validator("percentage", validate_percentage)
        self.register_custom_validator("url", validate_url)
        self.register_custom_validator("email", validate_email)
        self.register_custom_validator("ip_address", validate_ip_address)

    def register_schema(self, schema: ConfigSchema) -> bool:
        """Register a configuration schema for validation."""
        try:
            self._schemas[schema.name] = schema
            logger.debug(f"Registered schema for {schema.name}")
            return True
        except Exception as e:
            logger.error(f"Failed to register schema for {schema.name}: {e}")
            return False

    def register_custom_validator(
        self, name: str, validator_func: Callable[[Any], Optional[str]]
    ) -> bool:
        """Register a custom validator function."""
        try:
            self._custom_validators[name] = validator_func
            logger.debug(f"Registered custom validator: {name}")
            return True
        except Exception as e:
            logger.error(f"Failed to register custom validator {name}: {e}")
            return False

    def validate(self, section_name: str, data: Any) -> List[str]:
        """Validate configuration data against registered schema.

        If the schema has a version and the data has a different version,
        this will attempt to migrate the data to the current schema version
        before validation.

        Args:
            section_name: The name of the section to validate
            data: The configuration data to validate

        Returns:
            A list of validation error messages, or an empty list if validation passed
        """
        # Check if schema exists for this section
        if section_name not in self._schemas:
            logger.debug(
                f"No schema registered for section {section_name}, skipping validation"
            )
            return []

        schema = self._schemas[section_name]

        # Check if migration is needed
        if schema.version is not None and isinstance(data, dict) and "_version" in data:
            data_version = data["_version"]
            if data_version != schema.version:
                # Attempt to migrate data
                try:
                    data = self._migrate_data(schema, data, data_version)
                except Exception as e:
                    logger.error(f"Error migrating data for {section_name}: {e}")
                    return [f"Migration error: {str(e)}"]

        # Validate based on schema type
        try:
            if schema.schema_type == SchemaType.JSON_SCHEMA:
                return self._validate_json_schema(schema, data)
            elif schema.schema_type == SchemaType.PYTHON_TYPE:
                return self._validate_python_type(schema, data)
            elif schema.schema_type == SchemaType.CUSTOM:
                return self._validate_custom(schema, data)
            else:
                logger.warning(
                    f"Unknown schema type for {section_name}: {schema.schema_type}"
                )
                return [f"Unknown schema type: {schema.schema_type}"]
        except Exception as e:
            logger.error(f"Error during validation for {section_name}: {e}")
            return [f"Validation error: {str(e)}"]

    def _validate_json_schema(self, schema: ConfigSchema, data: Any) -> List[str]:
        """Validate using JSON Schema."""
        if not JSONSCHEMA_AVAILABLE:
            return [
                "JSON Schema validation not available (jsonschema package not installed)"
            ]

        errors = []
        try:
            jsonschema.validate(instance=data, schema=schema.schema)
        except jsonschema.exceptions.ValidationError as e:
            # Build the full path to the error
            path = "/".join(str(x) for x in e.path) if e.path else "root"
            errors.append(f"{path}: {e.message}")
        except Exception as e:
            errors.append(f"Schema validation error: {str(e)}")

        return errors

    def _validate_python_type(self, schema: ConfigSchema, data: Any) -> List[str]:
        """Validate using Python type annotations."""
        errors = []

        # Get the expected type from schema
        expected_type = schema.schema

        # Handle different validation approaches
        if isinstance(expected_type, type):
            # Simple type validation
            if not isinstance(data, expected_type):
                errors.append(
                    f"Expected type {expected_type.__name__}, got {type(data).__name__}"
                )
        elif isinstance(expected_type, dict):
            # Dictionary with type specifications for each key
            if not isinstance(data, dict):
                errors.append(f"Expected dictionary, got {type(data).__name__}")
            else:
                # Check required keys
                required_keys = expected_type.get("__required__", [])
                for key in required_keys:
                    if key not in data:
                        errors.append(f"Missing required key: {key}")

                # Check types for each key
                for key, value in data.items():
                    # Skip keys not in schema
                    if key not in expected_type and "*" not in expected_type:
                        continue

                    # Get expected type for this key
                    key_type = expected_type.get(key, expected_type.get("*", Any))

                    # Skip Any type (no validation)
                    if key_type is Any:
                        continue

                    # Validate type
                    if not isinstance(value, key_type):
                        errors.append(
                            f"{key}: Expected type {key_type.__name__}, got {type(value).__name__}"
                        )
        else:
            errors.append(f"Unsupported Python type schema: {expected_type}")

        return errors

    def _validate_custom(self, schema: ConfigSchema, data: Any) -> List[str]:
        """Validate using custom validation function or specification."""
        errors = []
        custom_schema = schema.schema

        # Custom schema can be a function or a dictionary of specifications
        if callable(custom_schema):
            # Direct validation function
            result = custom_schema(data)
            if result:
                errors.append(result)
        elif isinstance(custom_schema, dict):
            # Dictionary of validation specifications
            if not isinstance(data, dict):
                errors.append(f"Expected dictionary, got {type(data).__name__}")
            else:
                # Check each key in the data
                for key, value in data.items():
                    # Get validator for this key
                    validator_name = custom_schema.get(
                        key, custom_schema.get("*", None)
                    )
                    if not validator_name:
                        continue

                    # Get the validator function
                    validator = self._custom_validators.get(validator_name)
                    if not validator:
                        errors.append(f"{key}: Unknown validator: {validator_name}")
                        continue

                    # Apply validation
                    result = validator(value)
                    if result:
                        errors.append(f"{key}: {result}")

                # Check for required keys
                required_keys = custom_schema.get("__required__", [])
                for key in required_keys:
                    if key not in data:
                        errors.append(f"Missing required key: {key}")
        else:
            errors.append(f"Unsupported custom schema: {custom_schema}")

        return errors

    def get_schema(self, section_name: str) -> Optional[ConfigSchema]:
        """Get schema for a section."""
        return self._schemas.get(section_name)

    def get_all_schemas(self) -> Dict[str, ConfigSchema]:
        """Get all registered schemas."""
        return self._schemas.copy()

    def get_custom_validators(self) -> Dict[str, Callable]:
        """Get all registered custom validators."""
        return self._custom_validators.copy()

    def _migrate_data(
        self, schema: ConfigSchema, data: Dict[str, Any], data_version: str
    ) -> Dict[str, Any]:
        """Migrate data from one version to another.

        Args:
            schema: The schema containing migration functions
            data: The data to migrate
            data_version: The current version of the data

        Returns:
            The migrated data

        Raises:
            ValueError: If migration is not possible
        """
        target_version = schema.version
        migrations = schema.migrations

        # Create a copy of the data to avoid modifying the original
        result = data.copy()

        # Check if direct migration is available
        migration_key = f"{data_version}->{target_version}"
        if migration_key in migrations:
            # Direct migration
            result = migrations[migration_key](result)
            result["_version"] = target_version
            logger.info(
                f"Migrated {schema.name} from {data_version} to {target_version} (direct)"
            )
            return result

        # Try to find a migration path
        if SEMVER_AVAILABLE:
            # Use semver for version comparison
            try:
                # Get all available migrations
                available_versions = set([data_version, target_version])
                for key in migrations.keys():
                    if "->" in key:
                        from_ver, to_ver = key.split("->")
                        available_versions.add(from_ver)
                        available_versions.add(to_ver)

                # Sort versions
                sorted_versions = sorted(
                    list(available_versions), key=lambda v: semver.VersionInfo.parse(v)
                )

                # Find data_version and target_version in the sorted list
                start_idx = sorted_versions.index(data_version)
                end_idx = sorted_versions.index(target_version)

                # Determine direction (upgrade or downgrade)
                if start_idx < end_idx:
                    # Upgrade: apply migrations in ascending order
                    migration_path = sorted_versions[start_idx : end_idx + 1]
                    for i in range(len(migration_path) - 1):
                        from_ver = migration_path[i]
                        to_ver = migration_path[i + 1]
                        migration_key = f"{from_ver}->{to_ver}"

                        if migration_key in migrations:
                            result = migrations[migration_key](result)
                            result["_version"] = to_ver
                            logger.info(
                                f"Migrated {schema.name} from {from_ver} to {to_ver}"
                            )
                        else:
                            logger.warning(
                                f"Missing migration for {schema.name} from {from_ver} to {to_ver}"
                            )
                else:
                    # Downgrade: apply migrations in descending order
                    migration_path = sorted_versions[end_idx : start_idx + 1]
                    migration_path.reverse()
                    for i in range(len(migration_path) - 1):
                        from_ver = migration_path[i]
                        to_ver = migration_path[i + 1]
                        migration_key = f"{from_ver}->{to_ver}"

                        if migration_key in migrations:
                            result = migrations[migration_key](result)
                            result["_version"] = to_ver
                            logger.info(
                                f"Migrated {schema.name} from {from_ver} to {to_ver}"
                            )
                        else:
                            logger.warning(
                                f"Missing migration for {schema.name} from {from_ver} to {to_ver}"
                            )
            except Exception as e:
                logger.error(f"Error during semver-based migration: {e}")
                raise ValueError(
                    f"Cannot migrate {schema.name} from {data_version} to {target_version}: {e}"
                )
        else:
            # Simple string comparison for versions
            # This is less reliable but works without semver
            logger.warning(
                "Using string comparison for version migration (semver not available)"
            )

            # Try to find a direct path through intermediate versions
            current = data_version
            while current != target_version:
                found_next = False

                # Find any migration that starts with the current version
                for key in migrations.keys():
                    if key.startswith(f"{current}->"):
                        from_ver, to_ver = key.split("->")
                        result = migrations[key](result)
                        result["_version"] = to_ver
                        logger.info(
                            f"Migrated {schema.name} from {from_ver} to {to_ver}"
                        )
                        current = to_ver
                        found_next = True
                        break

                if not found_next:
                    raise ValueError(
                        f"Cannot find migration path for {schema.name} from {current} to {target_version}"
                    )

        return result

    def register_migration(
        self,
        section_name: str,
        from_version: str,
        to_version: str,
        migration_func: Callable[[Dict[str, Any]], Dict[str, Any]],
    ) -> bool:
        """Register a migration function for a schema.

        Args:
            section_name: The name of the section (schema)
            from_version: The source version
            to_version: The target version
            migration_func: The migration function that transforms the data

        Returns:
            True if the migration was registered successfully, False otherwise
        """
        try:
            if section_name not in self._schemas:
                logger.warning(
                    f"Cannot register migration for unknown schema: {section_name}"
                )
                return False

            schema = self._schemas[section_name]

            # Ensure schema has version
            if schema.version is None:
                schema.version = to_version

            # Register migration
            migration_key = f"{from_version}->{to_version}"
            schema.migrations[migration_key] = migration_func

            logger.info(
                f"Registered migration for {section_name} from {from_version} to {to_version}"
            )
            return True
        except Exception as e:
            logger.error(f"Failed to register migration: {e}")
            return False


# Built-in validator functions


def validate_positive_integer(value: Any) -> Optional[str]:
    """Validate that a value is a positive integer."""
    if not isinstance(value, int):
        return f"Expected integer, got {type(value).__name__}"
    if value <= 0:
        return f"Expected positive integer, got {value}"
    return None


def validate_port_number(value: Any) -> Optional[str]:
    """Validate that a value is a valid port number (1-65535)."""
    if not isinstance(value, int):
        return f"Expected integer, got {type(value).__name__}"
    if value < 1 or value > 65535:
        return f"Port number must be between 1 and 65535, got {value}"
    return None


def validate_percentage(value: Any) -> Optional[str]:
    """Validate that a value is a percentage (0-100)."""
    if not isinstance(value, (int, float)):
        return f"Expected number, got {type(value).__name__}"
    if value < 0 or value > 100:
        return f"Percentage must be between 0 and 100, got {value}"
    return None


def validate_url(value: Any) -> Optional[str]:
    """Validate that a value is a URL."""
    if not isinstance(value, str):
        return f"Expected string, got {type(value).__name__}"
    # Simple URL validation pattern
    pattern = r"^(https?|ftp)://[^\s/$.?#].[^\s]*$"
    if not re.match(pattern, value):
        return f"Invalid URL format: {value}"
    return None


def validate_email(value: Any) -> Optional[str]:
    """Validate that a value is an email address."""
    if not isinstance(value, str):
        return f"Expected string, got {type(value).__name__}"
    # Simple email validation pattern
    pattern = r"^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$"
    if not re.match(pattern, value):
        return f"Invalid email format: {value}"
    return None


def validate_ip_address(value: Any) -> Optional[str]:
    """Validate that a value is an IP address."""
    if not isinstance(value, str):
        return f"Expected string, got {type(value).__name__}"
    # Simple IPv4 pattern
    ipv4_pattern = r"^(\d{1,3}\.){3}\d{1,3}$"
    # Simple IPv6 pattern
    ipv6_pattern = r"^([0-9a-fA-F]{1,4}:){7}[0-9a-fA-F]{1,4}$"

    if not re.match(ipv4_pattern, value) and not re.match(ipv6_pattern, value):
        return f"Invalid IP address format: {value}"

    # For IPv4, check the ranges
    if re.match(ipv4_pattern, value):
        octets = value.split(".")
        for octet in octets:
            num = int(octet)
            if num < 0 or num > 255:
                return f"IPv4 octet out of range (0-255): {value}"

    return None


# Singleton accessor
def get_validator() -> ConfigValidator:
    """Get the singleton configuration validator instance."""
    return ConfigValidator()
