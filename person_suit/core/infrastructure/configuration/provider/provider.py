"""
Person Suit - Configuration Provider

This module provides the storage and retrieval mechanisms for configuration data
in the Person Suit framework, offering different backend options for storing configuration.

The configuration provider acts as an abstraction layer that enables different
storage backends to be used for configuration persistence.
"""

import json
import logging
import os
import threading
import time
from abc import ABC
from abc import abstractmethod
from pathlib import Path
from typing import Any
from typing import Dict
from typing import List
from typing import Optional

# Configure logging
logger = logging.getLogger("person_suit.configuration.provider")


class ConfigurationStorageError(Exception):
    """Exception raised for errors in configuration storage operations."""

    pass


class ConfigStorageBackend(ABC):
    """Abstract base class for configuration storage backends."""

    @abstractmethod
    def load(self) -> Dict[str, Any]:
        """Load configuration from storage."""
        pass

    @abstractmethod
    def save(self, config: Dict[str, Any]) -> bool:
        """Save configuration to storage."""
        pass

    @abstractmethod
    def exists(self) -> bool:
        """Check if configuration storage exists."""
        pass


class MemoryStorageBackend(ConfigStorageBackend):
    """In-memory storage backend for configuration."""

    def __init__(self, initial_config: Dict[str, Any] = None):
        """Initialize with optional initial configuration."""
        self._config = initial_config.copy() if initial_config else {}

    def load(self) -> Dict[str, Any]:
        """Load configuration from memory."""
        return self._config.copy()

    def save(self, config: Dict[str, Any]) -> bool:
        """Save configuration to memory."""
        self._config = config.copy()
        return True

    def exists(self) -> bool:
        """Check if configuration exists in memory."""
        return True

    def clear(self) -> bool:
        """Clear all configuration data."""
        self._config = {}
        return True


class FileStorageBackend(ConfigStorageBackend):
    """File-based storage backend for configuration.

    Supports both JSON and YAML formats, with automatic detection based on file extension.
    """

    def __init__(self, file_path: str):
        """Initialize with file path."""
        self._file_path = Path(file_path)

        # Check for YAML support
        try:
            import yaml

            self._yaml_available = True
        except ImportError:
            self._yaml_available = False
            logger.warning(
                "PyYAML not installed. YAML configuration files will not be supported."
            )

    def load(self) -> Dict[str, Any]:
        """Load configuration from file.

        Supports both JSON and YAML formats, with automatic detection based on file extension.

        Returns:
            The loaded configuration dictionary

        Raises:
            ConfigurationStorageError: If the file cannot be loaded or parsed
        """
        if not self.exists():
            return {}

        file_ext = self._file_path.suffix.lower()

        try:
            with open(self._file_path, "r") as f:
                if file_ext in (".yaml", ".yml"):
                    if not self._yaml_available:
                        raise ConfigurationStorageError(
                            "Cannot load YAML file: PyYAML not installed"
                        )
                    import yaml

                    return yaml.safe_load(f)
                elif file_ext == ".json":
                    return json.load(f)
                else:
                    # Try to detect format based on content
                    content = f.read()
                    try:
                        return json.loads(content)
                    except json.JSONDecodeError:
                        if self._yaml_available:
                            try:
                                import yaml

                                return yaml.safe_load(content)
                            except yaml.YAMLError as e:
                                raise ConfigurationStorageError(
                                    f"Failed to parse file as YAML: {e}"
                                )
                        else:
                            raise ConfigurationStorageError(
                                "Unsupported file format and PyYAML not available"
                            )
        except json.JSONDecodeError as e:
            logger.error(f"Failed to parse configuration file as JSON: {e}")
            raise ConfigurationStorageError(f"Invalid JSON in configuration file: {e}")
        except Exception as e:
            logger.error(f"Failed to load configuration file: {e}")
            raise ConfigurationStorageError(f"Failed to load configuration: {e}")

    def save(self, config: Dict[str, Any]) -> bool:
        """Save configuration to file.

        Automatically determines the format based on file extension.

        Args:
            config: The configuration dictionary to save

        Returns:
            True if the save was successful, False otherwise
        """
        try:
            # Ensure directory exists
            os.makedirs(self._file_path.parent, exist_ok=True)

            # Determine format based on file extension
            file_ext = self._file_path.suffix.lower()

            # Create a temporary file
            temp_path = f"{self._file_path}.tmp"
            with open(temp_path, "w") as f:
                if file_ext in (".yaml", ".yml"):
                    if not self._yaml_available:
                        logger.error("Cannot save as YAML: PyYAML not installed")
                        return False
                    import yaml

                    yaml.dump(config, f, default_flow_style=False)
                else:  # Default to JSON
                    json.dump(config, f, indent=2)

            # Rename to target file (atomic operation)
            os.replace(temp_path, self._file_path)
            return True

        except Exception as e:
            logger.error(f"Failed to save configuration file: {e}")
            return False

    def exists(self) -> bool:
        """Check if configuration file exists."""
        return self._file_path.exists()


class ConfigProvider:
    """Provider for configuration storage and retrieval."""

    _instance = None
    _lock = threading.RLock()

    def __new__(cls):
        """Singleton pattern implementation."""
        with cls._lock:
            if cls._instance is None:
                cls._instance = super(ConfigProvider, cls).__new__(cls)
                cls._instance._initialized = False
            return cls._instance

    def __init__(self):
        """Initialize the configuration provider."""
        if self._initialized:
            return

        with self._lock:
            if not self._initialized:
                # Default to in-memory storage
                self._backend = MemoryStorageBackend()
                self._auto_save = False
                self._initialized = True

                logger.info("Configuration provider initialized")

    def set_backend(self, backend: ConfigStorageBackend) -> None:
        """Set the storage backend for configuration."""
        with self._lock:
            self._backend = backend
            logger.info(
                f"Changed configuration backend to {backend.__class__.__name__}"
            )

    def get_backend(self) -> ConfigStorageBackend:
        """Get the current storage backend."""
        return self._backend

    def use_memory_backend(self, initial_config: Dict[str, Any] = None) -> None:
        """Use in-memory storage backend."""
        self.set_backend(MemoryStorageBackend(initial_config))

    def use_file_backend(self, file_path: str) -> None:
        """Use file-based storage backend."""
        self.set_backend(FileStorageBackend(file_path))

    def set_auto_save(self, auto_save: bool) -> None:
        """Set whether to automatically save configuration changes."""
        self._auto_save = auto_save

    def load(self) -> Dict[str, Any]:
        """Load configuration from storage."""
        try:
            config = self._backend.load()
            logger.debug(f"Loaded configuration with {len(config)} sections")
            return config
        except Exception as e:
            logger.error(f"Failed to load configuration: {e}")
            return {}

    def save(self, config: Dict[str, Any]) -> bool:
        """Save configuration to storage."""
        try:
            result = self._backend.save(config)
            if result:
                logger.debug(f"Saved configuration with {len(config)} sections")
            return result
        except Exception as e:
            logger.error(f"Failed to save configuration: {e}")
            return False

    def merge_and_save(
        self, config: Dict[str, Any], current_config: Dict[str, Any]
    ) -> bool:
        """Merge new configuration with existing and save."""
        merged_config = current_config.copy()

        # Merge the configurations
        for section, values in config.items():
            if (
                section in merged_config
                and isinstance(merged_config[section], dict)
                and isinstance(values, dict)
            ):
                # Deep merge for dictionaries
                for key, value in values.items():
                    merged_config[section][key] = value
            else:
                # Replace or add section
                merged_config[section] = values

        # Save merged configuration
        return self.save(merged_config)

    def exists(self) -> bool:
        """Check if configuration storage exists."""
        return self._backend.exists()

    def create_backup(self, config: Dict[str, Any], backup_dir: str = None) -> bool:
        """Create a backup of the current configuration."""
        if not isinstance(self._backend, FileStorageBackend):
            logger.warning("Backup only supported for file-based configuration")
            return False

        try:
            # Determine backup path
            if backup_dir:
                backup_path = Path(backup_dir)
            else:
                # Use same directory as config file
                backup_path = self._backend._file_path.parent

            # Ensure backup directory exists
            os.makedirs(backup_path, exist_ok=True)

            # Create backup filename with timestamp
            timestamp = time.strftime("%Y%m%d-%H%M%S")
            file_name = f"config_backup_{timestamp}.json"
            backup_file = backup_path / file_name

            # Save backup
            with open(backup_file, "w") as f:
                json.dump(config, f, indent=2)

            logger.info(f"Created configuration backup: {backup_file}")
            return True

        except Exception as e:
            logger.error(f"Failed to create configuration backup: {e}")
            return False

    def restore_from_backup(self, backup_file: str) -> Optional[Dict[str, Any]]:
        """Restore configuration from a backup file."""
        try:
            backup_path = Path(backup_file)
            if not backup_path.exists():
                logger.warning(f"Backup file does not exist: {backup_file}")
                return None

            # Load backup
            with open(backup_path, "r") as f:
                backup_config = json.load(f)

            logger.info(f"Restored configuration from backup: {backup_file}")
            return backup_config

        except Exception as e:
            logger.error(f"Failed to restore from backup: {e}")
            return None

    def list_backups(self, backup_dir: str = None) -> List[str]:
        """List available backup files."""
        try:
            # Determine backup path
            if backup_dir:
                backup_path = Path(backup_dir)
            elif isinstance(self._backend, FileStorageBackend):
                # Use same directory as config file
                backup_path = self._backend._file_path.parent
            else:
                logger.warning(
                    "No backup directory specified and not using file backend"
                )
                return []

            # Find backup files
            if not backup_path.exists():
                return []

            backups = list(backup_path.glob("config_backup_*.json"))
            return [str(path) for path in sorted(backups, reverse=True)]

        except Exception as e:
            logger.error(f"Failed to list backups: {e}")
            return []


# Factory function for creating storage backends
def create_storage_backend(backend_type: str, **kwargs) -> ConfigStorageBackend:
    """Create a configuration storage backend."""
    if backend_type.lower() == "memory":
        return MemoryStorageBackend(kwargs.get("initial_config"))
    elif backend_type.lower() == "file":
        return FileStorageBackend(kwargs.get("file_path"))
    else:
        raise ValueError(f"Unknown storage backend type: {backend_type}")


# Singleton accessor
def get_config_provider() -> ConfigProvider:
    """Get the singleton configuration provider instance."""
    return ConfigProvider()
