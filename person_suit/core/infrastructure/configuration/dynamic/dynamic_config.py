"""
Person Suit - Dynamic Configuration

This module provides dynamic configuration capabilities for the Person Suit framework,
enabling runtime configuration updates and change notification to affected components.

The dynamic configuration system allows components to subscribe to configuration changes
and react accordingly, ensuring that the system can adapt to configuration changes without
requiring a restart.
"""

import asyncio
import logging
import threading
import time
from dataclasses import dataclass
from dataclasses import field
from enum import Enum
from enum import auto
from typing import Any
from typing import Callable
from typing import Coroutine
from typing import Dict
from typing import List
from typing import Optional
from typing import Union

# Configure logging
logger = logging.getLogger("person_suit.configuration.dynamic")


class ChangeType(Enum):
    """Configuration change type."""

    ADD = auto()
    MODIFY = auto()
    DELETE = auto()
    RESET = auto()


@dataclass
class ConfigChangeEvent:
    """Configuration change event."""

    section: str
    key: Optional[str]
    old_value: Any
    new_value: Any
    change_type: ChangeType
    user_id: Optional[str] = None
    timestamp: float = field(default_factory=lambda: time.time())

    def __post_init__(self):
        """Initialize timestamp if not provided."""
        if not hasattr(self, "timestamp") or self.timestamp is None:
            import time

            self.timestamp = time.time()

    def to_dict(self) -> Dict[str, Any]:
        """Convert event to dictionary representation."""
        return {
            "section": self.section,
            "key": self.key,
            "old_value": self.old_value,
            "new_value": self.new_value,
            "change_type": self.change_type.name,
            "user_id": self.user_id,
            "timestamp": self.timestamp,
        }

    @staticmethod
    def from_dict(data: Dict[str, Any]) -> "ConfigChangeEvent":
        """Create event from dictionary representation."""
        return ConfigChangeEvent(
            section=data["section"],
            key=data.get("key"),
            old_value=data.get("old_value"),
            new_value=data.get("new_value"),
            change_type=ChangeType[data["change_type"]],
            user_id=data.get("user_id"),
            timestamp=data.get("timestamp"),
        )


class ConfigChangeSubscription:
    """Subscription to configuration changes."""

    def __init__(
        self,
        subscriber_id: str,
        callback: Union[
            Callable[[ConfigChangeEvent], None],
            Callable[[ConfigChangeEvent], Coroutine[Any, Any, None]],
        ],
        section_filter: Optional[str] = None,
        key_filter: Optional[str] = None,
    ):
        """Initialize a subscription."""
        self.subscriber_id = subscriber_id
        self.callback = callback
        self.section_filter = section_filter
        self.key_filter = key_filter
        self.is_async = asyncio.iscoroutinefunction(callback)

    def matches(self, event: ConfigChangeEvent) -> bool:
        """Check if this subscription matches an event."""
        # Check section filter
        if self.section_filter and self.section_filter != "*":
            if self.section_filter != event.section:
                return False

        # Check key filter
        if self.key_filter and self.key_filter != "*":
            if event.key is None or self.key_filter != event.key:
                return False

        return True

    async def notify(self, event: ConfigChangeEvent) -> None:
        """Notify the subscriber of a configuration change."""
        try:
            if self.is_async:
                await self.callback(event)
            else:
                self.callback(event)
        except Exception as e:
            logger.error(
                f"Error in configuration change callback {self.subscriber_id}: {e}"
            )


class DynamicConfig:
    """Dynamic configuration system for runtime updates."""

    _instance = None
    _lock = threading.RLock()

    def __new__(cls):
        """Singleton pattern implementation."""
        with cls._lock:
            if cls._instance is None:
                cls._instance = super(DynamicConfig, cls).__new__(cls)
                cls._instance._initialized = False
            return cls._instance

    def __init__(self):
        """Initialize the dynamic configuration system."""
        if self._initialized:
            return

        with self._lock:
            if not self._initialized:
                self._subscriptions = []
                self._change_history = []
                self._max_history = 100
                self._initialized = True

                logger.info("Dynamic configuration system initialized")

    async def notify_change(
        self, event: ConfigChangeEvent, validate_first: bool = True
    ) -> bool:
        """Notify subscribers of a configuration change."""
        try:
            # If validation is requested, this would be done by the ConfigManager
            # before calling this method, so we don't need to do anything here

            # Store in history
            with self._lock:
                self._change_history.append(event)
                # Trim history if needed
                if len(self._change_history) > self._max_history:
                    self._change_history = self._change_history[-self._max_history :]

            # Notify subscribers
            await self._notify_subscribers(event)

            # Log the change
            self._log_change(event)

            return True
        except Exception as e:
            logger.error(f"Failed to process configuration change: {e}")
            return False

    async def _notify_subscribers(self, event: ConfigChangeEvent) -> None:
        """Notify all matching subscribers of a configuration change."""
        # Find matching subscriptions
        matching_subscriptions = [
            subscription
            for subscription in self._subscriptions
            if subscription.matches(event)
        ]

        # Create notification tasks for async callbacks
        notification_tasks = []

        for subscription in matching_subscriptions:
            # Handle async and sync callbacks differently
            if subscription.is_async:
                # Add to task list for concurrent execution
                notification_tasks.append(subscription.notify(event))
            else:
                # Execute sync callbacks immediately
                await subscription.notify(event)

        # Wait for all async notifications to complete
        if notification_tasks:
            await asyncio.gather(*notification_tasks, return_exceptions=True)

    def _log_change(self, event: ConfigChangeEvent) -> None:
        """Log a configuration change."""
        if event.key:
            logger.info(
                f"Configuration changed: {event.section}.{event.key} ({event.change_type.name})"
            )
        else:
            logger.info(
                f"Configuration section changed: {event.section} ({event.change_type.name})"
            )

        # Log details at debug level
        logger.debug(f"Change details: {event.to_dict()}")

    def subscribe(
        self,
        subscriber_id: str,
        callback: Union[
            Callable[[ConfigChangeEvent], None],
            Callable[[ConfigChangeEvent], Coroutine[Any, Any, None]],
        ],
        section_filter: Optional[str] = None,
        key_filter: Optional[str] = None,
    ) -> str:
        """Subscribe to configuration changes."""
        with self._lock:
            # Generate a unique subscription ID if not provided
            if not subscriber_id:
                subscriber_id = f"subscription_{len(self._subscriptions) + 1}"

            # Create subscription
            subscription = ConfigChangeSubscription(
                subscriber_id=subscriber_id,
                callback=callback,
                section_filter=section_filter,
                key_filter=key_filter,
            )

            # Add to subscriptions
            self._subscriptions.append(subscription)

            logger.debug(
                f"Added subscription {subscriber_id} for {section_filter or '*'}.{key_filter or '*'}"
            )

            return subscriber_id

    def unsubscribe(self, subscriber_id: str) -> bool:
        """Unsubscribe from configuration changes."""
        with self._lock:
            # Find subscription by ID
            for i, subscription in enumerate(self._subscriptions):
                if subscription.subscriber_id == subscriber_id:
                    # Remove subscription
                    del self._subscriptions[i]
                    logger.debug(f"Removed subscription {subscriber_id}")
                    return True

            logger.warning(f"Subscription not found: {subscriber_id}")
            return False

    def get_subscriptions(self) -> List[Dict[str, Any]]:
        """Get all active subscriptions."""
        with self._lock:
            return [
                {
                    "subscriber_id": subscription.subscriber_id,
                    "section_filter": subscription.section_filter,
                    "key_filter": subscription.key_filter,
                    "is_async": subscription.is_async,
                }
                for subscription in self._subscriptions
            ]

    def get_change_history(self) -> List[Dict[str, Any]]:
        """Get configuration change history."""
        with self._lock:
            return [event.to_dict() for event in self._change_history]

    def set_max_history(self, max_history: int) -> None:
        """Set the maximum number of change events to keep in history."""
        with self._lock:
            self._max_history = max(10, max_history)  # Minimum of 10

            # Trim history if needed
            if len(self._change_history) > self._max_history:
                self._change_history = self._change_history[-self._max_history :]

    def clear_change_history(self) -> None:
        """Clear the configuration change history."""
        with self._lock:
            self._change_history = []

    def get_subscribers_for_section(self, section: str) -> List[str]:
        """Get all subscribers for a specific section."""
        with self._lock:
            subscribers = []
            for subscription in self._subscriptions:
                if (
                    subscription.section_filter is None
                    or subscription.section_filter == "*"
                    or subscription.section_filter == section
                ):
                    subscribers.append(subscription.subscriber_id)
            return subscribers

    async def replay_changes(
        self, subscriber_id: str, from_timestamp: float = None
    ) -> int:
        """Replay configuration changes to a specific subscriber."""
        with self._lock:
            # Find subscription
            subscription = None
            for sub in self._subscriptions:
                if sub.subscriber_id == subscriber_id:
                    subscription = sub
                    break

            if not subscription:
                logger.warning(
                    f"Cannot replay changes: Subscription not found: {subscriber_id}"
                )
                return 0

            # Filter history events
            events = self._change_history
            if from_timestamp:
                events = [e for e in events if e.timestamp >= from_timestamp]

            # Filter events by subscription filters
            matching_events = [e for e in events if subscription.matches(e)]

            # Replay events
            for event in matching_events:
                await subscription.notify(event)

            return len(matching_events)


# Singleton accessor
def get_dynamic_config() -> DynamicConfig:
    """Get the singleton dynamic configuration instance."""
    return DynamicConfig()
