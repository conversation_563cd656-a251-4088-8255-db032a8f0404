"""
Person Suit - Configuration Management System (IC-5)

This module provides a comprehensive configuration management system for the Person Suit framework,
enabling unified access to configuration with schema validation, dynamic updating,
and environment awareness.

Components:
- ConfigManager: Central configuration management
- ConfigSchema: Schema definition for validation
- Environment: Environment-specific configuration
- ConfigProvider: Storage and retrieval of configuration
- DynamicConfig: Runtime configuration updates

The Configuration Management System ensures type-safe, validated configuration
across all components of Person Suit, with support for dynamic updates and
environment-specific overrides.
"""

import logging
import threading

# Import necessary components from typing
from typing import List
from typing import Optional

# Import audit components
from .audit import ConfigurationAudit
from .audit import ConfigurationAuditEvent
from .audit import ConfigurationAuditHandler
from .audit import FileAuditHandler
from .audit import audit_config_change
from .audit import get_config_audit

# Import the core configuration manager
from .config_manager import ConfigManager
from .config_manager import ConfigurationError
from .config_manager import ConfigurationNotFoundError
from .config_manager import SchemaValidationError
from .config_manager import get_config_manager as _get_config_manager_internal

# Import dynamic config components
from .dynamic.dynamic_config import ChangeType
from .dynamic.dynamic_config import ConfigChangeEvent
from .dynamic.dynamic_config import ConfigChangeSubscription
from .dynamic.dynamic_config import DynamicConfig
from .dynamic.dynamic_config import get_dynamic_config

# Import environment components
from .environment.handler import Environment
from .environment.handler import EnvironmentHandler
from .environment.handler import EnvironmentInfo
from .environment.handler import get_environment_handler

# Import provider
from .provider.provider import ConfigProvider
from .provider.provider import ConfigStorageBackend
from .provider.provider import FileStorageBackend
from .provider.provider import MemoryStorageBackend
from .provider.provider import create_storage_backend
from .provider.provider import get_config_provider

# Import schema components
from .schema.validator import ConfigSchema
from .schema.validator import ConfigValidator
from .schema.validator import SchemaType
from .schema.validator import get_validator
from .schema.validator import validate_email
from .schema.validator import validate_ip_address
from .schema.validator import validate_percentage
from .schema.validator import validate_port_number
from .schema.validator import validate_positive_integer
from .schema.validator import validate_url

# Import secure components
from .secure.credential_manager import AdvancedEncryptedCredentialBackend
from .secure.credential_manager import CredentialManager
from .secure.credential_manager import CredentialStorageBackend
from .secure.credential_manager import EncryptedFileCredentialBackend
from .secure.credential_manager import EnvironmentCredentialBackend
from .secure.credential_manager import get_credential_manager

# Import telemetry components
from .telemetry import ConfigurationTelemetry
from .telemetry import get_config_telemetry
from .telemetry import track_config_access

# Version info
__version__ = "1.0.0"

# Setup logger
logger = logging.getLogger(__name__)

# -*- coding: utf-8 -*-
"""
Configuration Management System.

Provides unified access to framework and component configuration settings
from various sources (files, environment variables, etc.). Includes
schema validation, dynamic updates, and secure handling.
"""

# Import key components from submodules to expose them at this level
from .provider import ConfigProvider
from .provider import FileStorageBackend

# Optional: Singleton access function (DI is preferred)
_config_manager_instance: Optional[ConfigManager] = None
_config_manager_lock = threading.RLock()


def get_config_manager(
    providers: Optional[List[ConfigProvider]] = None,
) -> ConfigManager:
    """
    Gets the singleton ConfigManager instance.

    Initializes it with default providers (e.g., file, env vars) if not already created.
    """
    global _config_manager_instance
    with _config_manager_lock:
        if _config_manager_instance is None:
            # Initialize with default providers if none specified
            if providers is None:
                logger.debug(
                    "No providers specified, initializing ConfigManager with default file/env providers."
                )
                # Example: Load from a default file and environment variables
                try:
                    default_providers = [
                        FileStorageBackend("config/default.yaml", optional=True),
                        FileStorageBackend("config/local.yaml", optional=True),
                    ]
                    providers = default_providers
                except Exception as e:
                    logger.warning(
                        f"Could not initialize default providers: {e}. ConfigManager may be uninitialized."
                    )
                    providers = []  # Initialize empty if defaults fail

            # Use the internal getter which might have different initialization logic if needed
            # Or directly instantiate if _get_config_manager_internal is just the class
            _config_manager_instance = ConfigManager(providers=providers)

            # Perform initial load/setup if needed by the manager
            if hasattr(_config_manager_instance, "initialize"):
                try:
                    # Assuming initialize might be async or sync
                    import asyncio

                    if asyncio.iscoroutinefunction(_config_manager_instance.initialize):
                        # In a non-async context, run it synchronously if possible
                        # This might need adjustment based on actual usage
                        try:
                            asyncio.run(_config_manager_instance.initialize())
                        except (
                            RuntimeError
                        ):  # Cannot run loop when one is already running
                            logger.warning(
                                "Could not run async initialize in current context."
                            )
                    else:
                        _config_manager_instance.initialize()
                except Exception as e:
                    logger.error(f"Error during ConfigManager post-initialization: {e}")

    return _config_manager_instance


__all__ = [
    # Core configuration
    "ConfigManager",
    "get_config_manager",
    "ConfigurationError",
    "SchemaValidationError",
    "ConfigurationNotFoundError",
    # Schema validation
    "ConfigSchema",
    "SchemaType",
    "ConfigValidator",
    "get_validator",
    "validate_positive_integer",
    "validate_port_number",
    "validate_percentage",
    "validate_url",
    "validate_email",
    "validate_ip_address",
    # Environment handling
    "Environment",
    "EnvironmentInfo",
    "EnvironmentHandler",
    "get_environment_handler",
    # Configuration providers
    "ConfigProvider",
    "ConfigStorageBackend",
    "MemoryStorageBackend",
    "FileStorageBackend",
    "get_config_provider",
    "create_storage_backend",
    # Dynamic configuration
    "ConfigChangeEvent",
    "ChangeType",
    "DynamicConfig",
    "get_dynamic_config",
    "ConfigChangeSubscription",
    # Secure credential management
    "CredentialManager",
    "CredentialStorageBackend",
    "EnvironmentCredentialBackend",
    "EncryptedFileCredentialBackend",
    "AdvancedEncryptedCredentialBackend",
    "get_credential_manager",
    # Telemetry
    "ConfigurationTelemetry",
    "get_config_telemetry",
    "track_config_access",
    # Audit
    "ConfigurationAudit",
    "ConfigurationAuditEvent",
    "ConfigurationAuditHandler",
    "FileAuditHandler",
    "get_config_audit",
    "audit_config_change",
    # Version
    "__version__",
]
