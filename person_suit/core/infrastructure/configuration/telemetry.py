"""
Person Suit - Configuration Telemetry

This module provides telemetry integration for the configuration system,
enabling tracking of configuration access patterns, performance metrics,
and health status.
"""

import logging
import threading
import time
from datetime import datetime
from functools import wraps
from typing import Any
from typing import Callable
from typing import Dict
from typing import Optional

logger = logging.getLogger("person_suit.configuration.telemetry")

# Try to import telemetry
try:
    from person_suit.core.infrastructure.telemetry import HealthStatus
    from person_suit.core.infrastructure.telemetry import get_telemetry_manager
    from person_suit.core.infrastructure.telemetry import increment_counter
    from person_suit.core.infrastructure.telemetry import observe_histogram
    from person_suit.core.infrastructure.telemetry import set_gauge
    from person_suit.core.infrastructure.telemetry import set_health_status

    TELEMETRY_AVAILABLE = True
except ImportError:
    TELEMETRY_AVAILABLE = False
    logger.warning("Telemetry not available. Configuration telemetry will be disabled.")


class ConfigurationTelemetry:
    """Telemetry for the configuration system."""

    _instance = None
    _lock = threading.RLock()

    def __new__(cls):
        """Singleton pattern implementation."""
        with cls._lock:
            if cls._instance is None:
                cls._instance = super(ConfigurationTelemetry, cls).__new__(cls)
                cls._instance._initialized = False
            return cls._instance

    def __init__(self):
        """Initialize the configuration telemetry."""
        if self._initialized:
            return

        with self._lock:
            if not self._initialized:
                self._enabled = TELEMETRY_AVAILABLE
                self._access_counts = {}
                self._last_access = {}
                self._initialized = True

                if self._enabled:
                    logger.info("Configuration telemetry initialized")
                else:
                    logger.warning("Configuration telemetry disabled")

    def enable(self) -> None:
        """Enable telemetry."""
        if not TELEMETRY_AVAILABLE:
            logger.warning(
                "Telemetry not available. Cannot enable configuration telemetry."
            )
            return

        with self._lock:
            self._enabled = True
            logger.info("Configuration telemetry enabled")

    def disable(self) -> None:
        """Disable telemetry."""
        with self._lock:
            self._enabled = False
            logger.info("Configuration telemetry disabled")

    def is_enabled(self) -> bool:
        """Check if telemetry is enabled.

        Returns:
            True if enabled, False otherwise
        """
        return self._enabled

    def record_access(
        self,
        section: Optional[str],
        key: Optional[str],
        operation: str,
        duration: float,
        success: bool,
        source: Optional[str] = None,
    ) -> None:
        """Record a configuration access.

        Args:
            section: Configuration section
            key: Configuration key
            operation: Operation performed (get, set, delete)
            duration: Duration of the operation in seconds
            success: Whether the operation was successful
            source: Source of the access (e.g., component name)
        """
        if not self._enabled:
            return

        try:
            # Record access count
            access_key = f"{section or 'all'}.{key or 'all'}.{operation}"
            with self._lock:
                self._access_counts[access_key] = (
                    self._access_counts.get(access_key, 0) + 1
                )
                self._last_access[access_key] = datetime.now()

            # Record telemetry
            dimensions = {
                "section": section or "all",
                "operation": operation,
                "success": str(success).lower(),
            }

            if key:
                dimensions["key"] = key

            if source:
                dimensions["source"] = source

            # Increment counter
            increment_counter("config.access.count", 1, dimensions)

            # Record duration
            observe_histogram("config.access.duration", duration, dimensions)

            # Record success rate
            increment_counter(
                "config.access.success" if success else "config.access.failure",
                1,
                dimensions,
            )

            logger.debug(
                f"Recorded configuration access: {access_key}, success={success}, duration={duration:.6f}s"
            )
        except Exception as e:
            logger.error(f"Failed to record configuration access: {e}")

    def record_validation(
        self, section: str, success: bool, error_count: int, duration: float
    ) -> None:
        """Record a configuration validation.

        Args:
            section: Configuration section
            success: Whether validation was successful
            error_count: Number of validation errors
            duration: Duration of validation in seconds
        """
        if not self._enabled:
            return

        try:
            # Record telemetry
            dimensions = {"section": section, "success": str(success).lower()}

            # Increment counter
            increment_counter("config.validation.count", 1, dimensions)

            # Record duration
            observe_histogram("config.validation.duration", duration, dimensions)

            # Record error count
            if error_count > 0:
                set_gauge("config.validation.errors", error_count, dimensions)

            logger.debug(
                f"Recorded configuration validation: {section}, success={success}, errors={error_count}, duration={duration:.6f}s"
            )
        except Exception as e:
            logger.error(f"Failed to record configuration validation: {e}")

    def record_health(
        self, component: str, status: str, details: Optional[Dict[str, Any]] = None
    ) -> None:
        """Record configuration health status.

        Args:
            component: Configuration component
            status: Health status (healthy, degraded, unhealthy)
            details: Additional health details
        """
        if not self._enabled:
            return

        try:
            # Map status string to HealthStatus enum
            if status.lower() == "healthy":
                health_status = HealthStatus.HEALTHY
            elif status.lower() == "degraded":
                health_status = HealthStatus.DEGRADED
            else:
                health_status = HealthStatus.UNHEALTHY

            # Set health status
            set_health_status(f"config.{component}", health_status, details or {})

            logger.debug(f"Recorded configuration health: {component}, status={status}")
        except Exception as e:
            logger.error(f"Failed to record configuration health: {e}")

    def get_access_statistics(self) -> Dict[str, Any]:
        """Get access statistics.

        Returns:
            Dictionary of access statistics
        """
        with self._lock:
            return {
                "access_counts": self._access_counts.copy(),
                "last_access": {k: v.isoformat() for k, v in self._last_access.items()},
            }


# Singleton accessor
def get_config_telemetry() -> ConfigurationTelemetry:
    """Get the singleton configuration telemetry instance."""
    return ConfigurationTelemetry()


# Decorator for tracking configuration access
def track_config_access(
    operation: str,
    section_arg: Optional[int] = None,
    key_arg: Optional[int] = None,
    source: Optional[str] = None,
):
    """Decorator for tracking configuration access.

    Args:
        operation: Operation being performed (get, set, delete)
        section_arg: Index of the section argument (or None)
        key_arg: Index of the key argument (or None)
        source: Source of the access (e.g., component name)

    Returns:
        Decorator function
    """

    def decorator(func: Callable) -> Callable:
        @wraps(func)
        def wrapper(*args, **kwargs):
            # Skip if telemetry is not enabled
            telemetry = get_config_telemetry()
            if not telemetry.is_enabled():
                return func(*args, **kwargs)

            # Get section and key from args or kwargs
            section = None
            if section_arg is not None:
                if section_arg < len(args):
                    section = args[section_arg]
                elif "section" in kwargs:
                    section = kwargs["section"]

            key = None
            if key_arg is not None:
                if key_arg < len(args):
                    key = args[key_arg]
                elif "key" in kwargs:
                    key = kwargs["key"]

            # Record start time
            start_time = time.time()

            try:
                # Call the function
                result = func(*args, **kwargs)

                # Record access
                duration = time.time() - start_time
                telemetry.record_access(section, key, operation, duration, True, source)

                return result
            except Exception:
                # Record failed access
                duration = time.time() - start_time
                telemetry.record_access(
                    section, key, operation, duration, False, source
                )

                # Re-raise the exception
                raise

        return wrapper

    return decorator
