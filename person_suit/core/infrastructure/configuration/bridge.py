"""
Person Suit - Configuration Bridge

This module provides a bridge between the basic ConfigManager and the advanced
configuration system, allowing seamless integration and migration between the two.

The bridge enables applications to start with the simpler ConfigManager and
gradually transition to the more advanced configuration system as needed.
"""

import asyncio
import logging
from pathlib import Path
from typing import Any
from typing import Dict
from typing import Optional
from typing import Union

from ...application.interfaces.config import IConfigManager
from ..config import ConfigManager as BasicConfigManager
from .config_manager import ConfigManager as AdvancedConfigManager
from .schema.validator import ConfigSchema
from .schema.validator import SchemaType

logger = logging.getLogger("person_suit.configuration.bridge")


class ConfigurationBridge:
    """Bridge between basic and advanced configuration implementations.

    This class provides methods to convert between the basic ConfigManager
    and the advanced configuration system, allowing seamless integration
    and migration between the two.
    """

    @staticmethod
    def basic_to_advanced(basic_config: BasicConfigManager) -> AdvancedConfigManager:
        """Convert a basic ConfigManager to an advanced ConfigManager.

        Args:
            basic_config: The basic ConfigManager instance

        Returns:
            An advanced ConfigManager instance with the same configuration
        """
        # Create a new advanced config manager
        advanced_config = AdvancedConfigManager()

        # Initialize it with the configuration from the basic manager
        asyncio.run(advanced_config.initialize(defaults=basic_config.get_full_config()))

        logger.info("Converted basic ConfigManager to advanced ConfigManager")
        return advanced_config

    @staticmethod
    def advanced_to_basic(advanced_config: AdvancedConfigManager) -> BasicConfigManager:
        """Convert an advanced ConfigManager to a basic ConfigManager.

        Args:
            advanced_config: The advanced ConfigManager instance

        Returns:
            A basic ConfigManager instance with the same configuration
        """
        # Create a new basic config manager
        basic_config = BasicConfigManager()

        # Copy the configuration from the advanced manager
        config_data = advanced_config.get_config()
        basic_config.update_config(config_data)

        logger.info("Converted advanced ConfigManager to basic ConfigManager")
        return basic_config

    @staticmethod
    def wrap_basic_config(basic_config: BasicConfigManager) -> IConfigManager:
        """Wrap a basic ConfigManager with advanced capabilities.

        This creates a proxy that implements the IConfigManager interface
        but provides access to advanced features when needed.

        Args:
            basic_config: The basic ConfigManager instance to wrap

        Returns:
            A proxy implementing IConfigManager with advanced capabilities
        """
        return ConfigBridgeProxy(basic_config)

    @staticmethod
    def register_schema_for_basic(
        basic_config: BasicConfigManager,
        section: str,
        schema: Dict[str, Any],
        schema_type: str = "json_schema",
    ) -> bool:
        """Register a schema for a basic ConfigManager.

        This creates a temporary advanced ConfigManager to register the schema,
        then validates the basic configuration against it.

        Args:
            basic_config: The basic ConfigManager instance
            section: The configuration section to validate
            schema: The schema definition
            schema_type: The type of schema ("json_schema", "python_type", or "custom")

        Returns:
            True if the schema was registered and validation passed, False otherwise
        """
        try:
            # Create a temporary advanced config manager
            advanced_config = ConfigurationBridge.basic_to_advanced(basic_config)

            # Determine schema type
            if schema_type.lower() == "json_schema":
                schema_enum = SchemaType.JSON_SCHEMA
            elif schema_type.lower() == "python_type":
                schema_enum = SchemaType.PYTHON_TYPE
            elif schema_type.lower() == "custom":
                schema_enum = SchemaType.CUSTOM
            else:
                logger.error(f"Unknown schema type: {schema_type}")
                return False

            # Create schema object
            config_schema = ConfigSchema(
                name=section,
                schema_type=schema_enum,
                schema=schema,
                description=f"Schema for {section} section",
            )

            # Register schema
            advanced_config.register_schema(section, config_schema)

            # Validate configuration
            section_data = basic_config.get_section(section) or {}
            validation_errors = advanced_config._validator.validate(
                section, section_data
            )

            if validation_errors:
                for error in validation_errors:
                    logger.error(f"Validation error in section {section}: {error}")
                return False

            logger.info(f"Schema registered and validated for section {section}")
            return True

        except Exception as e:
            logger.error(f"Error registering schema: {e}")
            return False


class ConfigBridgeProxy(IConfigManager):
    """Proxy that wraps a basic ConfigManager with advanced capabilities.

    This class implements the IConfigManager interface but provides access
    to advanced features when needed.
    """

    def __init__(self, basic_config: BasicConfigManager):
        """Initialize the proxy.

        Args:
            basic_config: The basic ConfigManager instance to wrap
        """
        self._basic_config = basic_config
        self._advanced_config = None
        self._using_advanced = False

    def load_config(self, config_source: Any) -> None:
        """Load configuration from a specified source.

        Args:
            config_source: The configuration source (e.g., file path)
        """
        if self._using_advanced:
            asyncio.run(
                self._get_advanced_config().initialize(
                    config_paths=[str(config_source)]
                )
            )
        else:
            self._basic_config.load_config(config_source)

    def get_section(self, section_name: str) -> Optional[Dict[str, Any]]:
        """Get an entire configuration section as a dictionary.

        Args:
            section_name: The name of the section to retrieve

        Returns:
            The section as a dictionary, or None if not found
        """
        if self._using_advanced:
            return self._get_advanced_config().get_config(section=section_name)
        else:
            return self._basic_config.get_section(section_name)

    def get_setting(self, key: str, default: Optional[Any] = None) -> Optional[Any]:
        """Get a specific setting using a dot-separated key.

        Args:
            key: The dot-separated key path to the setting
            default: The default value to return if not found

        Returns:
            The setting value, or the default if not found
        """
        if self._using_advanced:
            # Split the key into section and key parts
            parts = key.split(".", 1)
            if len(parts) == 1:
                # No dot in the key, treat as section
                return self._get_advanced_config().get_config(
                    section=key, default=default
                )
            else:
                # Get section and key
                section, subkey = parts
                return self._get_advanced_config().get_config(
                    section=section, key=subkey, default=default
                )
        else:
            return self._basic_config.get_setting(key, default)

    def get_full_config(self) -> Dict[str, Any]:
        """Get the entire configuration dictionary.

        Returns:
            The full configuration dictionary
        """
        if self._using_advanced:
            return self._get_advanced_config().get_config()
        else:
            return self._basic_config.get_full_config()

    def switch_to_advanced(self) -> None:
        """Switch to using the advanced configuration system."""
        self._using_advanced = True
        self._get_advanced_config()  # Ensure it's initialized
        logger.info("Switched to advanced configuration system")

    def switch_to_basic(self) -> None:
        """Switch to using the basic configuration system."""
        if self._using_advanced and self._advanced_config is not None:
            # Sync configuration back to basic
            config_data = self._advanced_config.get_config()
            self._basic_config.update_config(config_data)

        self._using_advanced = False
        logger.info("Switched to basic configuration system")

    def is_using_advanced(self) -> bool:
        """Check if currently using the advanced configuration system.

        Returns:
            True if using advanced, False if using basic
        """
        return self._using_advanced

    def _get_advanced_config(self) -> AdvancedConfigManager:
        """Get or create the advanced configuration manager.

        Returns:
            The advanced configuration manager instance
        """
        if self._advanced_config is None:
            self._advanced_config = ConfigurationBridge.basic_to_advanced(
                self._basic_config
            )
        return self._advanced_config

    def register_schema(
        self, section: str, schema: Dict[str, Any], schema_type: str = "json_schema"
    ) -> bool:
        """Register a schema for validation.

        Args:
            section: The configuration section to validate
            schema: The schema definition
            schema_type: The type of schema ("json_schema", "python_type", or "custom")

        Returns:
            True if the schema was registered successfully, False otherwise
        """
        if self._using_advanced:
            # Determine schema type
            if schema_type.lower() == "json_schema":
                schema_enum = SchemaType.JSON_SCHEMA
            elif schema_type.lower() == "python_type":
                schema_enum = SchemaType.PYTHON_TYPE
            elif schema_type.lower() == "custom":
                schema_enum = SchemaType.CUSTOM
            else:
                logger.error(f"Unknown schema type: {schema_type}")
                return False

            # Create schema object
            config_schema = ConfigSchema(
                name=section,
                schema_type=schema_enum,
                schema=schema,
                description=f"Schema for {section} section",
            )

            # Register schema
            return self._get_advanced_config().register_schema(section, config_schema)
        else:
            # Use the bridge to register schema for basic config
            return ConfigurationBridge.register_schema_for_basic(
                self._basic_config, section, schema, schema_type
            )

    def save_config(self, file_path: Union[str, Path], format: str = "auto") -> bool:
        """Save the current configuration to a file.

        Args:
            file_path: Path where the configuration should be saved
            format: Format to use ('json', 'yaml', or 'auto' to detect from extension)

        Returns:
            True if the save was successful, False otherwise
        """
        if self._using_advanced:
            # Export configuration and save using provider
            config_data = self._advanced_config.export_config()

            # Create a file storage backend
            from person_suit.core.infrastructure.configuration.provider.provider import (
                FileStorageBackend,
            )

            backend = FileStorageBackend(str(file_path))

            # Save configuration
            return backend.save(config_data)
        else:
            # Use basic config's save method
            return self._basic_config.save_config(file_path, format)
