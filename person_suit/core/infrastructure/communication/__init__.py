"""
Person Suit - Core Infrastructure: Secure Communication Implementation
-------------------------------------------------------------

This package provides the concrete implementations for the secure communication
interfaces defined in `core.application.interfaces.communication`.

It includes:
- Secure Channel and ChannelManager classes with capability-based security.
- Encrypted Message structure implementations with cryptographic verification.
- Telemetry and effect tracking for all communication operations.
- Zero-trust architecture implementation with continuous verification.

Security Features:
- End-to-end encryption for all messages
- Capability-based access control for channels
- Message authentication and integrity verification
- Audit logging for all communication operations
- Protection against replay attacks
"""

# Import message types from application interfaces
from ...application.interfaces.communication_interface import ErrorMessage
from ...application.interfaces.communication_interface import EventMessage
from ...application.interfaces.communication_interface import Message
from ...application.interfaces.communication_interface import MessageHeader
from ...application.interfaces.communication_interface import MessageType
from ...application.interfaces.communication_interface import RequestMessage
from ...application.interfaces.communication_interface import ResponseMessage

# Import secure channel implementations
from ..security.communication import <PERSON><PERSON><PERSON>y
from ..security.communication import EncryptedChannel
from ..security.communication import <PERSON>cure<PERSON>hannel
from ..security.communication import T<PERSON><PERSON>hannel
from .channel import Channel
from .channel_manager import ChannelManager
from .security_handlers import DecryptionHandler
from .security_handlers import EncryptionHandler
from .security_handlers import SecurityCheckHandler

# Export implementations from modularized files
from .types import Capability
from .types import ChannelMetadataImpl
from .types import IChannel
from .types import IChannelManager

__all__ = [
    # From types.py
    "ChannelMetadataImpl",
    "IChannel",
    "IChannelManager",
    "Capability",
    # From channel.py and channel_manager.py
    "Channel",
    "ChannelManager",
    # From security_handlers.py
    "SecurityCheckHandler",
    "EncryptionHandler",
    "DecryptionHandler",
    # From security.communication
    "SecureChannel",
    "EncryptedChannel",
    "TLSChannel",
    "ChannelFactory",
    # Message types from application interfaces
    "MessageType",
    "MessageHeader",
    "Message",
    "RequestMessage",
    "ResponseMessage",
    "EventMessage",
    "ErrorMessage",
]
