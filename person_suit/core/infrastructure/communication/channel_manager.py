"""
Implementation of the channel manager.

This module contains the ChannelManager class, which provides methods to create,
retrieve, and close channels. It integrates with a component registry and includes
enhanced security features.
"""

import asyncio
import logging
from collections import defaultdict
from typing import DefaultDict
from typing import Dict
from typing import Optional
from typing import Set

# Import from application interfaces
from ...application.interfaces.communication_interface import ChannelType
from ...application.interfaces.communication_interface import IChannel

# Import registry interface
from ...application.interfaces.registration_interface import IRegistry
from .channel import Channel

# Import local types and implementations
from .types import ChannelMetadataImpl

# Setup logging
logger = logging.getLogger(__name__)


class ChannelManager:
    """
    Manages the lifecycle of asynchronous communication channels.

    Provides methods to create, retrieve, and close channels. Integrates with
    a component registry and includes placeholders for future enhancements like
    Choreographic programming orchestration and advanced security policy enforcement.
    """

    def __init__(
        self,
        registry: Optional[IRegistry] = None,
        loop: Optional[asyncio.AbstractEventLoop] = None,
    ):
        """
        Initialize the Channel Manager.

        Args:
            registry: Optional component registry for validation (should support async methods).
            loop: The asyncio event loop to use.
        """
        self._channels: Dict[str, Channel] = {}
        self._component_channels: DefaultDict[str, Set[str]] = defaultdict(set)
        self._registry = registry  # Optional dependency for component validation
        self._lock = asyncio.Lock()  # Use async lock for managing channels dict/set
        self._loop = loop or asyncio.get_running_loop()

    async def create_channel(
        self,
        source_id: str,
        target_id: Optional[str] = None,
        channel_type: ChannelType = ChannelType.DIRECT,
        auto_open: bool = True,
        **properties,
    ) -> IChannel:
        """
        Asynchronously create and return a new communication channel.

        Args:
            source_id: ID of the source component.
            target_id: Optional ID of the target component.
            channel_type: Type of channel to create.
            auto_open: Whether to automatically open the channel.
            **properties: Additional channel properties.

        Returns:
            The created channel.

        Raises:
            ValueError: If component validation fails.
        """
        # Validate components if registry is available
        if self._registry:
            # Validate source component
            if not await self._registry.is_registered(source_id):
                raise ValueError(f"Source component {source_id} is not registered.")

            # Validate target component if specified
            if target_id and not await self._registry.is_registered(target_id):
                raise ValueError(f"Target component {target_id} is not registered.")

        # Create channel metadata
        metadata = ChannelMetadataImpl(
            source_id=source_id,
            target_id=target_id,
            channel_type=channel_type,
            properties=dict(properties),
        )

        # Create the channel
        channel = Channel(metadata, loop=self._loop)

        # Store the channel
        async with self._lock:
            self._channels[metadata.channel_id] = channel
            self._component_channels[source_id].add(metadata.channel_id)
            if target_id:
                self._component_channels[target_id].add(metadata.channel_id)

        logger.info(
            f"Created channel {metadata.channel_id} from {source_id} to {target_id or 'broadcast'}"
        )

        # Open the channel if auto_open is True
        if auto_open:
            await channel.open()

        return channel

    async def get_channel(self, channel_id: str) -> Optional[IChannel]:
        """
        Asynchronously get a channel by its ID.

        Args:
            channel_id: ID of the channel to retrieve.

        Returns:
            The channel or None if not found.
        """
        async with self._lock:
            return self._channels.get(channel_id)

    async def close_channel(
        self, channel_id: str, timeout: Optional[float] = None
    ) -> bool:
        """
        Asynchronously close a channel by its ID.

        Args:
            channel_id: ID of the channel to close.
            timeout: Optional timeout for waiting for the channel to close.

        Returns:
            True if the channel was closed, False if not found.
        """
        channel = None
        source_id = None
        target_id = None

        # Get the channel and remove it from the channels dict
        async with self._lock:
            channel = self._channels.pop(channel_id, None)
            if channel:
                source_id = channel.metadata.source_id
                target_id = channel.metadata.target_id

                # Remove from component mappings
                if source_id in self._component_channels:
                    self._component_channels[source_id].discard(channel_id)
                    # Clean up empty sets
                    if not self._component_channels[source_id]:
                        del self._component_channels[source_id]

                if target_id and target_id in self._component_channels:
                    self._component_channels[target_id].discard(channel_id)
                    # Clean up empty sets
                    if not self._component_channels[target_id]:
                        del self._component_channels[target_id]

        # Close the channel if found
        if channel:
            try:
                await channel.close(timeout=timeout)
                logger.info(
                    f"Closed channel {channel_id} from {source_id} to {target_id or 'broadcast'}"
                )
                return True
            except Exception as e:
                logger.error(f"Error closing channel {channel_id}: {e}")
                return False
        else:
            logger.warning(f"Channel {channel_id} not found for closing")
            return False

    async def close_component_channels(self, component_id: str) -> None:
        """
        Asynchronously close all channels associated with a component.

        Args:
            component_id: ID of the component whose channels to close.
        """
        # Get the channel IDs to close
        channel_ids_to_close = set()
        async with self._lock:
            channel_ids_to_close = self._component_channels.get(
                component_id, set()
            ).copy()

        if not channel_ids_to_close:
            logger.info(f"No channels found for component {component_id}")
            return

        logger.info(
            f"Closing {len(channel_ids_to_close)} channels associated with component {component_id}"
        )
        # Close channels concurrently
        close_tasks = [self.close_channel(ch_id) for ch_id in channel_ids_to_close]
        results = await asyncio.gather(*close_tasks, return_exceptions=True)

        # Log any errors during closing
        errors = []
        for ch_id, result in zip(channel_ids_to_close, results):
            if isinstance(result, Exception):
                errors.append(ch_id)
                logger.error(
                    f"Error closing channel {ch_id} for component {component_id}: {result}"
                )

        # Final log message
        if errors:
            logger.warning(
                f"Finished closing channels for component {component_id}. Errors occurred for channels: {errors}"
            )
        else:
            logger.info(
                f"Successfully closed all channels for component {component_id}."
            )
