"""
Communication infrastructure for the Person Suit framework.

This module provides a standardized mechanism for asynchronous communication
between components, with enhanced security features including capability-based
security, zero-trust validation, and crypto-agility.

This module has been modularized into several files:
- types.py: Type definitions and interfaces
- channel.py: Channel implementation
- channel_manager.py: Channel manager implementation
- security_handlers.py: Security-related effect handlers
"""

import logging

# Import and re-export channel implementation
# Import and re-export channel manager
# Import and re-export security handlers
from .security_handlers import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>
from .security_handlers import <PERSON>cry<PERSON><PERSON><PERSON>ler
from .security_handlers import SecurityCheckHandler

# Import and re-export types

# Setup logging
logger = logging.getLogger(__name__)


# Register effect handlers
def register_effect_handlers():
    """Register the communication-related effect handlers."""
    try:
        from person_suit.core.infrastructure.effects import register_handler

        # Register security handlers
        register_handler("security_check", SecurityCheckHandler())
        register_handler("encryption", EncryptionHandler())
        register_handler("decryption", DecryptionH<PERSON>ler())

        logger.info("Registered communication effect handlers")
    except ImportError:
        logger.warning(
            "Could not register communication effect handlers: effect system not available"
        )
    except Exception as e:
        logger.error(f"Error registering communication effect handlers: {e}")


# Register handlers when module is imported
register_effect_handlers()
