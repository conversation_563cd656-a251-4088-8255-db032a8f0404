"""
Security handlers for the communication infrastructure.

This module contains the security-related effect handlers for the communication
infrastructure, including capability-based security checks, encryption, and decryption.
"""

import logging

# Import Protocol and TYPE_CHECKING for interface definitions
from typing import TYPE_CHECKING
from typing import Any
from typing import Dict
from typing import Protocol

# Import message-based effects API (Phase 1.2 migration)
# Import telemetry system - REMOVED as it will be injected
# from person_suit.core.infrastructure.telemetry import (
#     increment_counter
# )
# Import security error base
from ...errors.error_types import SecurityError

# Define placeholder Protocols for injected services
# In a real scenario, these would be imported from their respective modules
if TYPE_CHECKING:

    class ITelemetryService(Protocol):
        def increment_counter(
            self, name: str, value: int = 1, tags: Dict[str, str] | None = None
        ) -> None: ...

    class ICapabilityVerifier(Protocol):
        async def verify(
            self,
            sender_capability: Any,
            required_capability: Any,
            context: Dict[str, Any] | None = None,
        ) -> bool: ...

    class IEncryptionService(Protocol):
        async def encrypt(
            self, data: bytes, algorithm: str, context: Dict[str, Any] | None = None
        ) -> Dict[str, Any]: ...

    class IDecryptionService(Protocol):
        async def decrypt(
            self,
            encrypted_data: Dict[str, Any],
            algorithm: str,
            context: Dict[str, Any] | None = None,
        ) -> bytes: ...

else:
    # Define placeholders for runtime if needed, helps avoid import errors if interfaces aren't ready
    ITelemetryService = Any
    ICapabilityVerifier = Any
    IEncryptionService = Any
    IDecryptionService = Any

# Setup logging
logger = logging.getLogger(__name__)


class SecurityCheckHandler:
    """Handler for security check effects using message-based API.

    Requires injected ICapabilityVerifier and ITelemetryService.
    """

    def __init__(
        self,
        capability_verifier: ICapabilityVerifier,
        telemetry_service: ITelemetryService,
    ):
        self._verifier = capability_verifier
        self._telemetry = telemetry_service
        logger.info("SecurityCheckHandler initialized with verifier and telemetry.")

    async def handle_security_check(self, payload: Dict[str, Any]) -> Any:
        """
        Handle a security check effect.

        Args:
            payload: The effect payload containing security check parameters.

        Returns:
            True if the security check passes.

        Raises:
            SecurityError: If the security check fails.
        """
        logger.debug(f"Handling security check effect: {payload}")

        # Extract capabilities from payload
        sender_capability = payload.get("sender_capability")
        required_capability = payload.get("required_capability")
        channel_id = payload.get("channel_id", "unknown")
        source_id = payload.get("source_id", "unknown")

        # Delegate actual verification to injected service
        is_authorized = False
        try:
            verification_context = {"channel_id": channel_id, "source_id": source_id}
            is_authorized = await self._verifier.verify(
                sender_capability, required_capability, verification_context
            )
            if not is_authorized:
                logger.warning(
                    f"Capability check failed via verifier: Sender capability {sender_capability} "
                    f"does not satisfy required {required_capability}"
                )
        except Exception as e:
            logger.error(
                f"Capability verification failed unexpectedly: {e}", exc_info=True
            )
            is_authorized = False
            # Raise a SecurityError to indicate failure clearly during handling
            raise SecurityError(f"Capability verification process failed: {e}") from e

        # Record security check in telemetry via injected service
        self._telemetry.increment_counter(
            "security.capability.check",
            1,
            {
                "result": "allowed" if is_authorized else "denied",
                "source_id": source_id,
                "channel_id": channel_id,
            },
        )

        if not is_authorized:
            # This exception might be redundant if the verifier already raises on failure,
            # but provides a clear failure point in the handler itself.
            raise SecurityError(
                f"Authorization denied: {sender_capability} does not satisfy {required_capability}"
            )

        return True  # Return True on success


class EncryptionHandler:
    """Handler for encryption effects using message-based API.

    Requires injected IEncryptionService and ITelemetryService.
    """

    def __init__(
        self,
        encryption_service: IEncryptionService,
        telemetry_service: ITelemetryService,
    ):
        self._encrypter = encryption_service
        self._telemetry = telemetry_service
        logger.info(
            "EncryptionHandler initialized with encryption service and telemetry."
        )

    async def handle_encryption(self, payload: Dict[str, Any]) -> Any:
        """
        Handle an encryption effect.

        Args:
            payload: The effect payload containing encryption parameters.

        Returns:
            A dictionary containing the encrypted data structure as returned by the service.

        Raises:
            SecurityError: If encryption fails.
        """
        # Payload should contain data to encrypt and algorithm info
        payload_data = payload.get("data", b"")
        algo = payload.get("algorithm", "AES-256-GCM")
        channel_id = payload.get("channel_id", "unknown")
        source_id = payload.get("source_id", "unknown")

        logger.debug(
            f"Handling encryption effect for {len(payload_data)} bytes using {algo}"
        )

        encrypted_payload = None
        try:
            # Delegate encryption to injected service
            encryption_context = {"channel_id": channel_id, "source_id": source_id}
            encrypted_payload = await self._encrypter.encrypt(
                payload_data, algo, encryption_context
            )

            # Record encryption in telemetry
            self._telemetry.increment_counter(
                "security.encryption",
                1,
                {
                    "algorithm": algo,
                    "channel_id": channel_id,
                    "source_id": source_id,
                    "status": "success",
                },
            )

            # Return the result from the encryption service
            # Assuming the service returns the dict structure expected by consumers
            return {"encrypted_data": encrypted_payload}

        except Exception as e:
            # Record encryption error in telemetry
            self._telemetry.increment_counter(
                "security.encryption.error",
                1,
                {
                    "algorithm": algo,
                    "channel_id": channel_id,
                    "source_id": source_id,
                    "error_type": type(e).__name__,
                },
            )

            logger.error(f"Encryption failed: {e}", exc_info=True)
            raise SecurityError(f"Encryption failed: {e}") from e


class DecryptionHandler:
    """Handler for decryption effects using message-based API.

    Requires injected IDecryptionService and ITelemetryService.
    """

    def __init__(
        self,
        decryption_service: IDecryptionService,
        telemetry_service: ITelemetryService,
    ):
        self._decrypter = decryption_service
        self._telemetry = telemetry_service
        logger.info(
            "DecryptionHandler initialized with decryption service and telemetry."
        )

    async def handle_decryption(self, payload: Dict[str, Any]) -> Any:
        """
        Handle a decryption effect.

        Args:
            payload: The effect payload containing decryption parameters.

        Returns:
            A dictionary containing the decrypted data: {"decrypted_data": bytes}.

        Raises:
            SecurityError: If decryption fails or data format is invalid.
        """
        # Payload should contain data structure to decrypt
        encrypted_payload = payload.get("encrypted_data", {})
        channel_id = payload.get("channel_id", "unknown")
        source_id = payload.get("source_id", "unknown")

        # Basic check for expected structure. The service might do more.
        if not isinstance(encrypted_payload, dict):
            self._telemetry.increment_counter(
                "security.decryption.error",
                1,
                {
                    "algorithm": "unknown",
                    "channel_id": channel_id,
                    "source_id": source_id,
                    "error_type": "InvalidFormat",
                },
            )
            raise SecurityError("Invalid encrypted data format: Expected a dictionary.")

        algo = encrypted_payload.get(
            "algorithm", "unknown"
        )  # Algorithm might be within the payload structure
        logger.debug(f"Handling decryption effect using {algo}")

        decrypted_data = None
        try:
            # Delegate decryption to injected service
            decryption_context = {"channel_id": channel_id, "source_id": source_id}
            # Pass the whole structure as the service might need metadata from it
            decrypted_data = await self._decrypter.decrypt(
                encrypted_payload, algo, decryption_context
            )

            # Record decryption in telemetry
            self._telemetry.increment_counter(
                "security.decryption",
                1,
                {
                    "algorithm": algo,
                    "channel_id": channel_id,
                    "source_id": source_id,
                    "status": "success",
                },
            )

            return {"decrypted_data": decrypted_data}

        except Exception as e:
            # Record decryption error in telemetry
            self._telemetry.increment_counter(
                "security.decryption.error",
                1,
                {
                    "algorithm": algo,
                    "channel_id": channel_id,
                    "source_id": source_id,
                    "error_type": type(e).__name__,
                },
            )

            logger.error(f"Decryption failed: {e}", exc_info=True)
            raise SecurityError(f"Decryption failed: {e}") from e
