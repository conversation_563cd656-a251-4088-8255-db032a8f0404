"""
Automatic Differentiation Engine
============================

This package implements the automatic differentiation engine for the
differentiable programming framework.
"""

# Import operations
# Import interfaces
from ..interfaces import IAutoDiffEngine
from ..interfaces import IContextSensitiveAutoDiffEngine
from ..interfaces import IDifferentiable
from ..interfaces import IDifferentiableType
from .operations import add
from .operations import cos
from .operations import divide
from .operations import exp
from .operations import log
from .operations import matmul
from .operations import multiply
from .operations import power
from .operations import relu
from .operations import sigmoid
from .operations import sin
from .operations import softmax
from .operations import subtract
from .operations import tan
from .operations import tanh

# Lazy imports for implementations to avoid circular dependencies
# These will be initialized when first accessed
AutoDiffEngine = None
get_autodiff_engine = None
ComputationGraph = None
ComputationNode = None
NodeType = None
ContextSensitiveAutoDiffEngine = None
get_context_sensitive_autodiff_engine = None

__all__ = [
    # Interfaces
    "IAutoDiffEngine",
    "IContextSensitiveAutoDiffEngine",
    "IDifferentiable",
    "IDifferentiableType",
    # Standard AutoDiff Engine
    "AutoDiffEngine",
    "get_autodiff_engine",
    "ComputationGraph",
    "ComputationNode",
    "NodeType",
    "add",
    "subtract",
    "multiply",
    "divide",
    "power",
    "exp",
    "log",
    "sin",
    "cos",
    "tan",
    "tanh",
    "sigmoid",
    "relu",
    "softmax",
    "matmul",
    # Context-Sensitive AutoDiff Engine
    "ContextSensitiveAutoDiffEngine",
    "get_context_sensitive_autodiff_engine",
]


# Initialize lazy imports
def _initialize_lazy_imports():
    global \
        AutoDiffEngine, \
        get_autodiff_engine, \
        ComputationGraph, \
        ComputationNode, \
        NodeType
    global ContextSensitiveAutoDiffEngine, get_context_sensitive_autodiff_engine

    if AutoDiffEngine is None:
        from person_suit.core.infrastructure.differentiable.autodiff.engine import AutoDiffEngine
        from person_suit.core.infrastructure.differentiable.autodiff.engine import (
            get_autodiff_engine,
        )

    if ComputationGraph is None:
        from person_suit.core.infrastructure.differentiable.autodiff.graph import ComputationGraph
        from person_suit.core.infrastructure.differentiable.autodiff.graph import ComputationNode
        from person_suit.core.infrastructure.differentiable.autodiff.graph import NodeType

    if ContextSensitiveAutoDiffEngine is None:
        from person_suit.core.infrastructure.differentiable.autodiff.context_sensitive_engine import (
            ContextSensitiveAutoDiffEngine,
        )
        from person_suit.core.infrastructure.differentiable.autodiff.context_sensitive_engine import (
            get_context_sensitive_autodiff_engine,
        )
