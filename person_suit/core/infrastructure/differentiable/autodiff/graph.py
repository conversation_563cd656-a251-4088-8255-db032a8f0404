"""
Computation Graph for Automatic Differentiation
=============================================

This module implements the computation graph for automatic differentiation.
The computation graph represents the operations performed on differentiable
objects and is used to compute gradients.

Related Files:
- engine.py: Automatic differentiation engine
- operations.py: Basic operations for the computation graph
- ../core.py: Core types and classes

Dependencies:
- Python 3.8+
- NumPy
"""

import logging
from enum import Enum
from enum import auto
from typing import Any
from typing import Callable
from typing import Dict
from typing import List
from typing import Optional

import numpy as np

# Configure logging
logger = logging.getLogger(__name__)


class NodeType(Enum):
    """Types of nodes in the computation graph."""

    VARIABLE = auto()  # A variable node (leaf)
    CONSTANT = auto()  # A constant node (leaf)
    PARAMETER = auto()  # A parameter node (leaf)
    OPERATION = auto()  # An operation node (internal)


class ComputationNode:
    """
    A node in the computation graph.

    Nodes can be variables, constants, parameters, or operations.
    """

    def __init__(
        self,
        node_id: str,
        node_type: NodeType,
        value: Any = None,
        grad_fn: Optional[Callable] = None,
        requires_grad: bool = True,
    ):
        """
        Initialize a computation node.

        Args:
            node_id: Unique identifier for the node
            node_type: Type of the node
            value: Value of the node
            grad_fn: Function to compute gradients
            requires_grad: Whether gradients should be computed for this node
        """
        self.node_id = node_id
        self.node_type = node_type
        self.value = value
        self.grad_fn = grad_fn
        self.requires_grad = requires_grad
        self.grad = None
        self.parents: List["ComputationNode"] = []
        self.children: List["ComputationNode"] = []

    def add_parent(self, parent: "ComputationNode") -> None:
        """
        Add a parent node.

        Args:
            parent: The parent node
        """
        if parent not in self.parents:
            self.parents.append(parent)

    def add_child(self, child: "ComputationNode") -> None:
        """
        Add a child node.

        Args:
            child: The child node
        """
        if child not in self.children:
            self.children.append(child)

    def __repr__(self) -> str:
        """String representation of the node."""
        return f"ComputationNode(id={self.node_id}, type={self.node_type}, value={self.value})"


class ComputationGraph:
    """
    A computation graph for automatic differentiation.

    The computation graph represents the operations performed on differentiable
    objects and is used to compute gradients.
    """

    def __init__(self):
        """Initialize a computation graph."""
        self.nodes: Dict[str, ComputationNode] = {}
        self.edges: Dict[str, List[str]] = {}  # node_id -> [child_id, ...]
        self.reverse_edges: Dict[str, List[str]] = {}  # node_id -> [parent_id, ...]

    def add_node(self, node: Any) -> None:
        """
        Add a node to the graph.

        Args:
            node: The node to add (can be a ComputationNode or a Differentiable)
        """
        # If the node is a Differentiable, convert it to a ComputationNode
        if not isinstance(node, ComputationNode):
            from person_suit.core.infrastructure.differentiable.core import Differentiable
            from person_suit.core.infrastructure.differentiable.core import DifferentiableType

            if isinstance(node, Differentiable):
                # Map DifferentiableType to NodeType
                node_type_map = {
                    DifferentiableType.VARIABLE: NodeType.VARIABLE,
                    DifferentiableType.CONSTANT: NodeType.CONSTANT,
                    DifferentiableType.PARAMETER: NodeType.PARAMETER,
                    DifferentiableType.OPERATION: NodeType.OPERATION,
                    DifferentiableType.CUSTOM: NodeType.OPERATION,
                }

                node_type = node_type_map.get(node.diff_type, NodeType.OPERATION)

                # Create a ComputationNode
                comp_node = ComputationNode(
                    node_id=node.name,
                    node_type=node_type,
                    value=node.value,
                    grad_fn=node._grad_fn,
                    requires_grad=node.requires_grad,
                )

                # Add the node to the graph
                self.nodes[comp_node.node_id] = comp_node
                self.edges[comp_node.node_id] = []
                self.reverse_edges[comp_node.node_id] = []

                return

        # Add the node to the graph
        self.nodes[node.node_id] = node
        self.edges[node.node_id] = []
        self.reverse_edges[node.node_id] = []

    def add_edge(self, parent: Any, child: Any) -> None:
        """
        Add an edge to the graph.

        Args:
            parent: The parent node
            child: The child node
        """
        # If the nodes are Differentiable, get their names
        if not isinstance(parent, ComputationNode):
            from person_suit.core.infrastructure.differentiable.core import Differentiable

            if isinstance(parent, Differentiable):
                parent_id = parent.name
                child_id = child.name

                # Add the edge
                if parent_id not in self.edges:
                    self.edges[parent_id] = []
                if child_id not in self.reverse_edges:
                    self.reverse_edges[child_id] = []

                self.edges[parent_id].append(child_id)
                self.reverse_edges[child_id].append(parent_id)

                return

        # Add the edge
        parent_id = parent.node_id
        child_id = child.node_id

        if parent_id not in self.edges:
            self.edges[parent_id] = []
        if child_id not in self.reverse_edges:
            self.reverse_edges[child_id] = []

        self.edges[parent_id].append(child_id)
        self.reverse_edges[child_id].append(parent_id)

        # Update parent-child relationships in the nodes
        parent.add_child(child)
        child.add_parent(parent)

    def get_node(self, node_id: str) -> Optional[ComputationNode]:
        """
        Get a node by ID.

        Args:
            node_id: The ID of the node

        Returns:
            The node, or None if not found
        """
        return self.nodes.get(node_id)

    def get_children(self, node_id: str) -> List[ComputationNode]:
        """
        Get the children of a node.

        Args:
            node_id: The ID of the node

        Returns:
            List of child nodes
        """
        child_ids = self.edges.get(node_id, [])
        return [
            self.nodes[child_id] for child_id in child_ids if child_id in self.nodes
        ]

    def get_parents(self, node_id: str) -> List[ComputationNode]:
        """
        Get the parents of a node.

        Args:
            node_id: The ID of the node

        Returns:
            List of parent nodes
        """
        parent_ids = self.reverse_edges.get(node_id, [])
        return [
            self.nodes[parent_id] for parent_id in parent_ids if parent_id in self.nodes
        ]

    def forward(self, node_id: str) -> Any:
        """
        Perform a forward pass starting from a node.

        Args:
            node_id: The ID of the node to start from

        Returns:
            The computed value
        """
        node = self.get_node(node_id)
        if node is None:
            raise ValueError(f"Node not found: {node_id}")

        # If the node is a leaf, return its value
        if node.node_type in (NodeType.VARIABLE, NodeType.CONSTANT, NodeType.PARAMETER):
            return node.value

        # If the node is an operation, compute its value
        if node.grad_fn is None:
            raise ValueError(f"Node has no gradient function: {node_id}")

        # Compute the values of the parents
        parent_values = [self.forward(parent.node_id) for parent in node.parents]

        # Compute the value of this node
        node.value = node.grad_fn(*parent_values)

        return node.value

    def backward(self, node_id: str, grad: Optional[Any] = None) -> None:
        """
        Perform a backward pass starting from a node.

        Args:
            node_id: The ID of the node to start from
            grad: The gradient to start with (None means initialize with ones)
        """
        node = self.get_node(node_id)
        if node is None:
            raise ValueError(f"Node not found: {node_id}")

        # Initialize gradient if not provided
        if grad is None:
            if isinstance(node.value, np.ndarray):
                grad = np.ones_like(node.value)
            elif isinstance(node.value, (int, float)):
                grad = 1.0
            else:
                raise TypeError(
                    f"Cannot initialize gradient for type {type(node.value)}"
                )

        # Accumulate gradient
        if node.grad is None:
            node.grad = grad
        else:
            if isinstance(node.grad, np.ndarray) and isinstance(grad, np.ndarray):
                node.grad += grad
            else:
                node.grad = node.grad + grad

        # If the node is a leaf, we're done
        if node.node_type in (NodeType.VARIABLE, NodeType.CONSTANT, NodeType.PARAMETER):
            return

        # If the node is an operation, propagate gradients to parents
        if node.grad_fn is None:
            raise ValueError(f"Node has no gradient function: {node_id}")

        # Compute gradients for parents
        parent_grads = node.grad_fn(grad)

        # Ensure parent_grads is a list
        if not isinstance(parent_grads, (list, tuple)):
            parent_grads = [parent_grads]

        # Ensure we have the right number of gradients
        if len(parent_grads) != len(node.parents):
            raise ValueError(
                f"Expected {len(node.parents)} gradients, got {len(parent_grads)}"
            )

        # Propagate gradients to parents
        for parent, parent_grad in zip(node.parents, parent_grads):
            if parent.requires_grad:
                self.backward(parent.node_id, parent_grad)

    def __repr__(self) -> str:
        """String representation of the graph."""
        return f"ComputationGraph(nodes={len(self.nodes)}, edges={sum(len(e) for e in self.edges.values())})"
