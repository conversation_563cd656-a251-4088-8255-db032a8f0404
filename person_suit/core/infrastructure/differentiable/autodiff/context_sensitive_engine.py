
# Message-based services initialization
from person_suit.core.infrastructure.message_based_imports import ensure_services_initialized


async def _ensure_message_services():
    """Ensure message-based services are initialized."""
    if not await ensure_services_initialized():
        raise RuntimeError("Failed to initialize message-based services")


"""
Context-Sensitive Automatic Differentiation Engine
================================================

This module implements a context-sensitive automatic differentiation engine
that adapts its behavior based on the current context. It extends the standard
AutoDiffEngine with context-awareness, enabling context-dependent gradient
computation and optimization.

Related Files:
- engine.py: Standard automatic differentiation engine
- graph.py: Computation graph implementation
- operations.py: Basic operations for the computation graph
- ../core.py: Core types and classes
- ../../wave/core.py: Wave-based information representation
- ../../caw/core.py: CAW components

Dependencies:
- Python 3.8+
- NumPy
"""

import asyncio
import logging
from typing import Any
from typing import Callable
from typing import Dict
from typing import List
from typing import Optional
from typing import TypeVar

import numpy as np

# from ...wave.core import ( # Original problematic import
# Context,
# Information,
# WaveFunction,
# )
from ....effects import Computation
from ....effects import effect

# from ...caw.core import CAWProcessor # Commented out - CAWProcessor and its module are missing
from .graph import ComputationGraph


# Placeholder for Information and WaveFunction until their canonical CAW location is clear
class Information:
    def __init__(self, value: Any, wave_function: Optional[Any] = None):
        self.value = value
        self.wave_function = wave_function

class WaveFunction:
    def __init__(self, amplitude_fn: Any, phase_fn: Any):
        self.amplitude_fn = amplitude_fn
        self.phase_fn = phase_fn

from person_suit.shared.utils.patterns import AsyncSingleton
from person_suit.shared.utils.telemetry import telemetry

# Lazy import to avoid circular imports
AutoDiffEngine = None
from ..core import Differentiable
from ..core import GradientTape

# Configure logging
logger = logging.getLogger(__name__)

# Type variables for generic types
T = TypeVar("T")
R = TypeVar("R")


class EnvironmentSensitiveAutoDiffEngine(AsyncSingleton, IEnvironmentSensitiveAutoDiffEngine):
    """
    Environment-sensitive engine for automatic differentiation.

    This class extends the standard AutoDiffEngine with environment-awareness,
    enabling environment-dependent gradient computation and optimization.
    """

    async def initialize(self, config: Optional[Dict[str, Any]] = None) -> None:
        """
        Initialize the context-sensitive automatic differentiation engine.

        Args:
            config: Optional configuration for the engine
        """
        self.config = config or {}
        self.logger = logger

        # Lazily import and initialize standard AutoDiff engine
        from person_suit.core.infrastructure.differentiable.autodiff.engine import AutoDiffEngine

        self.standard_engine = await AutoDiffEngine.get_instance()

        # Initialize context-specific computation graphs
        self._context_graphs: Dict[str, ComputationGraph] = {}

        # Initialize context-specific gradient tapes
        self._context_tapes: Dict[str, List[GradientTape]] = {}

        # Initialize CAW processor for context-sensitive operations
        # self.caw_processor = CAWProcessor() # Commented out
        self.caw_processor = None # Placeholder

        # Initialize wave functions for different operations
        self._initialize_wave_functions()

        # Initialize locks
        self._graph_lock = asyncio.Lock()

        # Flag for initialization
        self.is_initialized = True
        self.logger.info("Environment-Sensitive AutoDiff Engine initialized")

    def _initialize_wave_functions(self) -> None:
        """Initialize wave functions for different operations."""
        # Wave function for gradient computation
        WaveFunction(
            # Amplitude based on context priority
            lambda x, c: (
                1.0 if c.priority == "high" else PRIO_HIGH if c.priority == "normal" else PRIO_LOW
            ),
            # Phase based on context domain
            lambda x, c: 0.0 if c.domain == "optimization" else np.pi / 4,
        )
        # if self.caw_processor: # Guard access
        #     self.caw_processor.register_wave_function("gradient", gradient_wave) # Commented out

        # Wave function for operation creation
        WaveFunction(
            # Amplitude based on context constraints
            lambda x, c: 1.0 if "high_precision" in c.constraints else 0.8,
            # Phase based on context domain
            lambda x, c: 0.0 if c.domain == "computation" else np.pi / 4,
        )
        # if self.caw_processor: # Guard access
        #     self.caw_processor.register_wave_function("operation", operation_wave) # Commented out

    def _get_context_key(self, context: Context) -> str:
        """
        Get a key for the context.

        Args:
            context: The context

        Returns:
            A string key for the context
        """
        return f"{context.domain}_{context.priority}"

    def _get_or_create_context_graph(self, context: Context) -> ComputationGraph:
        """
        Get or create a computation graph for the context.

        Args:
            context: The context

        Returns:
            A computation graph for the context
        """
        context_key = self._get_context_key(context)
        if context_key not in self._context_graphs:
            self._context_graphs[context_key] = ComputationGraph()
        return self._context_graphs[context_key]

    def _get_context_tapes(self, context: Context) -> List[GradientTape]:
        """
        Get gradient tapes for the context.

        Args:
            context: The context

        Returns:
            A list of gradient tapes for the context
        """
        context_key = self._get_context_key(context)
        if context_key not in self._context_tapes:
            self._context_tapes[context_key] = []
        return self._context_tapes[context_key]

    @effect([Computation])
    @telemetry
    async def create_operation_in_context(
        self,
        op_type: str,
        inputs: List[Differentiable],
        compute_fn: Callable[..., Any],
        grad_fn: Callable[[Any], List[Any]],
        context: Context,
        name: Optional[str] = None,
        requires_grad: bool = True,
    ) -> Differentiable:
        """
        Create an operation in the computation graph for a specific context.

        Args:
            op_type: The type of operation
            inputs: The input differentiable objects
            compute_fn: Function to compute the output value
            grad_fn: Function to compute gradients with respect to inputs
            context: The context in which to create the operation
            name: Optional name for the operation
            requires_grad: Whether gradients should be computed for this operation

        Returns:
            Differentiable: The output of the operation
        """
        if not hasattr(self, "is_initialized") or not self.is_initialized:
            await self.initialize()

        # Get the computation graph for the context
        graph = self._get_or_create_context_graph(context)

        # Create information with the operation wave function
        operation_info = Information(
            {
                "op_type": op_type,
                "inputs": inputs,
                "compute_fn": compute_fn,
                "grad_fn": grad_fn,
                "name": name,
                "requires_grad": requires_grad,
            },
            # self.caw_processor.wave_functions["operation"], # Commented out
            None, # Placeholder for wave_function
        )

        # Process the operation information in the context
        # processed_info = self.caw_processor.process(operation_info, context) # Commented out
        # For now, assume no processing if caw_processor is None
        processed_values = operation_info.value

        # Extract processed values
        # processed_values = processed_info.value # Original line, moved up as part of commenting out caw_processor logic
        op_type = processed_values["op_type"]
        inputs = processed_values["inputs"]
        compute_fn = processed_values["compute_fn"]
        grad_fn = processed_values["grad_fn"]
        name = processed_values["name"]
        requires_grad = processed_values["requires_grad"]

        # Create the operation using the standard engine
        async with self._graph_lock:
            result = await self.standard_engine.create_operation(
                op_type=op_type,
                inputs=inputs,
                compute_fn=compute_fn,
                grad_fn=grad_fn,
                name=name,
                requires_grad=requires_grad,
            )

            # Add the operation to the context-specific graph
            graph.add_node(result)

            # Add the operation to active gradient tapes in this context
            for tape in self._get_context_tapes(context):
                if tape.is_recording:
                    tape.record_operation(result, inputs)

        return result

    @effect([Computation])
    @telemetry
    async def compute_gradients_in_context(
        self, target: Differentiable, sources: List[Differentiable], context: Context
    ) -> List[Optional[np.ndarray]]:
        """
        Compute gradients of a target with respect to sources in a specific context.

        Args:
            target: The target differentiable object
            sources: The source differentiable objects
            context: The context in which to compute gradients

        Returns:
            List of gradients, one for each source
        """
        if not hasattr(self, "is_initialized") or not self.is_initialized:
            await self.initialize()

        # Create information with the gradient wave function
        gradient_info = Information(
            {"target": target, "sources": sources},
            self.caw_processor.wave_functions["gradient"],
        )

        # Process the gradient information in the context
        processed_info = self.caw_processor.process(gradient_info, context)

        # Extract processed values
        processed_values = processed_info.value
        target = processed_values["target"]
        sources = processed_values["sources"]

        # Compute gradients using the standard engine
        return await self.standard_engine.compute_gradients(target, sources)

    @effect([Computation])
    @telemetry
    async def create_gradient_tape_in_context(
        self, context: Context, persistent: bool = False
    ) -> GradientTape:
        """
        Create a gradient tape in a specific context.

        Args:
            context: The context in which to create the gradient tape
            persistent: Whether the tape should be persistent

        Returns:
            A gradient tape
        """
        if not hasattr(self, "is_initialized") or not self.is_initialized:
            await self.initialize()

        # Create a gradient tape
        tape = GradientTape(persistent=persistent)

        # Add the tape to the context-specific tapes
        context_tapes = self._get_context_tapes(context)
        context_tapes.append(tape)

        return tape

    @effect([Computation])
    @telemetry
    async def remove_gradient_tape_in_context(
        self, tape: GradientTape, context: Context
    ) -> None:
        """
        Remove a gradient tape from a specific context.

        Args:
            tape: The gradient tape to remove
            context: The context from which to remove the tape
        """
        if not hasattr(self, "is_initialized") or not self.is_initialized:
            await self.initialize()

        # Remove the tape from the context-specific tapes
        context_tapes = self._get_context_tapes(context)
        if tape in context_tapes:
            context_tapes.remove(tape)


# Singleton instance
_instance = None


async def get_environment_sensitive_autodiff_engine() -> EnvironmentSensitiveAutoDiffEngine:
    """
    Get the singleton instance of the environment-sensitive automatic differentiation engine.

    Returns:
        The environment-sensitive automatic differentiation engine
    """
    global _instance
    if _instance is None:
        _instance = await EnvironmentSensitiveAutoDiffEngine.get_instance()
    return _instance
