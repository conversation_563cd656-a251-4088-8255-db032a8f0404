"""
Automatic Differentiation Engine
===============================

This module implements the automatic differentiation engine for the differentiable
programming framework. It provides the core functionality for computing gradients
of arbitrary computations.

Related Files:
- graph.py: Computation graph implementation
- operations.py: Basic operations for the computation graph
- ../core.py: Core types and classes

Dependencies:
- Python 3.8+
- NumPy
"""

import asyncio
import logging
from typing import Any
from typing import Callable
from typing import Dict
from typing import List
from typing import Optional
from typing import Tuple
from typing import Union

from person_suit.shared.utils.patterns import AsyncSingleton
from person_suit.shared.utils.telemetry import telemetry

from ....effects import effect
from ....effects.effect_types import Computation
from ....effects.effect_types import StateRead
from ..core import Constant
from ..core import Differentiable
from ..core import GradientTape
from ..core import Parameter
from ..core import Variable
from ..interfaces import IAutoDiffEngine
from .graph import ComputationGraph

# Configure logging
logger = logging.getLogger(__name__)


class AutoDiffEngine(AsyncSingleton, IAutoDiffEngine):
    """
    Engine for automatic differentiation.

    This class provides the core functionality for computing gradients
    of arbitrary computations using automatic differentiation.
    """

    async def initialize(self, config: Optional[Dict[str, Any]] = None) -> None:
        """
        Initialize the automatic differentiation engine.

        Args:
            config: Optional configuration for the engine
        """
        self.config = config or {}
        self.logger = logger

        # Initialize computation graph
        self._graph = ComputationGraph()

        # Initialize active gradient tapes
        self._active_tapes: List[GradientTape] = []

        # Initialize locks
        self._graph_lock = asyncio.Lock()

        # Flag for initialization
        self.is_initialized = True
        self.logger.info("AutoDiff Engine initialized")

    @effect([Computation])
    @telemetry
    async def create_variable(
        self, value: Any, name: Optional[str] = None, requires_grad: bool = True
    ) -> Variable:
        """
        Create a variable in the computation graph.

        Args:
            value: The value of the variable
            name: Optional name for the variable
            requires_grad: Whether gradients should be computed for this variable

        Returns:
            Variable: The created variable
        """
        if not hasattr(self, "is_initialized") or not self.is_initialized:
            await self.initialize()

        async with self._graph_lock:
            variable = Variable(value, name=name, requires_grad=requires_grad)
            self._graph.add_node(variable)

            # Add to active gradient tapes
            for tape in self._active_tapes:
                tape.watch(variable)

            return variable

    @effect([Computation])
    @telemetry
    async def create_constant(self, value: Any, name: Optional[str] = None) -> Constant:
        """
        Create a constant in the computation graph.

        Args:
            value: The value of the constant
            name: Optional name for the constant

        Returns:
            Constant: The created constant
        """
        if not hasattr(self, "is_initialized") or not self.is_initialized:
            await self.initialize()

        async with self._graph_lock:
            constant = Constant(value, name=name)
            self._graph.add_node(constant)
            return constant

    @effect([Computation])
    @telemetry
    async def create_parameter(
        self,
        value: Any,
        name: Optional[str] = None,
        requires_grad: bool = True,
        bounds: Optional[Tuple[Optional[float], Optional[float]]] = None,
    ) -> Parameter:
        """
        Create a parameter in the computation graph.

        Args:
            value: The value of the parameter
            name: Optional name for the parameter
            requires_grad: Whether gradients should be computed for this parameter
            bounds: Optional bounds for the parameter (min, max)

        Returns:
            Parameter: The created parameter
        """
        if not hasattr(self, "is_initialized") or not self.is_initialized:
            await self.initialize()

        async with self._graph_lock:
            parameter = Parameter(
                value, name=name, requires_grad=requires_grad, bounds=bounds
            )
            self._graph.add_node(parameter)

            # Add to active gradient tapes
            for tape in self._active_tapes:
                tape.watch(parameter)

            return parameter

    @effect([Computation])
    @telemetry
    async def create_operation(
        self,
        op_type: str,
        inputs: List[Differentiable],
        compute_fn: Callable[..., Any],
        grad_fn: Callable[[Any], List[Any]],
        name: Optional[str] = None,
        requires_grad: bool = True,
    ) -> Differentiable:
        """
        Create an operation in the computation graph.

        Args:
            op_type: The type of operation
            inputs: The input differentiable objects
            compute_fn: Function to compute the output value
            grad_fn: Function to compute gradients with respect to inputs
            name: Optional name for the operation
            requires_grad: Whether gradients should be computed for this operation

        Returns:
            Differentiable: The output of the operation
        """
        if not hasattr(self, "is_initialized") or not self.is_initialized:
            await self.initialize()

        async with self._graph_lock:
            # Create output node
            from person_suit.core.infrastructure.differentiable.core import Differentiable
            from person_suit.core.infrastructure.differentiable.core import DifferentiableType

            output = Differentiable(
                name=name,
                diff_type=DifferentiableType.OPERATION,
                requires_grad=requires_grad,
            )

            # Compute output value
            input_values = [input_node.value for input_node in inputs]
            output.value = compute_fn(*input_values)

            # Set up gradient function
            output._grad_fn = grad_fn

            # Set up parent-child relationships
            output._parents = inputs
            for input_node in inputs:
                input_node._children.append(output)

            # Add to computation graph
            self._graph.add_node(output)
            for input_node in inputs:
                self._graph.add_edge(input_node, output)

            # Add to active gradient tapes
            for tape in self._active_tapes:
                tape.operations.append(output)

            return output

    @effect([Computation])
    @telemetry
    async def gradient(
        self,
        outputs: Union[Differentiable, List[Differentiable]],
        inputs: Union[Differentiable, List[Differentiable]],
    ) -> Union[Any, List[Any]]:
        """
        Compute gradients of outputs with respect to inputs.

        Args:
            outputs: The output differentiable objects
            inputs: The input differentiable objects

        Returns:
            Gradients of outputs with respect to inputs
        """
        if not hasattr(self, "is_initialized") or not self.is_initialized:
            await self.initialize()

        # Convert to lists if not already
        if not isinstance(outputs, list):
            outputs = [outputs]
        if not isinstance(inputs, list):
            inputs = [inputs]

        # Create a gradient tape
        tape = GradientTape()

        # Watch inputs
        for input_node in inputs:
            tape.watch(input_node)

        # Compute gradients for each output
        gradients = []
        for output in outputs:
            # Zero out gradients
            for input_node in inputs:
                input_node.zero_grad()

            # Compute gradients
            output.backward()

            # Collect gradients
            output_gradients = [input_node.grad for input_node in inputs]
            gradients.append(output_gradients)

        # If there's only one output, return a flat list of gradients
        if len(outputs) == 1:
            return gradients[0]

        return gradients

    @effect([Computation])
    @telemetry
    async def value_and_grad(
        self, func: Callable[..., Differentiable], *args, **kwargs
    ) -> Tuple[Any, List[Any]]:
        """
        Compute both the value and gradient of a function.

        Args:
            func: Function that takes differentiable inputs and returns a differentiable output
            *args: Arguments to the function
            **kwargs: Keyword arguments to the function

        Returns:
            Tuple of (value, gradients)
        """
        if not hasattr(self, "is_initialized") or not self.is_initialized:
            await self.initialize()

        # Create a gradient tape
        tape = GradientTape()

        # Watch all differentiable arguments
        diff_args = []
        for arg in args:
            if isinstance(arg, Differentiable):
                tape.watch(arg)
                diff_args.append(arg)

        for _, arg in kwargs.items():
            if isinstance(arg, Differentiable):
                tape.watch(arg)
                diff_args.append(arg)

        # Call the function
        output = func(*args, **kwargs)

        # Compute gradients
        grads = tape.gradient(output, diff_args)

        return output.value, grads

    @effect([StateRead])
    @telemetry
    async def get_graph(self) -> ComputationGraph:
        """
        Returns the current computation graph.

        Returns:
            ComputationGraph: The computation graph
        """
        if not hasattr(self, "is_initialized") or not self.is_initialized:
            await self.initialize()

        return self._graph

    def gradient_tape(self, persistent: bool = False) -> GradientTape:
        """
        Create a gradient tape for recording operations.

        Args:
            persistent: Whether the tape should be persistent (can compute multiple gradients)

        Returns:
            GradientTape: The created gradient tape
        """
        tape = GradientTape(persistent=persistent)
        self._active_tapes.append(tape)
        return tape

    def stop_recording(self, tape: GradientTape) -> None:
        """
        Stop recording operations on a gradient tape.

        Args:
            tape: The gradient tape to stop recording on
        """
        if tape in self._active_tapes:
            self._active_tapes.remove(tape)


# Convenience function to get the autodiff engine
async def get_autodiff_engine() -> AutoDiffEngine:
    """
    Get the singleton instance of the autodiff engine.

    Returns:
        AutoDiffEngine: The autodiff engine instance
    """
    return await AutoDiffEngine.get_instance()
