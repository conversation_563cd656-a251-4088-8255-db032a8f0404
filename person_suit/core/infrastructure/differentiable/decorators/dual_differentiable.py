"""
Dual Wave Differentiable Decorator
===============================

This module implements the @dual_differentiable decorator, which makes functions
differentiable using the dual wave-particle representation. It enables
context-sensitive differentiation and gradient-based optimization using the
dual representation.

Key features:
- Context-sensitive differentiation using dual contexts
- Wave function-based gradient scaling
- Integration with the CAW paradigm
- Support for both synchronous and asynchronous functions

Related Files:
- differentiable.py: Standard differentiable decorator
- contextual_differentiable.py: Context-aware differentiable decorator
- ../integration/dual_wave_bridge.py: Bridge between differentiable and dual_wave
- ../../dual_wave/core.py: Dual wave-particle representation
- ../../dual_wave/differentiable_core.py: Differentiable dual wave components

Dependencies:
- Python 3.8+
- NumPy
"""

import asyncio
import functools
import inspect
import logging
from typing import Any
from typing import Callable
from typing import List
from typing import Optional
from typing import Tuple
from typing import TypeVar

from ...dual_wave.core import DualWaveFunction
from ...dual_wave.core import UnifiedContext
from ...effects.core import EffectType
from ...effects.core import effects
from ..integration.dual_wave_bridge import ContextSensitiveGradientTape
from ..integration.dual_wave_bridge import DualWaveDifferentiable
from ..integration.dual_wave_bridge import DualWaveVariable
from ..integration.dual_wave_bridge import convert_to_dual_wave

# Configure logging
logger = logging.getLogger(__name__)

# Type variables for generic functions
T = TypeVar("T")
R = TypeVar("R")


class DualDifferentiableResult:
    """
    Result of a dual differentiable function.

    This class wraps the result of a dual differentiable function,
    providing access to both the value and the gradients.
    """

    def __init__(
        self,
        value: Any,
        gradients: Optional[List[Any]] = None,
        context: Optional[UnifiedContext] = None,
        wave_function: Optional[DualWaveFunction] = None,
    ):
        """
        Initialize a dual differentiable result.

        Args:
            value: The result value
            gradients: Optional gradients
            context: Optional context
            wave_function: Optional wave function
        """
        self.value = value
        self.gradients = gradients or []
        self.context = context
        self.wave_function = wave_function

    def __repr__(self) -> str:
        """Get a string representation of the result."""
        return f"DualDifferentiableResult(value={self.value}, gradients={len(self.gradients)}, context={self.context})"


def dual_differentiable(
    context_param: str = "context", wave_function_param: Optional[str] = None
):
    """
    Decorator that makes a function differentiable using the dual wave-particle representation.

    Args:
        context_param: The name of the context parameter
        wave_function_param: Optional name of the wave function parameter

    Returns:
        A decorator that makes a function differentiable using the dual representation
    """

    def decorator(func: Callable[..., Any]) -> Callable[..., Any]:
        """
        Decorator that makes a function differentiable using the dual representation.

        Args:
            func: The function to make differentiable

        Returns:
            A differentiable version of the function
        """
        sig = inspect.signature(func)

        # Handle async functions
        if asyncio.iscoroutinefunction(func):

            @functools.wraps(func)
            @effects([EffectType.COMPUTATION])
            async def async_wrapper(*args, **kwargs):
                # Get the context from the arguments
                context = kwargs.get(context_param)
                if context is None:
                    # Try to get the context from positional arguments
                    param_names = list(sig.parameters.keys())
                    if context_param in param_names:
                        context_index = param_names.index(context_param)
                        if context_index < len(args):
                            context = args[context_index]

                # Get the wave function from the arguments
                wave_function = None
                if wave_function_param is not None:
                    wave_function = kwargs.get(wave_function_param)
                    if wave_function is None and wave_function_param in sig.parameters:
                        param_names = list(sig.parameters.keys())
                        wave_function_index = param_names.index(wave_function_param)
                        if wave_function_index < len(args):
                            wave_function = args[wave_function_index]

                # Create a gradient tape
                tape = ContextSensitiveGradientTape(context=context)

                # Convert inputs to dual wave differentiable objects
                diff_args = []
                for i, arg in enumerate(args):
                    if isinstance(arg, DualWaveDifferentiable):
                        diff_args.append(arg)
                        tape.watch(arg)
                    elif isinstance(arg, Differentiable):
                        dual_arg = convert_to_dual_wave(arg, context)
                        diff_args.append(dual_arg)
                        tape.watch(dual_arg)
                    else:
                        # Create a variable for non-differentiable inputs
                        param_name = (
                            list(sig.parameters.keys())[i]
                            if i < len(sig.parameters)
                            else f"arg_{i}"
                        )
                        var = DualWaveVariable(
                            value=arg,
                            context=context,
                            wave_function=wave_function,
                            name=param_name,
                        )
                        tape.watch(var)
                        diff_args.append(var)

                diff_kwargs = {}
                for key, arg in kwargs.items():
                    if key == context_param or (
                        wave_function_param is not None and key == wave_function_param
                    ):
                        diff_kwargs[key] = arg
                        continue

                    if isinstance(arg, DualWaveDifferentiable):
                        diff_kwargs[key] = arg
                        tape.watch(arg)
                    elif isinstance(arg, Differentiable):
                        dual_arg = convert_to_dual_wave(arg, context)
                        diff_kwargs[key] = dual_arg
                        tape.watch(dual_arg)
                    else:
                        # Create a variable for non-differentiable inputs
                        var = DualWaveVariable(
                            value=arg,
                            context=context,
                            wave_function=wave_function,
                            name=key,
                        )
                        tape.watch(var)
                        diff_kwargs[key] = var

                # Call the function with differentiable inputs
                result = await func(*diff_args, **diff_kwargs)

                # Compute gradients
                if isinstance(result, DualWaveDifferentiable):
                    sources = [
                        arg for arg in diff_args if isinstance(arg, Differentiable)
                    ]
                    sources.extend(
                        [
                            arg
                            for arg in diff_kwargs.values()
                            if isinstance(arg, Differentiable)
                            and key != context_param
                            and (
                                wave_function_param is None
                                or key != wave_function_param
                            )
                        ]
                    )
                    grads = tape.gradient(result, sources)
                    return DualDifferentiableResult(
                        value=result.value,
                        gradients=grads,
                        context=context,
                        wave_function=(
                            result.wave_function
                            if hasattr(result, "wave_function")
                            else None
                        ),
                    )
                elif isinstance(result, Differentiable):
                    dual_result = convert_to_dual_wave(result, context)
                    sources = [
                        arg for arg in diff_args if isinstance(arg, Differentiable)
                    ]
                    sources.extend(
                        [
                            arg
                            for arg in diff_kwargs.values()
                            if isinstance(arg, Differentiable)
                            and key != context_param
                            and (
                                wave_function_param is None
                                or key != wave_function_param
                            )
                        ]
                    )
                    grads = tape.gradient(dual_result, sources)
                    return DualDifferentiableResult(
                        value=dual_result.value,
                        gradients=grads,
                        context=context,
                        wave_function=(
                            dual_result.wave_function
                            if hasattr(dual_result, "wave_function")
                            else None
                        ),
                    )
                else:
                    return result

            return async_wrapper
        else:

            @functools.wraps(func)
            @effects([EffectType.COMPUTATION])
            def wrapper(*args, **kwargs):
                # Get the context from the arguments
                context = kwargs.get(context_param)
                if context is None:
                    # Try to get the context from positional arguments
                    param_names = list(sig.parameters.keys())
                    if context_param in param_names:
                        context_index = param_names.index(context_param)
                        if context_index < len(args):
                            context = args[context_index]

                # Get the wave function from the arguments
                wave_function = None
                if wave_function_param is not None:
                    wave_function = kwargs.get(wave_function_param)
                    if wave_function is None and wave_function_param in sig.parameters:
                        param_names = list(sig.parameters.keys())
                        wave_function_index = param_names.index(wave_function_param)
                        if wave_function_index < len(args):
                            wave_function = args[wave_function_index]

                # Create a gradient tape
                tape = ContextSensitiveGradientTape(context=context)

                # Convert inputs to dual wave differentiable objects
                diff_args = []
                for i, arg in enumerate(args):
                    if isinstance(arg, DualWaveDifferentiable):
                        diff_args.append(arg)
                        tape.watch(arg)
                    elif isinstance(arg, Differentiable):
                        dual_arg = convert_to_dual_wave(arg, context)
                        diff_args.append(dual_arg)
                        tape.watch(dual_arg)
                    else:
                        # Create a variable for non-differentiable inputs
                        param_name = (
                            list(sig.parameters.keys())[i]
                            if i < len(sig.parameters)
                            else f"arg_{i}"
                        )
                        var = DualWaveVariable(
                            value=arg,
                            context=context,
                            wave_function=wave_function,
                            name=param_name,
                        )
                        tape.watch(var)
                        diff_args.append(var)

                diff_kwargs = {}
                for key, arg in kwargs.items():
                    if key == context_param or (
                        wave_function_param is not None and key == wave_function_param
                    ):
                        diff_kwargs[key] = arg
                        continue

                    if isinstance(arg, DualWaveDifferentiable):
                        diff_kwargs[key] = arg
                        tape.watch(arg)
                    elif isinstance(arg, Differentiable):
                        dual_arg = convert_to_dual_wave(arg, context)
                        diff_kwargs[key] = dual_arg
                        tape.watch(dual_arg)
                    else:
                        # Create a variable for non-differentiable inputs
                        var = DualWaveVariable(
                            value=arg,
                            context=context,
                            wave_function=wave_function,
                            name=key,
                        )
                        tape.watch(var)
                        diff_kwargs[key] = var

                # Call the function with differentiable inputs
                result = func(*diff_args, **diff_kwargs)

                # Compute gradients
                if isinstance(result, DualWaveDifferentiable):
                    sources = [
                        arg for arg in diff_args if isinstance(arg, Differentiable)
                    ]
                    sources.extend(
                        [
                            arg
                            for arg in diff_kwargs.values()
                            if isinstance(arg, Differentiable)
                            and key != context_param
                            and (
                                wave_function_param is None
                                or key != wave_function_param
                            )
                        ]
                    )
                    grads = tape.gradient(result, sources)
                    return DualDifferentiableResult(
                        value=result.value,
                        gradients=grads,
                        context=context,
                        wave_function=(
                            result.wave_function
                            if hasattr(result, "wave_function")
                            else None
                        ),
                    )
                elif isinstance(result, Differentiable):
                    dual_result = convert_to_dual_wave(result, context)
                    sources = [
                        arg for arg in diff_args if isinstance(arg, Differentiable)
                    ]
                    sources.extend(
                        [
                            arg
                            for arg in diff_kwargs.values()
                            if isinstance(arg, Differentiable)
                            and key != context_param
                            and (
                                wave_function_param is None
                                or key != wave_function_param
                            )
                        ]
                    )
                    grads = tape.gradient(dual_result, sources)
                    return DualDifferentiableResult(
                        value=dual_result.value,
                        gradients=grads,
                        context=context,
                        wave_function=(
                            dual_result.wave_function
                            if hasattr(dual_result, "wave_function")
                            else None
                        ),
                    )
                else:
                    return result

            return wrapper

    return decorator


def dual_value_and_grad(
    context_param: str = "context", wave_function_param: Optional[str] = None
):
    """
    Decorator that computes both the value and gradient of a function using the dual representation.

    Args:
        context_param: The name of the context parameter
        wave_function_param: Optional name of the wave function parameter

    Returns:
        A decorator that computes both the value and gradient of a function
    """

    def decorator(func: Callable[..., Any]) -> Callable[..., Tuple[Any, List[Any]]]:
        """
        Decorator that computes both the value and gradient of a function.

        Args:
            func: The function to compute value and gradient for

        Returns:
            A function that returns both the value and gradient
        """
        # Apply the dual_differentiable decorator
        diff_func = dual_differentiable(context_param, wave_function_param)(func)

        # Handle async functions
        if asyncio.iscoroutinefunction(diff_func):

            @functools.wraps(func)
            @effects([EffectType.COMPUTATION])
            async def async_wrapper(*args, **kwargs):
                result = await diff_func(*args, **kwargs)
                if isinstance(result, DualDifferentiableResult):
                    return result.value, result.gradients
                else:
                    return result, []

            return async_wrapper
        else:

            @functools.wraps(func)
            @effects([EffectType.COMPUTATION])
            def wrapper(*args, **kwargs):
                result = diff_func(*args, **kwargs)
                if isinstance(result, DualDifferentiableResult):
                    return result.value, result.gradients
                else:
                    return result, []

            return wrapper

    return decorator


def dual_grad(
    context_param: str = "context", wave_function_param: Optional[str] = None
):
    """
    Decorator that computes the gradient of a function using the dual representation.

    Args:
        context_param: The name of the context parameter
        wave_function_param: Optional name of the wave function parameter

    Returns:
        A decorator that computes the gradient of a function
    """

    def decorator(func: Callable[..., Any]) -> Callable[..., List[Any]]:
        """
        Decorator that computes the gradient of a function.

        Args:
            func: The function to compute gradient for

        Returns:
            A function that returns the gradient
        """
        # Apply the dual_differentiable decorator
        diff_func = dual_differentiable(context_param, wave_function_param)(func)

        # Handle async functions
        if asyncio.iscoroutinefunction(diff_func):

            @functools.wraps(func)
            @effects([EffectType.COMPUTATION])
            async def async_wrapper(*args, **kwargs):
                result = await diff_func(*args, **kwargs)
                if isinstance(result, DualDifferentiableResult):
                    return result.gradients
                else:
                    return []

            return async_wrapper
        else:

            @functools.wraps(func)
            @effects([EffectType.COMPUTATION])
            def wrapper(*args, **kwargs):
                result = diff_func(*args, **kwargs)
                if isinstance(result, DualDifferentiableResult):
                    return result.gradients
                else:
                    return []

            return wrapper

    return decorator
