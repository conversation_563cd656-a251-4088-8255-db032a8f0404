"""
Differentiable Decorator
======================

This module implements the @differentiable decorator, which makes functions
differentiable by automatically tracking operations and computing gradients.

Related Files:
- ../autodiff/engine.py: Automatic differentiation engine
- ../autodiff/graph.py: Computation graph implementation
- ../core.py: Core types and classes

Dependencies:
- Python 3.8+
- NumPy
"""

import asyncio
import functools
import logging
from typing import Any
from typing import Callable
from typing import List
from typing import Optional
from typing import Tuple
from typing import TypeVar

from ..core import Differentiable
from ..core import GradientTape
from ..core import Variable

# Configure logging
logger = logging.getLogger(__name__)

# Type variables for generic types
T = TypeVar("T")
V = TypeVar("V")


class DifferentiableResult:
    """
    Represents the result of a differentiable function.

    This class wraps the result of a differentiable function and provides
    methods for computing gradients.
    """

    def __init__(
        self,
        value: Any,
        graph: Optional["ComputationGraph"] = None,
        inputs: Optional[List[Differentiable]] = None,
    ):
        """
        Initialize a differentiable result.

        Args:
            value: The result value
            graph: The computation graph
            inputs: The input differentiable objects
        """
        self.value = value
        self.graph = graph
        self.inputs = inputs or []

    def gradient(self, inputs: Optional[List[Differentiable]] = None) -> List[Any]:
        """
        Compute the gradient of the result with respect to inputs.

        Args:
            inputs: The input differentiable objects (defaults to all inputs)

        Returns:
            List of gradients, one for each input
        """
        if self.graph is None:
            raise ValueError("No computation graph available")

        inputs = inputs or self.inputs

        # Create a gradient tape
        tape = GradientTape()

        # Watch inputs
        for input_node in inputs:
            tape.watch(input_node)

        # Zero out gradients
        for input_node in inputs:
            input_node.zero_grad()

        # Compute gradients
        if isinstance(self.value, Differentiable):
            self.value.backward()
        else:
            raise ValueError("Result is not differentiable")

        # Return gradients
        return [input_node.grad for input_node in inputs]

    def __repr__(self) -> str:
        """String representation of the result."""
        return f"DifferentiableResult(value={self.value}, inputs={len(self.inputs) if self.inputs else 0})"


class DifferentiableContext:
    """
    Context manager for differentiable computations.

    This context manager creates a computation graph and tracks operations
    performed within its scope.
    """

    def __init__(self, persistent: bool = False):
        """
        Initialize a differentiable context.

        Args:
            persistent: Whether the context should be persistent (can compute multiple gradients)
        """
        self.tape = GradientTape(persistent=persistent)
        self.watched_variables = set()

    def __enter__(self) -> "DifferentiableContext":
        """Enter the context."""
        return self

    def __exit__(self, exc_type, exc_val, exc_tb) -> None:
        """Exit the context."""
        pass

    def watch(self, variable: Differentiable) -> None:
        """
        Watch a variable for gradient computation.

        Args:
            variable: The variable to watch
        """
        self.tape.watch(variable)
        self.watched_variables.add(variable)

    def gradient(
        self, target: Differentiable, sources: List[Differentiable]
    ) -> List[Optional[Any]]:
        """
        Compute the gradient of target with respect to sources.

        Args:
            target: The target differentiable object
            sources: The source differentiable objects

        Returns:
            List of gradients, one for each source
        """
        return self.tape.gradient(target, sources)


def differentiable(func: Callable[..., Any]) -> Callable[..., DifferentiableResult]:
    """
    Decorator to make a function differentiable.

    This decorator wraps a function to make it differentiable, automatically
    tracking operations and computing gradients.

    Args:
        func: The function to make differentiable

    Returns:
        A differentiable version of the function
    """

    @functools.wraps(func)
    def wrapper(*args, **kwargs):
        # Create a computation graph
        from person_suit.core.infrastructure.differentiable.autodiff.graph import ComputationGraph

        graph = ComputationGraph()

        # Convert inputs to differentiable variables
        diff_args = []
        for i, arg in enumerate(args):
            if isinstance(arg, Differentiable):
                diff_args.append(arg)
            else:
                # Create a variable for non-differentiable inputs
                var_name = f"arg_{i}"
                var = Variable(arg, name=var_name)
                graph.add_node(var)
                diff_args.append(var)

        diff_kwargs = {}
        for key, arg in kwargs.items():
            if isinstance(arg, Differentiable):
                diff_kwargs[key] = arg
            else:
                # Create a variable for non-differentiable inputs
                var_name = f"kwarg_{key}"
                var = Variable(arg, name=var_name)
                graph.add_node(var)
                diff_kwargs[key] = var

        # Call the function with differentiable inputs
        result = func(*diff_args, **diff_kwargs)

        # If result is already a DifferentiableResult, return it
        if isinstance(result, DifferentiableResult):
            return result

        # If result is a Differentiable, wrap it in a DifferentiableResult
        if isinstance(result, Differentiable):
            return DifferentiableResult(
                value=result, graph=graph, inputs=diff_args + list(diff_kwargs.values())
            )

        # Otherwise, create a variable for the result and wrap it
        result_var = Variable(result, name="result")
        graph.add_node(result_var)

        return DifferentiableResult(
            value=result_var, graph=graph, inputs=diff_args + list(diff_kwargs.values())
        )

    # Handle async functions
    if asyncio.iscoroutinefunction(func):

        @functools.wraps(func)
        async def async_wrapper(*args, **kwargs):
            # Create a computation graph
            from person_suit.core.infrastructure.differentiable.autodiff.graph import (
                ComputationGraph,
            )

            graph = ComputationGraph()

            # Convert inputs to differentiable variables
            diff_args = []
            for i, arg in enumerate(args):
                if isinstance(arg, Differentiable):
                    diff_args.append(arg)
                else:
                    # Create a variable for non-differentiable inputs
                    var_name = f"arg_{i}"
                    var = Variable(arg, name=var_name)
                    graph.add_node(var)
                    diff_args.append(var)

            diff_kwargs = {}
            for key, arg in kwargs.items():
                if isinstance(arg, Differentiable):
                    diff_kwargs[key] = arg
                else:
                    # Create a variable for non-differentiable inputs
                    var_name = f"kwarg_{key}"
                    var = Variable(arg, name=var_name)
                    graph.add_node(var)
                    diff_kwargs[key] = var

            # Call the function with differentiable inputs
            result = await func(*diff_args, **diff_kwargs)

            # If result is already a DifferentiableResult, return it
            if isinstance(result, DifferentiableResult):
                return result

            # If result is a Differentiable, wrap it in a DifferentiableResult
            if isinstance(result, Differentiable):
                return DifferentiableResult(
                    value=result,
                    graph=graph,
                    inputs=diff_args + list(diff_kwargs.values()),
                )

            # Otherwise, create a variable for the result and wrap it
            result_var = Variable(result, name="result")
            graph.add_node(result_var)

            return DifferentiableResult(
                value=result_var,
                graph=graph,
                inputs=diff_args + list(diff_kwargs.values()),
            )

        return async_wrapper

    return wrapper


def with_gradients(func: Callable[..., Any]) -> Callable[..., Tuple[Any, List[Any]]]:
    """
    Decorator to compute both value and gradients.

    This decorator wraps a function to compute both the value and gradients
    of the function with respect to its inputs.

    Args:
        func: The function to compute gradients for

    Returns:
        A function that returns both the value and gradients
    """

    @functools.wraps(func)
    def wrapper(*args, **kwargs):
        # Create a gradient tape
        tape = GradientTape()

        # Watch all differentiable arguments
        diff_args = []
        for arg in args:
            if isinstance(arg, Differentiable):
                tape.watch(arg)
                diff_args.append(arg)

        for _, arg in kwargs.items():
            if isinstance(arg, Differentiable):
                tape.watch(arg)
                diff_args.append(arg)

        # Call the function
        result = func(*args, **kwargs)

        # Compute gradients
        if isinstance(result, Differentiable):
            grads = tape.gradient(result, diff_args)
            return result.value, grads
        else:
            return result, [None] * len(diff_args)

    # Handle async functions
    if asyncio.iscoroutinefunction(func):

        @functools.wraps(func)
        async def async_wrapper(*args, **kwargs):
            # Create a gradient tape
            tape = GradientTape()

            # Watch all differentiable arguments
            diff_args = []
            for arg in args:
                if isinstance(arg, Differentiable):
                    tape.watch(arg)
                    diff_args.append(arg)

            for _, arg in kwargs.items():
                if isinstance(arg, Differentiable):
                    tape.watch(arg)
                    diff_args.append(arg)

            # Call the function
            result = await func(*args, **kwargs)

            # Compute gradients
            if isinstance(result, Differentiable):
                grads = tape.gradient(result, diff_args)
                return result.value, grads
            else:
                return result, [None] * len(diff_args)

        return async_wrapper

    return wrapper


def no_grad(func: Callable[..., T]) -> Callable[..., T]:
    """
    Decorator to disable gradient computation.

    This decorator wraps a function to disable gradient computation for
    operations performed within the function.

    Args:
        func: The function to disable gradient computation for

    Returns:
        A function that doesn't compute gradients
    """

    @functools.wraps(func)
    def wrapper(*args, **kwargs):
        # Convert differentiable arguments to their values
        new_args = []
        for arg in args:
            if isinstance(arg, Differentiable):
                new_args.append(arg.value)
            else:
                new_args.append(arg)

        new_kwargs = {}
        for key, arg in kwargs.items():
            if isinstance(arg, Differentiable):
                new_kwargs[key] = arg.value
            else:
                new_kwargs[key] = arg

        # Call the function with non-differentiable inputs
        return func(*new_args, **new_kwargs)

    # Handle async functions
    if asyncio.iscoroutinefunction(func):

        @functools.wraps(func)
        async def async_wrapper(*args, **kwargs):
            # Convert differentiable arguments to their values
            new_args = []
            for arg in args:
                if isinstance(arg, Differentiable):
                    new_args.append(arg.value)
                else:
                    new_args.append(arg)

            new_kwargs = {}
            for key, arg in kwargs.items():
                if isinstance(arg, Differentiable):
                    new_kwargs[key] = arg.value
                else:
                    new_kwargs[key] = arg

            # Call the function with non-differentiable inputs
            return await func(*new_args, **new_kwargs)

        return async_wrapper

    return wrapper


def differentiable_method(
    method: Callable[..., Any],
) -> Callable[..., DifferentiableResult]:
    """
    Decorator to make a method differentiable.

    This decorator wraps a method to make it differentiable, automatically
    tracking operations and computing gradients. It's similar to the
    @differentiable decorator, but it handles the 'self' parameter correctly.

    Args:
        method: The method to make differentiable

    Returns:
        A differentiable version of the method
    """

    @functools.wraps(method)
    def wrapper(self, *args, **kwargs):
        # Create a computation graph
        from person_suit.core.infrastructure.differentiable.autodiff.graph import ComputationGraph

        graph = ComputationGraph()

        # Convert inputs to differentiable variables
        diff_args = []
        for i, arg in enumerate(args):
            if isinstance(arg, Differentiable):
                diff_args.append(arg)
            else:
                # Create a variable for non-differentiable inputs
                var_name = f"arg_{i}"
                var = Variable(arg, name=var_name)
                graph.add_node(var)
                diff_args.append(var)

        diff_kwargs = {}
        for key, arg in kwargs.items():
            if isinstance(arg, Differentiable):
                diff_kwargs[key] = arg
            else:
                # Create a variable for non-differentiable inputs
                var_name = f"kwarg_{key}"
                var = Variable(arg, name=var_name)
                graph.add_node(var)
                diff_kwargs[key] = var

        # Call the method with differentiable inputs
        result = method(self, *diff_args, **diff_kwargs)

        # If result is already a DifferentiableResult, return it
        if isinstance(result, DifferentiableResult):
            return result

        # If result is a Differentiable, wrap it in a DifferentiableResult
        if isinstance(result, Differentiable):
            return DifferentiableResult(
                value=result, graph=graph, inputs=diff_args + list(diff_kwargs.values())
            )

        # Otherwise, create a variable for the result and wrap it
        result_var = Variable(result, name="result")
        graph.add_node(result_var)

        return DifferentiableResult(
            value=result_var, graph=graph, inputs=diff_args + list(diff_kwargs.values())
        )

    # Handle async methods
    if asyncio.iscoroutinefunction(method):

        @functools.wraps(method)
        async def async_wrapper(self, *args, **kwargs):
            # Create a computation graph
            from person_suit.core.infrastructure.differentiable.autodiff.graph import (
                ComputationGraph,
            )

            graph = ComputationGraph()

            # Convert inputs to differentiable variables
            diff_args = []
            for i, arg in enumerate(args):
                if isinstance(arg, Differentiable):
                    diff_args.append(arg)
                else:
                    # Create a variable for non-differentiable inputs
                    var_name = f"arg_{i}"
                    var = Variable(arg, name=var_name)
                    graph.add_node(var)
                    diff_args.append(var)

            diff_kwargs = {}
            for key, arg in kwargs.items():
                if isinstance(arg, Differentiable):
                    diff_kwargs[key] = arg
                else:
                    # Create a variable for non-differentiable inputs
                    var_name = f"kwarg_{key}"
                    var = Variable(arg, name=var_name)
                    graph.add_node(var)
                    diff_kwargs[key] = var

            # Call the method with differentiable inputs
            result = await method(self, *diff_args, **diff_kwargs)

            # If result is already a DifferentiableResult, return it
            if isinstance(result, DifferentiableResult):
                return result

            # If result is a Differentiable, wrap it in a DifferentiableResult
            if isinstance(result, Differentiable):
                return DifferentiableResult(
                    value=result,
                    graph=graph,
                    inputs=diff_args + list(diff_kwargs.values()),
                )

            # Otherwise, create a variable for the result and wrap it
            result_var = Variable(result, name="result")
            graph.add_node(result_var)

            return DifferentiableResult(
                value=result_var,
                graph=graph,
                inputs=diff_args + list(diff_kwargs.values()),
            )

        return async_wrapper

    return wrapper
