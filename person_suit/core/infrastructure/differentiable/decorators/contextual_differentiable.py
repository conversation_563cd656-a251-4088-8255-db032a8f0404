
# Message-based services initialization
from person_suit.core.infrastructure.message_based_imports import ensure_services_initialized


async def _ensure_message_services():
    """Ensure message-based services are initialized."""
    if not await ensure_services_initialized():
        raise RuntimeError("Failed to initialize message-based services")


"""
Context-Aware Differentiable Decorator
====================================

This module implements the @contextual_differentiable decorator, which makes
functions differentiable in a context-sensitive way. It adapts differentiation
based on the current context, enabling context-dependent gradient computation.

Related Files:
- differentiable.py: Standard differentiable decorator
- ../autodiff/context_sensitive_engine.py: Context-sensitive autodiff engine
- ../core.py: Core types and classes
- ../../wave/core.py: Wave-based information representation
- ../../caw/core.py: CAW components

Dependencies:
- Python 3.8+
- NumPy
"""

import asyncio
import functools
import inspect
import logging
from typing import Any
from typing import Callable
from typing import List
from typing import Optional
from typing import Tuple
from typing import TypeVar
from typing import Union

import numpy as np

from ..autodiff.context_sensitive_engine import get_context_sensitive_autodiff_engine
from ..core import Differentiable
from ..core import GradientTape
from ..core import Variable


# Placeholder for WaveFunction until its canonical CAW location is clear
class WaveFunction:
    def __init__(self, amplitude_fn: Any, phase_fn: Any):
        self.amplitude_fn = amplitude_fn
        self.phase_fn = phase_fn

# Configure logging
logger = logging.getLogger(__name__)

# Type variables for generic functions
T = TypeVar("T")
R = TypeVar("R")


class ContextualDifferentiableResult:
    """
    Result of a contextual differentiable function.

    This class wraps the result of a contextual differentiable function,
    providing access to both the value and the gradients.
    """

    def __init__(
        self,
        value: Any,
        gradients: Optional[List[Any]] = None,
        context: Optional[Context] = None,
    ):
        """
        Initialize a contextual differentiable result.

        Args:
            value: The result value
            gradients: Optional gradients
            context: Optional context
        """
        self.value = value
        self.gradients = gradients or []
        self.context = context

    def __repr__(self) -> str:
        """Get a string representation of the result."""
        return f"ContextualDifferentiableResult(value={self.value}, gradients={self.gradients}, context={self.context})"


class ContextualDifferentiableContext:
    """
    Context manager for contextual differentiable computations.

    This context manager creates a computation graph and tracks operations
    performed within its scope, adapting its behavior based on the context.
    """

    def __init__(self, context: Context, persistent: bool = False):
        """
        Initialize a contextual differentiable context.

        Args:
            context: The context in which to perform computations
            persistent: Whether the context should be persistent
        """
        self.context = context
        self.persistent = persistent
        self.tape = None
        self.watched_variables = set()

    async def __aenter__(self) -> "ContextualDifferentiableContext":
        """Enter the context asynchronously."""
        # Create a gradient tape in the context
        engine = await get_context_sensitive_autodiff_engine()
        self.tape = await engine.create_gradient_tape_in_context(
            context=self.context, persistent=self.persistent
        )
        return self

    async def __aexit__(self, exc_type, exc_val, exc_tb) -> None:
        """Exit the context asynchronously."""
        # Remove the gradient tape from the context
        if self.tape is not None:
            engine = await get_context_sensitive_autodiff_engine()
            await engine.remove_gradient_tape_in_context(
                tape=self.tape, context=self.context
            )

    def watch(self, variable: Differentiable) -> None:
        """
        Watch a variable for gradient computation.

        Args:
            variable: The variable to watch
        """
        if self.tape is not None:
            self.tape.watch(variable)
            self.watched_variables.add(variable)

    async def gradient(
        self, target: Differentiable, sources: Optional[List[Differentiable]] = None
    ) -> List[Optional[np.ndarray]]:
        """
        Compute gradients of a target with respect to sources.

        Args:
            target: The target differentiable object
            sources: The source differentiable objects

        Returns:
            List of gradients, one for each source
        """
        if self.tape is None:
            return [None] * (len(sources) if sources else 0)

        if sources is None:
            sources = list(self.watched_variables)

        # Compute gradients in the context
        engine = await get_context_sensitive_autodiff_engine()
        return await engine.compute_gradients_in_context(
            target=target, sources=sources, context=self.context
        )


def contextual_differentiable(
    context_param: str = "context",
    wave_function: Optional[Union[str, WaveFunction]] = None,
):
    """
    Decorator that makes a function differentiable in a context-sensitive way.

    Args:
        context_param: The name of the context parameter
        wave_function: Optional wave function or name of a registered wave function

    Returns:
        A decorator that makes a function differentiable in a context-sensitive way
    """

    def decorator(func: Callable[..., Any]) -> Callable[..., Any]:
        """
        Decorator that makes a function differentiable in a context-sensitive way.

        Args:
            func: The function to make differentiable

        Returns:
            A differentiable version of the function
        """
        sig = inspect.signature(func)

        # Check if the function has the context parameter
        if context_param not in sig.parameters:
            raise ValueError(
                f"Function {func.__name__} must have a parameter named {context_param}"
            )

        # Handle async functions
        if asyncio.iscoroutinefunction(func):

            @functools.wraps(func)
            async def async_wrapper(*args, **kwargs):
                # Get the context from the arguments
                context = kwargs.get(context_param)
                if context is None:
                    # Try to get the context from positional arguments
                    param_names = list(sig.parameters.keys())
                    if context_param in param_names:
                        context_index = param_names.index(context_param)
                        if context_index < len(args):
                            context = args[context_index]

                if context is None:
                    raise ValueError(f"Context parameter {context_param} not provided")

                # Create a contextual differentiable context
                async with ContextualDifferentiableContext(context) as ctx:
                    # Convert inputs to differentiable variables
                    diff_args = []
                    for i, arg in enumerate(args):
                        if isinstance(arg, Differentiable):
                            diff_args.append(arg)
                            ctx.watch(arg)
                        else:
                            # Create a variable for non-differentiable inputs
                            param_name = (
                                list(sig.parameters.keys())[i]
                                if i < len(sig.parameters)
                                else f"arg_{i}"
                            )
                            var = Variable(arg, name=param_name)
                            ctx.watch(var)
                            diff_args.append(var)

                    diff_kwargs = {}
                    for key, arg in kwargs.items():
                        if isinstance(arg, Differentiable):
                            diff_kwargs[key] = arg
                            ctx.watch(arg)
                        else:
                            # Create a variable for non-differentiable inputs
                            var = Variable(arg, name=key)
                            ctx.watch(var)
                            diff_kwargs[key] = var

                    # Call the function with differentiable inputs
                    result = await func(*diff_args, **diff_kwargs)

                    # Compute gradients
                    if isinstance(result, Differentiable):
                        sources = [
                            arg for arg in diff_args if isinstance(arg, Differentiable)
                        ]
                        sources.extend(
                            [
                                arg
                                for arg in diff_kwargs.values()
                                if isinstance(arg, Differentiable)
                            ]
                        )
                        grads = await ctx.gradient(result, sources)
                        return ContextualDifferentiableResult(
                            result.value, grads, context
                        )
                    else:
                        return result

            return async_wrapper
        else:

            @functools.wraps(func)
            def wrapper(*args, **kwargs):
                # Get the context from the arguments
                context = kwargs.get(context_param)
                if context is None:
                    # Try to get the context from positional arguments
                    param_names = list(sig.parameters.keys())
                    if context_param in param_names:
                        context_index = param_names.index(context_param)
                        if context_index < len(args):
                            context = args[context_index]

                if context is None:
                    raise ValueError(f"Context parameter {context_param} not provided")

                # Create a gradient tape
                tape = GradientTape()

                # Watch all differentiable arguments
                diff_args = []
                for arg in args:
                    if isinstance(arg, Differentiable):
                        tape.watch(arg)
                        diff_args.append(arg)

                for _, arg in kwargs.items():
                    if isinstance(arg, Differentiable):
                        tape.watch(arg)
                        diff_args.append(arg)

                # Call the function
                result = func(*args, **kwargs)

                # Compute gradients
                if isinstance(result, Differentiable):
                    grads = tape.gradient(result, diff_args)
                    return ContextualDifferentiableResult(result.value, grads, context)
                else:
                    return result

            return wrapper

    return decorator


def contextual_value_and_grad(
    context_param: str = "context",
    wave_function: Optional[Union[str, WaveFunction]] = None,
):
    """
    Decorator that computes both the value and gradient of a function in a context-sensitive way.

    Args:
        context_param: The name of the context parameter
        wave_function: Optional wave function or name of a registered wave function

    Returns:
        A decorator that computes both the value and gradient of a function
    """

    def decorator(func: Callable[..., Any]) -> Callable[..., Tuple[Any, List[Any]]]:
        """
        Decorator that computes both the value and gradient of a function.

        Args:
            func: The function to compute value and gradient for

        Returns:
            A function that returns both the value and gradient
        """
        # Apply the contextual_differentiable decorator
        diff_func = contextual_differentiable(context_param, wave_function)(func)

        # Handle async functions
        if asyncio.iscoroutinefunction(diff_func):

            @functools.wraps(func)
            async def async_wrapper(*args, **kwargs):
                result = await diff_func(*args, **kwargs)
                if isinstance(result, ContextualDifferentiableResult):
                    return result.value, result.gradients
                else:
                    return result, []

            return async_wrapper
        else:

            @functools.wraps(func)
            def wrapper(*args, **kwargs):
                result = diff_func(*args, **kwargs)
                if isinstance(result, ContextualDifferentiableResult):
                    return result.value, result.gradients
                else:
                    return result, []

            return wrapper

    return decorator


def contextual_grad(
    context_param: str = "context",
    wave_function: Optional[Union[str, WaveFunction]] = None,
):
    """
    Decorator that computes the gradient of a function in a context-sensitive way.

    Args:
        context_param: The name of the context parameter
        wave_function: Optional wave function or name of a registered wave function

    Returns:
        A decorator that computes the gradient of a function
    """

    def decorator(func: Callable[..., Any]) -> Callable[..., List[Any]]:
        """
        Decorator that computes the gradient of a function.

        Args:
            func: The function to compute gradient for

        Returns:
            A function that returns the gradient
        """
        # Apply the contextual_differentiable decorator
        diff_func = contextual_differentiable(context_param, wave_function)(func)

        # Handle async functions
        if asyncio.iscoroutinefunction(diff_func):

            @functools.wraps(func)
            async def async_wrapper(*args, **kwargs):
                result = await diff_func(*args, **kwargs)
                if isinstance(result, ContextualDifferentiableResult):
                    return result.gradients
                else:
                    return []

            return async_wrapper
        else:

            @functools.wraps(func)
            def wrapper(*args, **kwargs):
                result = diff_func(*args, **kwargs)
                if isinstance(result, ContextualDifferentiableResult):
                    return result.gradients
                else:
                    return []

            return wrapper

    return decorator
