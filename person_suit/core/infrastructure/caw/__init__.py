
# Message-based services initialization
from person_suit.core.infrastructure.message_based_imports import ensure_services_initialized


async def _ensure_message_services():
    """Ensure message-based services are initialized."""
    if not await ensure_services_initialized():
        raise RuntimeError("Failed to initialize message-based services")


"""
Core CAW (Contextual Adaptive Wave) Implementation

This module provides the foundational implementation of the CAW paradigm,
including wave-particle duality representations, context management, and
adaptive processing components.
"""

from typing import Any
from typing import Callable
from typing import Dict
from typing import Generic
from typing import Optional
from typing import Protocol
from typing import Type
from typing import TypeVar

from person_suit.core.infrastructure.message_based_imports import UnifiedContext

from ...caw.dual_information import DualInformation
from ...caw.particle_state import ImmutableHypergraph as ParticleState
from ..wave.transformer import Transformer

# from .caw_processor import CAWProcessor # Commented out due to missing file
from .wave_state import WaveState

__all__ = [
    # "CAWProcessor", # Commented out
    "WaveState",
    "ParticleState",
    "DualInformation",
    "Context",
    "Transformer",
]
