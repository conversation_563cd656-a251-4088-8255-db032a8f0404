"""person_suit.core.actors.wavetrace_exporter

<PERSON><PERSON><PERSON> → Prometheus exporter actor.

This actor subscribes to provenance completion events emitted by the HybridMessageBus
(`channel="sys.provenance.completed"`). Each provenance record contains WaveTrace span
information (timings, channel, status). The actor converts these into Prometheus
histogram observations and pushes them asynchronously to a Pushgateway. The exporter
runs under the standard Actor supervisor – crashes are automatically restarted.

The actor is intentionally lightweight: it uses aioprometheus' non-blocking client and
batches pushes to avoid spamming the Gateway under high message throughput.

This is the first step of the native monitoring enhancement requested on 2025-06-22.

Related files:
    • core/infrastructure/hybrid_message_bus.py – provenance writer emits the messages
    • devops/monitoring/grafana/wavetrace_latency_dashboard.json – dashboard template

"""

from __future__ import annotations

import asyncio
import json
import logging
from typing import Any, Dict, List

from aioprometheus import Gauge, Histogram, CollectorRegistry, Service  # type: ignore

from person_suit.core.actors.supervisor import actor_service  # Decorator registers actor
from person_suit.core.infrastructure.hybrid_message import HybridMessage
from person_suit.core.infrastructure.hybrid_message_bus import HybridMessageBus

logger = logging.getLogger(__name__)

# ---------------------------------------------------------------------------
# Prometheus metrics setup – we keep registry local to avoid global clashes
# ---------------------------------------------------------------------------

_REGISTRY: CollectorRegistry = CollectorRegistry()
_LATENCY_HISTOGRAM = Histogram(
    "hybrid_message_latency_seconds",
    "End-to-end latency of HybridMessages as recorded by WaveTrace",
    const_labels={"exemplar": "wave_trace"},
    buckets=(
        0.001,
        0.005,
        0.01,
        0.025,
        0.05,
        0.1,
        0.25,
        0.5,
        1.0,
        2.5,
        5.0,
        10.0,
    ),
    registry=_REGISTRY,
)
_SUCCESS_GAUGE = Gauge(
    "hybrid_message_success_total",
    "Total successful HybridMessages observed by WaveTrace",
    registry=_REGISTRY,
)
_FAILURE_GAUGE = Gauge(
    "hybrid_message_failure_total",
    "Total failed HybridMessages observed by WaveTrace",
    registry=_REGISTRY,
)

# ---------------------------------------------------------------------------
# Actor implementation
# ---------------------------------------------------------------------------

_PUSH_INTERVAL_SECONDS = 5.0  # Batch push cadence


@actor_service("WaveTraceExporterActor", priority=1)
class WaveTraceExporterActor:  # noqa: D101 – public actor, init docstring inline
    def __init__(self, bus: HybridMessageBus):
        self._bus = bus
        self._pending: List[HybridMessage] = []
        self._service: Service | None = None
        self._push_task: asyncio.Task[None] | None = None

    async def start(self) -> None:  # noqa: D401 – Actor lifecycle entrypoint
        """Subscribe to provenance events and start push loop."""
        self._bus.subscribe("sys.provenance.completed", self._on_provenance)
        self._service = Service(registry=_REGISTRY)
        await self._service.start(addr="0.0.0.0", port=8001)  # Expose /metrics
        self._push_task = asyncio.create_task(self._push_loop())
        logger.info("WaveTraceExporterActor started (exposes Prometheus on :8001)")

    async def stop(self) -> None:  # noqa: D401 – Actor lifecycle exit
        """Gracefully shut down exporter."""
        if self._push_task:
            self._push_task.cancel()
            with asyncio.sup

            try:
                await self._push_task
            except asyncio.CancelledError:
                pass
        if self._service:
            await self._service.stop()
        logger.info("WaveTraceExporterActor stopped")

    # ---------------------------------------------------------------------
    # Internal helpers
    # ---------------------------------------------------------------------

    async def _on_provenance(self, message: HybridMessage) -> None:  # noqa: D401
        """Handle a completed provenance record and buffer for batch processing."""
        self._pending.append(message)

    async def _push_loop(self) -> None:  # noqa: D401 – background task
        """Periodically publish metrics from buffered provenance spans."""
        while True:
            await asyncio.sleep(_PUSH_INTERVAL_SECONDS)
            # Snapshot & clear buffer
            if not self._pending:
                continue
            batch, self._pending = self._pending, []
            self._process_batch(batch)

    # ------------------------------------------------------------------
    # Metric processing
    # ------------------------------------------------------------------

    def _process_batch(self, batch: List[HybridMessage]) -> None:
        """Convert provenance batch into Prometheus observations."""
        for msg in batch:
            try:
                payload: Dict[str, Any] = msg.payload  # type: ignore[assignment]
                # Expected WaveTrace schema { "latency_sec": float, "status": "ok" | "error" }
                latency = float(payload.get("latency_sec", 0.0))
                status = str(payload.get("status", "ok"))
                channel = msg.channel
                _LATENCY_HISTOGRAM.observe(latency, labels={"channel": channel})
                if status == "ok":
                    _SUCCESS_GAUGE.inc()
                else:
                    _FAILURE_GAUGE.inc()
            except Exception as exc:  # noqa: BLE001
                logger.warning("Failed to process WaveTrace span: %s", exc, exc_info=exc) 