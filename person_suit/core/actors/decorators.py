"""
actor_service decorator
=======================

Sprint-3 introduces a lightweight ``@actor_service`` decorator used to mark
plain service classes (typically subclasses of ``BaseService``) as CAW actor
services.  The decorator does *not* transform the class at runtime – that would
risk breaking complex inheritance trees – but instead:

1. Sets an ``_is_actor_service`` attribute so that discovery tools and tests can
   assert that migration happened.
2. Automatically registers the class with the global ``ActorRegistry`` so that
   it is discoverable by capability-aware routing.
3. Optionally registers the class in the DI container, letting existing code
   resolve it the old way during the migration window.

The implementation purposefully avoids importing heavy async-only modules at
import-time; the registry imports are inside the decorator to keep import speed
fast for unrelated code paths.
"""
# ... existing code ...

from __future__ import annotations

import logging
from typing import Any
from typing import Type
from typing import TypeVar

from person_suit.core.di import DIContainer

T = TypeVar("T", bound=Type[Any])

logger = logging.getLogger(__name__)

# Global DI reference (lazy) – we use a private container instance so that
# registration works even if the application hasn't created a shared one yet.
_global_di: DIContainer | None = None


def _get_di() -> DIContainer:  # noqa: D401 – simple helper
    """Return (and lazily create) the global shim DI container."""
    global _global_di  # noqa: WPS420
    if _global_di is None:
        _global_di = DIContainer()
    return _global_di


def actor_service(cls: T) -> T:  # noqa: D401 – decorator signature
    """Decorate a service class so it is recognised as an *actor service*.

    Usage::

        from person_suit.core.actors.decorators import actor_service

        @actor_service
        class MemoryEncoderService(BaseService):
            ...

    The decorator performs **no** behavioural changes – existing code keeps
    working – but it enables tooling and the ActorSystem bootstrap to locate
    and spin-up wrappers automatically in future sprints.
    """

    # 1) Mark class so tests / reflection can spot migration status
    setattr(cls, "_is_actor_service", True)

    # 2) Attempt to register with ActorRegistry – done best-effort because we
    #    might be during early import where asyncio loop is unavailable.
    try:
        # Import inside try/except to avoid heavy deps if registry not ready.
        import asyncio

        from person_suit.core.actors.registry import get_registry  # noqa: WPS433

        async def _async_register() -> None:  # noqa: WPS430 – nested helper
            registry = await get_registry()
            # Instantiate once for metadata; actual actor envelope will be
            # created by dedicated wrapper in later sprint.
            instance = cls()  # type: ignore[arg-type]
            await registry.register_actor(instance)  # type: ignore[arg-type]

        # Schedule registration on running loop if present; else log hint.
        try:
            loop = asyncio.get_running_loop()
            loop.create_task(_async_register())
        except RuntimeError:
            # No loop yet – will register lazily when loop starts.
            logger.debug(
                "@actor_service: asyncio loop not running – deferred registry" " registration for %s",  # noqa: E501
                cls.__name__,
            )
    except Exception as exc:  # pragma: no cover – diagnostics only
        logger.debug("@actor_service unable to register %s: %s", cls.__name__, exc)

    # 3) Register the *class* in DI so legacy resolution works.
    try:
        di = _get_di()
        if di.resolve_optional(cls) is None:  # type: ignore[arg-type]
            di.register(cls, cls)  # type: ignore[arg-type]
    except Exception as exc:  # pragma: no cover – diagnostics only
        logger.debug("@actor_service DI registration failed for %s: %s", cls.__name__, exc)

    logger.info("Registered actor service class: %s", cls.__name__)
    return cls 