"""person_suit.core.actors.monitoring_actors

Async actor implementations of monitoring components.

This module provides actor-based replacements for the thread-based monitoring
components that were using blocking time.sleep() calls. These actors run under
the standard supervisor and use async/await patterns throughout.

Purpose: Replace blocking monitoring loops with async actors
Related Files: core/infrastructure/monitoring/integration.py
Dependencies: core.actors.supervisor, core.infrastructure.hybrid_message_bus
"""

from __future__ import annotations

import asyncio
import logging
from typing import Any
from typing import Dict
from typing import Optional
from typing import Protocol
from typing import runtime_checkable

from person_suit.core.actors.supervisor import actor_service
from person_suit.core.infrastructure.hybrid_message_bus import HybridMessageBus

logger = logging.getLogger(__name__)


# Import the protocols and types we need
try:
    from person_suit.core.infrastructure.monitoring.integration import HAS_RESOURCE_OPTIMIZATION
    from person_suit.core.infrastructure.monitoring.integration import HealthCheckStatus
    from person_suit.core.infrastructure.monitoring.integration import HealthIndicator
    from person_suit.core.infrastructure.monitoring.integration import get_health_registry
    from person_suit.core.infrastructure.monitoring.integration import get_resource_manager
except ImportError:
    # Fallback for when resource optimization is not available
    HAS_RESOURCE_OPTIMIZATION = False
    get_resource_manager = None
    get_health_registry = None
    HealthCheckStatus = None
    HealthIndicator = None


@runtime_checkable
class IAnomalyDetector(Protocol):
    """Protocol for anomaly detector dependency injection."""
    
    def detect(self, data: Any) -> bool:
        """Detect anomalies in the provided data."""
        ...


@actor_service("ResourceMetricsCollectorActor")
class ResourceMetricsCollectorActor:
    """
    Actor-based resource metrics collector.
    
    Replaces the thread-based ResourceOptimizationMetricsCollector
    with an async actor that uses asyncio.sleep instead of time.sleep.
    """
    
    def __init__(
        self,
        bus: HybridMessageBus,
        collection_interval_seconds: float = 10.0,
    ):
        """Initialize the metrics collector actor."""
        self._bus = bus
        self.collection_interval_seconds = collection_interval_seconds
        self._running = False
        self._collection_task: Optional[asyncio.Task] = None
        
    async def start(self) -> None:
        """Start the metrics collection loop."""
        if not HAS_RESOURCE_OPTIMIZATION:
            logger.info("Resource optimization not available, metrics collector not starting")
            return
            
        self._running = True
        self._collection_task = asyncio.create_task(self._collection_loop())
        logger.info("Started resource metrics collector actor")
        
    async def stop(self) -> None:
        """Stop the metrics collection loop."""
        self._running = False
        if self._collection_task:
            self._collection_task.cancel()
            try:
                await self._collection_task
            except asyncio.CancelledError:
                pass
        logger.info("Stopped resource metrics collector actor")
        
    async def _collection_loop(self) -> None:
        """Run the async collection loop."""
        while self._running:
            try:
                await self._collect_metrics()
            except Exception as e:
                logger.error(f"Error in resource metrics collection: {e}")
                
            # Use async sleep instead of blocking sleep
            await asyncio.sleep(self.collection_interval_seconds)
            
    async def _collect_metrics(self) -> None:
        """Collect and publish resource metrics."""
        if not HAS_RESOURCE_OPTIMIZATION or not get_resource_manager:
            return
            
        try:
            # Run the blocking resource manager call in executor
            loop = asyncio.get_running_loop()
            resource_manager = await loop.run_in_executor(None, get_resource_manager)
            metrics = await loop.run_in_executor(None, resource_manager.get_metrics)
            
            # Publish metrics via message bus
            from person_suit.core.infrastructure.hybrid_message import HybridMessage
            from person_suit.core.infrastructure.hybrid_message import MessageType
            
            message = HybridMessage(
                message_type=MessageType.EVENT,
                channel="sys.metrics.resource_optimization",
                payload={"metrics": metrics},
            )
            await self._bus.publish(message)
            
        except Exception as e:
            logger.error(f"Error collecting resource metrics: {e}")


@actor_service("ResourceHealthMonitorActor")
class ResourceHealthMonitorActor:
    """
    Actor-based resource health monitor.
    
    Replaces the thread-based ResourceHealthMonitor with an async actor.
    """
    
    def __init__(
        self,
        bus: HybridMessageBus,
        check_interval_seconds: float = 60.0,
    ):
        """Initialize the health monitor actor."""
        self._bus = bus
        self.check_interval_seconds = check_interval_seconds
        self._running = False
        self._check_task: Optional[asyncio.Task] = None
        
    async def start(self) -> None:
        """Start the health check loop."""
        if not HAS_RESOURCE_OPTIMIZATION:
            logger.info("Resource optimization not available, health monitor not starting")
            return
            
        self._running = True
        self._check_task = asyncio.create_task(self._check_loop())
        logger.info("Started resource health monitor actor")
        
    async def stop(self) -> None:
        """Stop the health check loop."""
        self._running = False
        if self._check_task:
            self._check_task.cancel()
            try:
                await self._check_task
            except asyncio.CancelledError:
                pass
        logger.info("Stopped resource health monitor actor")
        
    async def _check_loop(self) -> None:
        """Run the async health check loop."""
        while self._running:
            try:
                await self._check_resource_health()
            except Exception as e:
                logger.error(f"Error in resource health check: {e}")
                
            # Use async sleep instead of blocking sleep
            await asyncio.sleep(self.check_interval_seconds)
            
    async def _check_resource_health(self) -> None:
        """Check resource health and update indicators."""
        if not HAS_RESOURCE_OPTIMIZATION:
            return
            
        try:
            # Run blocking calls in executor
            loop = asyncio.get_running_loop()
            resource_manager = await loop.run_in_executor(None, get_resource_manager)
            system_info = await loop.run_in_executor(None, resource_manager.get_system_info)
            
            # Check various health aspects
            await self._check_memory_health(system_info.get("memory", {}))
            await self._check_cpu_health(system_info.get("cpu", {}))
            
        except Exception as e:
            logger.error(f"Error checking resource health: {e}")
            
    async def _check_memory_health(self, memory_info: Dict[str, Any]) -> None:
        """Check memory health and publish status."""
        memory_percent = memory_info.get("percent", 0)
        
        if memory_percent > 90:
            status = "critical"
            score = 0.0
        elif memory_percent > 80:
            status = "unhealthy"
            score = 0.25
        elif memory_percent > 70:
            status = "degraded"
            score = 0.5
        else:
            status = "healthy"
            score = 1.0
            
        # Publish health status via message bus
        from person_suit.core.infrastructure.hybrid_message import HybridMessage
        from person_suit.core.infrastructure.hybrid_message import MessageType
        
        message = HybridMessage(
            message_type=MessageType.EVENT,
            channel="sys.health.memory",
            payload={
                "status": status,
                "score": score,
                "memory_percent": memory_percent,
            },
        )
        await self._bus.publish(message)
        
    async def _check_cpu_health(self, cpu_info: Dict[str, Any]) -> None:
        """Check CPU health and publish status."""
        cpu_percent = cpu_info.get("usage_percent", 0)
        
        if cpu_percent > 90:
            status = "critical"
            score = 0.0
        elif cpu_percent > 80:
            status = "unhealthy"
            score = 0.25
        elif cpu_percent > 70:
            status = "degraded"
            score = 0.5
        else:
            status = "healthy"
            score = 1.0
            
        # Publish health status via message bus
        from person_suit.core.infrastructure.hybrid_message import HybridMessage
        from person_suit.core.infrastructure.hybrid_message import MessageType
        
        message = HybridMessage(
            message_type=MessageType.EVENT,
            channel="sys.health.cpu",
            payload={
                "status": status,
                "score": score,
                "cpu_percent": cpu_percent,
            },
        )
        await self._bus.publish(message)


@actor_service("ResourceAnomalyDetectorActor")
class ResourceAnomalyDetectorActor:
    """
    Actor-based resource anomaly detector.
    
    Replaces the thread-based ResourceAnomalyDetector with an async actor.
    """
    
    def __init__(
        self,
        bus: HybridMessageBus,
        anomaly_detector: IAnomalyDetector,
        detection_interval_seconds: float = 30.0,
    ):
        """Initialize the anomaly detector actor."""
        self._bus = bus
        self._anomaly_detector = anomaly_detector
        self.detection_interval_seconds = detection_interval_seconds
        self._running = False
        self._detection_task: Optional[asyncio.Task] = None
        
    async def start(self) -> None:
        """Start the anomaly detection loop."""
        if not HAS_RESOURCE_OPTIMIZATION:
            logger.info("Resource optimization not available, anomaly detector not starting")
            return
            
        self._running = True
        self._detection_task = asyncio.create_task(self._detection_loop())
        logger.info("Started resource anomaly detector actor")
        
    async def stop(self) -> None:
        """Stop the anomaly detection loop."""
        self._running = False
        if self._detection_task:
            self._detection_task.cancel()
            try:
                await self._detection_task
            except asyncio.CancelledError:
                pass
        logger.info("Stopped resource anomaly detector actor")
        
    async def _detection_loop(self) -> None:
        """Run the async anomaly detection loop."""
        while self._running:
            try:
                await self._detect_anomalies()
            except Exception as e:
                logger.error(f"Error in resource anomaly detection: {e}")
                
            # Use async sleep instead of blocking sleep
            await asyncio.sleep(self.detection_interval_seconds)
            
    async def _detect_anomalies(self) -> None:
        """Detect resource usage anomalies."""
        if not HAS_RESOURCE_OPTIMIZATION:
            return
            
        try:
            # Run blocking calls in executor
            loop = asyncio.get_running_loop()
            resource_manager = await loop.run_in_executor(None, get_resource_manager)
            system_info = await loop.run_in_executor(None, resource_manager.get_system_info)
            
            # Check for anomalies
            memory_percent = system_info.get("memory", {}).get("percent", 0)
            cpu_percent = system_info.get("cpu", {}).get("usage_percent", 0)
            
            # Detect anomalies (run in executor if detector is blocking)
            memory_anomaly = await loop.run_in_executor(
                None, self._anomaly_detector.detect, memory_percent
            )
            cpu_anomaly = await loop.run_in_executor(
                None, self._anomaly_detector.detect, cpu_percent
            )
            
            # Publish anomaly events if detected
            if memory_anomaly:
                await self._publish_anomaly("memory", memory_percent)
            if cpu_anomaly:
                await self._publish_anomaly("cpu", cpu_percent)
                
        except Exception as e:
            logger.error(f"Error detecting resource anomalies: {e}")
            
    async def _publish_anomaly(self, resource_type: str, value: float) -> None:
        """Publish an anomaly event."""
        from person_suit.core.infrastructure.hybrid_message import HybridMessage
        from person_suit.core.infrastructure.hybrid_message import MessageType
        
        message = HybridMessage(
            message_type=MessageType.EVENT,
            channel=f"sys.anomaly.{resource_type}",
            payload={
                "resource_type": resource_type,
                "value": value,
                "timestamp": asyncio.get_event_loop().time(),
            },
        )
        await self._bus.publish(message) 