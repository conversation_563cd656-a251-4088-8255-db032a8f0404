"""
CAW Choreography Engine and Definitions.

This module is now a re-export facade for the reorganized choreography package.
All implementations have been moved to the person_suit.core.actors.choreography package
to improve maintainability.

See person_suit.core.actors.choreography.__init__ for package details.
"""

# Re-export everything from the choreography package
# Need to use relative imports to avoid circular imports
from .choreography import BASIC_REQUEST_TEMPLATE
from .choreography import BASIC_RESPONSE_SCHEMA
from .choreography import GET_DATA_REQUEST_CHOREO_V2
from .choreography import PROCESS_DATA_CONTEXT_UPDATE_DICT
from .choreography import ActorID
from .choreography import ChoreographyDefinition
from .choreography import ChoreographyEngine
from .choreography import ChoreographyError
from .choreography import ChoreographyID
from .choreography import ChoreographyStep
from .choreography import generate_correlation_id

# Export all names
__all__ = [
    "ChoreographyStep",
    "ChoreographyDefinition",
    "ChoreographyEngine",
    "ChoreographyError",
    "ActorID",
    "ChoreographyID",
    "generate_correlation_id",
    "BASIC_REQUEST_TEMPLATE",
    "BASIC_RESPONSE_SCHEMA",
    "PROCESS_DATA_CONTEXT_UPDATE_DICT",
    "GET_DATA_REQUEST_CHOREO_V2",
]
