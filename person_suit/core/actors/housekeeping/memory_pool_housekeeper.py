from __future__ import annotations

"""MemoryPool Housekeeper Actor.
Ensures all MemoryPool instances run maintenance without individual cleanup loops.
Cancels underlying per-pool `_cleanup_task` and monkey-patches `_start_cleanup_task` to avoid new tasks.
"""

import asyncio
import logging
from typing import List

from person_suit.core.actors.base import Actor
from person_suit.core.actors.base import StandardActorMessage
from person_suit.core.actors.decorators import actor_service
from person_suit.core.infrastructure.resource_optimization.memory.pool import MemoryPool

logger = logging.getLogger(__name__)


TICK_MSG = "MEMORY_POOL_HK_TICK"


@actor_service
class MemoryPoolHousekeeper(Actor):  # noqa: D101
    _interval_seconds: float = 60.0

    async def pre_start(self) -> None:  # noqa: D401
        self._patch_memory_pool()
        await self._schedule_next_tick()

    async def post_stop(self) -> None:  # noqa: D401
        if hasattr(self, "_tick_handle") and self._tick_handle:
            self._tick_handle.cancel()

    # ---------------------------------------------------------------------
    def _patch_memory_pool(self) -> None:  # noqa: D401
        """Cancel existing per-pool cleanup tasks and monkey-patch startup."""
        for pool in list(MemoryPool._instances.values()):  # type: ignore[attr-defined]
            self._cancel_cleanup_task(pool)

        def _noop_start(self_: MemoryPool) -> None:  # noqa: D401
            pass

        MemoryPool._start_cleanup_task = _noop_start  # type: ignore[assignment]
        logger.info("MemoryPool cleanup tasks patched for actor supervision")

    @staticmethod
    def _cancel_cleanup_task(pool) -> None:  # noqa: D401
        task = getattr(pool, "_cleanup_task", None)
        if task and not task.done():
            task.cancel()
            logger.debug("Cancelled MemoryPool cleanup task for %s", pool)

    async def _schedule_next_tick(self) -> None:
        loop = asyncio.get_running_loop()

        def _enqueue() -> None:  # noqa: WPS430
            asyncio.create_task(self.tell(TICK_MSG, None))

        self._tick_handle = loop.call_later(self._interval_seconds, _enqueue)

    async def _perform_cleanup(self) -> None:  # noqa: D401
        pools: List[MemoryPool] = list(MemoryPool._instances.values())  # type: ignore[attr-defined]
        for pool in pools:
            try:
                await pool._cleanup_idle_resources()  # noqa: WPS437
            except Exception as exc:  # pragma: no cover
                logger.error("MemoryPoolHousekeeper maintenance error: %s", exc)

    async def receive(self, message: StandardActorMessage) -> None:  # noqa: D401
        if message.type == TICK_MSG:
            try:
                await self._perform_cleanup()
            except Exception as exc:  # pragma: no cover
                logger.error("MemoryPoolHousekeeper loop error: %s", exc)
            finally:
                await self._schedule_next_tick()
        else:
            logger.debug("MemoryPoolHousekeeper received %s – ignored", message.type) 