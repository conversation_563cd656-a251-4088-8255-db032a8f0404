from __future__ import annotations

"""ServiceRegistry Housekeeper Actor.
Ensures health checks for service discovery run under supervision.
"""

import asyncio
import contextlib
import logging

from person_suit.core.actors.base import Actor
from person_suit.core.actors.base import StandardActorMessage
from person_suit.core.actors.decorators import actor_service
from person_suit.core.services.discovery import get_service_discovery

logger = logging.getLogger(__name__)


@actor_service
class ServiceRegistryHousekeeper(Actor):  # noqa: D101
    _interval_seconds: float = 30.0

    async def pre_start(self) -> None:  # noqa: D401
        self._sd = await get_service_discovery()
        await self._sd.initialize()
        self._task = asyncio.create_task(self._loop())

    async def post_stop(self) -> None:  # noqa: D401
        if hasattr(self, "_task") and self._task:
            self._task.cancel()
            with contextlib.suppress(asyncio.CancelledError):
                await self._task

    async def _loop(self) -> None:  # noqa: D401
        registry = self._sd._registry  # accessing protected member intentionally
        while self._running:
            try:
                await registry._perform_health_checks()  # noqa: WPS437
                await asyncio.sleep(self._interval_seconds)
            except asyncio.CancelledError:
                break
            except Exception as exc:  # pragma: no cover
                logger.error("ServiceRegistryHousekeeper error: %s", exc)
                await asyncio.sleep(self._interval_seconds)

    async def receive(self, message: StandardActorMessage) -> None:  # noqa: D401
        logger.debug("ServiceRegistryHousekeeper received %s – ignored", message.type) 