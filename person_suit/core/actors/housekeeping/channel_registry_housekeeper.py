from __future__ import annotations

"""Channel Registry Housekeeper Actor (Sprint-3).

Periodically validates channel definitions and refreshes QoS defaults.
This is a minimal MVP to prove actor-envelope migration.
"""

import asyncio
import logging

from person_suit.core.actors.base import Actor
from person_suit.core.actors.base import StandardActorMessage
from person_suit.core.actors.decorators import actor_service
from person_suit.core.infrastructure.channel_registry import get_channel_registry

logger = logging.getLogger(__name__)


TICK_MSG = "CHANNEL_REGISTRY_HK_TICK"


@actor_service
class ChannelRegistryHousekeeper(Actor):  # noqa: D101
    _interval_seconds: float = 30.0  # default check interval

    async def pre_start(self) -> None:  # noqa: D401
        await self._schedule_next_tick()

    async def post_stop(self) -> None:  # noqa: D401
        if hasattr(self, "_tick_handle") and self._tick_handle:
            self._tick_handle.cancel()

    async def _schedule_next_tick(self) -> None:
        loop = asyncio.get_running_loop()

        def _enqueue() -> None:  # noqa: WPS430
            asyncio.create_task(self.tell(TICK_MSG, None))

        self._tick_handle = loop.call_later(self._interval_seconds, _enqueue)

    async def _run_validation(self) -> None:
        registry = get_channel_registry()
        for channel in registry.get_all_channels():
            registry.validate_channel(channel)

    async def receive(self, message: StandardActorMessage) -> None:  # noqa: D401
        if message.type == TICK_MSG:
            try:
                await self._run_validation()
            except Exception as exc:  # pragma: no cover
                logger.error("Housekeeper error: %s", exc)
            finally:
                await self._schedule_next_tick()
        else:
            logger.debug(
                "ChannelRegistryHousekeeper received message %s – ignoring",
                message.type,
            ) 