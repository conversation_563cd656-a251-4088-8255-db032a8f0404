from __future__ import annotations

"""MemoryManager Housekeeper Actor.
Runs MemoryManager._check_memory_pressure, _update_pool_stats, _perform_maintenance periodically under supervision.
"""

import asyncio
import logging

from person_suit.core.actors.base import Actor
from person_suit.core.actors.base import StandardActorMessage
from person_suit.core.actors.decorators import actor_service
from person_suit.core.infrastructure.resource_optimization.memory.manager import MemoryManager

logger = logging.getLogger(__name__)


TICK_MSG = "MEMORY_MANAGER_HK_TICK"


@actor_service
class MemoryManagerHousekeeper(Actor):  # noqa: D101
    _interval_seconds: float = 30.0

    async def pre_start(self) -> None:  # noqa: D401
        self._mm = MemoryManager(external_supervision=True)  # type: ignore[arg-type]
        await self._mm.initialize()
        await self._schedule_next_tick()

    async def post_stop(self) -> None:  # noqa: D401
        if hasattr(self, "_tick_handle") and self._tick_handle:
            self._tick_handle.cancel()
        await self._mm.shutdown()

    async def _schedule_next_tick(self) -> None:
        loop = asyncio.get_running_loop()

        def _enqueue() -> None:  # noqa: WPS430
            asyncio.create_task(self.tell(TICK_MSG, None))

        self._tick_handle = loop.call_later(self._interval_seconds, _enqueue)

    async def _maintenance(self) -> None:  # noqa: D401
        await self._mm._check_memory_pressure()  # noqa: WPS437
        await self._mm._update_pool_stats()
        await self._mm._perform_maintenance()

    async def receive(self, message: StandardActorMessage) -> None:  # noqa: D401
        if message.type == TICK_MSG:
            try:
                await self._maintenance()
            except Exception as exc:  # pragma: no cover
                logger.error("MemoryManagerHousekeeper error: %s", exc)
            finally:
                await self._schedule_next_tick()
        else:
            logger.debug("MemoryManagerHousekeeper received %s – ignored", message.type) 