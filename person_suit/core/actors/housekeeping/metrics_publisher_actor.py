"""MetricsPublisherActor

Periodically publishes runtime metrics to the monitoring subsystem.  During
ongoing refactor this stub simply sleeps to prevent busy-looping; tests care
only about actor registration and uptime gauge.
"""

from __future__ import annotations

import asyncio

from person_suit.core.actors.base import Actor
from person_suit.core.actors.base import StandardActorMessage
from person_suit.core.actors.decorators import actor_service


@actor_service  # Marks the class so discovery picks it up
class MetricsPublisherActor(Actor):  # noqa: D101 – publishes system metrics
    _interval_seconds: float = 2.0

    async def pre_start(self) -> None:  # noqa: D401 – lifecycle hook
        await super().pre_start()
        # Warm-up psutil so first reading is meaningful
        import psutil  # local import to keep module load light
        psutil.cpu_percent(interval=None)

    async def receive(self, message: StandardActorMessage) -> None:  # noqa: D401 override
        # This actor is timer-driven; ignore external messages.
        pass

    async def _run(self):  # type: ignore[override] – custom main loop
        """Main loop overridden to publish metrics periodically."""
        import psutil

        from person_suit.core.infrastructure.hybrid_message import HybridMessage
        from person_suit.core.infrastructure.hybrid_message_bus import get_message_bus

        bus = get_message_bus()

        while self._running:
            try:
                payload = {
                    "cpu_percent": psutil.cpu_percent(interval=None),
                    "mem_percent": psutil.virtual_memory().percent,
                    "queue_depth": bus.queue.depth if hasattr(bus.queue, "depth") else 0,
                }

                msg = HybridMessage(
                    message_type="EVENT",
                    channel="sys.metrics.update",
                    payload=payload,
                    response_expected=False,
                    priority=0.3,
                )

                # Fire-and-forget through the normal send path
                try:
                    await bus.send(msg)
                except Exception:  # noqa: WPS420 – ignore failures; supervisor logs elsewhere
                    pass
            except Exception as exc:  # pragma: no cover – keep loop alive
                # We deliberately keep the actor alive; supervision will log if needed.
                import logging

                logging.getLogger(__name__).error("MetricsPublisherActor error: %s", exc)

            await asyncio.sleep(self._interval_seconds) 