from __future__ import annotations

"""Telemetry Recorder Housekeeper Actor.
Flushes and resets in-memory telemetry metrics periodically to keep memory footprint low.
"""

import asyncio
import logging

from person_suit.core.actors.base import Actor
from person_suit.core.actors.base import StandardActorMessage
from person_suit.core.actors.decorators import actor_service
from person_suit.shared.utils.telemetry_recorder import get_default_recorder

logger = logging.getLogger(__name__)

TICK_MSG = "TELEMETRY_FLUSH_TICK"


@actor_service
class TelemetryHousekeeper(Actor):  # noqa: D101
    _interval_seconds: float = 60.0

    async def pre_start(self) -> None:  # noqa: D401
        await self._schedule_next_tick()

    async def post_stop(self) -> None:  # noqa: D401
        if hasattr(self, "_tick_handle") and self._tick_handle:
            self._tick_handle.cancel()

    async def _schedule_next_tick(self) -> None:  # noqa: D401
        loop = asyncio.get_running_loop()

        def _enqueue_tick() -> None:  # noqa: WPS430
            asyncio.create_task(self.tell(TICK_MSG, None))

        self._tick_handle = loop.call_later(self._interval_seconds, _enqueue_tick)

    async def _flush(self) -> None:  # noqa: D401
        recorder = get_default_recorder()
        if recorder:
            recorder.reset()

    async def receive(self, message: StandardActorMessage) -> None:  # noqa: D401
        if message.type == TICK_MSG:
            try:
                await self._flush()
            finally:
                await self._schedule_next_tick()
        else:
            logger.debug(
                "TelemetryHousekeeper received message %s – ignoring", message.type
            ) 