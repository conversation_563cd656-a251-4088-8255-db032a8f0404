"""Bridge between HybridMessageBus and Actor System.

This module provides the integration between the message bus and actor system,
allowing actors to receive messages from bus channels.

Purpose: Connect message bus to actor system
Related Files: hybrid_message_bus.py, actor_system.py
Dependencies: asyncio
"""

from __future__ import annotations

import logging
from typing import TYPE_CHECKING
from typing import Dict

from person_suit.core.actors.base import ActorRef
from person_suit.core.infrastructure.hybrid_message import HybridMessage
from person_suit.core.infrastructure.hybrid_message_bus import HybridMessageBus

if TYPE_CHECKING:
    pass

logger = logging.getLogger(__name__)


class BusActorBridge:
    """Bridge to route messages from HybridMessageBus to Actors."""
    
    def __init__(self, message_bus: HybridMessageBus):
        """Initialize the bridge with a message bus."""
        self.message_bus = message_bus
        self._channel_to_actor: Dict[str, ActorRef] = {}
        self._running = False
        
    async def register_actor_for_channel(self, channel: str, actor_ref: ActorRef) -> None:
        """Register an actor to receive messages from a specific channel.
        
        Args:
            channel: The bus channel to subscribe to
            actor_ref: The actor that will receive messages
        """
        if self._running:
            # Subscribe immediately if bridge is running
            await self._subscribe_channel(channel, actor_ref)
        else:
            # Store for later subscription
            self._channel_to_actor[channel] = actor_ref
            
    async def start(self) -> None:
        """Start the bridge and subscribe to all registered channels."""
        if self._running:
            return
            
        self._running = True
        
        # Subscribe to all registered channels
        for channel, actor_ref in self._channel_to_actor.items():
            await self._subscribe_channel(channel, actor_ref)
            
        logger.info(f"BusActorBridge started with {len(self._channel_to_actor)} channel subscriptions")
        
    async def stop(self) -> None:
        """Stop the bridge and unsubscribe from all channels."""
        if not self._running:
            return
            
        self._running = False
        
        # Unsubscribe from all channels
        for channel in self._channel_to_actor:
            try:
                # Note: HybridMessageBus doesn't have unsubscribe yet
                # This would need to be implemented
                pass
            except Exception as e:
                logger.error(f"Error unsubscribing from channel {channel}: {e}")
                
        logger.info("BusActorBridge stopped")
        
    async def _subscribe_channel(self, channel: str, actor_ref: ActorRef) -> None:
        """Subscribe to a channel and route messages to an actor."""
        async def message_handler(message: HybridMessage) -> None:
            """Convert bus message to actor message and deliver."""
            try:
                # Send to actor using the tell method
                await actor_ref.tell(
                    message_type="BusMessage",
                    payload={
                        "channel": message.channel,
                        "original_message": message,
                        "payload": message.payload,
                    },
                    context=message.context,
                )
                
            except Exception as e:
                logger.error(f"Error routing message from channel {channel} to actor: {e}")
                
        # Subscribe to the channel
        await self.message_bus.subscribe(
            pattern=channel,
            handler=message_handler,
            subscriber_id=f"actor_bridge_{channel}",
        )
        
        logger.debug(f"Subscribed actor to channel {channel}")


async def create_bus_actor_bridge(
    message_bus: HybridMessageBus,
    actor_refs: Dict[str, ActorRef],
) -> BusActorBridge:
    """Create and configure a bus-actor bridge.
    
    Args:
        message_bus: The message bus instance
        actor_refs: Mapping of channel patterns to actor references
        
    Returns:
        Configured and started bridge
    """
    bridge = BusActorBridge(message_bus)
    
    # Register all actor-channel mappings
    for channel, actor_ref in actor_refs.items():
        await bridge.register_actor_for_channel(channel, actor_ref)
        
    # Start the bridge
    await bridge.start()
    
    return bridge 