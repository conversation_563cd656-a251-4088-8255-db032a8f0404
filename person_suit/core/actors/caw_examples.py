
# Message-based services initialization
from person_suit.core.infrastructure.message_based_imports import ensure_services_initialized


async def _ensure_message_services():
    """Ensure message-based services are initialized."""
    if not await ensure_services_initialized():
        raise RuntimeError("Failed to initialize message-based services")


"""person_suit.core.actors.caw_examples
=================================================

Example CAW Actors for demonstration and testing.

- PersonaCoreActor: Simulates persona core logic
- AnalystActor: Simulates analyst logic
- PredictorActor: Simulates predictor logic

Each logs context and fidelity on message receipt.
"""

import logging

from person_suit.core.actors.base import StandardActorMessage
from person_suit.core.actors.caw_actor import DecoupledActor
from person_suit.core.infrastructure.message_based_imports import UnifiedContext

logger = logging.getLogger(__name__)

class PersonaCoreActor(DecoupledActor):
    async def receive_with_context(self, message: StandardActorMessage, context: UnifiedContext, fidelity: int) -> None:
        logger.info(f"[PersonaCoreActor] Received: {message.type}, Fidelity: {fidelity:.3f}, Context: {context.domain}, Priority: {context.priority}")
        # Simulate work
        if message.type == "persona_query":
            logger.info(f"PersonaCoreActor processing query with goals: {context.goals}")

class AnalystActor(DecoupledActor):
    async def receive_with_context(self, message: StandardActorMessage, context: UnifiedContext, fidelity: int) -> None:
        logger.info(f"[AnalystActor] Received: {message.type}, Fidelity: {fidelity:.3f}, Context: {context.domain}, Priority: {context.priority}")
        # Simulate work
        if message.type == "analysis_request":
            logger.info(f"AnalystActor analyzing with constraints: {context.constraints}")

class PredictorActor(DecoupledActor):
    async def receive_with_context(self, message: StandardActorMessage, context: UnifiedContext, fidelity: int) -> None:
        logger.info(f"[PredictorActor] Received: {message.type}, Fidelity: {fidelity:.3f}, Context: {context.domain}, Priority: {context.priority}")
        # Simulate work
        if message.type == "prediction_request":
            logger.info(f"PredictorActor predicting with wave/particle ratio: {context.wave_particle_ratio}") 