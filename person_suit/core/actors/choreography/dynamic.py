# -*- coding: utf-8 -*-
"""
Dynamic next step determination for the CAW Choreography system.

Provides functionality to dynamically determine the next step in a choreography
based on rules, conditions, and registered functions.

Related Files:
- person_suit.core.actors.choreography.engine.py
- person_suit.core.actors.choreography.execution.py
"""

import logging
from typing import Callable
from typing import Dict
from typing import Optional

# asteval for safe expression evaluation
try:
    import asteval  # type: ignore

    ASTEVAL_AVAILABLE = True
except ImportError:
    logging.warning(
        "asteval library not found. Conditional GOTO in choreographies will be disabled. "
        "Install with: pip install asteval"
    )
    ASTEVAL_AVAILABLE = False
    asteval = None  # Placeholder


def determine_dynamic_next_step(
    rule: str,
    instance_state: Dict,
    next_step_functions: Dict[str, Callable[[Dict], Optional[str]]],
) -> Optional[str]:
    """
    Evaluates a rule string to dynamically determine the next step ID.

    Supports:
    - GOTO:target_step_id
    - COND_GOTO:target_step_id IF <expression>
    - FUNC:function_name

    Uses asteval for safe evaluation of <expression> if available.
    Functions registered via register_next_step_function are called for FUNC: rules.

    Args:
        rule: The rule string.
        instance_state: The current state of the choreography instance.
        next_step_functions: Dictionary mapping function names to callable functions.

    Returns:
        The ID of the next step, or None to proceed sequentially.
    """
    rule = rule.strip()
    logging.debug(f"Evaluating next step rule: '{rule}'")

    # --- Handle FUNC: --- #
    func_prefix = "FUNC:"
    if rule.startswith(func_prefix):
        func_name = rule[len(func_prefix) :].strip()
        if not func_name:
            logging.warning(f"Invalid FUNC rule (missing function name): {rule}")
            return None
        target_func = next_step_functions.get(func_name)
        if not target_func:
            logging.error(f"FUNC rule error: Function '{func_name}' not registered.")
            return None  # Error: function not found
        try:
            # Pass the entire instance state dictionary to the function
            next_step_id = target_func(instance_state)
            if next_step_id is not None and not isinstance(next_step_id, str):
                logging.error(
                    f"FUNC '{func_name}' did not return a string step ID or None (returned {type(next_step_id)}). Proceeding sequentially."
                )
                return None
            logging.debug(f"FUNC '{func_name}' returned next step: {next_step_id}")
            return next_step_id  # Can be None or a string step_id
        except Exception as e:
            logging.exception(f"Error executing FUNC '{func_name}': {e}")
            return None  # Error during function execution

    # --- Handle GOTO: and COND_GOTO: --- #
    goto_prefix = "GOTO:"
    cond_goto_prefix = "COND_GOTO:"
    if_separator = " IF "

    target_step_id: Optional[str] = None
    condition_str: Optional[str] = None
    condition_met: bool = True  # Default to True for unconditional GOTO

    if rule.startswith(cond_goto_prefix):
        rule_body = rule[len(cond_goto_prefix) :].strip()
        if if_separator in rule_body:
            parts = rule_body.split(if_separator, 1)
            target_step_id = parts[0].strip()
            condition_str = parts[1].strip()
        else:
            logging.warning(f"Invalid COND_GOTO rule (missing IF condition): {rule}")
            return None
    elif rule.startswith(goto_prefix):
        target_step_id = rule[len(goto_prefix) :].strip()
        # condition_str remains None, condition_met remains True
    else:
        logging.warning(
            f"Invalid next_step_logic format (must start with GOTO:, COND_GOTO:, or FUNC:): {rule}"
        )
        return None

    if not target_step_id:
        logging.warning(f"Invalid GOTO/COND_GOTO rule (missing target step ID): {rule}")
        return None

    # Evaluate condition if present (using asteval if available)
    if condition_str:
        if not ASTEVAL_AVAILABLE or asteval is None:
            logging.error(
                f"Cannot evaluate condition '{condition_str}': asteval library not available."
            )
            return None  # Cannot evaluate condition, cannot proceed

        try:
            # Prepare safe symbol table for evaluation
            aeval = asteval.Interpreter()
            # Pass relevant parts of instance state
            aeval.symtable["context"] = instance_state.get("context")
            aeval.symtable["scratchpad"] = instance_state.get("scratchpad", {})
            aeval.symtable["participants"] = instance_state.get("participants", {})
            aeval.symtable["instance_id"] = instance_state.get("instance_id")
            aeval.symtable["defaults"] = instance_state.get("definition_defaults", {})
            aeval.symtable["len"] = len

            eval_result = aeval(condition_str)
            if aeval.error:
                err_details = ", ".join([err.msg for err in aeval.error])
                logging.error(
                    f"Error evaluating condition '{condition_str}': {err_details}"
                )
                condition_met = False
            else:
                condition_met = bool(eval_result)
                logging.debug(
                    f"Condition '{condition_str}' evaluated to: {condition_met}"
                )

        except Exception as e:
            logging.exception(
                f"Unexpected error during asteval evaluation of '{condition_str}': {e}"
            )
            condition_met = False

    # Return target step ID only if condition is met
    if condition_met:
        logging.debug(f"Condition met, returning target step ID: {target_step_id}")
        return target_step_id
    else:
        logging.debug("Condition NOT met, proceeding sequentially.")
        return None  # Condition not met or evaluated to False
