# -*- coding: utf-8 -*-
"""
Example definitions for the CAW Choreography system.

Provides example choreography definitions, payload templates,
validation rules, and context update definitions.

Related Files:
- person_suit.core.actors.choreography.engine.py
"""

from .base import ChoreographyDefinition
from .base import ChoreographyStep

# --- Example Payload Templates (as strings) ---
BASIC_REQUEST_TEMPLATE = """
{
    "request_id": "{{ instance_id }}",
    "target_key": "{{ context.custom_context.data_key | default('DEFAULT_KEY') }}",
    "timestamp": {{ context.timestamp | default(0) }},
    "priority": {{ scratchpad.priority | default(5) }}
}
"""

# --- Example Validation Rules (as dicts - JSON Schema) ---
BASIC_RESPONSE_SCHEMA = {
    "type": "object",
    "properties": {
        "status": {"type": "string", "enum": ["SUCCESS", "ERROR", "PENDING"]},
        "value": {
            "type": ["string", "number", "object", "array", "null"]
        },  # Allow various types for value
        "error_message": {"type": "string"},
    },
    "required": ["status"],
    # Conditional requirement: error_message required if status is ERROR
    "if": {"properties": {"status": {"const": "ERROR"}}},
    "then": {"required": ["error_message"]},
}

# --- Example Context Update Definition ---
# Option 1: Simple dict with Jinja values
PROCESS_DATA_CONTEXT_UPDATE_DICT = {
    "processing_status": "complete",
    "last_processed_value": "{{ scratchpad.provider_response_payload.value | default('N/A') }}",
    "items_processed": "{{ scratchpad.processed_items | length }}",
}

# --- Example Choreography Definition ---
GET_DATA_REQUEST_CHOREO_V2 = ChoreographyDefinition(
    choreography_id="get_data_request_v2",
    name="Get Data Request (Jinja/JSON Schema)",
    description="A requester asks a provider for data, using Jinja2 templates and JSON Schema validation.",
    roles={"requester": "RequestingActor", "provider": "ProvidingActor"},
    steps=[
        ChoreographyStep(
            step_id="ask_for_data",
            sender="requester",
            receiver="provider",
            message_type="GET_DATA_REQUEST",
            payload_template="basic_request_template_name",  # Name registered via load_payload_template
            expected_reply_type="DATA_RESPONSE",
            reply_validation_rule="basic_response_schema_name",  # Name registered via load_validation_rule
            store_reply_as="provider_response_payload",
            timeout_seconds=5.0,
            context_updates_on_success="process_data_context_update_name",  # Name registered via load_context_update_definition
            on_error={
                "TIMEOUT": "RETRY",  # Retry once on timeout
                "INVALID_REPLY": "FAIL",
                "PARTICIPANT_UNAVAILABLE": "FAIL",
            },
        ),
        ChoreographyStep(
            step_id="confirm_receipt",
            sender="requester",
            receiver="requester",  # Send message to self (e.g., log or trigger internal processing)
            message_type="LOG_DATA_RECEIPT",
            # Condition: Only run if the previous step stored the reply
            conditions=[
                {"type": "scratchpad_exists", "key": "provider_response_payload"}
            ],
            # No reply expected for this step
            payload_template=None,  # Minimal payload
        ),
    ],
)

# --- Example FUNC: Handler Name ---
# Define the name used in the FUNC: directive
ADAPTIVE_HANDLER_NAME = "adaptive_data_priority_handler"

# --- NEW: Adaptive Choreography Definition ---
ADAPTIVE_DATA_REQUEST_CHOREO = ChoreographyDefinition(
    choreography_id="adaptive_data_request_v1",
    name="Adaptive Data Request (COND_GOTO/FUNC)",
    description="Demonstrates conditional routing and function-based steps.",
    roles={
        "requester": "RequestingActor",
        "provider": "DataStoreActor",
        "notifier": "NotificationActor",
    },
    steps=[
        ChoreographyStep(
            step_id="initial_request",
            sender="requester",
            receiver="provider",
            message_type="GET_DATA_REQUEST",
            payload_template="basic_request_template_name",
            expected_reply_type="DATA_RESPONSE",
            reply_validation_rule="basic_response_schema_name",
            store_reply_as="provider_response_payload",
            timeout_seconds=5.0,
            # If priority in scratchpad > 5, go directly to high_priority step
            next_step_logic="COND_GOTO:high_priority_handler IF scratchpad.priority > 5",
            on_error={
                "TIMEOUT": "GOTO:notify_timeout",
                "INVALID_REPLY": "FAIL",
            },
        ),
        ChoreographyStep(
            step_id="standard_handler",  # Runs if priority <= 5
            sender="requester",
            receiver="requester",
            message_type="LOG_STANDARD_RECEIPT",
            # Determine next step using a registered function
            next_step_logic=f"FUNC:{ADAPTIVE_HANDLER_NAME}",
        ),
        ChoreographyStep(
            step_id="high_priority_handler",  # Runs if priority > 5
            sender="requester",
            receiver="requester",
            message_type="LOG_HIGH_PRIORITY_RECEIPT",
            # Also uses the function handler after logging
            next_step_logic=f"FUNC:{ADAPTIVE_HANDLER_NAME}",
        ),
        ChoreographyStep(
            step_id="notify_success",  # Possible target for FUNC handler
            sender="requester",
            receiver="notifier",
            message_type="SEND_NOTIFICATION",
            payload_template=None,  # Simple notification
        ),
        ChoreographyStep(
            step_id="notify_timeout",  # Target for timeout GOTO
            sender="requester",
            receiver="notifier",
            message_type="SEND_TIMEOUT_ALERT",
            payload_template=None,  # Simple alert
        ),
        ChoreographyStep(
            step_id="final_cleanup",  # Possible target for FUNC handler
            sender="requester",
            receiver="requester",
            message_type="PERFORM_CLEANUP",
        ),
    ],
)
