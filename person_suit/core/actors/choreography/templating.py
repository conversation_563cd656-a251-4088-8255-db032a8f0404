# -*- coding: utf-8 -*-
"""
Templating functionality for the CAW Choreography system.

Provides methods for payload construction and context updates using Jinja2.

Related Files:
- person_suit.core.actors.choreography.engine.py
- person_suit.core.actors.choreography.execution.py
"""

import json
import logging
from dataclasses import replace
from typing import Any
from typing import Dict
from typing import Optional
from typing import Union

# External Dependencies (Ensure these are installed)
try:
    from jinja2 import Environment
    from jinja2 import TemplateNotFound
    from jinja2 import TemplateSyntaxError
    from jinja2 import select_autoescape
except ImportError:
    logging.critical("Jinja2 not found. Please install it: pip install Jinja2")

    # Mock or raise to prevent execution without dependency
    class Environment:
        pass

    TemplateNotFound = Exception
    TemplateSyntaxError = Exception

    # Provide dummy functions if needed
    def select_autoescape(*args, **kwargs):
        pass


def construct_payload(
    jinja_env,
    template_name: Optional[str],
    template_registry: Dict[str, str],
    instance_state: Dict,
) -> Any:
    """
    Constructs message payload using a named Jinja2 template.

    Args:
        jinja_env: The Jinja2 environment
        template_name: Name of the template to use
        template_registry: Dictionary of template names to template strings
        instance_state: Current choreography instance state

    Returns:
        The constructed payload
    """
    if not template_name:
        # Return minimal payload if no template specified
        return {"instance_id": instance_state["instance_id"]}

    template_string = template_registry.get(template_name)
    if not template_string:
        logging.error(f"Payload template '{template_name}' not found in registry.")
        # Return error payload
        return {
            "instance_id": instance_state["instance_id"],
            "error": f"Payload template '{template_name}' not found.",
        }

    # Prepare context for Jinja rendering
    render_context = {
        "context": instance_state.get("context"),
        "scratchpad": instance_state.get("scratchpad", {}),
        "participants": instance_state.get("participants", {}),
        "instance_id": instance_state.get("instance_id"),
        "defaults": instance_state.get("definition_defaults", {}),
        # Add other relevant state parts if needed
    }

    try:
        template = jinja_env.from_string(template_string)
        # Use render() for sync environment, render_async() for async
        rendered_payload_str = template.render(render_context)
        # Assuming payload should be JSON/dict, parse the rendered string
        try:
            constructed_payload = json.loads(rendered_payload_str)
        except json.JSONDecodeError as json_err:
            logging.error(
                f"Failed to decode rendered payload template '{template_name}' as JSON: {json_err}. "
                f"Rendered string: '{rendered_payload_str[:200]}...'"
            )
            return {
                "instance_id": instance_state["instance_id"],
                "error": f"Rendered payload for '{template_name}' is not valid JSON.",
            }

        logging.debug(
            f"Constructed payload from Jinja2 template '{template_name}': {constructed_payload}"
        )
        return constructed_payload
    except TemplateSyntaxError as e:
        logging.exception(
            f"Syntax error rendering payload template '{template_name}': {e}"
        )
        return {
            "instance_id": instance_state["instance_id"],
            "error": f"Syntax error in payload template '{template_name}': {e}",
        }
    except TemplateNotFound as e:
        logging.exception(
            f"Template not found error during payload construction '{template_name}': {e}"
        )
        return {
            "instance_id": instance_state["instance_id"],
            "error": f"Template not found error for '{template_name}': {e}",
        }
    except Exception as e:
        # Catch other potential rendering errors
        logging.exception(
            f"Unexpected error rendering payload template '{template_name}': {e}"
        )
        return {
            "instance_id": instance_state["instance_id"],
            "error": f"Unexpected error rendering payload '{template_name}': {e}",
        }


def update_context(
    jinja_env,
    updates_ref: Optional[Union[str, Dict]],
    context_update_defs: Dict[str, Union[str, Dict]],
    context,
    instance_state: Dict,
):
    """
    Applies updates to context immutably, resolving Jinja2 templates if needed.

    Args:
        jinja_env: The Jinja2 environment
        updates_ref: Reference to updates, can be dict or template name
        context_update_defs: Dictionary mapping update definition names to templates/dicts
        context: Current context object
        instance_state: Current choreography instance state

    Returns:
        Updated context object
    """
    if not updates_ref:
        return context

    updates_definition = None
    if isinstance(updates_ref, str):
        # It's a reference name, look it up
        updates_definition = context_update_defs.get(updates_ref)
        if not updates_definition:
            logging.error(
                f"[{instance_state['instance_id']}] Context update definition '{updates_ref}' not found."
            )
            return context  # Return original context if definition missing
    elif isinstance(updates_ref, dict):
        # It's the definition dict directly
        updates_definition = updates_ref
    else:
        logging.error(
            f"[{instance_state['instance_id']}] Invalid type for context_updates_on_success: {type(updates_ref)}. Expected dict or str name."
        )
        return context

    logging.debug(
        f"[{instance_state['instance_id']}] Attempting context update using definition: {updates_ref}"
    )

    # Prepare context for Jinja rendering
    render_context = {
        "context": context,  # Current context
        "scratchpad": instance_state.get("scratchpad", {}),
        "participants": instance_state.get("participants", {}),
        "instance_id": instance_state.get("instance_id"),
        "defaults": instance_state.get("definition_defaults", {}),
    }

    resolved_updates = {}
    try:
        # If definition is a string, assume it's a Jinja template rendering to a JSON dict string
        if isinstance(updates_definition, str):
            template_string = updates_definition
            template = jinja_env.from_string(template_string)
            rendered_update_str = template.render(render_context)
            try:
                resolved_updates = json.loads(rendered_update_str)
                if not isinstance(resolved_updates, dict):
                    raise ValueError(
                        "Rendered context update template did not produce a JSON object."
                    )
            except (json.JSONDecodeError, ValueError) as json_err:
                logging.error(
                    f"[{instance_state['instance_id']}] Failed to decode rendered context update template "
                    f"'{updates_ref}' as JSON dict: {json_err}. Rendered: '{rendered_update_str[:200]}...'"
                )
                return context  # Return original on error
        # If definition is already a dict, resolve template strings within its values
        elif isinstance(updates_definition, dict):
            for key, value_template in updates_definition.items():
                if isinstance(value_template, str):
                    # Assume values containing "{{" are templates to render
                    if "{{" in value_template and "}}" in value_template:
                        value_template_obj = jinja_env.from_string(value_template)
                        resolved_updates[key] = value_template_obj.render(
                            render_context
                        )
                    else:
                        resolved_updates[key] = value_template  # Use as literal string
                else:
                    resolved_updates[key] = (
                        value_template  # Use non-string value directly
                    )
        else:
            # Should not happen based on initial check
            logging.error(
                f"Internal error: Invalid type for updates_definition: {type(updates_definition)}"
            )
            return context

    except (TemplateSyntaxError, TemplateNotFound, Exception) as render_err:
        logging.exception(
            f"[{instance_state['instance_id']}] Error rendering context update template '{updates_ref}': {render_err}"
        )
        return context  # Return original on rendering error

    # Apply the resolved updates immutably
    try:
        # Focus on updating custom_context for simplicity, but could update others
        current_custom = context.custom_context or {}
        # Merge resolved updates into custom context (resolved updates take precedence)
        new_custom = {**current_custom, **resolved_updates}

        # Create new context using replace for immutability
        import asyncio  # Import here to avoid circular imports

        new_context = replace(
            context,
            custom_context=new_custom,
            timestamp=asyncio.get_event_loop().time(),
            # Add other top-level fields here if updates target them
        )
        logging.debug(
            f"[{instance_state['instance_id']}] Context updated successfully."
        )
        return new_context
    except Exception as e:
        logging.exception(
            f"[{instance_state['instance_id']}] Error creating updated context object: {e}. Returning original context."
        )
        return context
