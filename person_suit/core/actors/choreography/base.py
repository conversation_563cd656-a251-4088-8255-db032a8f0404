# -*- coding: utf-8 -*-
"""
Core data structures and types for the CAW Choreography system.

Defines the fundamental data structures used in the choreography protocol system,
including ChoreographyStep and ChoreographyDefinition.

Related Files:
- docs/CAW_IMPLEMENTATION_OVERVIEW.md (Sec 5)
- person_suit.core.actors.actor_system.py
- person_suit.core.caw.schemas.py
"""

import uuid
from dataclasses import dataclass
from dataclasses import field
from typing import Any
from typing import Dict
from typing import List
from typing import Optional
from typing import Union

# --- Type Aliases ---
ActorID = str
ChoreographyID = str
MessagePayload = Any  # The content of the message
ErrorCondition = str  # e.g., "TIMEOUT", "INVALID_REPLY", "CONDITION_FAIL"
ErrorAction = str  # e.g., "FAIL", "RETRY", "SKIP", "GOTO:<step_id>"


@dataclass
class ChoreographyStep:
    """
    Represents a single, refined step in a choreography protocol.
    Includes details for message construction, reply handling, context updates,
    and error management.
    """

    step_id: str
    sender: ActorID  # ID (or role name) of the actor sending the message
    receiver: ActorID  # ID (or role name) of the actor receiving the message
    message_type: str  # Identifier for the type of message to send
    # Name of the Jinja2 template to use for generating the payload
    payload_template: Optional[str] = None
    # Optional: Conditions (evaluated against instance state/context) for this step to execute
    conditions: Optional[List[Dict[str, Any]]] = (
        None  # e.g., [{'type': 'context_match', 'key': 'status', 'value': 'READY'}]
    )

    # Reply Handling
    expected_reply_type: Optional[str] = None  # If None, no reply awaited
    # Name of the JSON Schema definition to use for validating the reply payload
    reply_validation_rule: Optional[str] = None
    store_reply_as: Optional[str] = (
        None  # Key to store validated reply payload in instance state
    )
    timeout_seconds: Optional[float] = (
        None  # Timeout for waiting for a reply (if expected)
    )

    # State & Context Updates
    # Name of template/dict defining updates to instance context on success
    context_updates_on_success: Optional[Union[str, Dict[str, Any]]] = (
        None  # Can be direct dict or template name
    )

    # Error Handling (replaces simple on_timeout)
    on_error: Optional[Dict[ErrorCondition, ErrorAction]] = (
        None  # Map error conditions to actions
    )
    # Example: {'TIMEOUT': 'FAIL', 'INVALID_REPLY': 'RETRY'}

    # Dynamic Next Step Logic
    # Optional rule/string defining how to determine the next step dynamically.
    # If None, proceeds sequentially. If evaluation fails, proceeds sequentially.
    # Example syntax: "GOTO:alt_step IF scratchpad.result == 'alt'", "FUNC:calculate_next"
    next_step_logic: Optional[str] = None


@dataclass
class ChoreographyDefinition:
    """
    Defines a complete choreography protocol.
    """

    choreography_id: ChoreographyID
    name: str
    description: Optional[str] = None
    # Sequence of steps defining the interaction
    steps: List[ChoreographyStep] = field(default_factory=list)
    # Optional: Roles involved and the actor types that can fulfill them
    # Value type is flexible, could be str, Type, etc. for validation. Use str for simplicity now.
    roles: Optional[Dict[str, str]] = (
        None  # e.g., {'client': 'ClientActorType', 'server': 'ServiceActorType'}
    )
    # Optional: Default Jinja2 template context variables for this choreography
    default_template_context: Optional[Dict[str, Any]] = None


class ChoreographyError(Exception):
    """Custom exception for choreography execution errors."""

    pass


def generate_correlation_id(instance_id: str, step_id: str) -> str:
    """Generate a unique correlation ID for a choreography step message."""
    return f"choreo_{instance_id}_{step_id}_{uuid.uuid4().hex[:8]}"
