# -*- coding: utf-8 -*-
"""
Utility functions for the CAW Choreography system.

Provides helper methods used across the choreography subsystem.

Related Files:
- person_suit.core.actors.choreography.engine.py
- person_suit.core.actors.choreography.execution.py
"""

import logging
from typing import Dict
from typing import Optional


def resolve_participant(name_or_id: str, instance_state: Dict) -> Optional[str]:
    """
    Resolves a role name or logical ID to a concrete ActorID.

    Args:
        name_or_id: The role name or ID to resolve
        instance_state: The current state of the choreography instance

    Returns:
        The resolved ActorID or None if not found
    """
    resolved_id = instance_state["participants"].get(name_or_id)
    if not resolved_id:
        logging.error(
            f"[{instance_state['instance_id']}] Failed to resolve participant: '{name_or_id}'"
        )
    return resolved_id


def evaluate_conditions(conditions: Optional[list], instance_state: Dict) -> bool:
    """
    Evaluates step conditions against instance state/context.

    Args:
        conditions: List of condition dictionaries to evaluate
        instance_state: The current state of the choreography instance

    Returns:
        True if all conditions are met, False otherwise
    """
    if not conditions:
        return True

    context = instance_state.get("context")
    scratchpad: Dict = instance_state.get("scratchpad", {})

    for condition in conditions:
        cond_type = condition.get("type")
        key = condition.get("key")
        expected_value = condition.get("value")

        if cond_type == "context_match":
            if not key or not context:
                logging.warning("Invalid context_match: missing key/context")
                return False
            # Simple lookup for now, enhance if needed
            actual_value = getattr(
                context,
                key,
                context.custom_context.get(key) if context.custom_context else None,
            )
            if actual_value != expected_value:
                logging.debug(
                    f"Cond FAIL: Context {key}={actual_value} != {expected_value}"
                )
                return False
        elif cond_type == "scratchpad_exists":
            if not key or key not in scratchpad:
                logging.debug(f"Cond FAIL: Scratchpad key '{key}' missing")
                return False
        elif cond_type == "scratchpad_match":
            if not key or scratchpad.get(key) != expected_value:
                logging.debug(
                    f"Cond FAIL: Scratchpad {key}={scratchpad.get(key)} != {expected_value}"
                )
                return False
        else:
            logging.warning(f"Unsupported cond type: {cond_type}")
            return False

    return True
