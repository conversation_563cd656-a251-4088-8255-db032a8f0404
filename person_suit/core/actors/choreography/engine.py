# -*- coding: utf-8 -*-
"""
Core engine for the CAW Choreography system.

Provides the ChoreographyEngine class responsible for loading, starting,
and managing choreography instances.

Related Files:
- person_suit.core.actors.choreography.execution.py
- person_suit.core.actors.choreography.dynamic.py
- person_suit.core.actors.choreography.templating.py
- person_suit.core.actors.choreography.validation.py
"""

import asyncio
import logging
import uuid
from collections import defaultdict
from typing import Any
from typing import Callable
from typing import Dict
from typing import List
from typing import Optional
from typing import Tuple
from typing import Union

# External Dependencies
from jinja2 import Environment
from jinja2 import select_autoescape

# Local imports
from .base import ChoreographyDefinition
from .base import ChoreographyError
from .base import ChoreographyID
from .execution import execute_instance

# Remove old import
# from ..actor_system import ActorSystemInterface 
# Add new import from local package
from .interfaces import ActorSystemInterface


class ChoreographyEngine:
    """
    Manages the execution of choreography instances based on definitions.
    Uses Jinja2 for payload templating and JSON Schema for reply validation.
    """

    def __init__(self, actor_system: ActorSystemInterface):
        """
        Initialize the choreography engine.

        Args:
            actor_system: The actor system interface
        """
        self._actor_system = actor_system
        self._definitions: Dict[ChoreographyID, ChoreographyDefinition] = {}
        # Stores the state of each running instance (context, scratchpad, step index etc.)
        self._running_instances: Dict[str, Dict[str, Any]] = {}
        # Stores the asyncio Task executing each instance
        self._tasks: Dict[str, asyncio.Task] = {}
        # Lock for modifying instance/task dictionaries concurrently
        self._lock = asyncio.Lock()

        # --- Enhanced Registries ---
        # Stores raw Jinja2 template strings
        self._payload_templates: Dict[str, str] = {}
        # Stores JSON Schema definitions (as dictionaries)
        self._validation_rules: Dict[str, Dict] = {}
        # Stores context update definitions (can be dict or Jinja2 template name)
        self._context_update_defs: Dict[str, Union[str, Dict]] = {}
        # Registry for dynamic next step functions
        self._next_step_functions: Dict[str, Callable[[Dict], Optional[str]]] = {}

        # --- Jinja2 Environment ---
        # Consider adding loaders (e.g., FileSystemLoader) for external templates
        self._jinja_env = Environment(
            # loader=FileSystemLoader('path/to/templates'), # Example for file loading
            autoescape=select_autoescape(["html", "xml"]),  # Basic autoescaping
            enable_async=False,  # Set to True if templates need async operations
        )
        # Add custom filters/globals to Jinja Env if needed
        # self._jinja_env.globals['custom_global'] = lambda x: ...
        # self._jinja_env.filters['custom_filter'] = lambda x: ...

        logging.info("ChoreographyEngine initialized with Jinja2 Env and registries.")

    def load_definition(self, definition: ChoreographyDefinition):
        """
        Loads a choreography definition into the engine.

        Args:
            definition: The choreography definition to load
        """
        if definition.choreography_id in self._definitions:
            logging.warning(f"Redefining choreography: {definition.choreography_id}")
        self._definitions[definition.choreography_id] = definition
        logging.info(
            f"Loaded choreography definition: {definition.choreography_id} ('{definition.name}')"
        )

    def load_payload_template(self, template_name: str, template_string: str):
        """
        Loads a named Jinja2 payload template string.

        Args:
            template_name: Name to register the template under
            template_string: The Jinja2 template string
        """
        if template_name in self._payload_templates:
            logging.warning(f"Redefining payload template: {template_name}")
        # Optional: Pre-compile template for performance?
        # try:
        #     self._jinja_env.from_string(template_string)
        # except TemplateSyntaxError as e:
        #     logging.error(f"Syntax error in payload template '{template_name}': {e}")
        #     # Decide whether to store invalid template or raise error
        #     raise ValueError(f"Invalid syntax in template '{template_name}'") from e
        self._payload_templates[template_name] = template_string
        logging.info(f"Loaded payload template: {template_name}")

    def load_validation_rule(self, rule_name: str, schema_definition: Dict):
        """
        Loads a named JSON Schema validation rule definition (as dict).

        Args:
            rule_name: Name to register the validation rule under
            schema_definition: The JSON Schema definition
        """
        if rule_name in self._validation_rules:
            logging.warning(f"Redefining validation rule: {rule_name}")
        # Optional: Validate the schema itself upon loading
        try:
            # Check if it's a valid schema (meta-validation)
            # Requires jsonschema V4+ validator: Draft7Validator.check_schema(schema_definition)
            # Pass for now, validation happens during use.
            pass
        except Exception as e:
            logging.error(f"Invalid JSON Schema for rule '{rule_name}': {e}")
            raise ValueError(f"Invalid schema for rule '{rule_name}'") from e
        self._validation_rules[rule_name] = schema_definition
        logging.info(f"Loaded validation rule: {rule_name}")

    def load_context_update_definition(
        self, definition_name: str, definition: Union[str, Dict]
    ):
        """
        Loads a named definition for context updates (can be a direct dict or a Jinja2 template name).

        Args:
            definition_name: Name to register the context update definition under
            definition: The context update definition (dict or template string)
        """
        if definition_name in self._context_update_defs:
            logging.warning(f"Redefining context update definition: {definition_name}")
        self._context_update_defs[definition_name] = definition
        logging.info(f"Loaded context update definition: {definition_name}")

    def register_next_step_function(
        self, name: str, func: Callable[[Dict], Optional[str]]
    ):
        """
        Registers a function that can be called via 'FUNC:' in next_step_logic.

        The function receives the current instance_state dict and should return
        the next step ID (str) or None to proceed sequentially.

        Args:
            name: Name to register the function under
            func: The function to register
        """
        if name in self._next_step_functions:
            logging.warning(f"Redefining next step function: {name}")
        # TODO: Add validation? Check signature?
        self._next_step_functions[name] = func
        logging.info(f"Registered next step function: {name}")

    async def start_choreography(
        self,
        choreography_id: ChoreographyID,
        initial_context,
        participants: Dict[
            str, str
        ],  # Mapping role name or logical name to specific ActorID
        # Optional: Initial scratchpad data
        initial_scratchpad: Optional[Dict[str, Any]] = None,
    ) -> str:
        """
        Starts a new instance of a defined choreography.

        Args:
            choreography_id: The ID of the choreography definition to use.
            initial_context: The initial context for this instance.
            participants: A dictionary mapping role names (from definition) or logical names
                          to concrete ActorIDs for this instance.
            initial_scratchpad: Optional initial data for the instance's scratchpad.

        Returns:
            The unique instance ID of the started choreography.

        Raises:
            ValueError: If choreography ID is unknown, participants invalid, or roles mismatch.
            ChoreographyError: If instance creation or task scheduling fails.
        """
        definition = self._definitions.get(choreography_id)
        if not definition:
            logging.error(f"Cannot start choreography: Unknown ID '{choreography_id}'")
            raise ValueError(f"Unknown choreography ID: {choreography_id}")

        # --- Participant Validation (Addresses TODO) ---
        if definition.roles:
            defined_roles = set(definition.roles.keys())
            provided_roles = set(participants.keys())
            if defined_roles != provided_roles:
                missing = defined_roles - provided_roles
                extra = provided_roles - defined_roles
                error_msg = (
                    f"Participant roles mismatch for choreography '{choreography_id}'."
                )
                if missing:
                    error_msg += f" Missing roles: {missing}."
                if extra:
                    error_msg += f" Unexpected roles: {extra}."
                logging.error(error_msg)
                raise ValueError(error_msg)
            # Optional: Further validation based on role type string if needed
            # for role, actor_id in participants.items():
            #     expected_type_str = definition.roles.get(role)
            #     # ... logic to check if actor_id corresponds to expected_type_str ...
        # Ensure all participant ActorIDs are provided
        if not all(participants.values()):
            missing_ids = [
                role for role, actor_id in participants.items() if not actor_id
            ]
            error_msg = f"Missing ActorIDs for roles: {missing_ids} in choreography '{choreography_id}'."
            logging.error(error_msg)
            raise ValueError(error_msg)
        # --- End Validation ---

        instance_id = f"choreo_{choreography_id}_{uuid.uuid4().hex[:12]}"
        logging.info(f"CHOREOGRAPHY ENGAGED: {instance_id} (Type: {choreography_id})")

        instance_state = {
            "instance_id": instance_id,
            "choreography_id": choreography_id,
            "context": initial_context,
            "participants": participants,
            "current_step_index": 0,
            "scratchpad": initial_scratchpad or {},  # Use provided or empty dict
            "status": "RUNNING",
            "retry_counts": defaultdict(int),  # Track retries per step
            "history": [],  # Track step execution history
            "definition_defaults": definition.default_template_context
            or {},  # Store defaults
        }

        # Acquire lock before modifying shared instance/task dictionaries
        async with self._lock:
            if instance_id in self._running_instances:
                # Extremely unlikely due to UUID, but safety check
                raise ChoreographyError(
                    f"Choreography instance ID collision: {instance_id}"
                )

            # Store instance state
            self._running_instances[instance_id] = instance_state

            # Create and store the execution task
            task = asyncio.create_task(
                execute_instance(
                    instance_id,
                    self,
                    self._actor_system,
                    self._definitions,
                    self._running_instances,
                    self._tasks,
                    self._jinja_env,
                    self._payload_templates,
                    self._validation_rules,
                    self._context_update_defs,
                    self._next_step_functions,
                    self._lock,
                )
            )
            self._tasks[instance_id] = task

            # Add a callback to clean up when the task finishes (or is cancelled)
            task.add_done_callback(lambda t: self._cleanup_instance(instance_id, t))

        logging.debug(f"Scheduled execution task for instance {instance_id}")
        return instance_id

    async def stop_choreography(self, instance_id: str):
        """
        Requests graceful termination of a running choreography instance.

        Args:
            instance_id: The ID of the choreography instance to stop.

        Raises:
            LookupError: If the instance_id is not found or not running.
            ChoreographyError: If cancellation fails.
        """
        logging.info(f"Attempting to stop choreography instance: {instance_id}")
        async with self._lock:
            instance_state = self._running_instances.get(instance_id)
            task = self._tasks.get(instance_id)

            if not instance_state or not task:
                logging.warning(
                    f"Cannot stop choreography: Instance {instance_id} not found or already stopped."
                )
                raise LookupError(
                    f"Choreography instance {instance_id} not found or not running."
                )

            if task.done():
                logging.info(
                    f"Choreography instance {instance_id} has already completed. No stop action needed."
                )
                # Optionally clean up here if not done by callback yet
                self._cleanup_instance(instance_id, task)  # Ensure cleanup
                return

            # Update status immediately
            instance_state["status"] = "STOPPING"
            logging.debug(f"Marked instance {instance_id} as STOPPING.")

        # Cancel the task outside the lock to avoid potential deadlocks if
        # the task cleanup tries to acquire the lock.
        try:
            logging.debug(f"Cancelling execution task for instance {instance_id}...")
            task.cancel()
            # Allow event loop iteration for cancellation to propagate
            await asyncio.sleep(0)
            logging.info(
                f"Cancellation requested for choreography instance {instance_id}."
            )
            # Note: Cleanup happens in the task's done callback (_cleanup_instance)
        except Exception as e:
            # This might happen if the task finished between lock release and cancel
            logging.exception(
                f"Error requesting cancellation for instance {instance_id}: {e}"
            )
            # Update status back? Or let cleanup handle?
            async with self._lock:  # Need lock to safely update state
                if instance_id in self._running_instances:  # Check if still exists
                    self._running_instances[instance_id]["status"] = "ERROR_STOPPING"
            raise ChoreographyError(
                f"Failed to request cancellation for {instance_id}: {e}"
            ) from e

    async def signal_choreography(self, instance_id: str, signal_data: Any):
        """
        Sends an external signal/data to a running choreography instance.

        This basic implementation stores the signal data in the instance's
        scratchpad under the key 'last_signal'. Steps can then check this key.

        Args:
            instance_id: The ID of the target choreography instance.
            signal_data: The data associated with the signal.

        Raises:
            LookupError: If the instance_id is not found or not running.
        """
        logging.info(
            f"Received signal for choreography instance {instance_id}. Data: {signal_data}"
        )
        async with self._lock:
            instance_state = self._running_instances.get(instance_id)
            task = self._tasks.get(instance_id)

            if not instance_state or not task or task.done():
                logging.warning(
                    f"Cannot signal choreography: Instance {instance_id} not found or not running."
                )
                raise LookupError(
                    f"Choreography instance {instance_id} not found or not running."
                )

            if instance_state["status"] != "RUNNING":
                logging.warning(
                    f"Received signal for non-RUNNING instance {instance_id} (Status: {instance_state['status']}). Signal stored but might not be processed."
                )

            # Store the signal data in the scratchpad
            instance_state["scratchpad"]["last_signal"] = signal_data
            # Optionally add a timestamp
            instance_state["scratchpad"]["last_signal_timestamp"] = (
                asyncio.get_event_loop().time()
            )
            logging.debug(
                f"Stored signal data in scratchpad for instance {instance_id}."
            )

    def _cleanup_instance(self, instance_id: str, task: asyncio.Task):
        """
        Callback function to clean up resources when a choreography task finishes.
        NOTE: This runs SYNCHRONOUSLY as a task callback. Avoid blocking/async operations.
        """
        # This callback runs synchronously, so direct modification is okay,
        # but locking is still needed if other async methods access these dicts.
        # Using a non-async lock or careful structure is required if strict safety is needed.
        # For simplicity here, we assume this callback won't race badly with other async ops.
        # Consider scheduling an async cleanup task if complex async ops are needed here.

        logging.debug(
            f"Choreography task for instance {instance_id} finished. Cleaning up..."
        )
        # Simulate lock acquisition context if needed (complex):
        # lock = self._lock
        # if lock.locked(): schedule_cleanup() else: with lock: cleanup_now()

        instance_state = self._running_instances.pop(instance_id, None)
        self._tasks.pop(instance_id, None)
        # Clean up pending reply waiters associated with this instance (if any left)
        # This is tricky as _pending_replies is global. Need a way to associate waiters
        # with instances or carefully manage their lifecycle.
        # Simple approach: Iterate and remove matching prefixes (can be slow/imprecise).
        # Better: Store instance_id in _pending_replies value?
        # For now, skip complex waiter cleanup in this callback. Timeout should handle most.

        if instance_state:
            # Log final status based on task exception state
            final_status = instance_state.get(
                "status", "UNKNOWN"
            )  # Status set by execute loop
            task_exception = None
            if not task.cancelled():
                try:
                    task_exception = task.exception()
                except asyncio.InvalidStateError:
                    pass  # Task completed normally

            if task.cancelled():
                # Status might be STOPPING or CANCELLED
                final_status = instance_state.get(
                    "status", "CANCELLED"
                )  # Respect status set by stop()
                if final_status not in ["STOPPED", "STOPPING", "CANCELLED"]:
                    final_status = "CANCELLED"  # Default if not set correctly
                logging.info(
                    f"Choreography instance {instance_id} finished with status: {final_status} (Cancelled)."
                )
            elif task_exception:
                final_status = "FAILED"  # Override status if task ended with exception
                logging.error(
                    f"Choreography instance {instance_id} finished with status: {final_status}. Exception: {task_exception}"
                )
            else:
                # Loop finished normally, check final status set by _execute_instance
                final_status = instance_state.get("status", "COMPLETED_UNKNOWN")
                logging.info(
                    f"Choreography instance {instance_id} finished with status: {final_status}."
                )
        else:
            logging.warning(
                f"Cleanup called for instance {instance_id}, but it was already removed."
            )

    # --- Public Interface Methods ---
    async def get_instance_state(self, instance_id: str) -> Optional[Dict[str, Any]]:
        """
        Returns a copy of the current state of a choreography instance.

        Args:
            instance_id: The ID of the choreography instance.

        Returns:
            A copy of the instance state, or None if not found.
        """
        async with self._lock:
            instance_state = self._running_instances.get(instance_id)
            # Return a copy to prevent external modification
            return instance_state.copy() if instance_state else None

    async def list_running_instances(self) -> List[Tuple[str, str, str]]:
        """
        Returns a list of running instances (ID, ChoreographyID, Status).

        Returns:
            List of tuples containing instance ID, choreography ID, and status
        """
        instances = []
        async with self._lock:
            for instance_id, state in self._running_instances.items():
                instances.append(
                    (
                        instance_id,
                        state.get("choreography_id", "Unknown"),
                        state.get("status", "Unknown"),
                    )
                )
        return instances
