# -*- coding: utf-8 -*-
"""
Execution logic for the CAW Choreography system.

Provides the core execution functionality for choreography instances,
including step execution, error handling, and control flow.

Related Files:
- person_suit.core.actors.choreography.engine.py
- person_suit.core.actors.choreography.templating.py
- person_suit.core.actors.choreography.validation.py
- person_suit.core.actors.choreography.dynamic.py
"""

import asyncio
import logging
from typing import Any
from typing import Dict
from typing import Optional

from .base import ChoreographyError
from .base import ErrorCondition
from .dynamic import determine_dynamic_next_step
from .templating import construct_payload
from .templating import update_context
from .utils import evaluate_conditions
from .utils import resolve_participant
from .validation import validate_reply


async def execute_instance(
    instance_id: str,
    engine,
    actor_system,
    definitions,
    running_instances,
    tasks,
    jinja_env,
    payload_templates,
    validation_rules,
    context_update_defs,
    next_step_functions,
    lock,
):
    """
    Main execution loop for a choreography instance. Drives the steps.

    Args:
        instance_id: ID of the choreography instance to execute
        engine: Reference to the ChoreographyEngine instance
        actor_system: The actor system interface
        definitions: Dictionary of choreography definitions
        running_instances: Dictionary of running instance states
        tasks: Dictionary of running instance tasks
        jinja_env: The Jinja2 environment
        payload_templates: Dictionary of template names to template strings
        validation_rules: Dictionary of validation rule names to schema objects
        context_update_defs: Dictionary of context update definitions
        next_step_functions: Dictionary of function names to callables
        lock: Lock for concurrency control
    """
    # Initial state fetch (should exist as task is created after state)
    async with lock:  # Quick lock to get initial state safely
        instance_state = running_instances.get(instance_id)

    if not instance_state:
        logging.error(
            f"Execute instance {instance_id} cannot start: Instance state not found."
        )
        return
    if instance_state["status"] != "RUNNING":
        logging.warning(
            f"Execute instance {instance_id} skipped: Status is not RUNNING (State: {instance_state['status']})."
        )
        return

    try:  # Top-level try block for the whole instance execution
        definition = definitions[instance_state["choreography_id"]]
        # Pre-calculate step_id -> index mapping for GOTO efficiency
        step_id_to_index = {step.step_id: i for i, step in enumerate(definition.steps)}
        # Store step definitions by index for easy access
        steps = definition.steps

        current_step_index = instance_state["current_step_index"]

        while (
            0 <= current_step_index < len(steps)
            and instance_state["status"] == "RUNNING"
        ):
            step = steps[current_step_index]
            # Update state with current index before potentially suspending
            async with lock:  # Lock for state update
                instance_state["current_step_index"] = current_step_index

            step_label = f"[{instance_id} Step {current_step_index}: {step.step_id}]"
            step_retry_key = f"_retry_count_{step.step_id}"

            # Acquire lock to access/modify instance state for this step attempt
            async with lock:
                instance_state["scratchpad"].setdefault(step_retry_key, 0)
                current_attempt = instance_state["scratchpad"][step_retry_key] + 1
                history_entry = {
                    "step_id": step.step_id,
                    "status": "STARTED",
                    "attempt": current_attempt,
                    "timestamp": asyncio.get_event_loop().time(),
                }
                instance_state["history"].append(history_entry)

            logging.info(
                f"{step_label} Starting execution (Attempt: {current_attempt})."
            )

            error_condition: Optional[ErrorCondition] = None
            error_details: Optional[str] = None
            next_step_index = current_step_index + 1  # Default next step
            jump_to_index: Optional[int] = None
            correlation_id = None  # Reset correlation ID per step

            try:
                # ----- Step Execution Logic ----- #
                logging.info(
                    f"CHOREOGRAPHY STEP ENGAGED: {step_label} [ChoreographyID: {instance_state['choreography_id']}]"
                )
                # Perform operations requiring locked state first
                async with lock:
                    # 1. Check Conditions
                    if not evaluate_conditions(step.conditions, instance_state):
                        error_condition = "CONDITION_FAIL"
                        error_details = "Conditions not met"
                        logging.warning(f"{step_label} {error_details}")
                        # Handle error immediately while locked
                        jump_to_index = await handle_step_error(
                            error_condition,
                            error_details,
                            step,
                            instance_state,
                            step_label,
                            step_id_to_index,
                            current_step_index,
                        )
                        if jump_to_index is not None:
                            current_step_index = jump_to_index
                            continue
                        else:
                            return  # FAIL

                    # 2. Resolve Participants
                    sender_role_or_id = step.sender
                    receiver_role_or_id = step.receiver
                    sender_id = resolve_participant(sender_role_or_id, instance_state)
                    receiver_id = resolve_participant(
                        receiver_role_or_id, instance_state
                    )
                    if not sender_id or not receiver_id:
                        # Raise error that will be caught below
                        raise ChoreographyError(
                            f"Failed to resolve sender '{sender_role_or_id}' or receiver '{receiver_role_or_id}'"
                        )

                    # 3. Construct Payload (using Jinja2)
                    payload = construct_payload(
                        jinja_env,
                        step.payload_template,
                        payload_templates,
                        instance_state,
                    )
                    # Check if payload construction failed (returned error dict)
                    if isinstance(payload, dict) and "error" in payload:
                        raise ChoreographyError(
                            f"Payload construction failed: {payload['error']}"
                        )

                    # 4. Prepare Message (using StandardActorMessage)
                    if step.expected_reply_type:
                        from .base import generate_correlation_id

                        correlation_id = generate_correlation_id(
                            instance_id, step.step_id
                        )

                    # Need to make a clean import here to avoid circular imports
                    from person_suit.core.actors.base import StandardActorMessage

                    message_to_send = StandardActorMessage(
                        type=step.message_type,
                        payload=payload,
                        sender_id=sender_id,  # Add sender ID
                        context=instance_state["context"],  # Pass current context
                        correlation_id=correlation_id,  # Will be None if no reply expected
                    )

                # ----- Release Lock Before Sending/Waiting ----- #

                # 5. Resolve Receiver Ref & Send Message
                receiver_ref = await actor_system.get_actor_ref(receiver_id)
                if not receiver_ref:
                    error_condition = "PARTICIPANT_UNAVAILABLE"
                    error_details = f"Receiver actor '{receiver_id}' (Role: {receiver_role_or_id}) not found by ActorSystem."
                    logging.error(f"{step_label} {error_details}")
                    async with lock:
                        jump_to_index = await handle_step_error(
                            error_condition,
                            error_details,
                            step,
                            instance_state,
                            step_label,
                            step_id_to_index,
                            current_step_index,
                        )
                    if jump_to_index is not None:
                        current_step_index = jump_to_index
                        continue
                    else:
                        return  # FAIL

                logging.debug(
                    f"{step_label} Sending to {receiver_id} (Ref: {receiver_ref}): Type={message_to_send.type}, Corr={message_to_send.correlation_id}"
                )
                try:
                    # Ensure send takes the correct message type
                    await actor_system.send(receiver_ref, message_to_send)
                except Exception as send_error:
                    logging.exception(f"{step_label} ActorSystem send failed.")
                    async with lock:
                        jump_to_index = await handle_step_error(
                            "SEND_FAIL",
                            str(send_error),
                            step,
                            instance_state,
                            step_label,
                            step_id_to_index,
                            current_step_index,
                        )
                    if jump_to_index is not None:
                        current_step_index = jump_to_index
                        continue
                    else:
                        return  # FAIL

                # 6. Handle Reply / Timeout (if reply expected)
                reply_payload = None  # Initialize reply_payload
                if step.expected_reply_type:
                    # Correlation ID should be in message_to_send.correlation_id
                    corr_id = message_to_send.correlation_id
                    timeout = step.timeout_seconds or 30.0  # Default timeout
                    logging.debug(
                        f"{step_label} Awaiting reply via actor_system.ask (timeout: {timeout}s, corr: {corr_id})"
                    )
                    try:
                        # Use ActorSystem's ask pattern
                        # Need to adapt ActorSystem.ask or the call here.
                        # Assuming ask takes type, payload, context, capability, timeout
                        # (Need to align with actual ActorSystem.ask signature)
                        reply_payload = await actor_system.ask(
                            target_ref=receiver_ref,
                            message_type=step.expected_reply_type,  # Assuming reply type indicates what ask expects back
                            payload={
                                "query": "choreography_step_reply",
                                "correlation_id": corr_id,
                            },  # Dummy payload for ask
                            context=instance_state["context"],
                            capability_token=None,  # TODO: Pass appropriate capability if needed for ask
                            timeout=timeout,
                        )
                        # The reply_payload from ask should be the actual payload sent back by the target actor
                        # We might need to wrap/unwrap this if ask returns the full StandardActorMessage
                        logging.info(f"{step_label} Received reply via ask.")
                        logging.debug(f"{step_label} Reply payload: {reply_payload}")

                    except asyncio.TimeoutError:
                        error_condition = "TIMEOUT"
                        error_details = "Timeout waiting for reply via actor_system.ask"
                        logging.warning(f"{step_label} {error_details}")
                        # Handle error (retry, fail, goto)
                        async with lock:
                            jump_to_index = await handle_step_error(
                                error_condition,
                                error_details,
                                step,
                                instance_state,
                                step_label,
                                step_id_to_index,
                                current_step_index,
                            )
                        if jump_to_index is not None:
                            current_step_index = jump_to_index
                            continue
                        else:
                            return  # FAIL
                    except asyncio.CancelledError:
                        logging.warning(
                            f"{step_label} Execution cancelled while awaiting ask reply."
                        )
                        raise  # Re-raise cancellation
                    except LookupError as e:
                        error_condition = "PARTICIPANT_UNAVAILABLE"  # ask raises LookupError if target not found
                        error_details = f"Target actor for ask not found: {e}"
                        logging.error(f"{step_label} {error_details}")
                        async with lock:
                            jump_to_index = await handle_step_error(
                                error_condition,
                                error_details,
                                step,
                                instance_state,
                                step_label,
                                step_id_to_index,
                                current_step_index,
                            )
                        if jump_to_index is not None:
                            current_step_index = jump_to_index
                            continue
                        else:
                            return  # FAIL
                    except Exception as ask_error:
                        logging.exception(f"{step_label} Error awaiting ask reply.")
                        async with lock:
                            jump_to_index = await handle_step_error(
                                "INTERNAL_ERROR",
                                f"Ask reply failed: {ask_error}",
                                step,
                                instance_state,
                                step_label,
                                step_id_to_index,
                                current_step_index,
                            )
                        if jump_to_index is not None:
                            current_step_index = jump_to_index
                            continue
                        else:
                            return  # FAIL

                # ----- Re-acquire Lock for Post-Send/Reply Processing ----- #
                async with lock:
                    # 7. Validate Reply (if received, using JSON Schema)
                    # Note: Need to decide if reply_payload from ask needs unwrapping
                    # Assume reply_payload is the direct payload dict for now
                    if step.expected_reply_type and not validate_reply(
                        step.reply_validation_rule,
                        validation_rules,
                        reply_payload,
                        step.expected_reply_type,
                        step.expected_reply_type,
                        instance_state,
                    ):
                        error_condition = "INVALID_REPLY"
                        error_details = "Invalid reply received (failed validation)."
                        logging.warning(
                            f"{step_label} {error_details}. Reply payload: {reply_payload}"
                        )
                        # Handle error
                        jump_to_index = await handle_step_error(
                            error_condition,
                            error_details,
                            step,
                            instance_state,
                            step_label,
                            step_id_to_index,
                            current_step_index,
                        )
                        if jump_to_index is not None:
                            current_step_index = jump_to_index
                            continue
                        else:
                            return  # FAIL

                    # 8. Store Reply (if valid and storing enabled)
                    if (
                        step.expected_reply_type
                        and reply_payload is not None
                        and step.store_reply_as
                    ):
                        instance_state["scratchpad"][step.store_reply_as] = (
                            reply_payload
                        )
                        logging.debug(
                            f"{step_label} Stored ask reply payload in scratchpad as '{step.store_reply_as}'"
                        )

                    # 9. Update Context (on success of this step, using Jinja2)
                    instance_state["context"] = update_context(
                        jinja_env,
                        step.context_updates_on_success,
                        context_update_defs,
                        instance_state["context"],
                        instance_state,
                    )

                    # Step completed successfully
                    instance_state["scratchpad"].pop(
                        step_retry_key, None
                    )  # Reset retry count
                    history_entry.update(
                        {
                            "status": "COMPLETED",
                            "reply": reply_payload,
                            "timestamp": asyncio.get_event_loop().time(),
                        }
                    )
                    logging.info(f"{step_label} Completed successfully.")

                    # --- Determine Next Step --- #
                    calculated_next_index = next_step_index  # Default: sequential
                    if step.next_step_logic:
                        logging.debug(
                            f"{step_label} Evaluating next_step_logic: '{step.next_step_logic}'"
                        )
                        try:
                            next_step_id = determine_dynamic_next_step(
                                step.next_step_logic,
                                instance_state,  # Pass full state for evaluation
                                next_step_functions,
                            )
                            if next_step_id:
                                target_index = step_id_to_index.get(next_step_id)
                                if target_index is not None:
                                    calculated_next_index = target_index
                                    logging.info(
                                        f"{step_label} Dynamic next step determined: GOTO '{next_step_id}' (index {calculated_next_index})"
                                    )
                                else:
                                    logging.error(
                                        f"{step_label} next_step_logic resulted in unknown step_id '{next_step_id}'. Proceeding sequentially."
                                    )
                            # else: Rule evaluated to None/False, proceed sequentially
                        except Exception as e:
                            logging.error(
                                f"{step_label} Error evaluating next_step_logic '{step.next_step_logic}': {e}. Proceeding sequentially.",
                                exc_info=True,
                            )
                    # --- End Determine Next Step --- #

                    current_step_index = (
                        calculated_next_index  # Move to determined next step
                    )

            # --- Inner Try Block Exception Handling --- #
            except ChoreographyError as e:
                logging.error(f"{step_label} Choreography error: {e}")
                async with lock:
                    instance_state["status"] = "FAILED"
                    instance_state["error_info"] = {
                        "step_id": step.step_id,
                        "error": str(e),
                    }
                    history_entry.update(
                        {
                            "status": "FAILED",
                            "reason": str(e),
                            "timestamp": asyncio.get_event_loop().time(),
                        }
                    )
                return  # Terminate execution loop
            except asyncio.CancelledError:
                raise  # Propagate cancellation correctly
            except Exception as e:
                logging.exception(
                    f"{step_label} Unexpected error during step execution."
                )
                async with lock:
                    jump_to_index = await handle_step_error(
                        "INTERNAL_ERROR",
                        f"Unexpected: {e}",
                        step,
                        instance_state,
                        step_label,
                        step_id_to_index,
                        current_step_index,
                    )
                if jump_to_index is not None:
                    current_step_index = jump_to_index
                    continue
                else:
                    return  # FAIL

            # Check if retry is needed before proceeding to next step
            retry_delay = instance_state.get("_retry_delay")
            if retry_delay is not None:
                instance_state.pop("_retry_delay")  # Consume the delay flag
                logging.info(
                    f"{step_label} Delaying {retry_delay:.2f}s before retry..."
                )
                # Return to the same step index to retry
                current_step_index = current_step_index

        # --- End of While Loop --- #
        # Check final status outside the loop
        async with lock:
            final_state_status = instance_state["status"]  # Read status under lock

        if final_state_status == "RUNNING":
            # If loop finished because index went out of bounds (success)
            if current_step_index >= len(steps):
                async with lock:
                    instance_state["status"] = "COMPLETED"
                logging.info(f"[{instance_id}] Choreography completed successfully.")
            elif current_step_index < 0:
                # GOTO resulted in invalid index
                async with lock:
                    instance_state["status"] = "FAILED"
                    instance_state["error_info"] = {
                        "step_id": "N/A",
                        "error": "Invalid GOTO resulted in negative step index",
                    }
                logging.error(
                    f"[{instance_id}] Choreography failed: Invalid GOTO resulted in negative step index."
                )
            # Else: loop exited while RUNNING for some other reason? Log?

    # --- Top Level Exception Handling --- #
    except asyncio.CancelledError:
        logging.warning(
            f"Choreography instance {instance_id} execution task was explicitly cancelled."
        )
        async with lock:
            # Status might already be STOPPING or set by inner handler
            if instance_id in running_instances:  # Check if still exists
                current_status = running_instances[instance_id].get("status", "RUNNING")
                if current_status not in ["STOPPED", "STOPPING", "FAILED", "COMPLETED"]:
                    running_instances[instance_id]["status"] = "CANCELLED"
                    running_instances[instance_id]["error_info"] = {
                        "reason": "Execution task cancelled"
                    }
        # Do not raise cancellation further, let the task finish cancelled.
    except Exception as e:
        logging.exception(
            f"[{instance_id}] Unexpected top-level error during execution."
        )
        async with lock:
            if instance_id in running_instances:  # Check if still exists
                # Only fail if not already stopped/failed/completed
                current_status = running_instances[instance_id].get("status", "RUNNING")
                if current_status not in [
                    "STOPPED",
                    "STOPPING",
                    "FAILED",
                    "COMPLETED",
                    "CANCELLED",
                ]:
                    running_instances[instance_id]["status"] = "FAILED"
                    step_id_at_error = (
                        steps[instance_state["current_step_index"]].step_id
                        if 0 <= instance_state["current_step_index"] < len(steps)
                        else "N/A"
                    )
                    running_instances[instance_id]["error_info"] = {
                        "step_id": step_id_at_error,
                        "error": f"Unexpected Top-Level: {e}",
                    }
    finally:
        # Log final status read from state if possible
        final_status_in_state = "INSTANCE_GONE"
        async with lock:  # Quick read lock if possible
            if instance_id in running_instances:
                final_status_in_state = running_instances[instance_id].get(
                    "status", "UNKNOWN"
                )
        logging.debug(
            f"Exiting execution loop for instance {instance_id}. Final status in state: {final_status_in_state}"
        )


async def handle_step_error(
    error_condition: ErrorCondition,
    error_details: str,
    step,
    instance_state: Dict[str, Any],
    step_label: str,
    step_id_to_index: Dict[str, int],
    current_step_index: int,  # Pass current index for RETRY/SKIP logic
    max_retries: int = 3,  # Default max retries
) -> Optional[int]:
    """
    Handles specified error conditions based on step.on_error definition.

    Args:
        error_condition: The type of error that occurred
        error_details: Details about the error
        step: The choreography step that encountered the error
        instance_state: Current choreography instance state
        step_label: Logging label for this step
        step_id_to_index: Dictionary mapping step IDs to indices
        current_step_index: The index of the step that failed
        max_retries: Maximum number of retries allowed for a step

    Returns:
        The index of the next step to execute if handling results in RETRY or GOTO.
        None if handling results in FAIL or an unhandled action (status set to FAILED).
    """
    action = (step.on_error or {}).get(error_condition, "FAIL")  # Default to FAIL
    # Ensure history entry exists before updating
    if not instance_state["history"]:
        logging.error(f"{step_label} Cannot update history: history list is empty.")
    else:
        instance_state["history"][-1].update(
            {
                "error_condition": error_condition,
                "error_details": error_details,
                "error_action_taken": action,
            }
        )

    if action == "FAIL":
        logging.error(
            f"{step_label} Error condition '{error_condition}', action is FAIL."
        )
        instance_state["status"] = "FAILED"
        instance_state["error_info"] = {
            "step_id": step.step_id,
            "error": error_condition,
            "details": error_details,
        }
        if instance_state["history"]:
            instance_state["history"][-1]["status"] = "FAILED"
        return None

    elif action == "RETRY":
        step_retry_key = f"_retry_count_{step.step_id}"
        current_retries = instance_state["scratchpad"].get(step_retry_key, 0)
        if current_retries < max_retries:
            instance_state["scratchpad"][step_retry_key] = current_retries + 1
            logging.warning(
                f"{step_label} Error condition '{error_condition}', attempting RETRY ({current_retries + 1}/{max_retries})."
            )
            if instance_state["history"]:
                instance_state["history"][-1]["status"] = "RETRYING"
            # Signal to main loop that retry is needed with delay
            instance_state["_retry_delay"] = 0.1 * (current_retries + 1)  # Store delay
            return current_step_index
        else:
            logging.error(
                f"{step_label} Error condition '{error_condition}', max retries ({max_retries}) exceeded. Failing."
            )
            instance_state["status"] = "FAILED"
            instance_state["error_info"] = {
                "step_id": step.step_id,
                "error": error_condition,
                "details": f"{error_details} (Max retries exceeded)",
            }
            if instance_state["history"]:
                instance_state["history"][-1]["status"] = "FAILED_AFTER_RETRY"
            return None

    elif action.startswith("GOTO:"):
        target_step_id = action.split(":", 1)[1]
        target_index = step_id_to_index.get(target_step_id)
        if target_index is not None:
            logging.warning(
                f"{step_label} Error condition '{error_condition}', action is GOTO '{target_step_id}' (index {target_index})."
            )
            # Reset retry counter for the current step before jumping away
            step_retry_key = f"_retry_count_{step.step_id}"
            instance_state["scratchpad"].pop(step_retry_key, None)
            if instance_state["history"]:
                instance_state["history"][-1]["status"] = "GOTO"
            return target_index
        else:
            logging.error(
                f"{step_label} Error condition '{error_condition}', GOTO target step '{target_step_id}' not found. Failing."
            )
            instance_state["status"] = "FAILED"
            instance_state["error_info"] = {
                "step_id": step.step_id,
                "error": error_condition,
                "details": f"GOTO target '{target_step_id}' not found",
            }
            if instance_state["history"]:
                instance_state["history"][-1]["status"] = "FAILED"
            return None

    elif action == "SKIP":
        logging.info(
            f"{step_label} Error condition '{error_condition}', action is SKIP."
        )
        step_retry_key = f"_retry_count_{step.step_id}"
        instance_state["scratchpad"].pop(step_retry_key, None)  # Reset retry count
        if instance_state["history"]:
            instance_state["history"][-1].update(
                {"status": "SKIPPED", "reason": error_condition}
            )
        # Return index of the step *after* the current one
        return current_step_index + 1

    else:
        # Unhandled action
        logging.error(
            f"{step_label} Error condition '{error_condition}', but action '{action}' is unsupported. Failing."
        )
        instance_state["status"] = "FAILED"
        instance_state["error_info"] = {
            "step_id": step.step_id,
            "error": error_condition,
            "details": f"Unsupported action: {action}",
        }
        if instance_state["history"]:
            instance_state["history"][-1]["status"] = "FAILED"
        return None
