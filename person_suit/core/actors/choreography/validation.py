# -*- coding: utf-8 -*-
"""
Validation functionality for the CAW Choreography system.

Provides methods for validating choreography replies against JSON Schema rules.

Related Files:
- person_suit.core.actors.choreography.engine.py
- person_suit.core.actors.choreography.execution.py
"""

import logging
from typing import Any
from typing import Dict
from typing import Optional

# JSON Schema validation dependency
try:
    from jsonschema import validate as validate_jsonschema
    from jsonschema.exceptions import SchemaError
    from jsonschema.exceptions import ValidationError as JsonSchemaValidationError
except ImportError:
    logging.critical("jsonschema not found. Please install it: pip install jsonschema")

    # Mock or raise to prevent execution without dependency
    def validate_jsonschema(*args, **kwargs):
        pass

    JsonSchemaValidationError = Exception
    SchemaError = Exception


def validate_reply(
    rule_name: Optional[str],
    rule_registry: Dict[str, Dict],
    reply_payload: Any,
    reply_type: str,
    expected_type: str,
    instance_state: Dict,
) -> bool:
    """
    Validates reply using a named JSON Schema rule from the registry.

    Args:
        rule_name: Name of the validation rule to use
        rule_registry: Dictionary mapping rule names to schema objects
        reply_payload: The payload to validate
        reply_type: The type of the received reply
        expected_type: The expected type of the reply
        instance_state: Current choreography instance state

    Returns:
        True if the validation passes, False otherwise
    """
    # 1. Validate Type
    if reply_type != expected_type:
        logging.warning(
            f"[{instance_state['instance_id']}] Reply type mismatch: Expected '{expected_type}', Got '{reply_type}'"
        )
        return False

    # 2. Skip Schema Validation if no rule specified
    if not rule_name:
        logging.debug(
            f"[{instance_state['instance_id']}] No validation rule specified for reply type '{reply_type}'. Skipping schema validation."
        )
        return True  # No rule means validation passes

    # 3. Load Schema
    schema = rule_registry.get(rule_name)
    if not schema:
        logging.error(
            f"[{instance_state['instance_id']}] Validation rule '{rule_name}' not found in registry. Cannot validate reply."
        )
        # Fail validation if rule specified but not found? Or just warn and pass? Fail for safety.
        return False

    # 4. Perform JSON Schema Validation
    try:
        validate_jsonschema(instance=reply_payload, schema=schema)
        logging.debug(
            f"[{instance_state['instance_id']}] Reply validation PASSED using JSON Schema rule '{rule_name}'"
        )
        return True
    except JsonSchemaValidationError as e:
        logging.warning(
            f"[{instance_state['instance_id']}] Reply validation FAILED using JSON Schema rule '{rule_name}': {e.message} (Path: {e.json_path})"
        )
        # Optionally log more details from 'e' if needed
        return False
    except SchemaError as e:
        # This indicates the schema itself is invalid (should ideally be caught at load time)
        logging.error(
            f"[{instance_state['instance_id']}] Invalid schema encountered during validation for rule '{rule_name}': {e.message}"
        )
        return False  # Fail validation if schema is broken
    except Exception as e:
        # Catch unexpected errors during validation
        logging.exception(
            f"[{instance_state['instance_id']}] Unexpected error during JSON Schema validation using rule '{rule_name}': {e}"
        )
        return False
