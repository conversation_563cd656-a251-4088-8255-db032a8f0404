"""
CAW Choreography Engine and Definitions.

Implements the CAW principle of Coordinated Interaction Protocols.
Defines structures for specifying choreographies (global interaction patterns)
and an engine for executing them, coordinating message flow between actors.

This package provides:
- ChoreographyStep: Definition for a single step in a choreography protocol
- ChoreographyDefinition: Definition for a complete choreography protocol
- ChoreographyEngine: Engine for executing and managing choreography instances
"""

from .base import ActorID
from .base import ChoreographyDefinition
from .base import ChoreographyError
from .base import ChoreographyID
from .base import ChoreographyStep
from .base import generate_correlation_id
from .engine import ChoreographyEngine
from .examples import ADAPTIVE_DATA_REQUEST_CHOREO
from .examples import ADAPTIVE_HANDLER_NAME
from .examples import BASIC_REQUEST_TEMPLATE
from .examples import BASIC_RESPONSE_SCHEMA
from .examples import GET_DATA_REQUEST_CHOREO_V2
from .examples import PROCESS_DATA_CONTEXT_UPDATE_DICT
from .interfaces import ActorRefProtocol
from .interfaces import ActorSystemInterface

__all__ = [
    "ChoreographyStep",
    "ChoreographyDefinition",
    "ChoreographyEngine",
    "ChoreographyError",
    "ActorID",
    "ChoreographyID",
    "generate_correlation_id",
    "BASIC_REQUEST_TEMPLATE",
    "BASIC_RESPONSE_SCHEMA",
    "PROCESS_DATA_CONTEXT_UPDATE_DICT",
    "GET_DATA_REQUEST_CHOREO_V2",
    "ADAPTIVE_DATA_REQUEST_CHOREO",
    "ADAPTIVE_HANDLER_NAME",
    "ActorRefProtocol",
    "ActorSystemInterface",
]
