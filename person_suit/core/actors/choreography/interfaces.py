# -*- coding: utf-8 -*-
"""
Interfaces for the CAW Choreography system.

Defines the protocols that an Actor System and Actor References must adhere to
for interaction with the ChoreographyEngine.
"""

from typing import Any
from typing import Optional
from typing import Protocol

from person_suit.core.actors.base import StandardActorMessage  # For strong typing on send/ask

# Message-based services initialization
from person_suit.core.infrastructure.message_based_imports import ensure_services_initialized

# Import types from .base or other relevant places if needed
from .base import ActorID  # Assuming ActorID is in base.py


async def _ensure_message_services():
    """Ensure message-based services are initialized."""
    if not await ensure_services_initialized():
        raise RuntimeError("Failed to initialize message-based services")

# Import UnifiedContext from canonical location
from person_suit.core.context.unified import UnifiedContext
from person_suit.core.security.capabilities import CapabilityToken  # For ask


class ActorRefProtocol(Protocol):
    """Protocol for an Actor Reference usable by the ChoreographyEngine."""

    @property
    def path(self) -> Any: # Actual type is ActorPath, but use Any for simplicity if ActorPath is complex/not easily imported
        """The unique path of the actor."""
        ...

    # Add other methods or properties if the ChoreographyEngine directly uses them
    # For now, it seems the engine mostly passes ActorRef instances back to ActorSystem methods.


class ActorSystemInterface(Protocol):
    """
    Protocol defining the interface an Actor System must provide
    to be used by the ChoreographyEngine.
    """

    async def get_actor_ref(self, actor_id: ActorID) -> Optional[ActorRefProtocol]:
        """
        Retrieve an actor reference by its ID.

        Args:
            actor_id: The ID of the actor.

        Returns:
            An ActorRefProtocol compliant object, or None if not found.
        """
        ...

    async def send(
        self,
        target_ref: ActorRefProtocol, # Changed from target_id to target_ref for consistency
        message: StandardActorMessage,
    ) -> None:
        """
        Send a message to an actor.
        Note: Choreography execution currently constructs StandardActorMessage.

        Args:
            target_ref: The reference of the target actor.
            message: The message to send (typically StandardActorMessage).
        """
        ...

    async def ask(
        self,
        target_ref: ActorRefProtocol,
        message_type: str, # This was the pattern in actor_system.ask
        payload: Any,
        context: Optional[UnifiedContext] = None,
        capability_token: Optional[CapabilityToken] = None,
        timeout: Optional[float] = 5.0,
    ) -> Any:
        """
        Send a message to an actor and expect a reply.
        The signature should match ActorSystem.ask if ChoreographyEngine uses it directly,
        or be adapted if ChoreographyEngine constructs its own message for ask.

        Args:
            target_ref: The reference of the target actor.
            message_type: The type of the message.
            payload: The payload of the message.
            context: CAW context for the message.
            capability_token: Capability token for the operation.
            timeout: Timeout in seconds for awaiting a reply.

        Returns:
            The reply payload from the actor.
        """
        ...

    # Potentially other methods like:
    # def get_capability_service_ref(self) -> Optional[ActorRefProtocol]: ...
    # if the choreography engine needs to interact with it directly. 