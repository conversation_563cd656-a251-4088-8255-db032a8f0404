from __future__ import annotations

"""person_suit.core.actors.caw_actor
=================================================

DecoupledActor Base Class
-------------------
This module introduces `DecoupledActor`, a thin wrapper around the existing
`person_suit.core.actors.base.Actor` that adds:

1. DualUnifiedContext awareness – every actor instance is created with a local
   `DualUnifiedContext`, and every incoming message envelope may carry its own
   context.  When a message arrives, the actor **composes** the local and
   message contexts to obtain a *processing context*.
2. Adaptive Computational Fidelity (ACF) integration – actors are
   initialised with (or automatically create) an `ACFManager` instance.
   Before delegating work to the concrete implementation they call
   `ACFManager.determine_fidelity()` to obtain a fidelity value for the
   current *operation* (in this case derived from the message type).
3. A simplified hook (`receive_with_context`) that concrete subclasses
   implement instead of the full `receive`.  This hook already receives the
   composed context and computed fidelity so business logic can focus on
   *what* to do rather than *how* to adapt.

The goal is to make CAW-aware actor implementations trivial while keeping the
existing rich actor infrastructure intact.

Related:
- person_suit/core/caw/context.py (DualUnifiedContext)
- person_suit/core/caw/acf.py (ACFManager)
- person_suit/core/actors/base.py (Actor, StandardActorMessage)
"""

# Message-based services initialization
from person_suit.core.constants.fixed_point_scale import FIDELITY_HIGH
from person_suit.core.infrastructure.message_based_imports import UnifiedContext
from person_suit.core.infrastructure.message_based_imports import ensure_services_initialized


async def _ensure_message_services():
    """Ensure message-based services are initialized."""
    if not await ensure_services_initialized():
        raise RuntimeError("Failed to initialize message-based services")

import logging
from abc import ABC
from abc import abstractmethod
from typing import Optional

from person_suit.core.actors.base import Actor
from person_suit.core.actors.base import StandardActorMessage
from person_suit.core.adaptivity.acf import ACFManager
from person_suit.core.adaptivity.acf import AdaptationStrategy

logger = logging.getLogger(__name__)


class DecoupledActor(Actor, ABC):
    """UnifiedContext- and ACF-aware actor base class (CAW Principle #4).

    Subclasses should implement :py:meth:`receive_with_context` instead of
    overriding :py:meth:`receive` directly.
    """

    def __init__(
        self,
        local_context: UnifiedContext,
        acf_manager: Optional[ACFManager] = None,
        *,
        adaptation_strategy: AdaptationStrategy = AdaptationStrategy.BALANCED,
    ) -> None:
        super().__init__()
        self._local_context: UnifiedContext = local_context
        self._acf: ACFManager = acf_manager or ACFManager(
            adaptation_strategy=adaptation_strategy
        )

    # ---------------------------------------------------------------------
    # Actor.receive override – composes context & computes fidelity
    # ---------------------------------------------------------------------
    async def receive(self, message: StandardActorMessage) -> None:  # noqa: D401
        """Internal receive wrapper.

        1. Compose the actor's own context with the message context
           (if present).
        2. Ask the ACF manager for a fidelity value.
        3. Delegate to :py:meth:`receive_with_context` implemented by the
           concrete subclass.
        """

        # 1. UnifiedContext composition
        msg_ctx: Optional[UnifiedContext] = (
            message.context if isinstance(message.context, UnifiedContext) else None
        )
        processing_ctx: UnifiedContext = (
            self._local_context.compose_with(msg_ctx) if msg_ctx else self._local_context
        )

        # 2. Fidelity determination (use message type as operation key)
        try:
            fidelity: int = self._acf.determine_fidelity(
                processing_ctx, operation=message.type
            )
        except Exception as err:  # Defensive – never fail the actor loop
            logger.error(
                "ACFManager.determine_fidelity failed for %s: %s", message.type, err
            )
            fidelity = FIDELITY_HIGH  # Reasonable default

        # 3. Delegate to subclass implementation
        try:
            await self.receive_with_context(message, processing_ctx, fidelity)
        except Exception as exc:  # Let supervision strategies handle it
            logger.exception(
                "Unhandled exception in receive_with_context %s: %s", self.path, exc
            )
            raise  # Re-raise so Actor._run delegates to supervision logic

    # ------------------------------------------------------------------
    # Subclass contract
    # ------------------------------------------------------------------
    @abstractmethod
    async def receive_with_context(
        self,
        message: StandardActorMessage,
        context: UnifiedContext,
        fidelity: int,
    ) -> None:
        """Handle a message given composed context & ACF-computed fidelity.""" 