# -*- coding: utf-8 -*-
"""
Module: person_suit.core.actors.supervision
Purpose: Defines supervision strategies for handling actor failures.
"""

import asyncio
import logging
from abc import ABC
from abc import abstractmethod
from enum import Enum
from enum import auto
from typing import TYPE_CHECKING
from typing import Dict
from typing import Optional
from typing import Type

# Import base actor types if needed for context in strategies (avoid if possible)
if TYPE_CHECKING:
    from person_suit.core.actors.base import ActorRef
    from person_suit.core.actors.base import StandardActorMessage

logger = logging.getLogger(__name__)


class SupervisorDirective(Enum):
    """Directives that a supervisor strategy can issue in response to a failure."""

    RESTART = auto()  # Restart the failed actor
    STOP = auto()  # Stop the failed actor
    RESUME = auto()  # Resume the failed actor (ignore the failure)
    ESCALATE = auto()  # Escalate the failure to the parent supervisor


class SupervisionStrategyImplementation(ABC):
    """
    Base class for supervision strategy implementations.

    Defines how to handle exceptions during actor message processing.
    """

    @abstractmethod
    async def decide(
        self,
        exception: Exception,
        failed_actor: Optional["ActorRef"] = None,  # Optional context
        failed_message: Optional["StandardActorMessage"] = None,  # Optional context
    ) -> SupervisorDirective:
        """
        Decide how to handle an exception based on the context.

        Args:
            exception: The exception that occurred.
            failed_actor: Reference to the actor that failed (optional).
            failed_message: The message being processed when failure occurred (optional).

        Returns:
            SupervisorDirective: The directive to apply (RESTART, STOP, RESUME, ESCALATE).
        """
        pass


class DefaultSupervisor(SupervisionStrategyImplementation):
    """
    Default supervision strategy: Restart on most errors, Stop on fatal errors.
    """

    async def decide(
        self,
        exception: Exception,
        failed_actor: Optional["ActorRef"] = None,
        failed_message: Optional["StandardActorMessage"] = None,
    ) -> SupervisorDirective:
        """
        Decides based on exception type.
        Stops on SystemExit, KeyboardInterrupt, MemoryError, SystemError.
        Restarts on all other Exceptions.
        """
        # Fatal exceptions that should stop the actor
        fatal_exceptions = (
            SystemExit,
            KeyboardInterrupt,
            MemoryError,
            SystemError,
            asyncio.CancelledError,  # Treat cancellation as stop
        )

        # Check if the exception is fatal
        if isinstance(exception, fatal_exceptions):
            logger.error(
                f"Supervisor: Fatal exception {type(exception).__name__} in actor {failed_actor.path if failed_actor else 'Unknown'}. Directive: STOP"
            )
            return SupervisorDirective.STOP

        # Resume for specific non-critical errors (Example)
        # if isinstance(exception, ValueError):
        #     logger.warning(f"Supervisor: Resuming actor {failed_actor.path if failed_actor else 'Unknown'} after ValueError.")
        #     return SupervisorDirective.RESUME

        # Restart requires capability check

        actor_path_str = str(failed_actor.path) if failed_actor else "system://unknown"
        # Deny when no capability token present (will be implemented in Sprint-4)
        allowed = False

        if not allowed:
            logger.error(
                "Supervisor: Restart denied by security policy for actor %s. Directive: STOP",
                actor_path_str,
            )
            return SupervisorDirective.STOP

        logger.warning(
            "Supervisor: Restarting actor %s due to exception: %r", actor_path_str, exception
        )
        return SupervisorDirective.RESTART


class OneForOneStrategy(SupervisionStrategyImplementation):
    """
    One-for-one supervision strategy: Applies directive only to the failed child.
    Allows configuration of directives per exception type.
    """

    def __init__(
        self,
        exception_handlers: Optional[Dict[Type[Exception], SupervisorDirective]] = None,
        default_strategy: Optional[SupervisionStrategyImplementation] = None,
        # TODO: Add max_retries / within_time_range logic if needed
    ):
        self.exception_handlers = exception_handlers or {}
        self.default_strategy = default_strategy or DefaultSupervisor()

    async def decide(
        self,
        exception: Exception,
        failed_actor: Optional["ActorRef"] = None,
        failed_message: Optional["StandardActorMessage"] = None,
    ) -> SupervisorDirective:
        """
        Checks custom handlers first, then falls back to the default strategy.
        """
        # Check for specific exception handler
        for exc_type, directive in self.exception_handlers.items():
            # Use issubclass for broader matching (e.g., handle all IOErrors)
            if isinstance(exception, exc_type):
                logger.info(
                    f"Supervisor (OneForOne): Applying custom directive {directive.name} for {type(exception).__name__} in actor {failed_actor.path if failed_actor else 'Unknown'}."
                )
                return directive

        # Apply default strategy if no custom handler matches
        logger.debug(
            f"Supervisor (OneForOne): No custom handler for {type(exception).__name__} in actor {failed_actor.path if failed_actor else 'Unknown'}. Using default."
        )
        return await self.default_strategy.decide(
            exception, failed_actor, failed_message
        )


# Example: A strategy that always stops the actor
class AlwaysStopStrategy(SupervisionStrategyImplementation):
    """A simple strategy that always stops the failing actor."""

    async def decide(
        self,
        exception: Exception,
        failed_actor: Optional["ActorRef"] = None,
        failed_message: Optional["StandardActorMessage"] = None,
    ) -> SupervisorDirective:
        logger.warning(
            f"Supervisor (AlwaysStop): Stopping actor {failed_actor.path if failed_actor else 'Unknown'} due to exception: {exception!r}"
        )
        return SupervisorDirective.STOP


# TODO: Implement AllForOneStrategy if needed - Requires access to supervisor's children
# class AllForOneStrategy(SupervisionStrategyImplementation):
#     ...
