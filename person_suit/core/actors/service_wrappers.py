from __future__ import annotations

"""Service → Actor wrapper utilities (Sprint-3).

This module defines a *generic* ``ServiceActor`` that wraps a class marked with
``@actor_service`` and exposes its public coroutine API via actor messages.

Message envelope convention (temporary MVP until proper choreography DSL):
--------------------------------------------------------------------------
StandardActorMessage.type → name of coroutine attribute on the wrapped service
StandardActorMessage.payload → positional / keyword arguments structure::

    {
        "args": [...],  # positional arguments
        "kwargs": {...}  # keyword arguments
    }

The wrapper forwards the call, awaits the result, and *does not* return a reply
(yet).  This is enough for envelope-migration tests and supervision logic.

A helper ``discover_actor_services`` walks through ``person_suit.core.services``
sub-packages, finds classes carrying ``_is_actor_service`` attribute, and returns
pre-instantiated ``ServiceActor`` objects ready for registration.
"""

import asyncio
import importlib
import inspect
import logging
import pkgutil
from typing import Any
from typing import List
from typing import Type

from person_suit.core.actors.base import Actor
from person_suit.core.actors.base import StandardActorMessage
from person_suit.core.services.base import BaseService

logger = logging.getLogger(__name__)


class ServiceActor(Actor):  # noqa: D101
    def __init__(self, service_cls: Type[Any]):
        super().__init__()
        self._service = service_cls()  # type: ignore[assignment] – instantiate eagerly

    async def receive(self, message: StandardActorMessage) -> None:  # noqa: D401
        method_name = message.type
        args = message.payload.get("args", []) if isinstance(message.payload, dict) else []
        kwargs = message.payload.get("kwargs", {}) if isinstance(message.payload, dict) else {}

        if not hasattr(self._service, method_name):
            logger.warning("Service %s missing method %s", type(self._service).__name__, method_name)
            return

        method = getattr(self._service, method_name)
        # ------------------------------------------------------------------
        # Enforce *no direct I/O* invariant
        # ------------------------------------------------------------------

        import builtins
        import socket as _ps_socket

        def _forbidden(*_a, **_kw):  # noqa: D401 – mini helper
            raise RuntimeError(
                "Direct I/O (open/socket) is forbidden inside ServiceActor – "
                "wrap the operation in an Effect and route via EffectInterpreter instead.",
            )

        # Keep originals for restoration
        _orig_open = builtins.open  # type: ignore[attr-defined]
        _orig_socket = _ps_socket.socket  # type: ignore[attr-defined]

        builtins.open = _forbidden  # type: ignore[assignment]
        _ps_socket.socket = _forbidden  # type: ignore[assignment]

        try:
            if inspect.iscoroutinefunction(method):
                await method(*args, **kwargs)
            else:
                loop = asyncio.get_running_loop()
                await loop.run_in_executor(None, method, *args, **kwargs)
        finally:
            # Restore primitives
            builtins.open = _orig_open  # type: ignore[assignment]
            _ps_socket.socket = _orig_socket  # type: ignore[assignment]


# ---------------------------------------------------------------------------
# Discovery helpers
# ---------------------------------------------------------------------------

def _iter_service_classes() -> List[Type[BaseService]]:  # noqa: D401
    """Yield classes in service & adapter packages marked as actor services."""
    pkgs = [
        "person_suit.core.services",
        "person_suit.io_layer.adapters",
    ]
    discovered: List[Type[BaseService]] = []

    for base_pkg in pkgs:
        try:
            base_mod = importlib.import_module(base_pkg)
            search_path = getattr(base_mod, "__path__", None)
            if search_path is None:
                continue
        except ImportError:
            continue

        for mod_info in pkgutil.walk_packages(search_path, prefix=f"{base_pkg}."):
            try:
                module = importlib.import_module(mod_info.name)
            except Exception as exc:  # pragma: no cover – ignore faulty imports
                logger.debug("Skip importing %s during actor service discovery: %s", mod_info.name, exc)
                continue

            for _, obj in inspect.getmembers(module, inspect.isclass):
                if getattr(obj, "_is_actor_service", False):
                    discovered.append(obj)

    return discovered


def discover_actor_services() -> List[ServiceActor]:  # noqa: D401
    """Instantiate ``ServiceActor`` wrappers for every discovered service class."""
    actors: List[ServiceActor] = []
    for cls in _iter_service_classes():
        try:
            actors.append(ServiceActor(cls))
            logger.info("Created ServiceActor wrapper for %s", cls.__name__)
        except Exception as exc:  # pragma: no cover – keep bootstrap resilient
            logger.error("Failed to create ServiceActor for %s: %s", cls.__name__, exc)
    return actors 