"""
CAW Actor Protocols - Interface contracts for all system actors.

This module defines the protocol interfaces that all CAW actors must implement,
enabling type-safe coordination without cross-domain dependencies.

Related Files:
- person_suit/core/actors/registry.py - Actor discovery and registration
- person_suit/core/actors/effect_router.py - Message routing to actors
- person_suit/effects/actors/ - Effect domain actor implementations

Dependencies: typing, abc, UnifiedContext, HybridMessage (runtime injection)
"""

from __future__ import annotations

from abc import abstractmethod
from dataclasses import dataclass
from dataclasses import field
from typing import Any
from typing import Dict
from typing import List
from typing import Optional
from typing import Protocol
from typing import Set
from typing import runtime_checkable


@runtime_checkable
class CapabilityToken(Protocol):
    """Protocol for capability tokens used in authorization."""
    
    def has_capability(self, capability: str) -> bool:
        """Check if token grants specific capability."""
        ...
    
    def get_capabilities(self) -> Set[str]:
        """Get all capabilities granted by this token."""
        ...


@dataclass(frozen=True)
class ActorCapability:
    """Represents a capability that an actor can provide."""
    
    name: str
    description: str
    resource_requirements: tuple = ()  # Use tuple instead of dict for hashability
    security_level: str = "basic"
    wave_particle_compatible: bool = True
    
    def __hash__(self) -> int:
        """Make ActorCapability hashable for use in sets."""
        return hash((self.name, self.description, self.resource_requirements, 
                    self.security_level, self.wave_particle_compatible))


@runtime_checkable
class ActorProtocol(Protocol):
    """Base protocol for all CAW actors in the system."""
    
    @abstractmethod
    async def get_actor_id(self) -> str:
        """Return unique identifier for this actor instance."""
        ...
    
    @abstractmethod
    async def get_capabilities(self) -> Set[ActorCapability]:
        """Return set of capabilities this actor provides."""
        ...
    
    @abstractmethod
    async def handle_message(self, message: Any, context: Any) -> Any:
        """Handle incoming message based on capabilities.
        
        Args:
            message: HybridMessage to process
            context: UnifiedContext for execution
            
        Returns:
            Result of message processing
            
        Raises:
            CapabilityError: If message requires unavailable capability
            ProcessingError: If message processing fails
        """
        ...
    
    @abstractmethod
    async def get_health_status(self) -> Dict[str, Any]:
        """Return current health and status information."""
        ...


@runtime_checkable
class EffectActorProtocol(Protocol):
    """Protocol for actors that handle effect execution."""
    
    # Base ActorProtocol methods
    @abstractmethod
    async def get_actor_id(self) -> str:
        """Return unique identifier for this actor instance."""
        ...
    
    @abstractmethod
    async def get_capabilities(self) -> Set[ActorCapability]:
        """Return set of capabilities this actor provides."""
        ...
    
    @abstractmethod
    async def handle_message(self, message: Any, context: Any) -> Any:
        """Handle incoming message based on capabilities."""
        ...
    
    @abstractmethod
    async def get_health_status(self) -> Dict[str, Any]:
        """Return current health and status information."""
        ...
    
    # Effect-specific methods
    @abstractmethod
    async def execute_effect(self, effect_message: Any, context: Any) -> Any:
        """Execute effect with differential execution and wave-particle duality.
        
        Args:
            effect_message: HybridMessage containing effect data
            context: UnifiedContext with execution parameters
            
        Returns:
            Effect execution result with telemetry data
        """
        ...
    
    @abstractmethod
    async def estimate_resource_usage(self, effect_message: Any) -> Dict[str, float]:
        """Estimate resource usage for effect execution."""
        ...
    
    @abstractmethod
    async def supports_wave_particle_duality(self) -> bool:
        """Check if actor supports wave-particle processing modes."""
        ...


@runtime_checkable
class MemoryActorProtocol(Protocol):
    """Protocol for actors that handle memory operations."""
    
    # Base ActorProtocol methods
    @abstractmethod
    async def get_actor_id(self) -> str:
        """Return unique identifier for this actor instance."""
        ...
    
    @abstractmethod
    async def get_capabilities(self) -> Set[ActorCapability]:
        """Return set of capabilities this actor provides."""
        ...
    
    @abstractmethod
    async def handle_message(self, message: Any, context: Any) -> Any:
        """Handle incoming message based on capabilities."""
        ...
    
    @abstractmethod
    async def get_health_status(self) -> Dict[str, Any]:
        """Return current health and status information."""
        ...
    
    # Memory-specific methods
    @abstractmethod
    async def store_memory(self, content: Any, context: Any) -> str:
        """Store content in appropriate memory layer."""
        ...
    
    @abstractmethod
    async def retrieve_memory(self, key: str, context: Any) -> Any:
        """Retrieve content from memory layers."""
        ...
    
    @abstractmethod
    async def consolidate_memories(self, context: Any) -> Dict[str, Any]:
        """Perform memory consolidation operations."""
        ...


@runtime_checkable
class CognitionActorProtocol(Protocol):
    """Protocol for actors that handle cognitive processing."""
    
    # Base ActorProtocol methods
    @abstractmethod
    async def get_actor_id(self) -> str:
        """Return unique identifier for this actor instance."""
        ...
    
    @abstractmethod
    async def get_capabilities(self) -> Set[ActorCapability]:
        """Return set of capabilities this actor provides."""
        ...
    
    @abstractmethod
    async def handle_message(self, message: Any, context: Any) -> Any:
        """Handle incoming message based on capabilities."""
        ...
    
    @abstractmethod
    async def get_health_status(self) -> Dict[str, Any]:
        """Return current health and status information."""
        ...
    
    # Cognition-specific methods
    @abstractmethod
    async def process_cognitive_load(self, task: Any, context: Any) -> Any:
        """Process cognitive task with CAM/SEM pathway coordination."""
        ...
    
    @abstractmethod
    async def adapt_processing_fidelity(self, new_fidelity: float, context: Any) -> bool:
        """Adapt cognitive processing fidelity based on ACF."""
        ...


@runtime_checkable
class AnalysisActorProtocol(Protocol):
    """Protocol for actors that handle analysis operations."""
    
    # Base ActorProtocol methods
    @abstractmethod
    async def get_actor_id(self) -> str:
        """Return unique identifier for this actor instance."""
        ...
    
    @abstractmethod
    async def get_capabilities(self) -> Set[ActorCapability]:
        """Return set of capabilities this actor provides."""
        ...
    
    @abstractmethod
    async def handle_message(self, message: Any, context: Any) -> Any:
        """Handle incoming message based on capabilities."""
        ...
    
    @abstractmethod
    async def get_health_status(self) -> Dict[str, Any]:
        """Return current health and status information."""
        ...
    
    # Analysis-specific methods
    @abstractmethod
    async def analyze_patterns(self, data: Any, context: Any) -> Any:
        """Perform pattern analysis on input data."""
        ...
    
    @abstractmethod
    async def extract_entities(self, text: str, context: Any) -> List[Dict[str, Any]]:
        """Extract entities from text input."""
        ...


@runtime_checkable
class PredictionActorProtocol(Protocol):
    """Protocol for actors that handle prediction operations."""
    
    # Base ActorProtocol methods
    @abstractmethod
    async def get_actor_id(self) -> str:
        """Return unique identifier for this actor instance."""
        ...
    
    @abstractmethod
    async def get_capabilities(self) -> Set[ActorCapability]:
        """Return set of capabilities this actor provides."""
        ...
    
    @abstractmethod
    async def handle_message(self, message: Any, context: Any) -> Any:
        """Handle incoming message based on capabilities."""
        ...
    
    @abstractmethod
    async def get_health_status(self) -> Dict[str, Any]:
        """Return current health and status information."""
        ...
    
    # Prediction-specific methods
    @abstractmethod
    async def generate_prediction(self, input_data: Any, context: Any) -> Any:
        """Generate prediction based on input data."""
        ...
    
    @abstractmethod
    async def train_model(self, training_data: Any, context: Any) -> Dict[str, Any]:
        """Train prediction model with provided data."""
        ...


@runtime_checkable
class IOActorProtocol(Protocol):
    """Protocol for actors that handle I/O operations."""
    
    # Base ActorProtocol methods
    @abstractmethod
    async def get_actor_id(self) -> str:
        """Return unique identifier for this actor instance."""
        ...
    
    @abstractmethod
    async def get_capabilities(self) -> Set[ActorCapability]:
        """Return set of capabilities this actor provides."""
        ...
    
    @abstractmethod
    async def handle_message(self, message: Any, context: Any) -> Any:
        """Handle incoming message based on capabilities."""
        ...
    
    @abstractmethod
    async def get_health_status(self) -> Dict[str, Any]:
        """Return current health and status information."""
        ...
    
    # I/O-specific methods
    @abstractmethod
    async def read_file(self, path: str, context: Any) -> bytes:
        """Read file from filesystem."""
        ...
    
    @abstractmethod
    async def write_file(self, path: str, content: bytes, context: Any) -> bool:
        """Write content to filesystem."""
        ...
    
    @abstractmethod
    async def make_request(self, url: str, context: Any) -> Any:
        """Make HTTP request."""
        ...


@runtime_checkable
class TelemetryActorProtocol(Protocol):
    """Protocol for actors that handle telemetry and monitoring."""
    
    # Base ActorProtocol methods
    @abstractmethod
    async def get_actor_id(self) -> str:
        """Return unique identifier for this actor instance."""
        ...
    
    @abstractmethod
    async def get_capabilities(self) -> Set[ActorCapability]:
        """Return set of capabilities this actor provides."""
        ...
    
    @abstractmethod
    async def handle_message(self, message: Any, context: Any) -> Any:
        """Handle incoming message based on capabilities."""
        ...
    
    @abstractmethod
    async def get_health_status(self) -> Dict[str, Any]:
        """Return current health and status information."""
        ...
    
    # Telemetry-specific methods
    @abstractmethod
    async def collect_metrics(self, source: str, context: Any) -> Dict[str, Any]:
        """Collect metrics from specified source."""
        ...
    
    @abstractmethod
    async def export_telemetry(self, destination: str, data: Any, context: Any) -> bool:
        """Export telemetry data to external system."""
        ...


# Exception types for actor operations
class ActorError(Exception):
    """Base exception for actor operations."""
    pass


class CapabilityError(ActorError):
    """Raised when actor lacks required capability."""
    pass


class ProcessingError(ActorError):
    """Raised when message processing fails."""
    pass


class ResourceError(ActorError):
    """Raised when insufficient resources for operation."""
    pass


# Actor registry types
@dataclass
class RegisteredActor:
    """Information about a registered actor."""
    
    actor_id: str
    protocol_type: str
    capabilities: Set[ActorCapability]
    endpoint: str
    health_status: str = "unknown"
    last_seen: Optional[float] = None
    load_factor: float = 0.0
    metadata: Dict[str, Any] = field(default_factory=dict)


@runtime_checkable
class ActorRegistryProtocol(Protocol):
    """Protocol for actor registry systems."""
    
    @abstractmethod
    async def register_actor(self, actor: ActorProtocol) -> bool:
        """Register actor in the registry."""
        ...
    
    @abstractmethod
    async def find_actors_by_capability(self, capability: str) -> List[RegisteredActor]:
        """Find all actors that provide specific capability."""
        ...
    
    @abstractmethod
    async def get_actor_by_id(self, actor_id: str) -> Optional[RegisteredActor]:
        """Get specific actor by ID."""
        ...
    
    @abstractmethod
    async def health_check_all(self) -> Dict[str, str]:
        """Perform health check on all registered actors."""
        ... 