"""
File: mocks.py
Purpose: Mock implementations of interfaces for testing the Central State Actor
Related Files:
    - interfaces.py: Contains the interfaces being mocked
    - actor.py: The Central State Actor implementation that uses these mocks
    - test_central_state_actor.py: Tests that use these mocks
Dependencies:
    - typing, logging
    - person_suit core types
"""

import logging
import time
from typing import Any
from typing import Dict
from typing import List
from typing import Optional

from ...capabilities.capability import Capability
from ...context.unified import UnifiedContext
from ...effects.base_effect import BaseEffect
from ...events.event_types import EventID
from ...information.dual_information import DualInformation
from ...information.dual_information import StateRef
from ...notification.notification_types import BaseNotification
from ...security.permissions.permission_types import CapabilityScope
from ...security.permissions.permission_types import Permission
from .interfaces import CapabilityValidatorInterface
from .interfaces import EventLogError
from .interfaces import EventLogServiceInterface
from .interfaces import NotificationError
from .interfaces import NotificationServiceInterface
from .interfaces import StateStorageServiceInterface
from .interfaces import StateStoreError
from .interfaces import StateTransformationLogicInterface

logger = logging.getLogger(__name__)


class MockCapabilityValidator(CapabilityValidatorInterface):
    """Mock implementation of capability validator for testing."""

    def __init__(self, always_valid: bool = True):
        """
        Initialize the mock validator.

        Args:
            always_valid: If True, all validation requests will pass
        """
        self.always_valid = always_valid
        self.validation_history: List[Dict[str, Any]] = []

    def validate(
        self,
        capability: Capability,
        required_permission: Permission,
        required_scope: CapabilityScope,
        context: UnifiedContext,
        current_state: Optional[DualInformation] = None,
        effect: Optional[BaseEffect] = None,
    ) -> bool:
        """Record validation request and return configured result."""
        validation_record = {
            "capability": capability,
            "required_permission": required_permission,
            "required_scope": required_scope,
            "context": context,
            "current_state": current_state,
            "effect": effect,
            "timestamp": time.time(),
        }
        self.validation_history.append(validation_record)

        logger.debug(
            f"MockCapabilityValidator.validate called with capability={capability.capability_id}, "
            f"permission={required_permission}, scope={required_scope}"
        )
        return self.always_valid


class MockStateStorageService(StateStorageServiceInterface):
    """Mock implementation of state storage service for testing."""

    def __init__(self, initial_state: Optional[DualInformation] = None):
        """
        Initialize the mock storage service.

        Args:
            initial_state: Optional state to return for initial load
        """
        self.states: Dict[str, List[DualInformation]] = {}  # entity_id -> [states]
        self.initial_state = initial_state

    async def store_snapshot(self, state: DualInformation) -> None:
        """Store state snapshot in memory."""
        if not state.entity_id:
            logger.error("Cannot store state: missing entity_id")
            raise StateStoreError("State must have an entity_id")

        if state.entity_id not in self.states:
            self.states[state.entity_id] = []

        self.states[state.entity_id].append(state)
        logger.debug(
            f"Stored state snapshot for entity {state.entity_id}, version {state.version}"
        )

    async def load_latest_snapshot(self, entity_id: str) -> Optional[DualInformation]:
        """Load the latest snapshot for an entity."""
        if self.initial_state and not self.states.get(entity_id):
            return self.initial_state

        if entity_id in self.states and self.states[entity_id]:
            return self.states[entity_id][-1]

        return None

    async def load_snapshot_by_version(
        self, entity_id: str, version: int
    ) -> Optional[DualInformation]:
        """Load a specific version of a state snapshot."""
        if entity_id not in self.states:
            return None

        for state in self.states[entity_id]:
            if state.version == version:
                return state

        return None


class MockNotificationService(NotificationServiceInterface):
    """Mock implementation of notification service for testing."""

    def __init__(self):
        """Initialize the mock notification service."""
        self.notifications: List[BaseNotification] = []
        self.error_on_publish = False

    async def publish(self, notification: BaseNotification) -> None:
        """Store notification in memory."""
        if self.error_on_publish:
            raise NotificationError("Simulated notification error")

        self.notifications.append(notification)
        logger.debug(
            f"Published notification: type={type(notification).__name__}, "
            f"timestamp={notification.timestamp}"
        )


class MockEventLogService(EventLogServiceInterface):
    """Mock implementation of event logging service for testing."""

    def __init__(self):
        """Initialize the mock event log service."""
        self.events: List[Dict[str, Any]] = []
        self.error_on_log = False

    async def log_event(
        self,
        event_id: EventID,
        effect: BaseEffect,
        context: UnifiedContext,
        previous_state_ref: StateRef,
        resulting_state_ref: StateRef,
        entity_id: str,
        timestamp: float,
        capability_id: str,
        granter_id: str,
    ) -> None:
        """Store event in memory."""
        if self.error_on_log:
            raise EventLogError("Simulated event logging error")

        event_record = {
            "event_id": event_id,
            "effect_type": type(effect).__name__,
            "effect_id": effect.get_effect_id(),
            "context": context,
            "previous_state_ref": previous_state_ref,
            "resulting_state_ref": resulting_state_ref,
            "entity_id": entity_id,
            "timestamp": timestamp,
            "capability_id": capability_id,
            "granter_id": granter_id,
        }
        self.events.append(event_record)
        logger.debug(f"Logged event {event_id} for entity {entity_id}")

    async def get_events_for_entity(
        self,
        entity_id: str,
        start_time: Optional[float] = None,
        end_time: Optional[float] = None,
    ) -> List[Dict[str, Any]]:
        """Get filtered events for an entity."""
        filtered_events = [e for e in self.events if e["entity_id"] == entity_id]

        if start_time is not None:
            filtered_events = [
                e for e in filtered_events if e["timestamp"] >= start_time
            ]

        if end_time is not None:
            filtered_events = [e for e in filtered_events if e["timestamp"] <= end_time]

        return filtered_events


class MockStateTransformationLogic(StateTransformationLogicInterface):
    """Mock implementation of state transformation logic for testing."""

    def __init__(self, preconditions_met: bool = True):
        """
        Initialize the mock transformation logic.

        Args:
            preconditions_met: If True, all precondition checks will pass
        """
        self.preconditions_met = preconditions_met
        self.applied_effects: List[Dict[str, Any]] = []
        self.error_on_apply = False

    async def check_preconditions(
        self, effect: BaseEffect, current_state: DualInformation, context: UnifiedContext
    ) -> bool:
        """Check if preconditions are met based on configuration."""
        logger.debug(
            f"Checking preconditions for effect {effect.get_effect_id()} "
            f"on entity {current_state.entity_id}"
        )
        return self.preconditions_met

    async def apply_effect(
        self, effect: BaseEffect, current_state: DualInformation, context: UnifiedContext
    ) -> DualInformation:
        """Apply effect by creating a new state with incremented version."""
        if self.error_on_apply:
            raise ValueError("Simulated error applying effect")

        effect_record = {
            "effect": effect,
            "current_state": current_state,
            "context": context,
            "timestamp": time.time(),
        }
        self.applied_effects.append(effect_record)

        # Create new state with incremented version
        new_state = DualInformation(
            entity_id=current_state.entity_id,
            version=current_state.version + 1,
            data=current_state.data.copy(),  # Make a copy to avoid shared state
            vector=current_state.vector[:] if current_state.vector else None,
            timestamp=time.time(),
        )

        logger.debug(
            f"Applied effect {effect.get_effect_id()} to create new state "
            f"version {new_state.version}"
        )
        return new_state
