"""
File: interfaces.py
Purpose: Defines interfaces for services used by the Central State Actor
Related Files:
    - actor.py: Implementation of the Central State Actor
    - person_suit/core/information/dual_information.py: Contains the DualInformation class
    - person_suit/core/effects/base_effect.py: Contains the BaseEffect class
Dependencies:
    - abc, typing
"""

from abc import ABC
from abc import abstractmethod
from typing import Any
from typing import Dict
from typing import List
from typing import Optional

from ...capabilities.capability import Capability
from ...context.unified import UnifiedContext
from ...effects.base_effect import BaseEffect
from ...events.event_types import EventID
from ...information.dual_information import DualInformation
from ...information.dual_information import StateRef
from ...notification.notification_types import BaseNotification
from ...security.permissions.permission_types import CapabilityScope
from ...security.permissions.permission_types import Permission


class StateStorageServiceInterface(ABC):
    """Interface for storing and retrieving state snapshots."""

    @abstractmethod
    async def store_snapshot(self, state: DualInformation) -> None:
        """
        Store a state snapshot.

        Args:
            state: The state to store

        Raises:
            StateStoreError: If there's an error storing the state
        """
        pass

    @abstractmethod
    async def load_latest_snapshot(self, entity_id: str) -> Optional[DualInformation]:
        """
        Load the latest state snapshot for an entity.

        Args:
            entity_id: The ID of the entity

        Returns:
            The latest state snapshot, or None if no snapshot exists

        Raises:
            StateLoadError: If there's an error loading the state
        """
        pass

    @abstractmethod
    async def load_snapshot_by_version(
        self, entity_id: str, version: int
    ) -> Optional[DualInformation]:
        """
        Load a specific version of a state snapshot for an entity.

        Args:
            entity_id: The ID of the entity
            version: The version to load

        Returns:
            The requested state snapshot, or None if no such snapshot exists

        Raises:
            StateLoadError: If there's an error loading the state
        """
        pass


class NotificationServiceInterface(ABC):
    """Interface for publishing notifications."""

    @abstractmethod
    async def publish(self, notification: BaseNotification) -> None:
        """
        Publish a notification.

        Args:
            notification: The notification to publish

        Raises:
            NotificationError: If there's an error publishing the notification
        """
        pass


class EventLogServiceInterface(ABC):
    """Interface for logging state change events."""

    @abstractmethod
    async def log_event(
        self,
        event_id: EventID,
        effect: BaseEffect,
        context: UnifiedContext,
        previous_state_ref: StateRef,
        resulting_state_ref: StateRef,
        entity_id: str,
        timestamp: float,
        capability_id: str,
        granter_id: str,
    ) -> None:
        """
        Log a state change event.

        Args:
            event_id: Unique ID for the event
            effect: The effect that was applied
            context: UnifiedContext in which the effect was applied
            previous_state_ref: Reference to the previous state
            resulting_state_ref: Reference to the resulting state
            entity_id: The ID of the entity
            timestamp: The time the event occurred
            capability_id: The ID of the capability used to apply the effect
            granter_id: The ID of the entity that granted the capability

        Raises:
            EventLogError: If there's an error logging the event
        """
        pass

    @abstractmethod
    async def get_events_for_entity(
        self,
        entity_id: str,
        start_time: Optional[float] = None,
        end_time: Optional[float] = None,
    ) -> List[Dict[str, Any]]:
        """
        Get events for an entity within a time range.

        Args:
            entity_id: ID of the entity
            start_time: Start of time range (None for no lower bound)
            end_time: End of time range (None for no upper bound)

        Returns:
            List of event records
        """
        pass


class CapabilityValidatorInterface(ABC):
    """Interface for validating capabilities against permissions."""

    @abstractmethod
    def validate(
        self,
        capability: Capability,
        required_permission: Permission,
        required_scope: CapabilityScope,
        context: UnifiedContext,
        current_state: Optional[DualInformation] = None,
        effect: Optional[BaseEffect] = None,
    ) -> bool:
        """
        Validate a capability against required permissions.

        Args:
            capability: The capability to validate
            required_permission: The permission required for the operation
            required_scope: The scope required for the operation
            context: The context of the operation
            current_state: The current state (if relevant)
            effect: The effect being applied (if relevant)

        Returns:
            True if the capability has the required permission, False otherwise
        """
        pass


class StateTransformationLogicInterface(ABC):
    """Interface for state transformation logic."""

    @abstractmethod
    async def check_preconditions(
        self, effect: BaseEffect, current_state: DualInformation, context: UnifiedContext
    ) -> bool:
        """
        Check if preconditions for applying an effect are met.

        Args:
            effect: The effect to apply
            current_state: The current state
            context: The context of the operation

        Returns:
            True if preconditions are met, False otherwise
        """
        pass

    @abstractmethod
    async def apply_effect(
        self, effect: BaseEffect, current_state: DualInformation, context: UnifiedContext
    ) -> DualInformation:
        """
        Apply an effect to transform the state.

        Args:
            effect: The effect to apply
            current_state: The current state
            context: The context of the operation

        Returns:
            The new state after applying the effect

        Raises:
            ValueError: If the effect is not valid for the current state
        """
        pass


# Custom exceptions
class StateLoadError(Exception):
    """Raised when there's an error loading a state snapshot."""

    pass


class StateStoreError(Exception):
    """Raised when there's an error storing a state snapshot."""

    pass


class NotificationError(Exception):
    """Raised when there's an error publishing a notification."""

    pass


class EventLogError(Exception):
    """Raised when there's an error logging an event."""

    pass
