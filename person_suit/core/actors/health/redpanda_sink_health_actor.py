"""RedpandaSinkHealthActor
========================

Periodic actor that probes the Redpanda provenance sink to ensure events are
successfully written.  Emits health events so monitoring dashboards can
surface outage quickly.
"""

from __future__ import annotations

import asyncio
import contextlib
import logging

from person_suit.core.actors.base import Actor
from person_suit.core.actors.base import StandardActorMessage
from person_suit.core.infrastructure.bus.kernel import get_message_bus
from person_suit.core.infrastructure.hybrid_message import HybridMessage
from person_suit.core.provenance.backends.redpanda import get_redpanda_backend

logger = logging.getLogger(__name__)


class RedpandaSinkHealthActor(Actor):  # noqa: D101 – simple periodic actor
    """Periodic health check for the Redpanda provenance sink."""

    _poll_interval_sec: float = 10.0

    # ------------------------------------------------------------------ lifecycle hooks
    async def pre_start(self) -> None:  # noqa: D401 – override
        """Spawn the background probe coroutine when the actor starts."""
        self._probe_task = asyncio.create_task(self._probe_loop())  # type: ignore[attr-defined]

    async def post_stop(self) -> None:  # noqa: D401 – override
        """Ensure the probe coroutine terminates on actor shutdown."""
        if hasattr(self, "_probe_task"):
            self._probe_task.cancel()
            with contextlib.suppress(asyncio.CancelledError):
                await self._probe_task

    # ------------------------------------------------------------------ mandatory API
    async def receive(self, message: StandardActorMessage) -> None:  # noqa: D401
        """This actor does not process external messages."""
        logger.debug("RedpandaSinkHealthActor received unexpected message: %s", message.type)

    # ------------------------------------------------------------------ internals
    async def _probe_loop(self) -> None:  # noqa: D401
        backend = await get_redpanda_backend()
        bus = get_message_bus()

        while True:
            try:
                ok = await backend.is_alive()
            except Exception as exc:  # noqa: BLE001 – robustness
                logger.error("Redpanda health probe failed: %s", exc)
                ok = False

            await self._publish_health(bus, ok)
            await asyncio.sleep(self._poll_interval_sec)

    async def _publish_health(self, bus, is_ok: bool) -> None:  # noqa: D401
        """Publish a `sys.monitor.provenance.health` event via the bus."""
        evt = HybridMessage(
            message_type="EVENT",
            channel="sys.monitor.provenance.health",
            payload={
                "component": "redpanda_sink",
                "status": "healthy" if is_ok else "unhealthy",
            },
            response_expected=False,
            metadata={"record_provenance": False},
        )
        try:
            await bus.send(evt)
        except Exception as exc:  # noqa: BLE001
            logger.debug("Cannot publish health event: %s", exc) 