# -*- coding: utf-8 -*-
"""
Actor System Death Watch Implementation.

This module provides death watch functionality for the Actor System, allowing
actors to monitor other actors and be notified when they terminate.

CAW Alignment:
- Implements notification through standard actor messaging.
- Ensures proper capability checks for watch operations.
- Maintains bidirectional watch relationships for proper cleanup.

Related Files:
- person_suit.core.actors.actor_system.py (Main ActorSystem class)
- person_suit.core.actors.messages.py (Defines Terminated message)
"""

import asyncio
import logging
from typing import Dict
from typing import Optional
from typing import Set

from ..security.capabilities import CapabilityScope
from ..security.capabilities import CapabilityToken
from ..security.capabilities import Permission
from ..security.capabilities import verify_capability
from .base import ActorPath
from .base import ActorRef
from .base import StandardActorMessage
from .messages import Terminated

logger = logging.getLogger(__name__)


class DeathWatchManager:
    """Manages actor death watch functionality."""

    def __init__(self, actor_system):
        """
        Initialize death watch manager.

        Args:
            actor_system: Reference to the parent ActorSystem
        """
        self._actor_system = actor_system
        # Death Watch tracking:
        # watched -> {watcher1, watcher2}
        self._watchers: Dict[ActorPath, Set[ActorRef]] = {}
        # watcher -> {watched1, watched2}
        self._watching: Dict[ActorPath, Set[ActorRef]] = {}

    def clear(self):
        """Clear all watch relationships."""
        self._watchers.clear()
        self._watching.clear()

    async def watch(
        self,
        watcher_ref: ActorRef,
        target_ref: ActorRef,
        invoker_token: Optional[CapabilityToken] = None,
    ):
        """
        Register watcher_ref to receive Terminated when target_ref stops.
        Requires WATCH_ACTOR capability scoped to the target.

        Args:
            watcher_ref: Actor to be notified when target stops
            target_ref: Actor to watch
            invoker_token: Capability token for permission check

        Raises:
            PermissionError: If invoker lacks required capability
        """
        if not self._actor_system._running:
            logger.warning("Cannot watch on stopped system.")
            return

        # --- Capability Check --- #
        required_permission = Permission.WATCH_ACTOR
        required_scope = CapabilityScope.ACTOR(str(target_ref.path))
        if not verify_capability(invoker_token, required_permission, required_scope):
            raise PermissionError(
                f"Invoker {watcher_ref.path} lacks capability {required_permission.name} for target {target_ref.path} in scope {required_scope}"
            )
        # --- End Capability Check --- #

        # Prevent watching self
        if watcher_ref.path == target_ref.path:
            logger.warning(f"Actor {watcher_ref.path} cannot watch itself.")
            return

        target_actor = self._actor_system._actors.get(target_ref.path)
        if target_actor is None or not target_actor._running:
            # Target already dead or doesn't exist, notify immediately
            logger.debug(
                f"Actor {watcher_ref.path} watching non-existent/stopped actor {target_ref.path}. Notifying immediately."
            )
            term_msg = StandardActorMessage(
                type="Terminated",
                payload=Terminated(actor_ref=target_ref),
                context=None,
            )
            await self._actor_system.deliver_message(watcher_ref.path, term_msg)
        else:
            logger.debug(f"Actor {watcher_ref.path} now watching {target_ref.path}")
            # Initialize the set if not already present
            if target_ref.path not in self._watchers:
                self._watchers[target_ref.path] = set()
            if watcher_ref.path not in self._watching:
                self._watching[watcher_ref.path] = set()

            self._watchers[target_ref.path].add(watcher_ref)
            self._watching[watcher_ref.path].add(target_ref)  # Add inverse mapping

    async def unwatch(
        self,
        watcher_ref: ActorRef,
        target_ref: ActorRef,
        invoker_token: Optional[CapabilityToken] = None,
    ):
        """
        Unregister watcher_ref from watching target_ref.
        Requires WATCH_ACTOR capability scoped to the target.

        Args:
            watcher_ref: Actor that will stop watching
            target_ref: Actor that is being watched
            invoker_token: Capability token for permission check

        Raises:
            PermissionError: If invoker lacks required capability
        """
        if not self._actor_system._running:
            return

        # --- Capability Check --- #
        # Decide if UNWATCH needs its own permission or if WATCH is sufficient.
        # Let's assume WATCH is sufficient for now.
        required_permission = Permission.WATCH_ACTOR  # Or Permission.UNWATCH_ACTOR
        required_scope = CapabilityScope.ACTOR(str(target_ref.path))
        if not verify_capability(invoker_token, required_permission, required_scope):
            raise PermissionError(
                f"Invoker {watcher_ref.path} lacks capability {required_permission.name} for target {target_ref.path} in scope {required_scope}"
            )
        # --- End Capability Check --- #

        logger.debug(f"Actor {watcher_ref.path} unwatching {target_ref.path}")
        # Remove from watchers list
        watchers_set = self._watchers.get(target_ref.path)
        if watchers_set:
            watchers_set.discard(watcher_ref)
            if not watchers_set:
                del self._watchers[target_ref.path]

        # Remove from watching list
        watching_set = self._watching.get(watcher_ref.path)
        if watching_set:
            watching_set.discard(target_ref)
            if not watching_set:
                del self._watching[watcher_ref.path]

    async def notify_termination(self, terminated_ref: ActorRef):
        """
        Notify watchers and cleanup watches involving the terminated actor.

        Args:
            terminated_ref: Actor that has terminated
        """
        # 1. Notify actors watching the terminated actor
        watchers_to_notify = self._watchers.pop(terminated_ref.path, set())
        if watchers_to_notify:
            logger.debug(
                f"Notifying {len(watchers_to_notify)} watchers about termination of {terminated_ref.path}"
            )
            term_msg = StandardActorMessage(
                type="Terminated",
                payload=Terminated(actor_ref=terminated_ref),
                context=None,
            )
            notify_tasks = [
                self._actor_system.deliver_message(watcher.path, term_msg)
                for watcher in watchers_to_notify
            ]
            results = await asyncio.gather(*notify_tasks, return_exceptions=True)
            # Log errors during notification
            for i, result in enumerate(results):
                if isinstance(result, Exception):
                    watcher_ref = list(watchers_to_notify)[i]
                    logger.error(
                        f"Error sending Terminated message to watcher {watcher_ref.path}: {result}"
                    )

            # Also remove the terminated actor from each watcher's `_watching` set
            for watcher in watchers_to_notify:
                watching_set = self._watching.get(watcher.path)
                if watching_set:
                    watching_set.discard(terminated_ref)
                    if not watching_set:
                        del self._watching[watcher.path]

        # 2. Clean up watches initiated *by* the terminated actor
        targets_watched_by_terminated = self._watching.pop(terminated_ref.path, set())
        if targets_watched_by_terminated:
            logger.debug(
                f"Cleaning up {len(targets_watched_by_terminated)} watches initiated by terminated actor {terminated_ref.path}"
            )
            for target_ref in targets_watched_by_terminated:
                watchers_set = self._watchers.get(target_ref.path)
                if watchers_set:
                    watchers_set.discard(terminated_ref)
                    if not watchers_set:
                        del self._watchers[target_ref.path]
