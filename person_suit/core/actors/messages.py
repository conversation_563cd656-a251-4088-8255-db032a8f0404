# -*- coding: utf-8 -*-
"""
Module: person_suit.core.actors.messages
Purpose: Defines standard system messages used for actor lifecycle and supervision.
"""

from dataclasses import dataclass
from dataclasses import field
from typing import List
from typing import Optional
from uuid import UUID

from person_suit.core.capabilities.types import Capability  # canonical data class
from person_suit.core.security.capabilities import CapabilityScope
from person_suit.core.security.capabilities import Permission

from ..context.unified import UnifiedContext
from ..state_models import BaseEffect
from ..state_models import EventID
from ..state_models import RejectionErrorType
from ..state_models import StateRef
from ..state_models import StateUpdateStatus
from .base import ActorPath
from .base import ActorRef

# --- Death Watch Messages ---


@dataclass
class Terminated:
    """System message indicating an actor has terminated."""

    actor_ref: ActorRef  # Reference to the actor that terminated
    # Optionally include reason, existence_confirmed, address_terminated?


@dataclass
class Watch:
    """System message requesting to watch another actor."""

    target_ref: ActorRef  # Actor to be watched
    watcher_ref: ActorRef  # Actor doing the watching


@dataclass
class Unwatch:
    """System message requesting to stop watching another actor."""

    target_ref: Actor<PERSON>ef  # Actor that was being watched
    watcher_ref: ActorRef  # Actor that was watching


# --- Supervision Messages (Optional - could be handled by direct calls) ---
# Example: If supervisor needs to explicitly message the system


@dataclass
class RestartActor:
    """Request to restart a specific actor."""

    actor_ref: ActorRef
    reason: Exception


@dataclass
class StopActor:
    """Request to stop a specific actor."""

    actor_ref: ActorRef
    reason: Optional[Exception] = None


# --- Capability Service Messages ---


@dataclass
class RequestInitialCapabilities:
    """Request for the initial set of capabilities for a new actor."""

    requester_ref: ActorRef  # The actor needing capabilities (usually the new one)
    actor_path: ActorPath  # Path of the new actor
    actor_class_name: str  # Class name for potential role-based initial caps
    parent_ref: Optional[ActorRef] = None  # Parent, if any
    creator_token: Optional[Capability] = None  # Token used to create the actor


@dataclass
class RequestDelegatedCapability:
    """Request to delegate a capability from an existing one."""

    requester_ref: ActorRef  # Actor requesting delegation
    parent_capability: Capability  # The capability being delegated FROM
    requested_permissions: List[Permission]  # Subset of permissions requested
    requested_scope: Optional[CapabilityScope] = (
        None  # Optional further restriction of scope
    )
    # Add requested constraints, target subject etc. if needed


@dataclass
class GrantCapabilitiesReply:
    """Reply granting one or more capabilities."""

    granted_capabilities: List[Capability]
    success: bool = True
    error: Optional[str] = None


"""
Actor System Message Types

Defines message types used for actor communication, particularly
for state update requests and replies.
"""

@dataclass
class StateUpdateEffectRequest:
    """Message sent from an Updater Actor to the Central State Actor."""
    request_id: UUID
    entity_id: str
    effect: BaseEffect
    context: UnifiedContext
    
    
@dataclass
class StateUpdateReply:
    """Base class for the direct reply from the State Actor."""
    request_id: UUID
    entity_id: str
    event_id: EventID
    status: StateUpdateStatus
    timestamp: float


@dataclass
class StateUpdateAcceptedReply(StateUpdateReply):
    """Reply on successful validation and acceptance of the Effect request."""
    status: StateUpdateStatus = field(default=StateUpdateStatus.ACCEPTED, init=False)
    new_state_ref: StateRef = None  # The new state reference after update


@dataclass
class StateUpdateRejectedReply(StateUpdateReply):
    """Reply on failed validation or rejection of the Effect request."""
    status: StateUpdateStatus = field(default=StateUpdateStatus.REJECTED, init=False)
    error_type: RejectionErrorType = RejectionErrorType.INTERNAL_ERROR
    error_message: str = ""
