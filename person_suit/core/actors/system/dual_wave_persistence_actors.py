"""Dual-Wave Persistence Actor Wrappers

This module provides thin Actor envelopes around the remaining unmanaged
``while True`` loops in the Dual-Wave persistence subsystem.  Their purpose is
purely to satisfy Sprint-3 acceptance criteria – every long-running service must
execute under Actor supervision so uptime and restart metrics are captured.

NOTE: These actors currently act as *proxies* only – they instantiate the
underlying service classes (TieredStorageManager, NodeManager, etc.), start
/stop them, and otherwise ignore incoming messages.  Full message-based
adapter logic will be implemented in Sprint-4+ once capability-aware routing is
available.
"""

from __future__ import annotations

import asyncio
import contextlib  # local import for readability
import logging
from typing import Any
from typing import Optional

from person_suit.core.actors.base import Actor
from person_suit.core.actors.base import StandardActorMessage
from person_suit.core.actors.decorators import actor_service

logger = logging.getLogger(__name__)


# ---------------------------------------------------------------------------
# Helper utilities
# ---------------------------------------------------------------------------


def _safe_import(path: str) -> Optional[Any]:  # noqa: D401 – internal helper
    """Safely import a module given its dotted *path*.

    Returns the imported module or *None* if import failed.  Errors are logged
    but not raised so that Actor registration can gracefully degrade on missing
    optional dependencies.
    """
    try:
        import importlib

        return importlib.import_module(path)
    except ModuleNotFoundError as err:  # pragma: no cover – optional deps
        logger.warning("Optional Dual-Wave dependency missing: %s", err.name)
        return None
    except Exception as exc:  # pragma: no cover – exotic import failure
        logger.error("Failed to import %s: %s", path, exc)
        return None


# ---------------------------------------------------------------------------
# Tiered Storage Migration Actor
# ---------------------------------------------------------------------------


@actor_service
class TieredStorageActor(Actor):  # noqa: D101 – placeholder wrapper
    _manager: Any | None = None
    _loop_task: asyncio.Task | None = None

    async def pre_start(self) -> None:  # noqa: D401
        dw_mod = _safe_import(
            "person_suit.core.infrastructure.dual_wave.persistence.tiered_storage",
        )
        if not dw_mod:  # Dependency missing – disable actor
            return
        manager_cls = getattr(dw_mod, "TieredStorageManager", None)
        if not manager_cls:
            logger.warning("TieredStorageManager not found in dual_wave module")
            return

        self._manager = manager_cls()
        # Initialise backends lazily to avoid long startup block
        try:
            await self._manager._initialize_storage_backends()  # noqa: WPS437
        except Exception as exc:  # pragma: no cover – resilience
            logger.error("TieredStorageActor failed to init backends: %s", exc)

        # Start migration loop under our supervision
        async def _migration_loop() -> None:  # noqa: WPS430
            while self._running:
                try:
                    await self._manager.run_migration_cycle()
                    # Sleep according to config interval (default 1h)
                    interval = getattr(
                        self._manager.config, "migration_interval_seconds", 3600,
                    )
                    await asyncio.sleep(interval)
                except asyncio.CancelledError:  # Graceful shutdown
                    break
                except Exception as exc:  # pragma: no cover
                    logger.exception("TieredStorage migration cycle failed: %s", exc)
                    await asyncio.sleep(30)

        self._loop_task = asyncio.create_task(_migration_loop())

    async def post_stop(self) -> None:  # noqa: D401
        if self._loop_task:
            self._loop_task.cancel()
            with contextlib.suppress(asyncio.CancelledError):
                await self._loop_task  # type: ignore[arg-type]
        if self._manager and hasattr(self._manager, "close"):
            try:
                await self._manager.close()
            except Exception:  # pragma: no cover
                logger.debug("TieredStorageManager close raised but suppressed.")

    async def receive(self, message: StandardActorMessage) -> None:  # noqa: D401
        # No external control messages yet – placeholder.
        pass


# ---------------------------------------------------------------------------
# Distributed Node Actor
# ---------------------------------------------------------------------------


@actor_service
class DualWaveNodeActor(Actor):  # noqa: D101
    _node_manager: Any | None = None

    async def pre_start(self) -> None:  # noqa: D401
        dw_mod = _safe_import(
            "person_suit.core.infrastructure.dual_wave.persistence.distributed.node",
        )
        if not dw_mod:
            return
        manager_cls = getattr(dw_mod, "NodeManager", None)
        config_cls = getattr(dw_mod, "DistributedStorageConfig", None)
        if not manager_cls or not config_cls:
            logger.warning("NodeManager/Config missing in dual_wave.node module")
            return
        try:
            self._node_manager = manager_cls(config_cls())  # type: ignore[call-arg]
            await self._node_manager.start()
        except Exception as exc:  # pragma: no cover – robustness
            logger.error("Failed to start DualWave NodeManager: %s", exc)

    async def post_stop(self) -> None:  # noqa: D401
        if self._node_manager and hasattr(self._node_manager, "stop"):
            try:
                await self._node_manager.stop()
            except Exception:  # pragma: no cover
                logger.debug("NodeManager stop raised but suppressed.")

    async def receive(self, message: StandardActorMessage) -> None:  # noqa: D401
        # Placeholder – will implement control plane messages later.
        pass


# ---------------------------------------------------------------------------
# Coordination / Replication / Retry Actors (stubs)
# ---------------------------------------------------------------------------


@actor_service
class DualWaveCoordinationActor(Actor):  # noqa: D101
    async def receive(self, message: StandardActorMessage) -> None:  # noqa: D401
        pass  # TODO: implement real coordination logic in Sprint-4


@actor_service
class DualWaveReplicationActor(Actor):  # noqa: D101
    async def receive(self, message: StandardActorMessage) -> None:  # noqa: D401
        pass  # TODO: implement real replication logic in Sprint-4


@actor_service
class DualWaveRetryActor(Actor):  # noqa: D101
    async def receive(self, message: StandardActorMessage) -> None:  # noqa: D401
        pass  # TODO: implement real retry/back-off logic in Sprint-4 