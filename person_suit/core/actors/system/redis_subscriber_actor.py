"""RedisSubscriberActor

Actor-wrapper for Redis Pub/Sub subscribers.  It allows the Redis blocking
subscription loop to run under standard Actor supervision so crashes or network
interruptions trigger exponential back-off restarts.
"""

from __future__ import annotations

import asyncio
import json
import logging
from typing import Optional

import redis.asyncio as redis

from person_suit.core.actors.base import Actor
from person_suit.core.actors.base import ActorPath
from person_suit.core.actors.base import StandardActorMessage
from person_suit.core.actors.decorators import actor_service

logger = logging.getLogger(__name__)


@actor_service
class RedisSubscriberActor(Actor):  # noqa: D101
    _reconnect_delay: float = 5.0

    def __init__(self, entity_id: str, redis_cfg: Optional[dict] = None):
        super().__init__()
        self._entity_id = entity_id
        self._redis_cfg = redis_cfg or {}
        self._task: Optional[asyncio.Task] = None

    # ---------------------------------------------------------------------
    async def pre_start(self) -> None:  # noqa: D401
        await super().pre_start()
        self._task = asyncio.create_task(self._run_subscriber_loop())

    async def post_stop(self) -> None:  # noqa: D401
        if self._task:
            self._task.cancel()
            with contextlib.suppress(asyncio.CancelledError):
                await self._task
        await super().post_stop()

    # ------------------------------------------------------------------
    async def _run_subscriber_loop(self) -> None:  # noqa: WPS430
        channel_name = f"caw_events:{self._entity_id}"
        while self._running:
            try:
                client = redis.Redis(**self._redis_cfg)
                async with client.pubsub() as pubsub:
                    await pubsub.subscribe(channel_name)
                    logger.info("Subscribed to Redis Pub/Sub channel %s", channel_name)
                    while self._running:
                        try:
                            message = await pubsub.get_message(
                                ignore_subscribe_messages=True,
                                timeout=1.0,
                            )
                            if message is None:
                                continue
                            # Broadcast internal message (could route to bus)
                            payload = json.loads(message["data"])
                            logger.debug("RedisSubscriberActor received %s", payload)
                        except asyncio.TimeoutError:
                            continue
            except Exception as exc:  # pragma: no cover – keep alive
                logger.error("RedisSubscriberActor error: %s", exc)
                await asyncio.sleep(self._reconnect_delay)

    async def receive(self, message: StandardActorMessage) -> None:  # noqa: D401
        # Future: allow dynamic unsubscribe/close.
        pass

    async def get_actor_id(self) -> str:  # noqa: D401
        return f"redis_subscriber:{self._entity_id}"

    @property  # type: ignore[override]
    def path(self) -> ActorPath:  # noqa: D401
        return ActorPath(system_name="redis", path_elements=(self._entity_id,)) 