"""ChannelProcessor<PERSON>ctor

Wraps a `Channel` instance (core.infrastructure.communication.Channel) in a CAW
Actor envelope so its internal message‐processing loop runs under supervision.

Sprint-3 migration: replaces unmanaged `while True` loop.
"""

from __future__ import annotations

from person_suit.core.actors.base import Actor
from person_suit.core.actors.base import ActorPath
from person_suit.core.actors.base import StandardActorMessage
from person_suit.core.actors.decorators import actor_service
from person_suit.core.infrastructure.communication.channel import Channel


@actor_service
class ChannelProcessorActor(Actor):  # noqa: D101
    def __init__(self, channel: Channel):
        super().__init__()
        self._channel = channel

    async def pre_start(self) -> None:  # noqa: D401
        await super().pre_start()
        # Open the channel – this spawns its internal processing task
        try:
            await self._channel.open()
        except Exception as exc:  # pragma: no cover – supervision will handle
            import logging

            logging.getLogger(__name__).error("Failed to open channel %s: %s", self._channel.metadata.channel_id, exc)
            raise

    async def post_stop(self) -> None:  # noqa: D401
        # Ensure channel is closed cleanly
        try:
            await self._channel.close()
        except Exception:  # pragma: no cover
            pass
        await super().post_stop()

    async def receive(self, message: StandardActorMessage) -> None:  # noqa: D401
        # Currently no external control messages – could add flush/close later.
        pass

    # --- Protocol helpers -------------------------------------------------

    async def get_actor_id(self) -> str:  # noqa: D401
        return f"channel_processor:{self._channel.metadata.channel_id}"

    @property  # type: ignore[override]
    def path(self) -> ActorPath:  # noqa: D401
        cid = self._channel.metadata.channel_id
        return ActorPath(system_name="channel", path_elements=(cid,)) 