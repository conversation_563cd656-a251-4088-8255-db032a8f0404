"""Stub SystemHealthActor for legacy tests."""

from enum import Enum
from enum import auto
from typing import Any


class SystemState(Enum):
    UNKNOWN = auto()
    HEALTHY = auto()
    DEGRADED = auto()
    UNHEALTHY = auto()


class SystemHealthActor:  # pragma: no cover
    """Placeholder actor that always reports HEALTHY."""

    def __init__(self, *args: Any, **kwargs: Any) -> None:  # noqa: D401, ANN001, D401
        self.state = SystemState.HEALTHY

    async def get_state(self) -> SystemState:  # noqa: D401
        return self.state