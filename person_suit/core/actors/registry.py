from __future__ import annotations

import asyncio
import logging
import time
from collections import defaultdict
from typing import Any
from typing import Dict
from typing import List
from typing import Optional
from typing import Set

from .protocols import ActorCapability
from .protocols import ActorProtocol
from .protocols import RegisteredActor

logger = logging.getLogger(__name__)

class ActorRegistry:
    """
    Protocol-compliant, capability-based actor registry.
    This implementation adheres strictly to the ActorRegistryProtocol.
    """
    
    def __init__(self):
        self._actors: Dict[str, RegisteredActor] = {}
        self._capability_index: Dict[str, Set[str]] = defaultdict(set)
        self._protocol_index: Dict[str, Set[str]] = defaultdict(set)
        self._health_check_interval = 30.0
        self._health_check_task: Optional[asyncio.Task] = None
        self._lock = asyncio.Lock()
        
    async def start(self):
        """Start the registry background tasks."""
        if self._health_check_task is None:
            self._health_check_task = asyncio.create_task(self._health_check_loop())
            logger.info("ActorRegistry health checker started.")

    async def stop(self):
        """Stop the registry background tasks."""
        if self._health_check_task:
            self._health_check_task.cancel()
            try:
                await self._health_check_task
            except asyncio.CancelledError:
                pass
            self._health_check_task = None
            logger.info("ActorRegistry health checker stopped.")

    async def register_actor(self, actor: ActorProtocol) -> bool:
        """Registers an actor based on the ActorProtocol."""
        try:
            actor_id = await actor.get_actor_id()
            capabilities = await actor.get_capabilities()
            health_status = await actor.get_health_status()
            protocol_type = self._infer_protocol_type(capabilities)
            
            async with self._lock:
                registered_actor = RegisteredActor(
                    actor_id=actor_id,
                    protocol_type=protocol_type,
                    capabilities=capabilities,
                    endpoint=f"actor://{actor_id}",
                    health_status=health_status.get("status", "unknown"),
                    last_seen=time.time(),
                    load_factor=health_status.get("load_factor", 0.0),
                    metadata=health_status.get("metadata", {})
                )
                
                self._actors[actor_id] = registered_actor
                self._protocol_index[protocol_type].add(actor_id)
                for cap in capabilities:
                    self._capability_index[cap.name].add(actor_id)
                
                logger.info(f"Registered actor '{actor_id}' with protocol '{protocol_type}' and {len(capabilities)} capabilities.")
                return True
        except Exception as e:
            logger.error(f"Failed to register actor: {e}", exc_info=True)
            return False

    async def unregister_actor(self, actor_id: str) -> bool:
        """Unregisters an actor by its ID."""
        async with self._lock:
            actor_to_remove = self._actors.pop(actor_id, None)
            if not actor_to_remove:
                return False
            
            self._protocol_index[actor_to_remove.protocol_type].discard(actor_id)
            for cap in actor_to_remove.capabilities:
                self._capability_index[cap.name].discard(actor_id)
        
        logger.info(f"Unregistered actor '{actor_id}'.")
        return True

    async def find_actors_by_capability(self, capability: str) -> List[RegisteredActor]:
        """Finds healthy actors that provide a specific capability, sorted by load."""
        async with self._lock:
            actor_ids = self._capability_index.get(capability, set())
            actors = [self._actors[aid] for aid in actor_ids if aid in self._actors and self._actors[aid].health_status == "healthy"]
        
        actors.sort(key=lambda a: a.load_factor)
        return actors

    async def get_actor_by_id(self, actor_id: str) -> Optional[RegisteredActor]:
        """Gets a specific actor by its ID."""
        async with self._lock:
            return self._actors.get(actor_id)

    async def get_all_actors(self) -> List[RegisteredActor]:
        """Gets all registered actors."""
        async with self._lock:
            return list(self._actors.values())

    async def get_registry_stats(self) -> Dict[str, Any]:
        """Returns statistics about the registry's state."""
        async with self._lock:
            return {
                "total_actors": len(self._actors),
                "total_capabilities": len(self._capability_index),
                "total_protocols": len(self._protocol_index),
                "actors_by_protocol": {
                    proto: len(ids) for proto, ids in self._protocol_index.items()
                }
            }

    async def health_check_all(self) -> Dict[str, str]:
        """Performs a health check on all registered actors."""
        # This is a simplified health check. A real implementation would ping actors.
        async with self._lock:
            health_status = {}
            for actor_id, actor in self._actors.items():
                if actor.last_seen and (time.time() - actor.last_seen) < 60:
                    health_status[actor_id] = "healthy"
                else:
                    health_status[actor_id] = "stale"
            return health_status
            
    def _infer_protocol_type(self, capabilities: Set[ActorCapability]) -> str:
        """Infers protocol type from capabilities."""
        names = {cap.name for cap in capabilities}
        if any(n.startswith("effect.") for n in names): return "EffectActorProtocol"
        if any(n.startswith("memory.") for n in names): return "MemoryActorProtocol"
        return "ActorProtocol"

    async def _health_check_loop(self):
        """Background task to periodically check actor health."""
        while True:
            await asyncio.sleep(self._health_check_interval)
            try:
                latest_health = await self.health_check_all()
                async with self._lock:
                    for actor_id, status in latest_health.items():
                        if actor_id in self._actors:
                            self._actors[actor_id].health_status = status
            except asyncio.CancelledError:
                break
            except Exception as e:
                logger.error(f"Error in health check loop: {e}", exc_info=True)

# --- Global Singleton Management ---
_registry_instance: Optional[ActorRegistry] = None
_registry_lock = asyncio.Lock()

async def get_registry() -> ActorRegistry:
    """Initializes and returns the global actor registry singleton."""
    global _registry_instance
    if _registry_instance is None:
        async with _registry_lock:
            if _registry_instance is None:
                _registry_instance = ActorRegistry()
                await _registry_instance.start()
    return _registry_instance