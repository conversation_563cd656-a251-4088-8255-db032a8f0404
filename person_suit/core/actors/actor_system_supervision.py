# -*- coding: utf-8 -*-
"""
Actor System Supervision Implementation.

This module provides supervision functionality for the Actor System, handling actor
failures according to supervision strategies and managing actor lifecycle.

CAW Alignment:
- Uses supervision strategies for resilience.
- Supports hierarchical error handling (escalation to parents).
- Ensures proper capability checks for actor operations.

Related Files:
- person_suit.core.actors.actor_system.py (Main ActorSystem class)
- person_suit.core.actors.supervision.py (Defines SupervisionStrategy classes)
"""

import asyncio
import logging
import os
import time
from typing import Dict
from typing import Optional
from typing import Tuple

from ..security.capabilities import CapabilityScope
from ..security.capabilities import CapabilityToken
from ..security.capabilities import Permission
from ..security.capabilities import verify_capability
from .base import ActorRef
from .base import StandardActorMessage
from .supervision import SupervisorDirective

logger = logging.getLogger(__name__)


class SupervisionManager:
    """Manages actor supervision and lifecycle."""

    def __init__(self, actor_system):
        """
        Initialize supervision manager.

        Args:
            actor_system: Reference to the parent ActorSystem
        """
        self._actor_system = actor_system
        # Track restart counts & timestamps for exponential back-off
        self._restart_state: Dict[str, Tuple[int, float]] = {}
        self._max_backoff: float = float(os.getenv("ACTOR_BACKOFF_MAX", "60"))

    async def handle_actor_failure(
        self,
        failed_actor_ref: ActorRef,
        exception: Exception,
        message: Optional[StandardActorMessage],
    ) -> bool:
        """
        Handle an exception from an actor according to supervision strategy.
        Called by the Actor's _run loop.

        Args:
            failed_actor_ref: Reference to the actor that failed
            exception: The exception that occurred
            message: The message being processed when the failure occurred

        Returns:
            bool: True if the actor should resume/continue, False if it should stop
        """
        actor = self._actor_system._actors.get(failed_actor_ref.path)
        if not actor:
            logger.error(f"Cannot supervise non-existent actor {failed_actor_ref.path}")
            return False  # Should stop if it doesn't exist

        strategy = (
            actor.context.supervision_strategy or self._actor_system._default_supervisor
        )
        parent_ref = actor.context.parent

        try:
            # Decide directive based on the strategy
            directive = await strategy.decide(exception)
        except Exception as strategy_err:
            logger.critical(
                f"CRITICAL: Supervision strategy {type(strategy).__name__} failed for actor {failed_actor_ref.path}: {strategy_err}",
                exc_info=True,
            )
            directive = SupervisorDirective.STOP  # Stop if strategy fails

        logger.info(
            f"Supervisor directive for {failed_actor_ref.path} due to {type(exception).__name__}: {directive.name}"
        )

        if directive == SupervisorDirective.RESUME:
            logger.info(f"Resuming actor {failed_actor_ref.path} after exception.")
            return True  # Continue running

        elif directive == SupervisorDirective.STOP:
            logger.warning(
                f"Stopping actor {failed_actor_ref.path} due to supervisor directive."
            )
            await self.stop_actor(failed_actor_ref)  # Ensure cleanup and notification
            return False  # Stop running

        elif directive == SupervisorDirective.RESTART:
            logger.warning(
                f"Restarting actor {failed_actor_ref.path} due to supervisor directive."
            )
            await self.restart_actor(failed_actor_ref, exception)
            # Actor is restarted, effectively continuing, but the old instance stops
            return True  # Signal to original instance's loop to potentially exit if restart replaces it

        elif directive == SupervisorDirective.ESCALATE:
            logger.warning(
                f"Escalating failure from {failed_actor_ref.path} to parent."
            )
            if parent_ref:
                # Delegate to parent supervisor (recursive call basically)
                # Parent handles failure of the *child* (failed_actor_ref)
                return await self.handle_actor_failure(
                    failed_actor_ref, exception, message
                )
            else:
                # No parent, escalate means stop at the top level
                logger.error(
                    f"Top-level actor {failed_actor_ref.path} escalated failure. Stopping actor."
                )
                await self.stop_actor(failed_actor_ref)
                return False
        else:
            logger.error(
                f"Unknown supervisor directive {directive}. Stopping actor {failed_actor_ref.path}."
            )
            await self.stop_actor(failed_actor_ref)
            return False

    async def stop_actor(
        self, actor_ref: ActorRef, invoker_token: Optional[CapabilityToken] = None
    ):
        """
        Stop a single actor and clean up its resources and watches.
        Requires STOP_ACTOR capability scoped to the target actor.

        Args:
            actor_ref: Reference to the actor to stop
            invoker_token: Capability token for permission check

        Raises:
            PermissionError: If invoker lacks required capability
        """
        # --- Capability Check --- #
        required_permission = Permission.STOP_ACTOR
        required_scope = CapabilityScope.ACTOR(str(actor_ref.path))
        if not verify_capability(invoker_token, required_permission, required_scope):
            raise PermissionError(
                f"Invoker lacks capability {required_permission.name} for target {actor_ref.path} in scope {required_scope}"
            )
        # --- End Capability Check --- #

        actor = self._actor_system._actors.get(actor_ref.path)
        if actor:
            logger.info(f"Stopping actor {actor_ref.path} via system request.")
            # Stop the actor's run loop and call post_stop
            # actor.stop() now calls notify_termination internally after post_stop
            await actor.stop()

            # Remove actor from registry *after* stop completes and notifications are sent
            self._actor_system._actors.pop(actor_ref.path, None)

            # Remove from parent's children list
            if actor.context.parent:
                parent_actor = self._actor_system._actors.get(actor.context.parent.path)
                if parent_actor:
                    parent_actor.context.children.discard(actor_ref)
            # Watch cleanup is now handled within notify_termination, called by actor.stop()
        else:
            logger.warning(f"Request to stop non-existent actor: {actor_ref.path}")

    async def restart_actor(
        self,
        actor_ref: ActorRef,
        reason: Exception,
        invoker_token: Optional[CapabilityToken] = None,
    ):
        """
        Restart a failed actor.
        Requires STOP_ACTOR and CREATE_CHILD (implicitly checked by stop_actor/create_actor).

        Args:
            actor_ref: Reference to the actor to restart
            reason: The reason for restarting the actor
            invoker_token: Capability token for permission check

        Raises:
            PermissionError: If invoker lacks required capability and a token was provided
        """

        start_ts = time.time()

        COOLDOWN_S = 120.0  # reset window
        restart_history = self._restart_state.setdefault(str(actor_ref.path), [])  # type: ignore[assignment]
        # prune history outside cooldown
        restart_history[:] = [ts for ts in restart_history if start_ts - ts < COOLDOWN_S]
        restart_history.append(start_ts)
        restart_count = len(restart_history)
        delay = min(2 ** (restart_count - 1), self._max_backoff)
        if delay > 0:
            logger.warning(
                "Back-off: delaying restart of %s by %.1fs (restart #%s in %.0fs window)",
                actor_ref.path,
                delay,
                restart_count,
                COOLDOWN_S,
            )
            await asyncio.sleep(delay)

        # Capability Check: Need permission to stop the existing actor
        required_permission = Permission.STOP_ACTOR
        required_scope = CapabilityScope.ACTOR(str(actor_ref.path))
        if not verify_capability(invoker_token, required_permission, required_scope):
            # If invoker_token is None, this implies system/parent privilege assumed.
            # Only raise if a token *was* provided but is insufficient.
            if invoker_token is not None:
                raise PermissionError(
                    f"Invoker lacks capability {required_permission.name} for target {actor_ref.path} in scope {required_scope} needed for restart."
                )
            else:
                # Log assumption of privilege if no token provided for direct restart call
                logger.debug(
                    f"Assuming privilege for restart_actor({actor_ref.path}) as no invoker_token was provided."
                )

        actor = self._actor_system._actors.get(actor_ref.path)
        if not actor:
            logger.error(f"Cannot restart non-existent actor {actor_ref.path}")
            return

        logger.info(f"Attempting to restart actor {actor_ref.path} due to: {reason!r}")

        # Preserve context info needed for recreation
        original_context = actor.context
        parent_ref = original_context.parent
        actor_class = type(actor)  # Get the class of the instance
        # Retrieve original args/kwargs from the context
        args = original_context.creation_args
        kwargs = original_context.creation_kwargs
        name = actor_ref.path.name
        strategy = original_context.supervision_strategy

        # Stop the old instance first (this also notifies watchers)
        await self.stop_actor(actor_ref)

        # Recreate the actor
        try:
            logger.debug(f"Recreating actor {actor_ref.path}...")
            # Use create_actor to handle registration, context creation, start
            new_ref = await self._actor_system.create_actor(
                actor_class=actor_class,
                name=name,
                parent=parent_ref,
                supervision_strategy=strategy,
                args=args,
                kwargs=kwargs,
                invoker_token=None,  # Explicitly None, assuming system privilege for restart creation
            )
            # Call post_restart hook on the *new* instance
            new_actor = self._actor_system._actors.get(new_ref.path)
            if new_actor:
                await new_actor.post_restart(reason)
            logger.info(
                f"Successfully restarted actor {actor_ref.path} as {new_ref.path}"
            )

            try:
                from person_suit.monitoring.metrics import ACTOR_MTTR_SECONDS  # noqa: WPS433
                from person_suit.monitoring.metrics import ACTOR_RESTARTS_TOTAL  # noqa: WPS433
                from person_suit.monitoring.metrics import ACTOR_UPTIME_SECONDS  # noqa: WPS433

                elapsed = time.time() - start_ts
                ACTOR_RESTARTS_TOTAL.labels(actor_path=str(actor_ref.path)).inc()
                ACTOR_MTTR_SECONDS.labels(actor_path=str(actor_ref.path)).set(elapsed)
                ACTOR_UPTIME_SECONDS.remove(actor_path=str(actor_ref.path))  # type: ignore[attr-defined]
            except Exception:
                pass
            # Reset restart count on success
            self._restart_state[str(actor_ref.path)] = (0, time.time())
        except Exception as restart_err:
            logger.critical(
                f"CRITICAL: Failed to restart actor {actor_ref.path} after stopping. Error: {restart_err}",
                exc_info=True,
            )
            # Actor failed to restart, potentially escalate this failure?
