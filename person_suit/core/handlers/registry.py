from __future__ import annotations

import asyncio
import logging
from typing import TYPE_CHECKING
from typing import Any
from typing import Callable
from typing import Coroutine
from typing import Dict

if TYPE_CHECKING:
    # This avoids circular imports at runtime, which is crucial for
    # maintaining a clean architecture. The HybridMessageBus is a central
    # component, and this pattern prevents it from being a hard dependency
    # at module load time.
    from person_suit.core.infrastructure.hybrid_message_bus import HybridMessageBus


class CommandHandlerRegistry:
    """A singleton registry for command handlers.

    This class follows the singleton pattern to ensure that all parts of the
    application access the same registry instance, which is essential for the
    decorator-based registration to work reliably.
    """

    _instance: CommandHandlerRegistry | None = None

    def __new__(cls, *args: Any, **kwargs: Any) -> CommandHandlerRegistry:
        if cls._instance is None:
            cls._instance = super().__new__(cls)
        return cls._instance

    def __init__(self) -> None:
        if not hasattr(self, "initialized"):
            # The handlers dictionary stores callables for each channel.
            self.handlers: Dict[str, Callable[..., Coroutine[Any, Any, Any]]] = {}
            self.bus: HybridMessageBus | None = None
            self.initialized = True

    def set_bus(self, bus: HybridMessageBus) -> None:
        """Sets the message bus instance for the registry.

        This should be called once during application startup.
        """
        self.bus = bus

    async def bind_bus(self, bus: HybridMessageBus) -> None:
        """Binds the message bus and subscribes all registered handlers.
        
        This is the main integration method that should be called during startup.
        It combines set_bus() and subscribe_all() for production deployment.
        """
        logger = logging.getLogger(__name__)
        logger.info("="*60)
        logger.info("[REGISTRY] Binding command handler registry to message bus")
        logger.info(f"[REGISTRY] Number of registered handlers: {len(self.handlers)}")
        logger.debug(f"[REGISTRY] Registered channels: {list(self.handlers.keys())}")
        
        self.bus = bus
        logger.info("[REGISTRY] Bus reference set")
        
        await self.subscribe_all()
        logger.info("[REGISTRY] All handlers subscribed")
        logger.info("="*60)

    def register(
        self, channel: str,
        handler: Callable[..., Coroutine[Any, Any, Any]],
        *,
        priority: int = 0,
    ) -> None:
        """Register *handler* for *channel* with optional *priority*.

        If the message bus is already attached and running this call will
        automatically subscribe the handler, eliminating the need for an
        explicit ``bind_bus`` in most production code paths.  This keeps the
        decorator syntax simple while preserving the fully-deterministic
        bootstrap sequence required by tests.
        """

        if channel in self.handlers:
            raise ValueError(
                f"Handler for channel '{channel}' is already registered.")

        # Store (handler, priority) so subscribe_all can use the priority
        self.handlers[channel] = (handler, priority)  # type: ignore[assignment]

        # ------------------------------------------------------------------
        # Auto-subscribe when the bus is already initialised.  We schedule
        # the coroutine via ``asyncio.create_task`` to avoid making the
        # register() API async and to keep decorator usage ergonomic.
        # ------------------------------------------------------------------
        bus_ref = self.bus
        if bus_ref is not None:
            import asyncio  # noqa: WPS433  – local import to avoid event loop check at module import

            async def _auto_sub() -> None:  # noqa: D401 – inner helper
                try:
                    await bus_ref.subscribe(
                        channel,
                        handler,
                        subscriber_id=f"command_handler_{channel}",
                        handler_priority=priority,
                    )
                    logging.getLogger(__name__).debug(
                        "Auto-subscribed command handler %s", channel,
                    )
                except Exception as exc:  # noqa: BLE001
                    logging.getLogger(__name__).warning(
                        "Deferred subscription for %s failed: %s", channel, exc,
                    )

            try:
                asyncio.create_task(_auto_sub())
            except RuntimeError:
                # Not in an event loop – defer until bind_bus() is called.
                pass

    async def subscribe_all(self) -> None:
        """Subscribes all registered handlers to the message bus.

        This method should be called during the application's asynchronous
        startup sequence, after the bus has been initialized and set.
        """
        logger = logging.getLogger(__name__)
        logger.info("[REGISTRY] Starting handler subscription process")
        
        if not self.bus:
            logger.error("[REGISTRY] No message bus set!")
            raise RuntimeError(
                "Message bus has not been set on the CommandHandlerRegistry."
            )

        assert self.bus is not None  # Narrow type for static analysers

        for channel, hp in self.handlers.items():
            # ``hp`` is either a bare handler (legacy behaviour) or a tuple of
            # (handler, priority) after the refactor.  Normalise here.
            if isinstance(hp, tuple):
                handler, prio = hp
                logger.debug(f"[REGISTRY] Channel {channel}: handler={handler.__name__}, priority={prio}")
            else:  # pragma: no cover – backward compatibility
                handler, prio = hp, 0
                logger.debug(f"[REGISTRY] Channel {channel}: handler={handler.__name__} (legacy format)")

            subscriber_id = f"command_handler_{channel}"
            logger.info(f"[REGISTRY] Subscribing handler for channel: {channel}")
            logger.debug(f"[REGISTRY]   Subscriber ID: {subscriber_id}")
            logger.debug(f"[REGISTRY]   Priority: {prio}")
            
            await self.bus.subscribe(
                channel,
                handler,
                subscriber_id=subscriber_id,
                handler_priority=prio,
            )
            
            logger.info(f"[REGISTRY] ✅ Successfully subscribed handler for: {channel}")
            logging.getLogger(__name__).info(
                "✅ Registered command handler for channel: %s (prio=%s)", channel, prio,
            )


# Global singleton instance. Decorators will populate this at module import time.
command_handler_registry = CommandHandlerRegistry()


def command_handler(
    channel: str, *, priority: int = 0
) -> Callable[
    [Callable[..., Coroutine[Any, Any, Any]]], Callable[..., Coroutine[Any, Any, Any]]
]:
    """
    A decorator to register a function as a handler for a specific command channel.

    Args:
        channel: The command channel to listen to.
        priority: The priority of the handler. This can be used by the bus
                  for routing or queuing decisions.
    """

    def decorator(
        func: Callable[..., Coroutine[Any, Any, Any]]
    ) -> Callable[..., Coroutine[Any, Any, Any]]:
        if not asyncio.iscoroutinefunction(func):
            raise TypeError("Command handler must be a coroutine function (async def).")

        command_handler_registry.register(channel, func, priority=priority)
        return func

    return decorator


def get_command_handler_registry() -> CommandHandlerRegistry:
    """Public accessor so tests and application code can retrieve the singleton."""
    return command_handler_registry 