"""
Core Capabilities Package
=========================

This package provides the core components for Capability-Based Security (CBS)
as defined by the CAW paradigm. It includes tools for issuing, validating,
and defining capabilities.

Key Components:
- Capability: The main data structure for a capability token.
- CapabilityValidator: The service responsible for validating capabilities.
- CapabilityIssuer: The service responsible for creating and signing capabilities.
"""

from .capability_issuer import CapabilityIssuer
from .types import Capability
from .types import ConstraintRule
from .types import PermissionRule
from .validator import CapabilityValidationError
from .validator import CapabilityValidator

__all__ = [
    "Capability",
    "PermissionRule",
    "ConstraintRule",
    "CapabilityValidator",
    "CapabilityValidationError",
    "CapabilityIssuer",
] 