# -*- coding: utf-8 -*-
"""
File: person_suit/core/capabilities/crypto_utils.py
Purpose: Cryptographic Utilities for CAW Capabilities using OQS.

NOTE: This file was moved from core/security/ during refactoring.

Provides wrappers for OQS signature schemes (e.g., CRYSTALS-Dilithium)
for key generation, signing, and verification.

Requires the 'oqs' package (liboqs-python).
"""

import logging
import os
import threading
from typing import Dict
from typing import NewType
from typing import Optional
from typing import Tuple

from ..config.loader import get_config_value  # Import config helper


# --- Custom Exceptions --- #
class CryptoError(Exception):
    """Base class for crypto utility errors."""

    pass


class OqsConfigError(CryptoError):
    """Error related to OQS library configuration or availability."""

    pass


class KeyManagementError(CryptoError):
    """Error related to key generation, storage, or retrieval."""

    pass


class SigningError(CryptoError):
    """Error during cryptographic signing."""

    pass


class VerificationError(CryptoError):
    """Error during cryptographic verification."""

    pass


# --- OQS Configuration --- #
# Load preferred algorithm from config, fallback to default
CONFIG_OQS_ALGORITHM = get_config_value("security.oqs_algorithm", "Dilithium2")

# Attempt to initialize OQS with the configured algorithm
_oqs_available = False
_active_sig_alg = f"{CONFIG_OQS_ALGORITHM}-Placeholder"
try:
    import oqs  # type: ignore

    # Check if the configured algorithm is supported
    if (
        hasattr(oqs, "MechanismNotSupportedError")
        and CONFIG_OQS_ALGORITHM in oqs.get_enabled_sig_mechanisms()
    ):
        _oqs_available = True
        _active_sig_alg = CONFIG_OQS_ALGORITHM
        logging.info(
            f"OQS library found and configured algorithm '{_active_sig_alg}' is enabled."
        )
    else:
        logging.warning(
            f"OQS library found, but configured algorithm '{CONFIG_OQS_ALGORITHM}' is NOT enabled/supported. Signature operations will be disabled."
        )
        oqs = None
except ImportError:
    logging.warning(
        "OQS library (liboqs-python) not found. Capability signature operations will be skipped."
    )
    oqs = None
except Exception as e:
    logging.exception(f"An unexpected error occurred importing or checking OQS: {e}")
    oqs = None
# --- End OQS Configuration --- #

# Define type hints for clarity (even if OQS not available)
PublicKey = NewType("PublicKey", bytes)
PrivateKey = NewType("PrivateKey", bytes)
Signature = NewType("Signature", bytes)

# --- Placeholder Key Stores (Non-Production) --- #
# Public keys can be stored less securely, but ideally in a proper registry.
_public_key_store: Dict[str, PublicKey] = {}
# Private key store ONLY for dummy fallback when OQS is unavailable.
# DO NOT RELY ON THIS FOR ACTUAL OQS OPERATIONS.
_dummy_private_key_store: Dict[str, PrivateKey] = {}
_key_store_lock = threading.Lock()  # Basic locking for the stores


def generate_oqs_keypair(entity_id: str) -> Optional[Tuple[PublicKey, PrivateKey]]:
    """
    Generates a key pair using the default OQS signature algorithm.
    Stores the PUBLIC key in the placeholder store.
    The CALLER is responsible for securely storing the returned PrivateKey.

    Args:
        entity_id: The identifier for the entity owning the key pair.

    Returns:
        A tuple (public_key, private_key) as bytes, or None if OQS is unavailable/fails.
    """
    logging.debug(
        f"Attempting to generate OQS keypair for {entity_id} with algorithm {_active_sig_alg}"
    )
    if not _oqs_available or oqs is None:  # Check oqs itself
        logging.warning(
            f"OQS not available or {_active_sig_alg} not supported. Generating dummy keys for {entity_id}."
        )
        pk_bytes = f"dummy_pk_{entity_id}_{os.urandom(4).hex()}".encode("utf-8")
        sk_bytes = f"dummy_sk_{entity_id}_{os.urandom(8).hex()}".encode("utf-8")
    else:
        try:
            signer = oqs.Signature(_active_sig_alg)
            pk_bytes = signer.generate_keypair()  # Returns public key bytes
            sk_bytes = signer.export_secret_key()  # Returns private key bytes
            logging.info(
                f"Generated OQS {_active_sig_alg} keypair for {entity_id}. PK len: {len(pk_bytes)}, SK len: {len(sk_bytes)}"
            )
        except oqs.MechanismNotSupportedError:
            logging.error(
                f"OQS Error: Configured algorithm '{_active_sig_alg}' suddenly not supported during keygen?"
            )
            raise OqsConfigError(
                f"Algorithm '{_active_sig_alg}' not supported during keygen."
            ) from None
        except Exception as e:
            logging.exception(f"OQS key generation failed for {_active_sig_alg}: {e}")
            raise KeyManagementError(f"Failed to generate OQS keys: {e}") from e

    public_key = PublicKey(pk_bytes)
    private_key = PrivateKey(sk_bytes)

    # Store ONLY the public key internally. Store dummy private key for fallback.
    with _key_store_lock:
        _public_key_store[entity_id] = public_key
        if not _oqs_available:
            _dummy_private_key_store[entity_id] = (
                private_key  # Store for dummy path only
            )
        logging.debug(
            f"Stored public key for {entity_id}. Private key returned to caller."
        )

    return public_key, private_key  # Return both


def register_public_key(entity_id: str, public_key: PublicKey):
    """Registers a public key (bytes) for an entity in the placeholder store."""
    if not isinstance(public_key, bytes):
        logging.error(f"Attempted to register non-bytes public key for {entity_id}")
        return
    with _key_store_lock:
        logging.info(
            f"Registering public key for entity: {entity_id} (Length: {len(public_key)})"
        )
        _public_key_store[entity_id] = public_key


def get_public_key(entity_id: str) -> Optional[PublicKey]:
    """Retrieves a public key (bytes) from the placeholder store."""
    with _key_store_lock:
        key = _public_key_store.get(entity_id)
        return key


# --- Internal Helper for DUMMY operations ONLY --- #
def _get_dummy_private_key(issuer_id: str) -> Optional[PrivateKey]:
    """Retrieves a private key from the insecure dummy store (for non-OQS fallback ONLY)."""
    # DO NOT USE IN PRODUCTION OQS PATH
    with _key_store_lock:
        key = _dummy_private_key_store.get(issuer_id)
        return key


def sign_capability_data(
    data_to_sign: bytes,
    private_key: PrivateKey,
    algorithm: Optional[str] = None,
    *,
    issuer_id_for_dummy: Optional[str] = None,
) -> Tuple[Signature, str]:  # Changed return type - raises error on fail
    """
    Signs the provided data using the provided private key and specified algorithm.
    Falls back to dummy signing if OQS is not available.

    Args:
        data_to_sign: The byte string to sign.
        private_key: The private key bytes to use for signing (if OQS available).
        algorithm: The OQS signature algorithm to use.
        issuer_id_for_dummy: The issuer ID, used ONLY to retrieve the key for
                             dummy signing when OQS is unavailable.

    Returns:
        A tuple (signature_bytes, algorithm_name).

    Raises:
        SigningError: If signing fails (OQS or dummy path).
        OqsConfigError: If OQS is needed but unavailable/misconfigured.
        KeyManagementError: If dummy key cannot be found when needed.
    """
    active_algorithm = algorithm or _active_sig_alg

    if not _oqs_available or oqs is None:
        logging.warning(
            f"OQS not available or {active_algorithm} not supported. Generating dummy signature."
        )
        if not issuer_id_for_dummy:
            msg = "Cannot generate dummy signature: issuer_id_for_dummy is required when OQS is unavailable."
            logging.error(msg)
            raise SigningError(msg)
        import hashlib

        dummy_private_key = _get_dummy_private_key(issuer_id_for_dummy)
        if dummy_private_key:
            sig_bytes = hashlib.sha256(data_to_sign + dummy_private_key).digest()
            return Signature(sig_bytes), active_algorithm
        else:
            msg = f"Cannot generate dummy signature: Dummy private key not found for issuer {issuer_id_for_dummy}."
            logging.error(msg)
            raise KeyManagementError(msg)

    # --- Proceed with OQS signing using the PROVIDED private key --- #
    if not private_key:
        msg = "Cannot sign capability data: Private key was not provided."
        logging.error(msg)
        raise SigningError(msg)

    try:
        signer = oqs.Signature(active_algorithm, bytes(private_key))
        signature_bytes = signer.sign(data_to_sign)
        logging.debug(
            f"Generated OQS {active_algorithm} signature ({len(signature_bytes)} bytes)."
        )
        return Signature(signature_bytes), active_algorithm
    except oqs.MechanismNotSupportedError:
        logging.error(
            f"OQS signing failed: Algorithm {active_algorithm} not supported."
        )
        raise SigningError(
            f"Algorithm {active_algorithm} not supported for signing."
        ) from None
    except ValueError as ve:
        logging.error(
            f"OQS signing failed for {active_algorithm}: {ve}. Check private key compatibility."
        )
        raise SigningError(f"Invalid private key for {active_algorithm}: {ve}") from ve
    except Exception as e:
        logging.exception(f"OQS signing failed unexpectedly: {e}")
        raise SigningError(f"Unexpected OQS signing error: {e}") from e


def verify_capability_signature(
    signed_data: bytes, signature: Signature, issuer_id: str, algorithm: str
) -> bool:
    """
    Verifies the signature against the signed data using the issuer's public key.
    Falls back to dummy verification if OQS is not available.
    """
    if not _oqs_available or oqs is None:
        logging.warning(
            f"OQS not available or {algorithm} not supported. Using dummy verification logic for issuer {issuer_id}."
        )
        import hashlib

        public_key = get_public_key(issuer_id)
        dummy_private_key = _get_dummy_private_key(issuer_id)
        if public_key and dummy_private_key:
            expected_sig_bytes = hashlib.sha256(
                signed_data + dummy_private_key
            ).digest()
            is_valid = bytes(signature) == expected_sig_bytes
            logging.debug(f"Dummy verification result for {issuer_id}: {is_valid}")
            return is_valid
        else:
            logging.error(
                f"Cannot perform dummy verification: Keys not found for issuer {issuer_id}."
            )
            return False

    # --- Proceed with OQS verification using the public key store --- #
    public_key = get_public_key(issuer_id)
    if not public_key:
        logging.error(
            f"Cannot verify capability signature: Public key not found for issuer {issuer_id}."
        )
        return False

    try:
        verifier = oqs.Signature(algorithm)
        is_valid = verifier.verify(signed_data, bytes(signature), bytes(public_key))
        logging.debug(
            f"OQS {algorithm} signature verification result for issuer {issuer_id}: {is_valid}"
        )
        return is_valid
    except oqs.MechanismNotSupportedError:
        logging.error(f"OQS verification failed: Algorithm {algorithm} not supported.")
        return False
    except ValueError as ve:
        logging.error(
            f"OQS verification failed for {algorithm}: {ve}. Check key/signature compatibility."
        )
        return False
    except Exception as e:
        logging.exception(f"OQS verification failed unexpectedly: {e}")
        return False
