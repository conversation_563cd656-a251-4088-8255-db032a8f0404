# -*- coding: utf-8 -*-
"""
Implementation of the Capability Validator.

This module provides the concrete implementation for validating CAW Capability
tokens against proposed effects, current state, and context, adhering to the
design outlined in docs/design/Capability_Management_Design.md.

Related Files:
- schemas.python_schema.py
- docs/design/Capability_Management_Design.md
- person_suit.core.actors.central_state_actor.py (Uses this validator)
"""

from __future__ import annotations

import logging
from typing import TYPE_CHECKING
from typing import Any
from typing import Optional

from cryptography.exceptions import InvalidSignature

# Cryptography
from cryptography.hazmat.primitives import hashes
from cryptography.hazmat.primitives import serialization
from cryptography.hazmat.primitives.asymmetric import ec
from cryptography.hazmat.primitives.asymmetric import padding
from cryptography.hazmat.primitives.asymmetric import rsa
from cryptography.hazmat.primitives.asymmetric import utils

from ..state_models import RejectionErrorType
from .types import Capability

if TYPE_CHECKING:
    pass

# Placeholder for the actual interface definition if needed separately
# from person_suit.core.actors.central_state_actor import CapabilityValidatorInterface

# --- Cryptographic Verification Helper --- #


def _get_public_key_pem_for_granter(granter_id: str) -> Optional[bytes]:
    """
    Placeholder function to retrieve the PEM-encoded public key for a given granter.

    In a real system, this would interact with a key management service, database,
    or configuration to securely fetch the appropriate public key.
    Returning None indicates the key could not be found.

    *** TEMPORARY IMPLEMENTATION FOR TESTING ***
    Checks for a specific granter_id ('test_granter') and attempts to load
    a corresponding public key file ('test_granter_pub.pem') from the current
    working directory.
    """
    # Specific ID for the temporary test key
    test_granter_id = "test_granter"
    test_public_key_file = "test_granter_pub.pem"  # Relative to workspace root

    if granter_id == test_granter_id:
        logging.debug(
            f"Attempting to load temporary public key for {granter_id} from {test_public_key_file}"
        )
        try:
            # Ensure the path is correct relative to where the script is run from
            # For consistency, assume it's relative to the workspace root.
            # CWD might vary depending on execution context.
            # Using a simple relative path here.
            with open(test_public_key_file, "rb") as key_file:
                key_bytes = key_file.read()
                logging.info(
                    f"Successfully loaded test public key for granter {granter_id}"
                )
                return key_bytes
        except FileNotFoundError:
            logging.error(
                f"Test public key file '{test_public_key_file}' not found for granter {granter_id}. Generate keys and place file in workspace root."
            )
            return None
        except Exception as e:
            logging.exception(
                f"Error reading test public key file '{test_public_key_file}' for granter {granter_id}: {e}"
            )
            return None
    else:
        logging.warning(
            f"No public key retrieval configured for granter_id '{granter_id}'. Returning None."
        )
        return None


def verify_capability_signature(capability: Capability, public_key_pem: bytes) -> bool:
    """
    Verifies the capability's signature using the provided public key.

    Args:
        capability: The Capability object.
        public_key_pem: The PEM-encoded public key of the granter.

    Returns:
        True if the signature is valid, False otherwise.
    """
    try:
        public_key = serialization.load_pem_public_key(public_key_pem)
        data_to_verify = capability._prepare_for_signing()

        # Determine padding/algorithm based on key type
        if isinstance(public_key, rsa.RSAPublicKey):
            padding_algo = padding.PSS(
                mgf=padding.MGF1(hashes.SHA256()), salt_length=padding.PSS.MAX_LENGTH
            )
            hash_algo = hashes.SHA256()
        elif isinstance(public_key, ec.EllipticCurvePublicKey):
            padding_algo = ec.ECDSA(
                utils.Prehashed(hashes.SHA256())
            )  # Sign hash directly
            hash_algo = hashes.SHA256()  # Need to hash data first for ECDSA verify
            # Important: ECDSA's verify method expects the hash of the data, not the data itself.
            hasher = hashes.Hash(hash_algo)
            hasher.update(data_to_verify)
            data_to_verify = hasher.finalize()  # Verify the hash
        else:
            logging.error(
                f"Unsupported public key type for verification: {type(public_key)}"
            )
            return False

        # Perform verification
        public_key.verify(
            capability.signature,
            data_to_verify,  # This is the hash for ECDSA
            padding_algo,  # This is the ECDSA object for EC keys
            hash_algo,  # This is ignored by ECDSA verify, but required by API
        )
        return True  # Verification successful

    except InvalidSignature:
        logging.warning(f"Invalid signature for capability {capability.capability_id}")
        return False
    except ValueError as e:
        # Catches errors like incorrect key format/size
        logging.error(
            f"ValueError during signature verification for {capability.capability_id}: {e}"
        )
        return False
    except Exception as e:
        # Catch other unexpected errors during verification
        logging.exception(
            f"Unexpected error verifying signature for {capability.capability_id}: {e}"
        )
        return False


class CapabilityValidationError(Exception):
    """Custom exception for validation failures."""

    def __init__(self, message: str, error_type: RejectionErrorType):
        super().__init__(message)
        self.error_type = error_type


class CapabilityValidator:
    """Validates capabilities required for effects."""
    
    def __init__(self):
        logger.info("CapabilityValidator initialized")

    async def has(self, context: Any, capability: str) -> bool:
        """Return ``True`` only when *capability* is present inside *context*.

        This first-pass implementation supports both
        ``UnifiedContext`` objects and plain ``dict`` contexts used by legacy
        callers.  It looks for a ``security_context.capabilities`` iterable or
        a top-level ``capabilities`` list.
        """

        try:
            supplied: list[str] = []

            if context is None:
                supplied = []
            elif isinstance(context, dict):
                # Modern dict representation
                sc = context.get("security_context", {})
                supplied.extend(sc.get("capabilities", []))
                supplied.extend(context.get("capabilities", []))  # legacy path
            else:
                # Dataclass / object path
                if hasattr(context, "security_context") and context.security_context:
                    supplied.extend(getattr(context.security_context, "capabilities", []))
                if hasattr(context, "capabilities"):
                    supplied.extend(getattr(context, "capabilities", []))

            has_cap = capability in supplied
            logger.debug("Capability check | required=%s | supplied=%s | result=%s", capability, supplied, has_cap)
            return has_cap
        except Exception as exc:  # Defensive — never break the bus due to validator
            logger.exception("CapabilityValidator error: %s", exc)
            return False

    # Protocol alias (CapabilityManagerProtocol)
    async def check_permission(self, context: Any, capability: str) -> bool:  # noqa: D401
        """Alias for :py:meth:`has` to satisfy CapabilityManagerProtocol."""

        return await self.has(context, capability)

_validator_instance: Optional[CapabilityValidator] = None

async def get_capability_validator() -> CapabilityValidator:
    """Get a singleton instance of the CapabilityValidator."""
    global _validator_instance
    if _validator_instance is None:
        _validator_instance = CapabilityValidator()
    return _validator_instance

logger = logging.getLogger(__name__)
