"""
Capability Types

Core types for the capability-based security system.
"""

from dataclasses import dataclass
from dataclasses import field
from datetime import datetime
from typing import Any
from typing import Dict
from typing import List
from typing import Optional

from ..state_models import CapabilityID


@dataclass(frozen=True)
class PermissionRule:
    """Defines a specific permission within a Capability."""
    effect_type_pattern: str  # Pattern matching for allowed effect types
    target_entity_pattern: str  # Pattern matching for allowed target entities
    parameter_constraints: Optional[Dict[str, Any]] = None


@dataclass(frozen=True)
class ConstraintRule:
    """Defines a constraint attached to a Capability."""
    constraint_type: str  # e.g., "CONTEXT_MATCH", "TIME_BOUND", "RATE_LIMIT"
    parameters: Dict[str, Any]  # Parameters specific to the constraint type


@dataclass(frozen=True)
class Capability:
    """
    Represents a CAW Capability token for authorization.
    
    Designed to be immutable and cryptographically unforgeable (via signature).
    """
    capability_id: CapabilityID
    granter_id: str  # ID of the entity that granted/issued this capability
    issued_at: float  # Timestamp of issuance
    signature: bytes  # Store raw signature bytes
    permissions: List[PermissionRule] = field(default_factory=list)
    constraints: List[ConstraintRule] = field(default_factory=list)
    parent_capability_id: Optional[CapabilityID] = None
    subject_id: Optional[str] = None  # Intended recipient/subject
    expires_at: Optional[float] = None  # Optional expiration timestamp
    
    def is_expired(self, comparison_time: Optional[float] = None) -> bool:
        """Check if this capability has expired."""
        if self.expires_at is None:
            return False
        if comparison_time is None:
            comparison_time = datetime.now().timestamp()
        return comparison_time > self.expires_at


# Re-export types that should be available from this module
__all__ = [
    "Capability",
    "CapabilityID", 
    "PermissionRule",
    "ConstraintRule"
] 