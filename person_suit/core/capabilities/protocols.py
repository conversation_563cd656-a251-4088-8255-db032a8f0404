from __future__ import annotations

"""capabilities.protocols
===========================

PEP-544 runtime-checkable *protocols* for capability and ACF managers used by
Sprint-2 Day-4 hooks.  These are intentionally minimal abstractions providing
only the operations currently required by the `EffectInterpreter`.
"""

from typing import Any
from typing import Protocol
from typing import runtime_checkable


@runtime_checkable
class CapabilityManagerProtocol(Protocol):
    """A subset of expected methods for a capability manager/validator."""

    async def has(self, context: Any, capability: str) -> bool:  # noqa: D401
        """Return *True* if *context* possesses *capability*."""

    async def check_permission(self, context: Any, capability: str) -> bool:  # noqa: D401
        """Alias for *has* allowing richer semantics."""


@runtime_checkable
class ACFManagerProtocol(Protocol):
    """Protocol for adaptive computational fidelity decision helpers."""

    def get_execution_policy(self, context: Any, effect: Any) -> dict:  # noqa: D401, ANN401
        """Return an execution policy dict (e.g. fidelity, timeout).""" 