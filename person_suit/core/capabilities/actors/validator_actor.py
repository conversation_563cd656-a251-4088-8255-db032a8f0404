from __future__ import annotations

"""validator_actor.py

Bus-driven capability validation actor.
Runs inside *core.capabilities* layer and offers validation as a service over
`HybridMessageBus`.  It listens on the channel ``capability.validation.request``
and emits a reply on the message's ``reply_channel`` (default
``capability.validation.response``).

This replaces direct imports of :pymod:`person_suit.core.capabilities.validator`
from other layers, satisfying the **Absolute Decoupling** rule.
"""

import logging
from typing import Any
from typing import Dict

from person_suit.core.capabilities.validator import CapabilityValidator
from person_suit.core.infrastructure.hybrid_message import HybridMessage
from person_suit.core.infrastructure.hybrid_message_bus import get_message_bus

logger = logging.getLogger(__name__)


class CapabilityValidatorActor:
    """Asynchronous actor exposing capability validation via the bus."""

    CHANNEL_REQUEST = "capability.validation.request"

    def __init__(self) -> None:
        self._validator = CapabilityValidator()
        self._subscription_id: str | None = None

    async def start(self) -> None:
        """Subscribe to validation requests."""
        bus = get_message_bus()
        # Ensure bus is running
        await bus.start()

        self._subscription_id = await bus.subscribe(
            channel_pattern=self.CHANNEL_REQUEST,
            handler=self._handle_request,
            subscriber_id="capability.validation.actor",
        )
        logger.info("CapabilityValidatorActor subscribed on %s", self.CHANNEL_REQUEST)

    async def stop(self) -> None:
        """Unsubscribe from the bus."""
        if self._subscription_id is None:
            return
        bus = get_message_bus()
        bus.unsubscribe(self._subscription_id, self.CHANNEL_REQUEST)
        self._subscription_id = None

    # ---------------------------------------------------------------------
    # Internal helpers
    # ---------------------------------------------------------------------

    async def _handle_request(self, message: HybridMessage) -> None:
        """Process a single validation request message."""
        payload: Dict[str, Any] = message.payload or {}

        required_permission: str = payload.get("required_permission", "")
        granted: list[str] = payload.get("granted_capabilities", [])
        payload.get("resource", {})

        # Quick validation: simple membership check + hook to full validator
        allowed = required_permission in granted

        # TODO: (Optional) Delegate to full validator when advanced checks needed.
        # This placeholder avoids expensive data reconstruction for MVP.

        reply = message.create_reply(payload={"allowed": allowed}, channel=message.reply_channel or "capability.validation.response")

        bus = get_message_bus()
        await bus.send(reply, timeout=1.0)


# Convenience singleton ------------------------------------------------------

_actor_instance: CapabilityValidatorActor | None = None


async def start_capability_validator_actor() -> None:
    global _actor_instance
    if _actor_instance is None:
        _actor_instance = CapabilityValidatorActor()
        await _actor_instance.start()


async def stop_capability_validator_actor() -> None:
    if _actor_instance is not None:
        await _actor_instance.stop() 