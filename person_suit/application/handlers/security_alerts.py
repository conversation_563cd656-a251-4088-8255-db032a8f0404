"""
Event handler for processing security alerts.

This module defines a handler that subscribes to security events from the
message bus and takes appropriate actions, such as logging or triggering
further workflows. It is context-aware, using the UnifiedContext from events.
"""

import asyncio
import logging
from dataclasses import fields
from typing import Dict
from typing import Optional

from person_suit.core.context.unified import UnifiedContext
from person_suit.core.infrastructure.hybrid_message import HybridMessage
from person_suit.core.infrastructure.hybrid_message_bus import HybridMessageBus
from person_suit.core.infrastructure.hybrid_message_bus import get_message_bus
from person_suit.shared.events.security_events import SecurityEventPayload

logger = logging.getLogger(__name__)


class SecurityAlertHandler:
    """
    A subscriber that listens for and processes security-related messages.

    This handler is a key component of the application layer, responsible for
    reacting to security messages published by the core SecurityManager. It
    subscribes directly to the HybridMessageBus.
    """

    def __init__(self) -> None:
        """Initializes the handler."""
        self._message_bus: Optional[HybridMessageBus] = None
        self._subscriptions: Dict[str, str] = {}
        self._lock = asyncio.Lock()
        self.is_running = False

    async def _get_bus(self) -> HybridMessageBus:
        """Lazily initializes and returns the message bus instance."""
        async with self._lock:
            if self._message_bus is None:
                self._message_bus = get_message_bus()
            return self._message_bus

    async def start(self) -> None:
        """Initializes and starts the handler by subscribing to security channels."""
        if self.is_running:
            logger.warning("Handler already started.")
            return

        bus = await self._get_bus()
        # Subscribe to a general security channel and specific sub-channels
        sub_map = {
            "security.alerts.generic": self._handle_security_alert,
            "security.alerts.auth.unauthorized": self._handle_unauthorized_access,
            "security.alerts.capabilities.revoked": self._handle_capability_revoked,
        }

        for channel, handler in sub_map.items():
            sub_id = bus.subscribe(channel, handler)
            self._subscriptions[channel] = sub_id

        self.is_running = True
        logger.info(
            "SecurityAlertHandler started and subscribed to %d channels.",
            len(self._subscriptions),
        )

    async def stop(self) -> None:
        """Stops the handler and unsubscribes from all channels."""
        if not self.is_running:
            return

        bus = await self._get_bus()
        for channel, sub_id in self._subscriptions.items():
            try:
                # Note: Unsubscribe might need channel_pattern, adjust if bus API requires it
                await bus.unsubscribe(subscriber_id=sub_id, channel_pattern=channel)
            except Exception as e:
                logger.error(
                    f"Failed to unsubscribe {sub_id} from {channel}: {e}", exc_info=True
                )
        self._subscriptions.clear()
        self.is_running = False
        logger.info("SecurityAlertHandler stopped and unsubscribed from all channels.")

    def _reconstruct_payload(
        self, payload_data: Dict
    ) -> Optional[SecurityEventPayload]:
        """Safely reconstructs the payload from a dictionary."""
        if not payload_data:
            return None
        try:
            return SecurityEventPayload.model_validate(payload_data)
        except Exception as e:
            logger.error(f"Failed to reconstruct SecurityEventPayload: {e}")
            return None

    def _reconstruct_context(self, context_data: Dict) -> Optional[UnifiedContext]:
        """Safely reconstructs the context from a dictionary."""
        if not context_data:
            return None
        try:
            # Get the set of valid __init__ fields from the dataclass
            valid_fields = {f.name for f in fields(UnifiedContext) if f.init}
            # Filter the input dict to only include valid fields
            filtered_data = {k: v for k, v in context_data.items() if k in valid_fields}
            return UnifiedContext(**filtered_data)
        except Exception as e:
            logger.error(f"Failed to reconstruct UnifiedContext: {e}")
            return None

    async def _handle_security_alert(self, msg: HybridMessage) -> None:
        """Handles generic security alerts."""
        payload = self._reconstruct_payload(msg.payload)
        context = self._reconstruct_context(msg.context)

        if payload:
            logger.warning(
                f"SECURITY ALERT: {payload.description} (Severity: {payload.severity})",
                extra={"context": context, "details": payload.details},
            )
        else:
            logger.error("Received security alert with no payload.", extra={"context": context})

    async def _handle_unauthorized_access(self, msg: HybridMessage) -> None:
        """Handles unauthorized access attempt events."""
        payload = self._reconstruct_payload(msg.payload)
        context = self._reconstruct_context(msg.context)

        if payload:
            logger.critical(
                f"UNAUTHORIZED ACCESS ATTEMPT: {payload.details}",
                extra={"context": context},
            )
        else:
            logger.error(
                "Received unauthorized access event with no payload.",
                extra={"context": context},
            )

    async def _handle_capability_revoked(self, msg: HybridMessage) -> None:
        """Handles capability revocation events."""
        payload = self._reconstruct_payload(msg.payload)
        context = self._reconstruct_context(msg.context)

        if payload:
            logger.info(
                f"CAPABILITY REVOKED: {payload.description}",
                extra={"context": context, "details": payload.details},
            )
        else:
            logger.error(
                "Received capability revoked event with no payload.",
                extra={"context": context},
            )

