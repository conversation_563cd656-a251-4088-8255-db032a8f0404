"""WaveTrace Prometheus Exporter
================================
Exposes counters for CAW branch decisions by subscribing to bus events.

• wave_branches_total
• particle_branches_total

This keeps monitoring self-contained without requiring Grafana. Run at
bootstrap; the HTTP server listens on 0.0.0.0:9103 by default.
"""

from __future__ import annotations

import asyncio
import logging

from prometheus_client import Counter  # type: ignore
from prometheus_client import Gauge  # type: ignore
from prometheus_client import Histogram  # type: ignore
from prometheus_client import start_http_server  # type: ignore

from person_suit.core.infrastructure.hybrid_message import HybridMessage
from person_suit.core.infrastructure.hybrid_message_bus import get_message_bus

logger = logging.getLogger(__name__)

# ---------------------------------------------------------------------------
# Prometheus metrics
# ---------------------------------------------------------------------------
BRANCH_COUNTER = Counter(
    "caw_branch_decisions_total",
    "Count of CAW computational branch decisions",
    labelnames=["branch"],
)

MAILBOX_DEPTH_GAUGE = Counter(
    "actor_mailbox_depth_events_total",
    "Count of mailbox depth events (depth aggregated separately in dashboards)",
    labelnames=["actor_path"],
)

SECURITY_DENIALS = Counter(
    "security_denials_total",
    "Denied messages or subscriptions by security middleware",
    labelnames=["reason"],
)

LATENCY_HIST = Histogram(
    "effect_execution_latency_seconds",
    "Latency of effect execution as reported by completion events",
    buckets=(0.005, 0.05, 0.2, 1.0, 2.5, 5.0, 10.0),
)

PROVENANCE_LOSS_GAUGE = Gauge(
    "provenance_loss_ratio",
    "Ratio of provenance records lost as reported by bus metrics",
)


async def _handle_branch_event(msg: HybridMessage) -> None:  # noqa: D401
    """Increment Prometheus counter for branch event."""
    branch = msg.payload.get("branch")
    if branch in {"WAVE", "PARTICLE"}:
        BRANCH_COUNTER.labels(branch=branch.lower()).inc()


async def _handle_mailbox_event(msg: HybridMessage) -> None:  # noqa: D401
    actor_path = msg.payload.get("actor_path")
    depth = msg.payload.get("depth")
    if actor_path and depth is not None:
        MAILBOX_DEPTH_GAUGE.labels(actor_path=actor_path).inc()


async def _handle_security_event(msg: HybridMessage) -> None:  # noqa: D401
    reason = msg.payload.get("event_type") or msg.payload.get("reason")
    if reason:
        SECURITY_DENIALS.labels(reason=reason).inc()


async def _handle_effect_completed(msg: HybridMessage) -> None:  # noqa: D401
    exec_time = msg.payload.get("execution_time") or msg.payload.get("execution_time_ms")
    if exec_time is not None:
        # payload may be ms or seconds – normalise
        secs = exec_time / 1000 if exec_time > 100 else exec_time
        LATENCY_HIST.observe(secs)


async def _handle_bus_metrics(msg: HybridMessage) -> None:  # noqa: D401
    m = msg.payload.get("metrics", {})
    processed = m.get("messages_processed")
    loss = m.get("provenance_loss", 0)
    if processed:
        PROVENANCE_LOSS_GAUGE.set(loss / processed if processed else 0)


async def start_exporter(port: int = 9103) -> None:  # noqa: D401
    """Start HTTP server and subscribe to branch events."""
    start_http_server(port)
    logger.info("WaveTrace Prometheus exporter listening on :%d/metrics", port)

    bus = get_message_bus()
    await bus.subscribe("sys.monitor.caw_branch", _handle_branch_event, subscriber_id="prom_branch_exp", handler_priority=-10)
    await bus.subscribe("sys.monitor.mailbox_depth", _handle_mailbox_event, subscriber_id="prom_mailbox_exp", handler_priority=-10)
    await bus.subscribe("event.security.*", _handle_security_event, subscriber_id="prom_sec_exp", handler_priority=-10)
    await bus.subscribe("event.effect.completed", _handle_effect_completed, subscriber_id="prom_latency_exp", handler_priority=-10)
    await bus.subscribe("sys.metrics.bus", _handle_bus_metrics, subscriber_id="prom_prov_exp", handler_priority=-10)

    # Idle task keeps event-loop alive
    while True:
        await asyncio.sleep(3600)


# Convenience for stand-alone execution (e.g. `python -m ...exporter`)
# Entry point removed for bootstrap unification compliance
# To run standalone: python -c "import asyncio; from person_suit.monitoring.wavetrace_prom_exporter import start_exporter; asyncio.run(start_exporter())"