"""
main.py - Person Suit Canonical Entry Point
==========================================

The ONE and ONLY entry point for the Person Suit system.
All other entry points and bootstrap mechanisms have been removed and
their logic consolidated here to ensure a single, authoritative startup sequence.

This provides a resilient, message-based, and architecturally pure bootstrap
for the Person Suit meta-system architecture.

Usage:
- Console command: person-suit
- Python module: python -m person_suit
- Direct call: python person_suit/main.py
"""

import asyncio
import logging
import signal
import time
from typing import Any
from typing import Dict
from typing import Set
from typing import TYPE_CHECKING

# Type checking imports to avoid circular dependencies
if TYPE_CHECKING:
    from person_suit.core.actors.actor_system import ActorSystem
    from person_suit.core.context.unified import UnifiedContext

from person_suit.core.infrastructure.system_setup import install_optional_torch_stub

# ---------------------------------------------------------------------------
# Dependency stubs must be installed *before* importing the rest of the core
# modules which may (indirectly) attempt to `import torch`.
# ---------------------------------------------------------------------------
from person_suit.core.infrastructure.system_setup import setup_logging

# Install the stub as early as possible – this runs before any further imports
install_optional_torch_stub()

# Core Imports – executed *after* optional stubs are in place
# NOTE: ActorSystem, UnifiedContext, and get_config imports moved to lazy loading to prevent circular dependencies
from person_suit.core.constants.fixed_point_scale import PRIO_CRITICAL
from person_suit.core.constants.fixed_point_scale import PRIO_NORMAL
from person_suit.core.infrastructure.hybrid_message import (
    HybridMessage,
    MessageType,
)

logger = logging.getLogger(__name__)


class CanonicalBootstrap:
    """
    The single, authoritative bootstrap for the entire Person Suit application.
    It initializes core services and meta-systems in a defined, resilient order.
    """

    def __init__(self):
        self._system_context: "UnifiedContext | None" = None
        self._services: Dict[str, Any] = {}
        self._initialized_services: Set[str] = set()
        self._failed_services: Set[str] = set()
        self._is_shutting_down = False
        self._running_tasks: Set[asyncio.Task] = set()
        self._started = False

    async def start(self, stay_alive: bool = True) -> None:
        """Initializes and starts all system components."""
        logger.info("🚀 Starting Person Suit Canonical Bootstrap...")
        start_time = time.time()

        try:
            self._setup_signal_handlers()

            # 1. Initialize Core Infrastructure
            await self._initialize_core_infrastructure()

            # 2. Initialize Middleware
            await self._initialize_middleware()

            # 3. Initialize Meta-Systems
            await self._initialize_meta_systems()

            # 4. Finalize Startup
            await self._finalize_startup()

        except Exception as e:
            logger.critical(f"💥 CRITICAL BOOTSTRAP FAILURE: {e}", exc_info=True)
            await self.shutdown()
            return

        elapsed = time.time() - start_time
        logger.info(f"✅ Person Suit bootstrapped successfully in {elapsed:.2f} seconds.")

        self._started = True

        if stay_alive:
            logger.info("System is now running. Press Ctrl+C to shut down.")
            # Keep the system alive
            await self._keep_alive()

    def _setup_signal_handlers(self) -> None:
        """Sets up signal handlers for graceful shutdown."""
        loop = asyncio.get_running_loop()
        for sig in (signal.SIGINT, signal.SIGTERM):
            loop.add_signal_handler(sig, lambda s=sig: asyncio.create_task(self.shutdown(s)))
        logger.info("Signal handlers for graceful shutdown installed.")

    async def _initialize_core_infrastructure(self):
        """Initializes essential services like config, bus, and actor system."""
        logger.info("--- Phase 1: Initializing Core Infrastructure ---")

        # Config - use direct loader to avoid circular imports
        from person_suit.core.config.loader import load_config
        config = load_config()
        self._services["config"] = config
        self._initialized_services.add("config")
        logger.info("  ✅ Config: Ready")

        # System Context - lazy import to prevent circular dependencies
        from person_suit.core.context.unified import UnifiedContext
        self._system_context = UnifiedContext.create_default("person_suit_canonical")
        self._system_context.priority = PRIO_CRITICAL
        self._initialized_services.add("system_context")
        logger.info("  ✅ UnifiedContext: Ready")

        # Message Bus – obtain singleton instance
        from person_suit.core.infrastructure.hybrid_message_bus import get_message_bus
        bus = get_message_bus()
        logger.info("  - Got bus instance. Starting...")
        
        # Enhanced bus state consistency check
        if not getattr(bus, "is_running", False):
            await bus.start()
            
            # Additional safeguard: Verify bus is actually operational after start
            max_startup_wait = 2.0  # Wait up to 2 seconds for bus to become fully operational
            startup_timeout = time.time() + max_startup_wait
            
            while time.time() < startup_timeout:
                if getattr(bus, "is_running", False):
                    break
                await asyncio.sleep(0.05)  # Check every 50ms
            
            # Log final bus state for diagnostics
            final_state = getattr(bus, "is_running", False)
            logger.info(f"  - Bus final state after startup: is_running={final_state}")
            
        self._services["message_bus"] = bus
        self._initialized_services.add("message_bus")
        logger.info("  ✅ HybridMessageBus: Ready")

        # Actor System - lazy import to prevent circular dependencies
        logger.info("  - Creating ActorSystem instance...")
        from person_suit.core.actors.actor_system import ActorSystem
        actor_system = ActorSystem()
        logger.info("  - ActorSystem instance created. Starting...")
        await actor_system.start()
        self._services["actor_system"] = actor_system
        self._initialized_services.add("actor_system")
        logger.info("  ✅ ActorSystem: Ready")

        # Effects System
        try:
            from person_suit.core.effects import execute_effect
            self._services["effects"] = execute_effect
            self._initialized_services.add("effects")
            logger.info("  ✅ Effects System: Ready (using canonical execute_effect)")
        except Exception as e:
            logger.warning(f"  ⚠️ Effects system: Failed to link ({e})")
            self._failed_services.add("effects")
        
        # Foundation Actors - Replace thread-based infrastructure
        await self._initialize_foundation_actors()

        # Bus Startup Reporter (WaveTrace cold-start metric)
        try:
            from person_suit.core.actors.system.bus_startup_reporter import (
                BusStartupReporterActor,
            )

            logger.info("  DEBUG: About to create BusStartupReporterActor")
            logger.info(f"  DEBUG: Bus is_running status: {getattr(bus, 'is_running', 'UNKNOWN')}")
            reporter_ref = await actor_system.create_actor(
                BusStartupReporterActor,
                name="bus_startup_reporter",
                parent=None,
            )
            logger.info("  DEBUG: BusStartupReporterActor created successfully")
            self._services["bus_startup_reporter"] = reporter_ref
            logger.info("  ✅ BusStartupReporterActor: Started")
        except Exception as e:  # noqa: WPS420 – continue bootstrap
            logger.warning("  ⚠️ BusStartupReporterActor failed: %s", e)
    
    async def _initialize_foundation_actors(self):
        """Initialize foundation actors to replace thread-based infrastructure."""
        logger.info("  - Initializing Foundation Actors...")
        
        try:
            from person_suit.core.actors.foundation_actors import DashboardActor
            from person_suit.core.actors.foundation_actors import EnergyManagementActor
            from person_suit.core.actors.foundation_actors import FoundationSupervisorActor
            from person_suit.core.actors.foundation_actors import HealthMonitorActor
            from person_suit.core.actors.foundation_actors import MetricsCollectorActor
            from person_suit.core.actors.foundation_actors import RuntimeVerificationActor
            
            actor_system = self._services.get("actor_system")
            bus = self._services.get("message_bus")
            
            if not actor_system or not bus:
                logger.error("    ❌ Foundation Actors: Missing dependencies")
                self._failed_services.add("foundation_actors")
                return
            
            # Create and start the foundation supervisor using ActorSystem
            supervisor_ref = await actor_system.create_actor(
                FoundationSupervisorActor,
                name="foundation_supervisor",
                parent=None,  # Root-level actor
                skip_capabilities=True,
            )
            self._services["foundation_supervisor"] = supervisor_ref
            
            # Create foundation actors under supervisor with guard timeouts
            async def _spawn(name: str, actor_cls, **kws):
                return await actor_system.create_actor(
                    actor_cls, name=name, parent=supervisor_ref, skip_capabilities=True, **kws
                )
            
            health_ref = await _spawn(
                "health_monitor", HealthMonitorActor, kwargs={"check_interval_seconds": 60.0}
            )
            
            metrics_ref = await _spawn("metrics_collector", MetricsCollectorActor)
            
            runtime_ref = await _spawn("runtime_verifier", RuntimeVerificationActor)
            
            energy_ref = await _spawn(
                "energy_manager", EnergyManagementActor, kwargs={"harvest_interval_seconds": 1.0}
            )
            
            dashboard_ref = await _spawn(
                "dashboard", DashboardActor, kwargs={"update_interval_seconds": 5.0}
            )
            
            # Store actor references
            self._services["foundation_actors"] = {
                "supervisor": supervisor_ref,
                "health_monitor": health_ref,
                "metrics_collector": metrics_ref,
                "runtime_verifier": runtime_ref,
                "energy_manager": energy_ref,
                "dashboard": dashboard_ref,
            }
            
            # Create bus-actor bridge to route messages to actors
            logger.info("    - Creating bus-actor bridge ...")
            from person_suit.core.actors.bus_actor_bridge import create_bus_actor_bridge
            
            # Map channels to actors
            channel_mappings = {
                "health.check.*": health_ref,
                "metrics.collect.*": metrics_ref,
                "runtime.verify.*": runtime_ref,
                "energy.manage.*": energy_ref,
                "dashboard.update.*": dashboard_ref,
                "foundation.supervisor.*": supervisor_ref,
            }
            
            try:
                bridge = await create_bus_actor_bridge(bus, channel_mappings)
                logger.info("    - Bus-actor bridge created.")
            except Exception as e:
                logger.error(f"    ❌ Failed to create bus-actor bridge: {e}")
                raise
            
            self._services["bus_actor_bridge"] = bridge
            
            # Initial tasks are now started by the actors themselves in pre_start.
            # No need to send schedule messages from the bootstrap.
            
            self._initialized_services.add("foundation_actors")
            logger.info("    ✅ Foundation Actors: Initialized with supervision (health, metrics, runtime, energy, dashboard)")
            
        except Exception as e:
            logger.error(f"    ❌ Foundation Actors: Failed to initialize ({e})", exc_info=True)
            self._failed_services.add("foundation_actors")

    async def _initialize_middleware(self):
        """Initializes all bus middleware and the effect interpreter."""
        logger.info("--- Phase 2: Initializing Middleware ---")
        bus = self._services.get("message_bus")
        if not bus:
            logger.error("Message bus not found, cannot initialize middleware.")
            return

        try:
            logger.info("  DEBUG: Importing middleware modules...")
            from person_suit.core.bootstrap.effects import create_effects_bootstrap_feature
            from person_suit.core.infrastructure.middleware import acf
            from person_suit.core.infrastructure.middleware import choreography
            from person_suit.core.infrastructure.middleware import message_processing
            from person_suit.core.infrastructure.middleware import provenance
            from person_suit.core.infrastructure.middleware import security
            from person_suit.core.infrastructure.middleware import telemetry

            # Core infrastructure middleware first
            logger.info("  DEBUG: Initializing security middleware...")
            await security.initialize(bus)
            logger.info("  ✅ Security Middleware: Initialized")
            
            logger.info("  DEBUG: Initializing provenance middleware...")
            await provenance.initialize(bus)
            logger.info("  ✅ Provenance Middleware: Initialized")
            
            logger.info("  DEBUG: Initializing telemetry middleware...")
            await telemetry.initialize(bus)
            logger.info("  ✅ Telemetry Middleware: Initialized")

            # Advanced CAW middleware
            logger.info("  DEBUG: Initializing ACF middleware...")
            await acf.initialize(bus)
            logger.info("  ✅ ACF Middleware: Initialized")
            
            logger.info("  DEBUG: Initializing choreography middleware...")
            await choreography.initialize(bus)
            logger.info("  ✅ Choreography Middleware: Initialized")
            
            logger.info("  DEBUG: Initializing message processing middleware...")
            await message_processing.initialize(bus)
            logger.info("  ✅ Message Processing Middleware: Initialized")

            # Sprint 1: Initialize Effects System (Command-Effect-Event flow)
            logger.info("  DEBUG: Creating effects bootstrap feature...")
            effects_feature = create_effects_bootstrap_feature()
            context = {"message_bus": bus}
            logger.info("  DEBUG: Initializing effects feature...")
            await effects_feature.initialize(context)
            logger.info("  ✅ Effects System: Command-Effect-Event flow initialized")

            # Import services to trigger command handler registration
            logger.info("  DEBUG: Importing memory encoder service...")
            from person_suit.services.memory_encoder import service as _  # noqa: F401
            logger.info("  ✅ Services: Command handlers auto-registered")

        except Exception as e:
            logger.critical(f"  💥 Middleware or EffectInterpreter initialization failed: {e}", exc_info=True)
            self._failed_services.add("middleware")

    async def _initialize_meta_systems(self):
        """Initializes the optional meta-systems (PC, AN, PR)."""
        logger.info("--- Phase 3: Initializing Meta-Systems ---")

        # This is where the logic from the deleted bootstrap files will be integrated.
        # For now, we stub it out.
        await self._initialize_persona_core()
        await self._initialize_analyst()
        await self._initialize_predictor()

    async def _initialize_persona_core(self):
        """
        Consolidated initialization logic for the PersonaCore meta-system.
        Replaces logic from all deleted legacy bootstrap files.
        """
        try:
            logger.info("  - Initializing PersonaCore...")
            # Here, we would use the actor_system to spawn the necessary actors
            # for PersonaCore, based on the logic that was in the deleted files.
            # Example:
            # from person_suit.meta_systems.persona_core.actors import PersonaCoreManagerActor
            # await self._services["actor_system"].spawn_actor(
            #     PersonaCoreManagerActor, "pc_manager"
            # )
            self._initialized_services.add("persona_core")
            logger.info("    ✅ PersonaCore: Initialized")
        except Exception as e:
            logger.warning(f"    ⚠️ PersonaCore: Failed to initialize ({e})")
            self._failed_services.add("persona_core")

    async def _initialize_analyst(self):
        logger.info("  - Initializing Analyst... (SKIPPED - Not Implemented)")

    async def _initialize_predictor(self):
        logger.info("  - Initializing Predictor... (SKIPPED - Not Implemented)")

    async def _finalize_startup(self):
        """Publishes system-ready event and performs final checks."""
        logger.info("--- Phase 4: Finalizing Startup ---")
        bus = self._services.get("message_bus")
        if bus and self._system_context:
            system_ready_event = HybridMessage(
                message_type=MessageType.EVENT,
                channel="system.lifecycle.ready",
                payload={
                    "timestamp": time.time(),
                    "initialized": sorted(list(self._initialized_services)),
                    "failed": sorted(list(self._failed_services)),
                },
                priority=PRIO_CRITICAL,
                context=self._system_context.to_dict()
            )
            await bus.send(system_ready_event)
            logger.info("  ✅ System Ready event published to message bus.")

    async def shutdown(self, sig: signal.Signals | None = None) -> None:
        """Gracefully shuts down all system components."""
        if self._is_shutting_down:
            return
        self._is_shutting_down = True

        if sig:
            logger.warning(f"Received shutdown signal: {sig.name}. Initiating graceful shutdown...")
        else:
            logger.warning("Shutdown initiated manually or due to error.")

        # Shutdown in reverse order of initialization
        # 1. Announce shutdown
        bus = self._services.get("message_bus")
        if bus and self._system_context:
            try:
                shutdown_event = HybridMessage(
                    message_type=MessageType.EVENT,
                    channel="system.lifecycle.shutdown",
                    payload={"timestamp": time.time()},
                    priority=PRIO_CRITICAL,
                    context=self._system_context.to_dict()
                )
                await bus.send(shutdown_event)
                logger.info("Shutdown event published.")
            except Exception as e:
                logger.error(f"Error publishing shutdown event: {e}")

        # 2. Shutdown Meta-Systems (if they have shutdown logic)
        logger.info("Shutting down meta-systems...")

        # 3. Shutdown Core Infrastructure
        logger.info("Shutting down core infrastructure...")
        actor_system = self._services.get("actor_system")
        bus = self._services.get("message_bus")

        for name, service, stop_coro in [
            ("ActorSystem", actor_system, actor_system.stop() if actor_system else None),
            ("HybridMessageBus", bus, bus.stop() if bus else None),
        ]:
            if service and stop_coro:
                try:
                    logger.info(f"Attempting to stop {name}...")
                    await asyncio.wait_for(stop_coro, timeout=5.0)
                    logger.info(f"{name} stopped successfully.")
                except asyncio.TimeoutError:
                    logger.error(f"{name} failed to stop within 5 seconds.")
                except Exception as e:
                    logger.error(f"Error stopping {name}: {e}")

        # 4. Cancel any remaining tasks
        for task in self._running_tasks:
            task.cancel()
        await asyncio.gather(*self._running_tasks, return_exceptions=True)

        logger.info("✅ System shutdown complete.")

        self._started = False

    async def _keep_alive(self):
        """
        Keeps the main coroutine running until a shutdown is triggered.
        """
        while not self._is_shutting_down:
            await asyncio.sleep(1)

    # -------------------------------------------------------------------
    # Convenience helpers for tests (non-production API)
    # -------------------------------------------------------------------

    async def stop(self) -> None:  # noqa: D401 – simple alias for shutdown
        """Alias for shutdown() to mirror legacy tests."""
        await self.shutdown()

    def get_system_status(self) -> Dict[str, Any]:  # noqa: D401 – testing helper
        """Return minimal system status information for tests."""
        status = "running" if self._started and not self._is_shutting_down else "stopped"
        actors = 0
        actor_system = self._services.get("actor_system")
        if actor_system and hasattr(actor_system, "actor_count"):
            actors = actor_system.actor_count()
        return {
            "status": status,
            "registered_actors": actors,
            "initialized_services": sorted(self._initialized_services),
            "failed_services": sorted(self._failed_services),
        }


async def main() -> None:
    """The canonical entry point for the Person Suit application."""
    import sys
    
    from person_suit.core.infrastructure.system_setup import install_event_loop_policy
    install_event_loop_policy()

    setup_logging()
    
    # Check for --diag flag for CI/testing
    diag_mode = "--diag" in sys.argv
    
    bootstrap = CanonicalBootstrap()
    try:
        if diag_mode:
            logger.info("Running in diagnostic mode...")
            await bootstrap.start(stay_alive=False)
            # Print diagnostic info and exit
            status = bootstrap.get_system_status()
            if status["status"] == "running":
                logger.info("System Ready")
                logger.info(f"Initialized services: {', '.join(status['initialized_services'])}")
                if status['failed_services']:
                    logger.warning(f"Failed services: {', '.join(status['failed_services'])}")
            else:
                logger.error("System failed to start")
            # Give a moment for async tasks to complete
            await asyncio.sleep(0.1)
            # Explicitly shutdown to exit cleanly
            await bootstrap.shutdown()
            return
        else:
            await bootstrap.start()
    except asyncio.CancelledError:
        logger.info("Main task cancelled. Application shutting down.")
    finally:
        # Ensure shutdown is called even if start() fails partway through
        if not bootstrap._is_shutting_down and not diag_mode:
            await bootstrap.shutdown()


if __name__ == "__main__":
    asyncio.run(main())
