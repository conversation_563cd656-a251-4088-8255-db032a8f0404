Binary file tests/lint/__pycache__/test_bare_subscription_checker.cpython-313-pytest-8.3.5.pyc matches
person_suit/core/infrastructure/hybrid_message_bus.py:class HybridMessageBus(BusKernel):
scripts/load_tests/acf_stress_test.py:        self.bus = HybridMessageBus()
scripts/load_tests/caw_principles_simple_test.py:        self.bus = HybridMessageBus()
scripts/load_tests/caw_principles_stress_test.py:        self.bus = HybridMessageBus()
scripts/load_tests/extreme_acf_test.py:        self.bus = HybridMessageBus()
scripts/load_tests/minimal_cee_stress_test.py:    bus = HybridMessageBus()
scripts/load_tests/simple_acf_test.py:        self.bus = HybridMessageBus()
scripts/test_cee_simple.py:    bus = HybridMessageBus()
scripts/test_native_provenance.py:    bus = HybridMessageBus()
scripts/test_ps005_rule.py:    """Test that direct HybridMessageBus() calls are detected."""
scripts/test_ps005_rule.py:    """Test that module.HybridMessageBus() is detected."""
scripts/test_ps005_rule.py:bus = bus_module.HybridMessageBus()
scripts/test_ps005_rule.py:bus = HybridMessageBus()
tests/bus/test_bus_autosubscribe.py:    bus = HybridMessageBus()
tests/choreography/test_step_metric.py:    bus = HybridMessageBus()
tests/core/infrastructure/test_bus_thread_safety.py:                bus = HybridMessageBus()
tests/handlers/test_handler_reality_check.py:        bus = HybridMessageBus()
tests/integration/test_cee_flow.py:    bus_instance = HybridMessageBus()
tests/lint/test_bare_subscription_checker.py:bus = HybridMessageBus()
tests/performance/test_acf_priority_adaptation.py:    bus = HybridMessageBus()
tests/performance/test_hybrid_bus_throughput.py:    bus = HybridMessageBus()
tests/provenance/test_redpanda_sink.py:    bus = HybridMessageBus()
tests/security/test_routing_denial.py:    bus = HybridMessageBus()
