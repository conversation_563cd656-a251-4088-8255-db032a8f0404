/Users/<USER>/Lokalne_Kody/Coversational/diagnostic_validation_test.py:if __name__ == "__main__":
/Users/<USER>/Lokalne_Kody/Coversational/person_suit/shared/information/core.py
Binary file person_suit/meta_systems/persona_core/folded_mind/.ruff_cache/0.11.6/10776158372106444667 matches
Binary file person_suit/meta_systems/persona_core/folded_mind/.ruff_cache/0.11.6/13011302693533012005 matches
Binary file person_suit/meta_systems/persona_core/folded_mind/.ruff_cache/0.11.6/1425758283337358432 matches
Binary file person_suit/meta_systems/persona_core/folded_mind/.ruff_cache/0.11.6/14560324934471790518 matches
Binary file person_suit/meta_systems/persona_core/folded_mind/.ruff_cache/0.11.6/15014403304535495537 matches
Binary file person_suit/meta_systems/persona_core/folded_mind/.ruff_cache/0.11.6/15424288424656541273 matches
Binary file person_suit/meta_systems/persona_core/folded_mind/.ruff_cache/0.11.6/1643290606633524984 matches
Binary file person_suit/meta_systems/persona_core/folded_mind/.ruff_cache/0.11.6/16469802721816680300 matches
Binary file person_suit/meta_systems/persona_core/folded_mind/.ruff_cache/0.11.6/16678384086304382882 matches
Binary file person_suit/meta_systems/persona_core/folded_mind/.ruff_cache/0.11.6/16761975299367165275 matches
Binary file person_suit/meta_systems/persona_core/folded_mind/.ruff_cache/0.11.6/17382197324664717372 matches
Binary file person_suit/meta_systems/persona_core/folded_mind/.ruff_cache/0.11.6/17771730831821175986 matches
Binary file person_suit/meta_systems/persona_core/folded_mind/.ruff_cache/0.11.6/4107262457787004581 matches
Binary file person_suit/meta_systems/persona_core/folded_mind/.ruff_cache/0.11.6/6272773850761899018 matches
Binary file person_suit/meta_systems/persona_core/folded_mind/.ruff_cache/0.11.6/7398276292080183661 matches
Binary file person_suit/meta_systems/persona_core/folded_mind/.ruff_cache/0.11.6/9365034068558861558 matches
Binary file person_suit/meta_systems/persona_core/folded_mind/.ruff_cache/0.11.6/9847919926671642639 matches
Binary file person_suit/meta_systems/persona_core/folded_mind/cam/algorithmic_reasoning/.ruff_cache/0.11.6/878553895102874401 matches
Binary file person_suit/meta_systems/persona_core/folded_mind/sem/atomization/test/__pycache__/setup_test_env.cpython-313.pyc matches
examples/acf_integration_demo.py:if __name__ == "__main__":
examples/dashboard_demo.py:if __name__ == "__main__":
examples/developmental_tracking_demo.py:if __name__ == "__main__":
examples/dual_wave/concrete_backends_example.py:if __name__ == "__main__":
examples/dual_wave/distributed_storage_example.py:if __name__ == "__main__":
examples/dual_wave/performance_optimization_example.py:if __name__ == "__main__":
examples/dual_wave/simple_message_bus_test.py:if __name__ == "__main__":
examples/dual_wave/tiered_storage_example.py:if __name__ == "__main__":
examples/effect_system_demo.py:if __name__ == "__main__":
examples/episodic_future_thinking_example.py:if __name__ == "__main__":
examples/evaluation_demo.py:if __name__ == "__main__":
examples/hypothesis_generation_demo.py:if __name__ == "__main__":
examples/isolation/async_event_example.py:if __name__ == "__main__":
examples/isolation/basic_isolation_example.py:if __name__ == "__main__":
examples/isolation/boundary_extensions.py:if __name__ == "__main__":
examples/memory_consolidation_demo.py:if __name__ == "__main__":
examples/memory_integrity_demo.py:if __name__ == "__main__":
examples/memory_optimization_example.py:if __name__ == "__main__":
examples/path_integration_demo.py:if __name__ == "__main__":
examples/probabilistic/context_sensitive_probabilistic_programming.py:if __name__ == "__main__":
examples/quick_extended_demo.py:if __name__ == "__main__":
examples/resource_optimization_demo.py:if __name__ == "__main__":
examples/tracing/basic_tracing.py:if __name__ == "__main__":
examples/tracing/demo.py:if __name__ == "__main__":
examples/tracing/distributed_tracing.py:if __name__ == "__main__":
examples/tracing/performance_test.py:if __name__ == "__main__":
examples/tracing/simple_tracing.py:if __name__ == "__main__":
examples/tracing/tracing_visualization.py:if __name__ == "__main__":
person_suit/__main__.py:if __name__ == "__main__":
person_suit/choreography/cli.py:if __name__ == "__main__":  # pragma: no cover
person_suit/choreography/compiler.py:if __name__ == "__main__":
person_suit/core/application/interfaces/events_interface.py
person_suit/core/context/acf_demo.py:if __name__ == "__main__":
person_suit/core/deployment_validator.py:if __name__ == "__main__":
person_suit/core/infrastructure/dependency_injection/containers.py:if __name__ == "__main__":
person_suit/core/infrastructure/dual_wave/scripts/migrate_all.py:if __name__ == "__main__":
person_suit/core/infrastructure/dual_wave/scripts/migrate_folded_mind_sem.py:if __name__ == "__main__":
person_suit/core/infrastructure/dual_wave/scripts/migrate_to_dual_wave.py:if __name__ == "__main__":
person_suit/core/infrastructure/dual_wave/scripts/migrate_wave_modules.py:if __name__ == "__main__":
person_suit/core/infrastructure/dual_wave/scripts/migrate_wave_particle.py:if __name__ == "__main__":
person_suit/core/infrastructure/dual_wave/scripts/migrate_wave.py:if __name__ == "__main__":
person_suit/core/infrastructure/message_based_migration.py:if __name__ == "__main__":
person_suit/core/infrastructure/resource_optimization/compute/resource_monitor.py:if __name__ == "__main__":
person_suit/core/infrastructure/tests/run_tests.py:if __name__ == "__main__":
person_suit/core/infrastructure/tracing/demo.py:if __name__ == "__main__":
person_suit/core/test_issue_2_truly_resolved.py:if __name__ == "__main__":
person_suit/core/test_issue_3_bootstrap_conflict.py:if __name__ == "__main__":
person_suit/core/test_issue_4_caw_compliance.py:if __name__ == "__main__":
person_suit/core/test_message_based_production.py:if __name__ == "__main__":
person_suit/core/test_phase_2_1_complete.py:if __name__ == "__main__":
person_suit/core/test_service_e2e.py:if __name__ == "__main__":
person_suit/core/test_true_independence.py:if __name__ == "__main__":
person_suit/core/test_true_message_based_demo.py:if __name__ == "__main__":
person_suit/io_layer/adapters/chat/effects_adapter.py:if __name__ == "__main__":
person_suit/io_layer/interfaces/admin/cli.py:if __name__ == "__main__":
person_suit/io_layer/interfaces/cli/cli.py:if __name__ == "__main__":
person_suit/io_layer/interfaces/cli/commands/diagnostic_commands.py:if __name__ == "__main__":
person_suit/io_layer/interfaces/cli/interface.py:if __name__ == "__main__":
person_suit/io_layer/interfaces/cli/main.py:if __name__ == "__main__":
person_suit/io_layer/interfaces/cli/scripts/direct_generate_completions.py:if __name__ == "__main__":
person_suit/io_layer/interfaces/cli/scripts/generate_completions.py:if __name__ == "__main__":
person_suit/main.py:if __name__ == "__main__":
person_suit/meta_systems/analyst/analytics/context_extraction/cli_runner.py:if __name__ == "__main__":
person_suit/meta_systems/analyst/analytics/context_extraction/cli_standalone.py:if __name__ == "__main__":
person_suit/meta_systems/analyst/analytics/context_extraction/examples/basic_extraction.py:if __name__ == "__main__":
person_suit/meta_systems/analyst/analytics/context_extraction/examples/behavioral_extraction_example.py:if __name__ == "__main__":
person_suit/meta_systems/analyst/analytics/context_extraction/examples/comprehensive_extraction.py:if __name__ == "__main__":
person_suit/meta_systems/analyst/analytics/context_extraction/examples/comprehensive_system_test.py:if __name__ == "__main__":
person_suit/meta_systems/analyst/analytics/context_extraction/examples/cross_context_relationships_example.py:if __name__ == "__main__":
person_suit/meta_systems/analyst/analytics/context_extraction/examples/intentional_extraction_example.py:if __name__ == "__main__":
person_suit/meta_systems/analyst/analytics/context_extraction/examples/memory_integration_example.py:if __name__ == "__main__":
person_suit/meta_systems/analyst/analytics/context_extraction/examples/multimodal_extraction_example.py:if __name__ == "__main__":
person_suit/meta_systems/analyst/analytics/context_extraction/examples/self_learning_example.py:if __name__ == "__main__":
person_suit/meta_systems/analyst/analytics/context_extraction/examples/simple_self_learning_test.py:if __name__ == "__main__":
person_suit/meta_systems/analyst/analytics/context_extraction/examples/situational_extraction_example.py:if __name__ == "__main__":
person_suit/meta_systems/analyst/entity_tracking/examples/entity_tracking_test.py:if __name__ == "__main__":
person_suit/meta_systems/analyst/entity_tracking/persistence/examples/optimized_storage_example.py:if __name__ == "__main__":
person_suit/meta_systems/analyst/entity_tracking/relationship/advanced_relationship_example.py:if __name__ == "__main__":
person_suit/meta_systems/analyst/entity_tracking/temporal/workstreams/benchmark.py:if __name__ == "__main__":
person_suit/meta_systems/analyst/entity_tracking/temporal/workstreams/cli.py:if __name__ == "__main__":
person_suit/meta_systems/analyst/entity_tracking/temporal/workstreams/example.py:if __name__ == "__main__":
person_suit/meta_systems/analyst/entity_tracking/temporal/workstreams/visualizer.py:if __name__ == "__main__":
person_suit/meta_systems/analyst/entity_tracking/visual/multimodal_correlation_example.py:if __name__ == "__main__":
person_suit/meta_systems/analyst/entity_tracking/visual/visual_recognition_example.py:if __name__ == "__main__":
person_suit/meta_systems/analyst/integration/examples/context_entity_integration_example.py:if __name__ == "__main__":
person_suit/meta_systems/analyst/integration/examples/memory_entity_integration_example.py:if __name__ == "__main__":
person_suit/meta_systems/analyst/integration/examples/pattern_entity_integration_example.py:if __name__ == "__main__":
person_suit/meta_systems/analyst/token_analysis/examples/advanced_pipeline_example.py:if __name__ == "__main__":
person_suit/meta_systems/persona_core/core/application/setup.py:if __name__ == "__main__":
person_suit/meta_systems/persona_core/core/application/setup.py.bak:if __name__ == "__main__":
person_suit/meta_systems/persona_core/core/di/examples/async_factory_example.py:if __name__ == "__main__":
person_suit/meta_systems/persona_core/core/di/examples/constructor_side_effects_example.py:if __name__ == "__main__":
person_suit/meta_systems/persona_core/core/di/examples/container_lifecycle_example.py:if __name__ == "__main__":
person_suit/meta_systems/persona_core/core/di/examples/fallback_provider_example.py:if __name__ == "__main__":
person_suit/meta_systems/persona_core/core/di/examples/generic_example.py:if __name__ == "__main__":
person_suit/meta_systems/persona_core/core/di/examples/logging_performance_test.py:if __name__ == "__main__":
person_suit/meta_systems/persona_core/core/di/examples/memory_management_example.py:if __name__ == "__main__":
person_suit/meta_systems/persona_core/core/di/examples/scope_management_example.py:if __name__ == "__main__":
person_suit/meta_systems/persona_core/core/di/examples/typevar_example.py:if __name__ == "__main__":
person_suit/meta_systems/persona_core/core/di/integration.py:if __name__ == "__main__":
person_suit/meta_systems/persona_core/core/di/tools/analyze_dependencies.py:if __name__ == "__main__":
person_suit/meta_systems/persona_core/core/event/examples/event_integration_example.py.bak:if __name__ == "__main__":
person_suit/meta_systems/persona_core/core/typing/examples/secure_payment.py:if __name__ == "__main__":
person_suit/meta_systems/persona_core/emotion_cognition/neurochemical/examples/neurochemical_emotion_example.py:if __name__ == "__main__":
person_suit/meta_systems/persona_core/folded_mind/backups/20250318_190032/backups/20250318_121044/messaging/examples.py.bak:if __name__ == "__main__":
person_suit/meta_systems/persona_core/folded_mind/backups/20250318_190032/backups/20250318_121044/rename_computational_to_cam.py.bak:if __name__ == "__main__":
person_suit/meta_systems/persona_core/folded_mind/backups/20250318_190032/backups/20250318_121044/rename_subjective_to_sem.py.bak:if __name__ == "__main__":
person_suit/meta_systems/persona_core/folded_mind/backups/20250318_190032/backups/20250318_121044/SEM/analogical_reasoning/example.py.bak:if __name__ == "__main__":
person_suit/meta_systems/persona_core/folded_mind/backups/20250318_190032/backups/20250318_121044/SEM/atomization/abstraction/test_utils.py.bak:if __name__ == "__main__":
person_suit/meta_systems/persona_core/folded_mind/backups/20250318_190032/backups/20250318_121044/SEM/atomization/benchmark/benchmark_runner.py.bak:if __name__ == "__main__":
person_suit/meta_systems/persona_core/folded_mind/backups/20250318_190032/backups/20250318_121044/SEM/atomization/test/automated_tests.py.bak:if __name__ == "__main__":
person_suit/meta_systems/persona_core/folded_mind/backups/20250318_190032/backups/20250318_121044/SEM/atomization/test/knowledge_graph_analysis_test.py.bak:if __name__ == "__main__":
person_suit/meta_systems/persona_core/folded_mind/backups/20250318_190032/backups/20250318_121044/SEM/atomization/test/knowledge_integration_test.py.bak:if __name__ == "__main__":
person_suit/meta_systems/persona_core/folded_mind/backups/20250318_190032/backups/20250318_121044/SEM/atomization/test/run_cohesiveness_tests.py.bak:if __name__ == "__main__":
person_suit/meta_systems/persona_core/folded_mind/backups/20250318_190032/backups/20250318_121044/SEM/atomization/test/setup_test_env.py.bak:if __name__ == "__main__":
person_suit/meta_systems/persona_core/folded_mind/backups/20250318_190032/backups/20250318_121044/SEM/component/examples.py.bak:if __name__ == "__main__":
person_suit/meta_systems/persona_core/folded_mind/backups/20250318_190032/backups/20250318_121044/SEM/concurrency/manager.py.bak:if __name__ == "__main__":
person_suit/meta_systems/persona_core/folded_mind/backups/20250318_190032/backups/20250318_121044/SEM/concurrency/process_pool.py.bak:if __name__ == "__main__":
person_suit/meta_systems/persona_core/folded_mind/backups/20250318_190032/backups/20250318_121044/SEM/concurrency/synchronization.py.bak:if __name__ == "__main__":
person_suit/meta_systems/persona_core/folded_mind/backups/20250318_190032/backups/20250318_121044/SEM/concurrency/tasks.py.bak:if __name__ == "__main__":
person_suit/meta_systems/persona_core/folded_mind/backups/20250318_190032/backups/20250318_121044/SEM/concurrency/thread_pool.py.bak:if __name__ == "__main__":
person_suit/meta_systems/persona_core/folded_mind/backups/20250318_190032/backups/20250318_121044/SEM/cross_domain_mapping/example.py.bak:if __name__ == "__main__":
person_suit/meta_systems/persona_core/folded_mind/backups/20250318_190032/backups/20250318_121044/SEM/emotional_metaphor/check_components.py.bak:if __name__ == "__main__":
person_suit/meta_systems/persona_core/folded_mind/backups/20250318_190032/backups/20250318_121044/SEM/emotional_metaphor/coherence_checking.py.bak:if __name__ == "__main__":
person_suit/meta_systems/persona_core/folded_mind/backups/20250318_190032/backups/20250318_121044/SEM/emotional_metaphor/cross_modal_mappings.py.bak:if __name__ == "__main__":
person_suit/meta_systems/persona_core/folded_mind/backups/20250318_190032/backups/20250318_121044/SEM/emotional_metaphor/cultural_mapping.py.bak:if __name__ == "__main__":
person_suit/meta_systems/persona_core/folded_mind/backups/20250318_190032/backups/20250318_121044/SEM/emotional_metaphor/dimension_mapping_utils.py.bak:if __name__ == "__main__":
person_suit/meta_systems/persona_core/folded_mind/backups/20250318_190032/backups/20250318_121044/SEM/emotional_metaphor/embodied_simulation.py.bak:if __name__ == "__main__":
person_suit/meta_systems/persona_core/folded_mind/backups/20250318_190032/backups/20250318_121044/SEM/emotional_metaphor/integration_tests.py.bak:if __name__ == "__main__":
person_suit/meta_systems/persona_core/folded_mind/backups/20250318_190032/backups/20250318_121044/SEM/emotional_metaphor/metaphor_impact.py.bak:if __name__ == "__main__":
person_suit/meta_systems/persona_core/folded_mind/backups/20250318_190032/backups/20250318_121044/SEM/emotional_metaphor/mock_integration_test.py.bak:if __name__ == "__main__":
person_suit/meta_systems/persona_core/folded_mind/backups/20250318_190032/backups/20250318_121044/SEM/emotional_metaphor/modality_visualizer.py.bak:if __name__ == "__main__":
person_suit/meta_systems/persona_core/folded_mind/backups/20250318_190032/backups/20250318_121044/SEM/emotional_metaphor/multimodal_integration_tests.py.bak:if __name__ == "__main__":
person_suit/meta_systems/persona_core/folded_mind/backups/20250318_190032/backups/20250318_121044/SEM/emotional_metaphor/nlp_modal_analysis.py.bak:if __name__ == "__main__":
person_suit/meta_systems/persona_core/folded_mind/backups/20250318_190032/backups/20250318_121044/SEM/emotional_metaphor/performance_optimization.py.bak:if __name__ == "__main__":
person_suit/meta_systems/persona_core/folded_mind/backups/20250318_190032/backups/20250318_121044/SEM/emotional_metaphor/run_integration_tests.py.bak:if __name__ == "__main__":
person_suit/meta_systems/persona_core/folded_mind/backups/20250318_190032/backups/20250318_121044/SEM/emotional_metaphor/sentiment_alignment.py.bak:if __name__ == "__main__":
person_suit/meta_systems/persona_core/folded_mind/backups/20250318_190032/backups/20250318_121044/SEM/error_handling/examples.py.bak:if __name__ == "__main__":
person_suit/meta_systems/persona_core/folded_mind/backups/20250318_190032/backups/20250318_121044/SEM/events/examples.py.bak:if __name__ == "__main__":
person_suit/meta_systems/persona_core/folded_mind/backups/20250318_190032/backups/20250318_121044/SEM/imagination/tests/test_imagination.py.bak:if __name__ == "__main__":
person_suit/meta_systems/persona_core/folded_mind/backups/20250318_190032/backups/20250318_121044/SEM/logging/examples.py.bak:if __name__ == "__main__":
person_suit/meta_systems/persona_core/folded_mind/backups/20250318_190032/backups/20250318_121044/SEM/logging/run_examples.py.bak:if __name__ == "__main__":
person_suit/meta_systems/persona_core/folded_mind/backups/20250318_190032/backups/20250318_121044/SEM/memory/examples/basic_usage.py.bak:if __name__ == "__main__":
person_suit/meta_systems/persona_core/folded_mind/backups/20250318_190032/backups/20250318_121044/SEM/memory/knowledge/emotional_tagging/example.py.bak:if __name__ == "__main__":
person_suit/meta_systems/persona_core/folded_mind/backups/20250318_190032/backups/20250318_121044/SEM/memory/knowledge/examples/basic_usage.py.bak:if __name__ == "__main__":
person_suit/meta_systems/persona_core/folded_mind/backups/20250318_190032/backups/20250318_121044/SEM/memory/knowledge/examples/simple_test.py.bak:if __name__ == "__main__":
person_suit/meta_systems/persona_core/folded_mind/backups/20250318_190032/backups/20250318_121044/SEM/memory/knowledge/metrics/examples/alerting_example.py.bak:if __name__ == "__main__":
person_suit/meta_systems/persona_core/folded_mind/backups/20250318_190032/backups/20250318_121044/SEM/memory/knowledge/metrics/examples/storage_example.py.bak:if __name__ == "__main__":
person_suit/meta_systems/persona_core/folded_mind/backups/20250318_190032/backups/20250318_121044/SEM/memory/knowledge/retrieval/examples/clustering_example.py.bak:if __name__ == "__main__":
person_suit/meta_systems/persona_core/folded_mind/backups/20250318_190032/backups/20250318_121044/SEM/memory/knowledge/retrieval/performance_test.py.bak:if __name__ == "__main__":
person_suit/meta_systems/persona_core/folded_mind/backups/20250318_190032/backups/20250318_121044/SEM/memory/knowledge/retrieval/tests/test_clustering.py.bak:if __name__ == "__main__":
person_suit/meta_systems/persona_core/folded_mind/backups/20250318_190032/backups/20250318_121044/SEM/memory/knowledge/test_knowledge_atom.py.bak:if __name__ == "__main__":
person_suit/meta_systems/persona_core/folded_mind/backups/20250318_190032/backups/20250318_121044/SEM/memory/test_memory.py.bak:if __name__ == "__main__":
person_suit/meta_systems/persona_core/folded_mind/backups/20250318_190032/backups/20250318_121044/SEM/metaphor_blending/example.py.bak:if __name__ == "__main__":
person_suit/meta_systems/persona_core/folded_mind/backups/20250318_190032/backups/20250318_121044/SEM/metaphor_evolution/examples/feedback_example.py.bak:if __name__ == "__main__":
person_suit/meta_systems/persona_core/folded_mind/backups/20250318_190032/backups/20250318_121044/SEM/metaphor_generation/example.py.bak:if __name__ == "__main__":
person_suit/meta_systems/persona_core/folded_mind/backups/20250318_190032/backups/20250318_121044/SEM/metaphor_generation/tests/generation_test.py.bak:if __name__ == "__main__":
person_suit/meta_systems/persona_core/folded_mind/backups/20250318_190032/backups/20250318_121044/SEM/metrics_test.py.bak:if __name__ == "__main__":
person_suit/meta_systems/persona_core/folded_mind/backups/20250318_190032/backups/20250318_121044/SEM/mode_switching/standalone_test.py.bak:if __name__ == "__main__":
person_suit/meta_systems/persona_core/folded_mind/backups/20250318_190032/backups/20250318_121044/SEM/mode_switching/test_integration.py.bak:if __name__ == "__main__":
person_suit/meta_systems/persona_core/folded_mind/backups/20250318_190032/backups/20250318_121044/SEM/pattern_test.py.bak:if __name__ == "__main__":
person_suit/meta_systems/persona_core/folded_mind/backups/20250318_190032/backups/20250318_121044/SEM/resources/examples.py.bak:if __name__ == "__main__":
person_suit/meta_systems/persona_core/folded_mind/backups/20250318_190032/backups/20250318_121044/SEM/simplified_pattern_test.py.bak:if __name__ == "__main__":
person_suit/meta_systems/persona_core/folded_mind/backups/20250318_190032/backups/20250318_121044/SEM/state_preservation/backup.py.bak:if __name__ == "__main__":
person_suit/meta_systems/persona_core/folded_mind/backups/20250318_190032/backups/20250318_121044/SEM/state_preservation/compression.py.bak:if __name__ == "__main__":
person_suit/meta_systems/persona_core/folded_mind/backups/20250318_190032/backups/20250318_121044/SEM/state_preservation/integrity.py.bak:if __name__ == "__main__":
person_suit/meta_systems/persona_core/folded_mind/backups/20250318_190032/backups/20250318_121044/SEM/state_preservation/manager.py.bak:if __name__ == "__main__":
person_suit/meta_systems/persona_core/folded_mind/backups/20250318_190032/backups/20250318_121044/SEM/state_preservation/migration.py.bak:if __name__ == "__main__":
person_suit/meta_systems/persona_core/folded_mind/backups/20250318_190032/backups/20250318_121044/SEM/state_preservation/schema.py.bak:if __name__ == "__main__":
person_suit/meta_systems/persona_core/folded_mind/backups/20250318_190032/backups/20250318_121044/SEM/state_preservation/serialization.py.bak:if __name__ == "__main__":
person_suit/meta_systems/persona_core/folded_mind/backups/20250318_190032/backups/20250318_121044/SEM/state_preservation/storage.py.bak:if __name__ == "__main__":
person_suit/meta_systems/persona_core/folded_mind/backups/20250318_190032/backups/20250318_121044/SEM/tests/run_sem_integration_tests.py.bak:if __name__ == "__main__":
person_suit/meta_systems/persona_core/folded_mind/backups/20250318_190032/backups/20250318_121044/SEM/tests/sem_integration_test.py.bak:if __name__ == "__main__":
person_suit/meta_systems/persona_core/folded_mind/bridges/examples/bridge_example.py:if __name__ == "__main__":
person_suit/meta_systems/persona_core/folded_mind/cam/algorithmic_reasoning/demo.py:if __name__ == "__main__":
person_suit/meta_systems/persona_core/folded_mind/cam/algorithmic_reasoning/validate_implementation.py:if __name__ == "__main__":
person_suit/meta_systems/persona_core/folded_mind/cam/data_integration/examples/basic_integration.py:if __name__ == "__main__":
person_suit/meta_systems/persona_core/folded_mind/cam/data_integration/examples/knowledge_integration_example.py:if __name__ == "__main__":
person_suit/meta_systems/persona_core/folded_mind/cam/data_integration/examples/multimodal_fusion_example.py:if __name__ == "__main__":
person_suit/meta_systems/persona_core/folded_mind/cam/data_integration/examples/quality_management_example.py:if __name__ == "__main__":
person_suit/meta_systems/persona_core/folded_mind/cam/data_integration/examples/schema_evolution_example.py:if __name__ == "__main__":
person_suit/meta_systems/persona_core/folded_mind/cam/data_integration/examples/security_privacy_example.py:if __name__ == "__main__":
person_suit/meta_systems/persona_core/folded_mind/cam/data_integration/examples/temporal_integration_example.py:if __name__ == "__main__":
person_suit/meta_systems/persona_core/folded_mind/cam/decision/decision_tree/example.py:if __name__ == "__main__":
person_suit/meta_systems/persona_core/folded_mind/cam/decision/decision_tree/pattern_integration_example.py:if __name__ == "__main__":
person_suit/meta_systems/persona_core/folded_mind/cam/decision/risk/example.py:if __name__ == "__main__":
person_suit/meta_systems/persona_core/folded_mind/cam/integration/cam_integrator.py:if __name__ == "__main__":
person_suit/meta_systems/persona_core/folded_mind/cam/knowledge/examples/knowledge_representation_example.py:if __name__ == "__main__":
person_suit/meta_systems/persona_core/folded_mind/cam/knowledge/examples/semantic_network_example.py:if __name__ == "__main__":
person_suit/meta_systems/persona_core/folded_mind/cam/knowledge/examples/standalone_example.py:if __name__ == "__main__":
person_suit/meta_systems/persona_core/folded_mind/cam/logical_processing/examples/deduction_example.py:if __name__ == "__main__":
person_suit/meta_systems/persona_core/folded_mind/cam/tool_framework/adapters/examples/config_demo.py:if __name__ == "__main__":
person_suit/meta_systems/persona_core/folded_mind/cam/tool_framework/adapters/examples/telemetry_demo.py:if __name__ == "__main__":
person_suit/meta_systems/persona_core/folded_mind/cam/tool_framework/integration/conflict_resolvers/examples/visualization_demo.py:if __name__ == "__main__":
person_suit/meta_systems/persona_core/folded_mind/error/examples.py:if __name__ == "__main__":
person_suit/meta_systems/persona_core/folded_mind/examples/dream_state_demo.py:if __name__ == "__main__":
person_suit/meta_systems/persona_core/folded_mind/examples/dual_pathway_example.py:if __name__ == "__main__":
person_suit/meta_systems/persona_core/folded_mind/examples/flexible_mind_example.py:if __name__ == "__main__":
person_suit/meta_systems/persona_core/folded_mind/folded_mind.py:if __name__ == "__main__":
person_suit/meta_systems/persona_core/folded_mind/integration/content_combiner.py:if __name__ == "__main__":
person_suit/meta_systems/persona_core/folded_mind/integration/dream_integration_handler.py:if __name__ == "__main__":
person_suit/meta_systems/persona_core/folded_mind/integration/resolution_applier.py:if __name__ == "__main__":
person_suit/meta_systems/persona_core/folded_mind/messaging/examples.py:if __name__ == "__main__":
person_suit/meta_systems/persona_core/folded_mind/monitoring/examples/basic_usage.py:if __name__ == "__main__":
person_suit/meta_systems/persona_core/folded_mind/monitoring/examples/cognitive_monitoring_demo.py:if __name__ == "__main__":
person_suit/meta_systems/persona_core/folded_mind/monitoring/examples/decision_making_example.py:if __name__ == "__main__":
person_suit/meta_systems/persona_core/folded_mind/monitoring/tests/__init__.py:if __name__ == "__main__":
person_suit/meta_systems/persona_core/folded_mind/README_MONITORING.md:if __name__ == "__main__":
person_suit/meta_systems/persona_core/folded_mind/run_example.py:if __name__ == "__main__":
person_suit/meta_systems/persona_core/folded_mind/run_tests.py:if __name__ == "__main__":
person_suit/meta_systems/persona_core/folded_mind/sem/analogical_reasoning/creative_reasoning/examples/metaphor_manager_example.py:if __name__ == "__main__":
person_suit/meta_systems/persona_core/folded_mind/sem/analogical_reasoning/example_problem_solving.py:if __name__ == "__main__":
person_suit/meta_systems/persona_core/folded_mind/sem/analogical_reasoning/example_schema_abstraction.py:if __name__ == "__main__":
person_suit/meta_systems/persona_core/folded_mind/sem/analogical_reasoning/example.py:if __name__ == "__main__":
person_suit/meta_systems/persona_core/folded_mind/sem/analogical_reasoning/examples/creative_reasoning_example.py:if __name__ == "__main__":
person_suit/meta_systems/persona_core/folded_mind/sem/analogical_reasoning/examples/persistence_example.py:if __name__ == "__main__":
person_suit/meta_systems/persona_core/folded_mind/sem/analogical_reasoning/tests/integration_test.py:if __name__ == "__main__":
person_suit/meta_systems/persona_core/folded_mind/sem/atomization/benchmark/benchmark_runner.py:if __name__ == "__main__":
person_suit/meta_systems/persona_core/folded_mind/sem/atomization/test/automated_tests.py:if __name__ == "__main__":
person_suit/meta_systems/persona_core/folded_mind/sem/atomization/test/knowledge_graph_analysis_test.py:if __name__ == "__main__":
person_suit/meta_systems/persona_core/folded_mind/sem/atomization/test/knowledge_integration_test.py:if __name__ == "__main__":
person_suit/meta_systems/persona_core/folded_mind/sem/atomization/test/run_cohesiveness_tests.py:if __name__ == "__main__":
person_suit/meta_systems/persona_core/folded_mind/sem/atomization/test/setup_test_env.py:if __name__ == "__main__":
person_suit/meta_systems/persona_core/folded_mind/sem/cleanup.py:if __name__ == "__main__":
person_suit/meta_systems/persona_core/folded_mind/sem/component/examples.py:if __name__ == "__main__":
person_suit/meta_systems/persona_core/folded_mind/sem/concurrency/examples/background_task_example.py:if __name__ == "__main__":
person_suit/meta_systems/persona_core/folded_mind/sem/concurrency/manager.py:if __name__ == "__main__":
person_suit/meta_systems/persona_core/folded_mind/sem/concurrency/process_pool.py:if __name__ == "__main__":
person_suit/meta_systems/persona_core/folded_mind/sem/concurrency/synchronization.py:if __name__ == "__main__":
person_suit/meta_systems/persona_core/folded_mind/sem/concurrency/tasks.py:if __name__ == "__main__":
person_suit/meta_systems/persona_core/folded_mind/sem/concurrency/thread_pool.py:if __name__ == "__main__":
person_suit/meta_systems/persona_core/folded_mind/sem/cross_domain_mapping/example.py:if __name__ == "__main__":
person_suit/meta_systems/persona_core/folded_mind/sem/emotional_metaphor/check_components.py:if __name__ == "__main__":
person_suit/meta_systems/persona_core/folded_mind/sem/emotional_metaphor/coherence_checking.py:if __name__ == "__main__":
person_suit/meta_systems/persona_core/folded_mind/sem/emotional_metaphor/cross_modal_extender/cross_modal_extender.py:if __name__ == "__main__":
person_suit/meta_systems/persona_core/folded_mind/sem/emotional_metaphor/cross_modal_extender/examples/demo_matrix_generator.py:if __name__ == "__main__":
person_suit/meta_systems/persona_core/folded_mind/sem/emotional_metaphor/cross_modal_extender/examples/demonstrate_auditory_to_text.py:if __name__ == "__main__":
person_suit/meta_systems/persona_core/folded_mind/sem/emotional_metaphor/cross_modal_extender/examples/demonstrate_auditory_to_visual_simplified.py:if __name__ == "__main__":
person_suit/meta_systems/persona_core/folded_mind/sem/emotional_metaphor/cross_modal_extender/examples/demonstrate_auditory_to_visual.py:if __name__ == "__main__":
person_suit/meta_systems/persona_core/folded_mind/sem/emotional_metaphor/cross_modal_extender/examples/demonstrate_tactile_to_text.py:if __name__ == "__main__":
person_suit/meta_systems/persona_core/folded_mind/sem/emotional_metaphor/cross_modal_extender/examples/demonstrate_tactile_to_visual.py:if __name__ == "__main__":
person_suit/meta_systems/persona_core/folded_mind/sem/emotional_metaphor/cross_modal_extender/examples/demonstrate_text_to_auditory.py:if __name__ == "__main__":
person_suit/meta_systems/persona_core/folded_mind/sem/emotional_metaphor/cross_modal_extender/examples/demonstrate_text_to_tactile.py:if __name__ == "__main__":
person_suit/meta_systems/persona_core/folded_mind/sem/emotional_metaphor/cross_modal_extender/examples/demonstrate_text_to_visual.py:if __name__ == "__main__":
person_suit/meta_systems/persona_core/folded_mind/sem/emotional_metaphor/cross_modal_extender/examples/demonstrate_transformations.py:if __name__ == "__main__":
person_suit/meta_systems/persona_core/folded_mind/sem/emotional_metaphor/cross_modal_extender/examples/demonstrate_visual_to_auditory_simplified.py:if __name__ == "__main__":
person_suit/meta_systems/persona_core/folded_mind/sem/emotional_metaphor/cross_modal_extender/examples/demonstrate_visual_to_auditory.py:if __name__ == "__main__":
person_suit/meta_systems/persona_core/folded_mind/sem/emotional_metaphor/cross_modal_extender/examples/demonstrate_visual_to_tactile.py:if __name__ == "__main__":
person_suit/meta_systems/persona_core/folded_mind/sem/emotional_metaphor/cross_modal_extender/examples/demonstrate_visual_to_text.py:if __name__ == "__main__":
person_suit/meta_systems/persona_core/folded_mind/sem/emotional_metaphor/cross_modal_extender/examples/transformer_example.py:if __name__ == "__main__":
person_suit/meta_systems/persona_core/folded_mind/sem/emotional_metaphor/cross_modal_extender/modality_transformer.py:if __name__ == "__main__":
person_suit/meta_systems/persona_core/folded_mind/sem/emotional_metaphor/cross_modal_extender/utils/implementation_progress_tracker.py:if __name__ == "__main__":
person_suit/meta_systems/persona_core/folded_mind/sem/emotional_metaphor/cross_modal_extender/utils/transformation_matrix_generator.py:if __name__ == "__main__":
person_suit/meta_systems/persona_core/folded_mind/sem/emotional_metaphor/cross_modal_extender/visual_metaphor_generator.py:if __name__ == "__main__":
person_suit/meta_systems/persona_core/folded_mind/sem/emotional_metaphor/cross_modal_mappings.py:if __name__ == "__main__":
person_suit/meta_systems/persona_core/folded_mind/sem/emotional_metaphor/cultural_framework/benchmark.py:if __name__ == "__main__":
person_suit/meta_systems/persona_core/folded_mind/sem/emotional_metaphor/cultural_framework/demonstrate_caching.py:if __name__ == "__main__":
person_suit/meta_systems/persona_core/folded_mind/sem/emotional_metaphor/cultural_framework/demonstrate_linguistic_adaptation.py:if __name__ == "__main__":
person_suit/meta_systems/persona_core/folded_mind/sem/emotional_metaphor/cultural_mapping.py:if __name__ == "__main__":
person_suit/meta_systems/persona_core/folded_mind/sem/emotional_metaphor/dimension_mapping_utils.py:if __name__ == "__main__":
person_suit/meta_systems/persona_core/folded_mind/sem/emotional_metaphor/direct_validation.py:if __name__ == "__main__":
person_suit/meta_systems/persona_core/folded_mind/sem/emotional_metaphor/embodied_simulation.py:if __name__ == "__main__":
person_suit/meta_systems/persona_core/folded_mind/sem/emotional_metaphor/integration_tests.py:if __name__ == "__main__":
person_suit/meta_systems/persona_core/folded_mind/sem/emotional_metaphor/metaphor_impact.py:if __name__ == "__main__":
person_suit/meta_systems/persona_core/folded_mind/sem/emotional_metaphor/mock_integration_test.py:if __name__ == "__main__":
person_suit/meta_systems/persona_core/folded_mind/sem/emotional_metaphor/modality_visualizer.py:if __name__ == "__main__":
person_suit/meta_systems/persona_core/folded_mind/sem/emotional_metaphor/multimodal_integration_tests.py:if __name__ == "__main__":
person_suit/meta_systems/persona_core/folded_mind/sem/emotional_metaphor/performance_optimization.py:if __name__ == "__main__":
person_suit/meta_systems/persona_core/folded_mind/sem/emotional_metaphor/run_integration_tests.py:if __name__ == "__main__":
person_suit/meta_systems/persona_core/folded_mind/sem/emotional_metaphor/run_sem5_3_validation.py:if __name__ == "__main__":
person_suit/meta_systems/persona_core/folded_mind/sem/emotional_metaphor/sentiment_alignment.py:if __name__ == "__main__":
person_suit/meta_systems/persona_core/folded_mind/sem/emotional_metaphor/update_therapeutic_application.py:if __name__ == "__main__":
person_suit/meta_systems/persona_core/folded_mind/sem/emotional_metaphor/validate_sem5_3_implementation.py:if __name__ == "__main__":
person_suit/meta_systems/persona_core/folded_mind/sem/error_handling/examples.py:if __name__ == "__main__":
person_suit/meta_systems/persona_core/folded_mind/sem/error_handling/examples/meta_error_handling_example.py:if __name__ == "__main__":
person_suit/meta_systems/persona_core/folded_mind/sem/error_handling/integration_example.py:if __name__ == "__main__":
person_suit/meta_systems/persona_core/folded_mind/sem/events/examples.py:if __name__ == "__main__":
person_suit/meta_systems/persona_core/folded_mind/sem/examples/emotional_dynamics_example.py:if __name__ == "__main__":
person_suit/meta_systems/persona_core/folded_mind/sem/examples/emotional_pipeline_example.py:if __name__ == "__main__":
person_suit/meta_systems/persona_core/folded_mind/sem/examples/integrated_emotional_processing_example.py:if __name__ == "__main__":
person_suit/meta_systems/persona_core/folded_mind/sem/examples/sem_pathway_example.py:if __name__ == "__main__":
person_suit/meta_systems/persona_core/folded_mind/sem/logging/examples.py:if __name__ == "__main__":
person_suit/meta_systems/persona_core/folded_mind/sem/logging/run_examples.py:if __name__ == "__main__":
person_suit/meta_systems/persona_core/folded_mind/sem/memory/examples/basic_usage.py:if __name__ == "__main__":
person_suit/meta_systems/persona_core/folded_mind/sem/memory/knowledge/emotional_tagging/example.py:if __name__ == "__main__":
person_suit/meta_systems/persona_core/folded_mind/sem/memory/knowledge/examples/basic_usage.py:if __name__ == "__main__":
person_suit/meta_systems/persona_core/folded_mind/sem/memory/knowledge/examples/simple_test.py:if __name__ == "__main__":
person_suit/meta_systems/persona_core/folded_mind/sem/memory/knowledge/metrics/examples/alerting_example.py:if __name__ == "__main__":
person_suit/meta_systems/persona_core/folded_mind/sem/memory/knowledge/metrics/examples/storage_example.py:if __name__ == "__main__":
person_suit/meta_systems/persona_core/folded_mind/sem/memory/knowledge/retrieval/examples/clustering_example.py:if __name__ == "__main__":
person_suit/meta_systems/persona_core/folded_mind/sem/memory/knowledge/retrieval/performance_test.py:if __name__ == "__main__":
person_suit/meta_systems/persona_core/folded_mind/sem/memory/knowledge/retrieval/query_language/simple_test.py:if __name__ == "__main__":
person_suit/meta_systems/persona_core/folded_mind/sem/metaphor_blending/example.py:if __name__ == "__main__":
person_suit/meta_systems/persona_core/folded_mind/sem/metaphor_evolution/examples/feedback_example.py:if __name__ == "__main__":
person_suit/meta_systems/persona_core/folded_mind/sem/metaphor_generation/example.py:if __name__ == "__main__":
person_suit/meta_systems/persona_core/folded_mind/sem/metaphor_generation/tests/generation_test.py:if __name__ == "__main__":
person_suit/meta_systems/persona_core/folded_mind/sem/metrics_test.py:if __name__ == "__main__":
person_suit/meta_systems/persona_core/folded_mind/sem/mode_switching/standalone_test.py:if __name__ == "__main__":
person_suit/meta_systems/persona_core/folded_mind/sem/neurochemical/examples/neurochemical_wave_example.py:if __name__ == "__main__":
person_suit/meta_systems/persona_core/folded_mind/sem/pattern_test.py:if __name__ == "__main__":
person_suit/meta_systems/persona_core/folded_mind/sem/resources/examples.py:if __name__ == "__main__":
person_suit/meta_systems/persona_core/folded_mind/sem/simplified_pattern_test.py:if __name__ == "__main__":
person_suit/meta_systems/persona_core/folded_mind/sem/state_preservation/backup.py:if __name__ == "__main__":
person_suit/meta_systems/persona_core/folded_mind/sem/state_preservation/compression.py:if __name__ == "__main__":
person_suit/meta_systems/persona_core/folded_mind/sem/state_preservation/integrity.py:if __name__ == "__main__":
person_suit/meta_systems/persona_core/folded_mind/sem/state_preservation/manager.py:if __name__ == "__main__":
person_suit/meta_systems/persona_core/folded_mind/sem/state_preservation/migration.py:if __name__ == "__main__":
person_suit/meta_systems/persona_core/folded_mind/sem/state_preservation/schema.py:if __name__ == "__main__":
person_suit/meta_systems/persona_core/folded_mind/sem/state_preservation/serialization.py:if __name__ == "__main__":
person_suit/meta_systems/persona_core/folded_mind/sem/state_preservation/storage.py:if __name__ == "__main__":
person_suit/meta_systems/persona_core/folded_mind/sem/tests/run_sem_integration_tests.py:if __name__ == "__main__":
person_suit/meta_systems/persona_core/folded_mind/sem/tests/run_sem_pathway_tests.py:if __name__ == "__main__":
person_suit/meta_systems/persona_core/folded_mind/sem/tests/sem_integration_test.py:if __name__ == "__main__":
person_suit/meta_systems/persona_core/folded_mind/soma/homeostatic_driver.py:# if __name__ == "__main__":
person_suit/meta_systems/persona_core/folded_mind/sync/examples/sync_demo.py:if __name__ == "__main__":
person_suit/meta_systems/persona_core/intelligence_enhancement/approaches/compact_models/examples/text_classification.py:if __name__ == "__main__":
person_suit/meta_systems/persona_core/intelligence_enhancement/approaches/hybrid_symbolic_neural/examples/basic_usage.py:if __name__ == "__main__":
person_suit/meta_systems/persona_core/intelligence_enhancement/approaches/hybrid_symbolic_neural/examples/neural_demo.py:if __name__ == "__main__":
person_suit/meta_systems/persona_core/intelligence_enhancement/approaches/hybrid_symbolic_neural/explainer/__main__.py:if __name__ == "__main__":
person_suit/meta_systems/persona_core/intelligence_enhancement/approaches/hybrid_symbolic_neural/explainer/testing.py:if __name__ == "__main__":
person_suit/meta_systems/persona_core/intelligence_enhancement/approaches/hybrid_symbolic_neural/explainer/testing/integration_tests.py:if __name__ == "__main__":
person_suit/meta_systems/persona_core/intelligence_enhancement/approaches/neuroevolution/examples/xor_example.py:if __name__ == "__main__":
person_suit/meta_systems/persona_core/intelligence_enhancement/examples/basic_usage.py:if __name__ == "__main__":
person_suit/meta_systems/persona_core/intelligence_enhancement/examples/hybrid_approach_demo.py:if __name__ == "__main__":
person_suit/meta_systems/persona_core/intelligence_enhancement/examples/hybrid_symbolic_neural_example.py:if __name__ == "__main__":
person_suit/meta_systems/persona_core/memory/advanced/integration_example.py:if __name__ == "__main__":
person_suit/meta_systems/persona_core/memory/embodied.py:if __name__ == "__main__":
person_suit/meta_systems/persona_core/memory/examples/complete_architecture_example.py:if __name__ == "__main__":
person_suit/meta_systems/persona_core/memory/examples/folded_mind_integration_example.py:if __name__ == "__main__":
person_suit/meta_systems/persona_core/memory/examples/forgetting_example.py:if __name__ == "__main__":
person_suit/meta_systems/persona_core/memory/examples/memory_layers_example.py:if __name__ == "__main__":
person_suit/meta_systems/persona_core/memory/integration/example_basic_usage.py:if __name__ == "__main__":
person_suit/meta_systems/persona_core/memory/test_consolidation_basic.py:if __name__ == "__main__":
person_suit/meta_systems/persona_core/memory/test_consolidation_fixed.py:if __name__ == "__main__":
person_suit/meta_systems/persona_core/memory/test_schema_wave_particle.py:if __name__ == "__main__":
person_suit/meta_systems/persona_core/memory/utils/concurrency.py:if __name__ == "__main__":
person_suit/meta_systems/persona_core/nlp/modal_analysis.py:if __name__ == "__main__":
person_suit/meta_systems/persona_core/pattern_detection/examples/hypothesis_testing_example.py:if __name__ == "__main__":
person_suit/meta_systems/prediction/examples/cam_sem_integration.py:if __name__ == "__main__":
person_suit/meta_systems/prediction/examples/cam_sem/main.py:if __name__ == "__main__":
person_suit/meta_systems/prediction/examples/cam_sem/README.md:if __name__ == "__main__":
person_suit/meta_systems/prediction/examples/cognitive_loop_visualizer.py:if __name__ == "__main__":
person_suit/meta_systems/prediction/examples/hypothesis_bridge_example.py:if __name__ == "__main__":
person_suit/meta_systems/prediction/examples/hypothesis_generation_example.py:if __name__ == "__main__":
person_suit/meta_systems/prediction/examples/pattern_hypothesis_model_integration.py:if __name__ == "__main__":
person_suit/meta_systems/prediction/examples/pattern_to_model_integration.py:if __name__ == "__main__":
person_suit/meta_systems/prediction/examples/prediction_engine_example.py:if __name__ == "__main__":
person_suit/meta_systems/prediction/examples/real_world_pattern_model.py:if __name__ == "__main__":
person_suit/meta_systems/prediction/examples/workstream3_hypothesis_integration.py:if __name__ == "__main__":
person_suit/meta_systems/prediction/hypothesis_generation/examples/hypothesis_model_integration_example.py:if __name__ == "__main__":
person_suit/meta_systems/prediction/integration/hypothesis_model_trainer_bridge.py:if __name__ == "__main__":
person_suit/meta_systems/prediction/model_training/distributed/data_utils.py:if __name__ == "__main__":
person_suit/meta_systems/prediction/model_training/examples/hypothesis_driven_training.py:if __name__ == "__main__":
person_suit/meta_systems/prediction/model_training/examples/train_experimental_model.py:if __name__ == "__main__":
person_suit/meta_systems/prediction/model_training/examples/train_small_model.py:if __name__ == "__main__":
person_suit/meta_systems/prediction/model_training/examples/use_pw3_model.py:if __name__ == "__main__":
person_suit/meta_systems/prediction/model_training/examples/use_workstream_factory.py:if __name__ == "__main__":
person_suit/meta_systems/prediction/model_training/examples/wave_particle_demo.py:if __name__ == "__main__":
person_suit/meta_systems/prediction/model_training/examples/wave_particle_meta_modal_demo.py:if __name__ == "__main__":
person_suit/meta_systems/prediction/model_training/examples/wave_particle_multimodal_demo.py:if __name__ == "__main__":
person_suit/meta_systems/prediction/model_training/shared/multimodal/benchmark.py:if __name__ == "__main__":
person_suit/meta_systems/prediction/model_training/shared/multimodal/examples/multimodal_example.py:if __name__ == "__main__":
person_suit/meta_systems/prediction/model_training/ultra_large_model/trainer.py:if __name__ == "__main__":
person_suit/meta_systems/prediction/model_training/workstream3/advanced_meta_modal.py:if __name__ == "__main__":
person_suit/meta_systems/prediction/model_training/workstream3/examples/advanced_meta_modal_example.py:if __name__ == "__main__":
person_suit/meta_systems/prediction/model_training/workstream3/examples/cross_modal_attention_example.py:if __name__ == "__main__":
person_suit/meta_systems/prediction/model_training/workstream3/examples/m3_max_optimization_example.py:if __name__ == "__main__":
person_suit/meta_systems/prediction/model_training/workstream3/examples/neural_predictor_integration_example.py:if __name__ == "__main__":
person_suit/meta_systems/prediction/model_training/workstream3/examples/pattern_integration_example.py:if __name__ == "__main__":
person_suit/meta_systems/prediction/model_training/workstream3/optimization/distributed_training.py:if __name__ == "__main__":
person_suit/meta_systems/prediction/model_training/workstream3/optimization/wave_particle_benchmarks.py:if __name__ == "__main__":
person_suit/meta_systems/prediction/neural_predictor/examples/advanced_examples/realtime_pattern_prediction.py:if __name__ == "__main__":
person_suit/meta_systems/prediction/neural_predictor/examples/confidence_scoring_example.py:if __name__ == "__main__":
person_suit/meta_systems/prediction/neural_predictor/examples/confidence_validation_integration_example.py:if __name__ == "__main__":
person_suit/meta_systems/prediction/neural_predictor/examples/confidence_visualization_example.py:if __name__ == "__main__":
person_suit/meta_systems/prediction/neural_predictor/examples/historical_confidence_example.py:if __name__ == "__main__":
person_suit/meta_systems/prediction/neural_predictor/examples/pattern_prediction_example.py:if __name__ == "__main__":
person_suit/meta_systems/prediction/neural_predictor/examples/specialized_models_example.py:if __name__ == "__main__":
person_suit/meta_systems/prediction/neural_predictor/examples/streaming_pattern_prediction.py:if __name__ == "__main__":
person_suit/meta_systems/prediction/neural_predictor/examples/validation_example.py:if __name__ == "__main__":
person_suit/meta_systems/prediction/neural_predictor/examples/visualization_example.py:if __name__ == "__main__":
person_suit/meta_systems/prediction/neural_predictor/multimodal.py:if __name__ == "__main__":
person_suit/meta_systems/prediction/neural_predictor/validation/benchmarking/visualization/dashboard.py:if __name__ == "__main__":
person_suit/meta_systems/prediction/neural_predictor/validation/selection.py:if __name__ == "__main__":
person_suit/meta_systems/prediction/pattern_detection/engine.py:if __name__ == "__main__":
person_suit/meta_systems/prediction/pattern_detection/examples/entity_pattern_integration_example.py:if __name__ == "__main__":
person_suit/meta_systems/prediction/pattern_detection/examples/extraction_examples.py:if __name__ == "__main__":
person_suit/meta_systems/prediction/pattern_detection/examples/integrated_framework_example.py:if __name__ == "__main__":
person_suit/meta_systems/prediction/pattern_detection/examples/mem_pred_integration_example.py:if __name__ == "__main__":
person_suit/meta_systems/prediction/pattern_detection/examples/pattern_learning_example.py:if __name__ == "__main__":
person_suit/meta_systems/prediction/pattern_detection/examples/pattern_model_integration_example.py:if __name__ == "__main__":
person_suit/meta_systems/prediction/pattern_detection/examples/pattern_storage_example.py:if __name__ == "__main__":
person_suit/meta_systems/prediction/pattern_detection/examples/pattern_validation_example.py:if __name__ == "__main__":
person_suit/meta_systems/prediction/pattern_detection/examples/statistical_detector_example.py:if __name__ == "__main__":
person_suit/meta_systems/prediction/pattern_detection/examples/temporal_detector_example.py:if __name__ == "__main__":
person_suit/meta_systems/prediction/pattern_detection/examples/user_activity_analysis.py:if __name__ == "__main__":
person_suit/meta_systems/prediction/pattern_detection/tests/run_tests.py:if __name__ == "__main__":
person_suit/monitoring/wavetrace_prom_exporter.py:if __name__ == "__main__":
person_suit/shared/data/nltk_data/taggers/universal_tagset/universal_tags.py:    if __name__ == "__main__":
person_suit/shared/io/distribution/resilient/examples/basic_usage.py:if __name__ == "__main__":
person_suit/shared/io/distribution/tests/run_tests.py:if __name__ == "__main__":
person_suit/shared/io/preprocessing/adaptive_demo.py:if __name__ == "__main__":
person_suit/shared/io/preprocessing/adaptive_simple_demo.py:if __name__ == "__main__":
person_suit/shared/io/preprocessing/demo_test.py:if __name__ == "__main__":
person_suit/shared/io/preprocessing/examples/optimized_validation_pipeline.py:if __name__ == "__main__":
person_suit/shared/io/preprocessing/simple_demo.py:if __name__ == "__main__":
person_suit/shared/io/protocol/examples/basic_usage.py:if __name__ == "__main__":
person_suit/shared/io/validation/advanced_validators_demo.py:if __name__ == "__main__":
person_suit/shared/io/validation/pipeline_demo.py:if __name__ == "__main__":
person_suit/shared/io/validation/tests/run_tests.py:if __name__ == "__main__":
person_suit/shared/memory/examples/db_service_example.py:if __name__ == "__main__":
person_suit/shared/utils/docstring_validator.py:if __name__ == "__main__":
person_suit/test_step_1_focused.py:if __name__ == "__main__":
scripts/counter_evidence/actor_metrics.py:if __name__ == "__main__":
scripts/counter_evidence/latency_baseline.py:if __name__ == "__main__":
scripts/counter_evidence/provenance_durability.py:if __name__ == "__main__":
scripts/counter_evidence/trace_continuity.py:if __name__ == "__main__":
scripts/dependency_graph.py:if __name__ == "__main__":
scripts/dev/create_service.py:if __name__ == "__main__":
scripts/diagnostics/hybrid_bus_diagnostics.py:if __name__ == "__main__":
scripts/diagnostics/verify_uvloop.py:if __name__ == "__main__":
scripts/lint_bus_singleton.sh:if __name__ == "__main__":
scripts/load_tests/acf_stress_test.py:if __name__ == "__main__":
scripts/load_tests/advanced_stress_test.py:if __name__ == "__main__":
scripts/load_tests/bus_load.py:if __name__ == "__main__":
scripts/load_tests/caw_principles_simple_test.py:if __name__ == "__main__":
scripts/load_tests/caw_principles_stress_test.py:if __name__ == "__main__":
scripts/load_tests/cee_smoke.py:if __name__ == "__main__":
scripts/load_tests/effect_throughput.py:if __name__ == "__main__":
scripts/load_tests/extreme_acf_test.py:if __name__ == "__main__":
scripts/load_tests/minimal_cee_stress_test.py:if __name__ == "__main__":
scripts/load_tests/simple_acf_test.py:if __name__ == "__main__":
scripts/maintenance/cleanup_old_effect_decorators.py:if __name__ == "__main__":
scripts/maintenance/comprehensive_security_fix.py:if __name__ == "__main__":
scripts/maintenance/context_handling_fix.py:if __name__ == "__main__":
scripts/maintenance/deprecate_thread_files.py:if __name__ == "__main__":
scripts/maintenance/fix_concurrent_and_security.py:if __name__ == "__main__":
scripts/maintenance/fix_dataclass_fields.py:if __name__ == "__main__":
scripts/maintenance/fix_effects_imports.py:if __name__ == "__main__":
scripts/maintenance/fix_get_message_bus_calls.py:if __name__ == "__main__":
scripts/maintenance/fix_integration_and_acf.py:if __name__ == "__main__":
scripts/maintenance/real_time_actor_health_fix.py:if __name__ == "__main__":
scripts/maintenance/test_critical_fixes_simple.py:if __name__ == "__main__":
scripts/maintenance/test_critical_fixes.py:if __name__ == "__main__":
scripts/maintenance/test_production_effects_fixed.py:if __name__ == "__main__":
scripts/maintenance/test_systemic_fixes.py:if __name__ == "__main__":
scripts/maintenance/validation_fix_test.py:if __name__ == "__main__":
scripts/migrate_imports.py:if __name__ == "__main__":
scripts/planning/holistic_architectural_refactoring_plan.py:if __name__ == "__main__":
scripts/python/install_dev.py:if __name__ == "__main__":
scripts/python/scan_deprecated_imports.py:if __name__ == "__main__":
scripts/python/setup_nltk.py:    if __name__ == "__main__":
scripts/read_wavetrace.py:if __name__ == "__main__":
scripts/refactor_caw_components.py:if __name__ == "__main__":
scripts/refactor_cawactor.py:if __name__ == "__main__":
scripts/refactor_cawmessage.py:if __name__ == "__main__":
scripts/restructure/audit_float_defaults.py:if __name__ == "__main__":
scripts/restructure/migrate_fidelity_to_scale.py:if __name__ == "__main__":
scripts/security/penetration.py:if __name__ == "__main__":
scripts/smoke_boot.py:if __name__ == "__main__":
scripts/test_cee_simple.py:if __name__ == "__main__":
scripts/test_native_provenance.py:if __name__ == "__main__":
scripts/test_ps005_rule.py:if __name__ == "__main__":
scripts/validate_cee_flow.py:if __name__ == "__main__":
