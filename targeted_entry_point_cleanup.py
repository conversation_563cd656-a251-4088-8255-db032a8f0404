#!/usr/bin/env python3
"""
Targeted Entry Point Cleanup for Bootstrap Unification
======================================================

Systematically identifies and removes unauthorized entry points from person_suit/ directory.
Only person_suit/main.py and person_suit/__main__.py should have entry points.
"""

import os
import re
from pathlib import Path
from typing import List, Set

def find_python_files_with_main_blocks() -> List[Path]:
    """Find all Python files in person_suit/ with main blocks."""
    person_suit_dir = Path("person_suit")
    if not person_suit_dir.exists():
        print("❌ person_suit directory not found")
        return []
    
    files_with_main = []
    
    # Walk through all Python files
    for py_file in person_suit_dir.rglob("*.py"):
        try:
            content = py_file.read_text(encoding='utf-8')
            # Look for if __name__ == "__main__": pattern
            if re.search(r'if\s+__name__\s*==\s*["\']__main__["\']\s*:', content):
                files_with_main.append(py_file)
        except Exception as e:
            print(f"⚠️ Error reading {py_file}: {e}")
    
    return files_with_main

def is_allowed_entry_point(file_path: Path) -> bool:
    """Check if this file is allowed to have an entry point."""
    allowed_files = {
        "person_suit/main.py",
        "person_suit/__main__.py"
    }
    return str(file_path) in allowed_files

def remove_main_block(file_path: Path) -> bool:
    """Remove the main block from a file."""
    try:
        content = file_path.read_text(encoding='utf-8')
        original_content = content
        
        # Pattern to match if __name__ == "__main__": and everything after it
        # This is more aggressive - removes everything from the main block to end of file
        pattern = r'\n\s*if\s+__name__\s*==\s*["\']__main__["\']\s*:.*$'
        new_content = re.sub(pattern, '', content, flags=re.MULTILINE | re.DOTALL)
        
        # Also try to match main blocks that aren't at the end
        pattern2 = r'\s*if\s+__name__\s*==\s*["\']__main__["\']\s*:\s*\n(?:(?:\s{4,}.*\n)*)'
        new_content = re.sub(pattern2, '\n', new_content, flags=re.MULTILINE)
        
        if new_content != original_content:
            # Clean up trailing whitespace and ensure file ends with newline
            new_content = new_content.rstrip() + '\n' if new_content.strip() else ''
            file_path.write_text(new_content, encoding='utf-8')
            return True
        return False
        
    except Exception as e:
        print(f"❌ Error processing {file_path}: {e}")
        return False

def categorize_files(files: List[Path]) -> dict:
    """Categorize files by type for reporting."""
    categories = {
        'core': [],
        'meta_systems': [],
        'examples': [],
        'tests': [],
        'tools': [],
        'other': []
    }
    
    for file_path in files:
        path_str = str(file_path)
        if '/core/' in path_str:
            categories['core'].append(file_path)
        elif '/meta_systems/' in path_str:
            categories['meta_systems'].append(file_path)
        elif '/examples/' in path_str or 'example' in path_str.lower():
            categories['examples'].append(file_path)
        elif '/test' in path_str or 'test_' in path_str:
            categories['tests'].append(file_path)
        elif '/tools/' in path_str:
            categories['tools'].append(file_path)
        else:
            categories['other'].append(file_path)
    
    return categories

def main():
    """Main cleanup function."""
    print("🧹 Targeted Entry Point Cleanup")
    print("=" * 40)
    
    # Find all files with main blocks
    files_with_main = find_python_files_with_main_blocks()
    
    if not files_with_main:
        print("✅ No files with main blocks found!")
        return
    
    print(f"Found {len(files_with_main)} files with main blocks")
    
    # Filter out allowed files
    forbidden_files = [f for f in files_with_main if not is_allowed_entry_point(f)]
    allowed_files = [f for f in files_with_main if is_allowed_entry_point(f)]
    
    print(f"Allowed entry points: {len(allowed_files)}")
    for f in allowed_files:
        print(f"  ✅ {f}")
    
    print(f"Forbidden entry points: {len(forbidden_files)}")
    
    if not forbidden_files:
        print("✅ No forbidden entry points found!")
        return
    
    # Categorize forbidden files
    categories = categorize_files(forbidden_files)
    
    print("\nCategories:")
    for category, files in categories.items():
        if files:
            print(f"  {category}: {len(files)} files")
    
    # Process files by priority (core first)
    removed_count = 0
    
    for category in ['core', 'meta_systems', 'tools', 'other', 'examples', 'tests']:
        files = categories[category]
        if files:
            print(f"\n📁 Processing {category} files ({len(files)})...")
            for file_path in files:
                if remove_main_block(file_path):
                    print(f"  ✅ Cleaned: {file_path}")
                    removed_count += 1
                else:
                    print(f"  ⚠️ No change: {file_path}")
    
    print(f"\n✨ Cleanup complete! Removed entry points from {removed_count} files.")
    
    # Verify cleanup
    remaining_files = find_python_files_with_main_blocks()
    remaining_forbidden = [f for f in remaining_files if not is_allowed_entry_point(f)]
    
    if remaining_forbidden:
        print(f"\n⚠️ {len(remaining_forbidden)} entry points still remain:")
        for f in remaining_forbidden[:5]:
            print(f"  - {f}")
        if len(remaining_forbidden) > 5:
            print(f"  ... and {len(remaining_forbidden) - 5} more")
    else:
        print("\n🎉 All unauthorized entry points removed!")
        print("Only person_suit/main.py and person_suit/__main__.py have entry points.")

if __name__ == "__main__":
    main()
