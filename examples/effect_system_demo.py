"""
Person Suit - Effect System Demo

A simple demonstration of the effect system.
"""

import logging
from typing import Any
from typing import Dict

from person_suit.core.effects.effect import Effect
from person_suit.core.effects.handler_registry import EffectHandlerRegistry
from person_suit.core.effects.handlers.logging_handler import LoggingEffectType
from person_suit.core.effects.handlers.logging_handler import <PERSON><PERSON><PERSON><PERSON><PERSON>


def main():
    """Run the effect system demo."""
    # Configure logging
    logging.basicConfig(
        level=logging.INFO,
        format="%(asctime)s - %(name)s - %(levelname)s - %(message)s",
    )

    # Create a registry and register handlers
    registry = EffectHandlerRegistry()
    logging_handler = LoggingHandler()
    registry.register_handler(logging_handler)

    # Create and handle a logging effect
    logging_type = LoggingEffectType()

    # Create INFO effect
    info_effect = Effect(
        logging_type, LoggingEffectType.INFO, {"message": "This is an info message"}
    )

    # Create WARNING effect
    warning_effect = Effect(
        logging_type,
        LoggingEffectType.WARNING,
        {"message": "This is a warning message"},
    )

    # Create ERROR effect
    error_effect = Effect(
        logging_type, LoggingEffectType.ERROR, {"message": "This is an error message"}
    )

    # Handle effects
    registry.handle_effect(info_effect)
    registry.handle_effect(warning_effect)
    registry.handle_effect(error_effect)

    # Example with context
    context: Dict[str, Any] = {"source": "effect_system_demo"}

    debug_effect = Effect(
        logging_type,
        LoggingEffectType.DEBUG,
        {"message": "This is a debug message with context"},
    )

    registry.handle_effect(debug_effect, context)

    print("Effect system demo completed.")


if __name__ == "__main__":
    main()
