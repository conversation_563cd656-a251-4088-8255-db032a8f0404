"""
Path Integration and Reconciliation System Demonstration

This script demonstrates the functionality of the Path Integration and Reconciliation
system, which harmonizes outputs from the Computational-Algorithmic Mind (CAM)
and Subjective-Experiential Mind (SEM) pathways. It shows how different integration
strategies are applied depending on context, and how conflicts are detected and
resolved during integration.

Features demonstrated:
- Basic integration of pathway outputs
- Context-sensitive integration with different modes
- Conflict detection and resolution
- Pathway weighting based on context and confidence
- Feedback collection and learning

Usage:
$ python -m examples.path_integration_demo
"""

import asyncio
import logging
import time
import uuid
from datetime import datetime
from typing import Any
from typing import Dict

# Set up logging
logging.basicConfig(
    level=logging.INFO,
    format="%(asctime)s - %(name)s - %(levelname)s - %(message)s",
)
logger = logging.getLogger("path_integration_demo")

from person_suit.meta_systems.persona_core.folded_mind.integration.conflict_detector import (
    ConflictDetector,
)
from person_suit.meta_systems.persona_core.folded_mind.integration.feedback import FeedbackDimension
from person_suit.meta_systems.persona_core.folded_mind.integration.feedback import FeedbackSource
from person_suit.meta_systems.persona_core.folded_mind.integration.feedback import (
    IntegrationFeedback,
)
from person_suit.meta_systems.persona_core.folded_mind.integration.feedback import (
    IntegrationFeedbackSystem,
)

# Import integration system components
from person_suit.meta_systems.persona_core.folded_mind.integration.integrator import (
    IntegrationConfig,
)
from person_suit.meta_systems.persona_core.folded_mind.integration.integrator import (
    IntegrationContext,
)
from person_suit.meta_systems.persona_core.folded_mind.integration.integrator import IntegrationMode
from person_suit.meta_systems.persona_core.folded_mind.integration.integrator import (
    IntegrationResult,
)
from person_suit.meta_systems.persona_core.folded_mind.integration.integrator import IntegrityLevel
from person_suit.meta_systems.persona_core.folded_mind.integration.integrator import (
    PathIntegrationSystem,
)
from person_suit.meta_systems.persona_core.folded_mind.integration.integrator import PathwayOutput
from person_suit.meta_systems.persona_core.folded_mind.integration.integrator import PathwayType
from person_suit.meta_systems.persona_core.folded_mind.integration.resolution import (
    ConflictResolver,
)
from person_suit.meta_systems.persona_core.folded_mind.integration.weighting import PathwayWeighter


async def create_sample_pathway_outputs(
    scenario: str,
) -> Dict[str, Dict[PathwayType, PathwayOutput]]:
    """
    Create sample pathway outputs for demonstration scenarios.

    Args:
        scenario: Name of the demonstration scenario

    Returns:
        Dictionary mapping scenario names to pathway outputs
    """
    outputs = {}

    # Scenario 1: Harmonious outputs with high confidence
    outputs["harmonious"] = {
        PathwayType.COMPUTATIONAL: PathwayOutput(
            content="The weather tomorrow will be sunny with a high of 75°F.",
            confidence=0.9,
            reasoning="Based on analysis of multiple weather forecasts and historical patterns.",
            source=PathwayType.COMPUTATIONAL,
            metadata={
                "data_sources": ["national_weather_service", "accuweather"],
                "timestamp": datetime.now().isoformat(),
                "analysis_method": "ensemble_prediction",
            },
        ),
        PathwayType.SUBJECTIVE: PathwayOutput(
            content="It feels like tomorrow will be a beautiful sunny day, perfect for outdoor activities.",
            confidence=0.8,
            reasoning="Intuitive sense based on current conditions and personal experience.",
            source=PathwayType.SUBJECTIVE,
            metadata={
                "emotional_tone": "positive",
                "personal_relevance": "high",
                "timestamp": datetime.now().isoformat(),
            },
        ),
    }

    # Scenario 2: Conflicting outputs with similar confidence
    outputs["conflicting"] = {
        PathwayType.COMPUTATIONAL: PathwayOutput(
            content="The optimal solution is to implement option A, which will be 15% more efficient.",
            confidence=0.75,
            reasoning="Quantitative analysis shows option A provides higher efficiency metrics.",
            source=PathwayType.COMPUTATIONAL,
            metadata={
                "analysis_type": "optimization",
                "metrics": ["efficiency", "cost", "time"],
                "timestamp": datetime.now().isoformat(),
            },
        ),
        PathwayType.SUBJECTIVE: PathwayOutput(
            content="Option B feels like the better choice, even though the metrics favor A slightly.",
            confidence=0.7,
            reasoning="Intuitive concern about hidden factors not captured in the metrics.",
            source=PathwayType.SUBJECTIVE,
            metadata={
                "emotional_tone": "cautious",
                "intuition_strength": "medium",
                "timestamp": datetime.now().isoformat(),
            },
        ),
    }

    # Scenario 3: Very different perspectives with varying confidence
    outputs["divergent"] = {
        PathwayType.COMPUTATIONAL: PathwayOutput(
            content="The data indicates a 78% probability of success for this project approach.",
            confidence=0.85,
            reasoning="Statistical analysis of similar projects and risk assessment models.",
            source=PathwayType.COMPUTATIONAL,
            metadata={
                "data_points": 142,
                "method": "bayesian_analysis",
                "timestamp": datetime.now().isoformat(),
            },
        ),
        PathwayType.SUBJECTIVE: PathwayOutput(
            content="This project approach feels problematic despite the promising statistics.",
            confidence=0.6,
            reasoning="Something feels off about the assumptions being made.",
            source=PathwayType.SUBJECTIVE,
            metadata={
                "emotional_tone": "concerned",
                "intuition_strength": "strong",
                "timestamp": datetime.now().isoformat(),
            },
        ),
    }

    # Scenario 4: Creative task with subjective pathway dominance
    outputs["creative"] = {
        PathwayType.COMPUTATIONAL: PathwayOutput(
            content="The design should incorporate elements A, B, and C based on best practices.",
            confidence=0.6,
            reasoning="Analysis of successful designs shows these elements perform well.",
            source=PathwayType.COMPUTATIONAL,
            metadata={
                "reference_count": 25,
                "pattern_match": "medium",
                "timestamp": datetime.now().isoformat(),
            },
        ),
        PathwayType.SUBJECTIVE: PathwayOutput(
            content="A bold, unconventional approach using elements D and E would be more innovative.",
            confidence=0.85,
            reasoning="Creative intuition indicates a novel approach would be more impactful.",
            source=PathwayType.SUBJECTIVE,
            metadata={
                "emotional_tone": "excited",
                "originality": "high",
                "timestamp": datetime.now().isoformat(),
            },
        ),
    }

    # Scenario 5: Analytical task with computational pathway dominance
    outputs["analytical"] = {
        PathwayType.COMPUTATIONAL: PathwayOutput(
            content="The solution to the equation is x = 3.14159, derived through numerical methods.",
            confidence=0.95,
            reasoning="Rigorous mathematical derivation with error checking.",
            source=PathwayType.COMPUTATIONAL,
            metadata={
                "precision": "high",
                "method": "iterative_approximation",
                "error_margin": 0.00001,
                "timestamp": datetime.now().isoformat(),
            },
        ),
        PathwayType.SUBJECTIVE: PathwayOutput(
            content="The solution seems approximately 3.14, which feels about right.",
            confidence=0.5,
            reasoning="Rough mental estimation based on previous experience.",
            source=PathwayType.SUBJECTIVE,
            metadata={
                "emotional_tone": "neutral",
                "precision": "low",
                "timestamp": datetime.now().isoformat(),
            },
        ),
    }

    return outputs.get(scenario, outputs["harmonious"])


async def create_sample_contexts() -> Dict[str, IntegrationContext]:
    """
    Create sample integration contexts for demonstration.

    Returns:
        Dictionary mapping context names to IntegrationContext objects
    """
    contexts = {}

    # General context
    contexts["general"] = IntegrationContext(task_type="general", domain="general")

    # Analytical context
    contexts["analytical"] = IntegrationContext(
        task_type="analysis",
        domain="science",
        precision_required=0.9,
        creativity_required=0.2,
        time_pressure=0.5,
        complexity=0.7,
    )

    # Creative context
    contexts["creative"] = IntegrationContext(
        task_type="creativity",
        domain="art",
        precision_required=0.3,
        creativity_required=0.9,
        time_pressure=0.2,
        complexity=0.6,
    )

    # Decision context
    contexts["decision"] = IntegrationContext(
        task_type="decision_making",
        domain="business",
        precision_required=0.7,
        creativity_required=0.5,
        time_pressure=0.8,
        complexity=0.8,
    )

    # Social context
    contexts["social"] = IntegrationContext(
        task_type="communication",
        domain="social",
        precision_required=0.4,
        creativity_required=0.7,
        time_pressure=0.3,
        complexity=0.5,
        emotional_context=True,
    )

    return contexts


async def initialize_integration_components() -> Dict[str, Any]:
    """
    Initialize all integration system components.

    Returns:
        Dictionary with initialized components
    """
    # Initialize weighting system
    weighter = PathwayWeighter()
    await weighter.initialize()

    # Initialize conflict detector
    detector = ConflictDetector()
    await detector.initialize()

    # Initialize conflict resolver
    resolver = ConflictResolver()
    await resolver.initialize()

    # Initialize feedback system
    feedback_system = IntegrationFeedbackSystem(pathway_weighter=weighter)
    await feedback_system.initialize()

    # Initialize integration system with all components
    integration_system = PathIntegrationSystem(
        conflict_detector=detector,
        conflict_resolver=resolver,
        pathway_weighter=weighter,
        feedback_system=feedback_system,
    )
    await integration_system.initialize()

    return {
        "integration_system": integration_system,
        "conflict_detector": detector,
        "conflict_resolver": resolver,
        "pathway_weighter": weighter,
        "feedback_system": feedback_system,
    }


async def demonstrate_integration(
    components: Dict[str, Any],
    scenario: str,
    context_type: str,
    integration_mode: IntegrationMode = IntegrationMode.ADAPTIVE,
) -> IntegrationResult:
    """
    Demonstrate integration for a specific scenario and context.

    Args:
        components: Dictionary with integration system components
        scenario: Name of the scenario to demonstrate
        context_type: Type of context to use
        integration_mode: Integration mode to use

    Returns:
        Result of integration
    """
    integration_system = components["integration_system"]

    # Get sample pathway outputs for scenario
    pathway_outputs = await create_sample_pathway_outputs(scenario)

    # Get context
    contexts = await create_sample_contexts()
    context = contexts.get(context_type, contexts["general"])

    # Set up integration configuration
    config = IntegrationConfig(
        integration_mode=integration_mode,
        integrity_level=IntegrityLevel.BALANCED,
    )

    # Perform integration
    start_time = time.time()
    integration_id = str(uuid.uuid4())

    logger.info(
        f"Starting integration {integration_id} for scenario '{scenario}' with context '{context_type}'"
    )

    # Log pathway outputs
    for pathway_type, output in pathway_outputs.items():
        logger.info(f"  {pathway_type.name} pathway output: {output.content[:80]}...")
        logger.info(f"  {pathway_type.name} confidence: {output.confidence:.2f}")

    # Integrate pathways
    result = await integration_system.integrate(
        computational_output=pathway_outputs[PathwayType.COMPUTATIONAL],
        subjective_output=pathway_outputs[PathwayType.SUBJECTIVE],
        context=context,
        config=config,
        integration_id=integration_id,
    )

    elapsed_ms = (time.time() - start_time) * 1000

    # Log result
    logger.info(f"Integration complete in {elapsed_ms:.2f}ms")
    logger.info(f"Integrated output: {result.integrated_output[:80]}...")
    logger.info(f"Integration mode used: {result.integration_mode.name}")

    if result.conflicts_detected:
        logger.info(f"Detected {len(result.conflicts_detected)} conflicts:")
        for conflict in result.conflicts_detected:
            logger.info(
                f"  {conflict.conflict_type.name} conflict with {conflict.severity.name} severity"
            )
            logger.info(
                f"  Resolution strategy: {conflict.resolution_strategy.name if conflict.resolution_strategy else 'None'}"
            )
    else:
        logger.info("No conflicts detected")

    logger.info(
        f"Weights applied: computational={result.weights_applied.get(PathwayType.COMPUTATIONAL, 0):.2f}, "
        + f"subjective={result.weights_applied.get(PathwayType.SUBJECTIVE, 0):.2f}"
    )

    return result


async def provide_feedback(
    components: Dict[str, Any],
    result: IntegrationResult,
    quality_score: float,
    source: FeedbackSource = FeedbackSource.SYSTEM,
) -> None:
    """
    Provide feedback on an integration result.

    Args:
        components: Dictionary with integration system components
        result: Integration result to provide feedback for
        quality_score: Overall quality score (0.0-1.0)
        source: Source of the feedback
    """
    feedback_system = components["feedback_system"]

    # Create feedback object
    feedback = IntegrationFeedback(
        integration_id=result.integration_id,
        source=source,
        scores={
            FeedbackDimension.OVERALL: quality_score,
            FeedbackDimension.COHERENCE: quality_score
            * 0.9,  # Simplified approximation
            FeedbackDimension.RELEVANCE: quality_score * 1.1
            if quality_score < 0.9
            else 1.0,
            FeedbackDimension.CONFLICT_HANDLING: 0.5
            if result.conflicts_detected
            else quality_score * 1.1,
        },
        context=result.context,
        integration_mode=result.integration_mode,
        conflict_strategies_used=[
            conflict.resolution_strategy
            for conflict in result.conflicts_detected
            if conflict.resolution_strategy is not None
        ],
        weighting_strategy=result.weighting_strategy,
        pathway_weights=result.weights_applied,
        explanation=f"Automated quality assessment with score {quality_score:.2f}",
    )

    # Record feedback
    await feedback_system.record_feedback(result, feedback)

    logger.info(
        f"Recorded feedback with overall score {quality_score:.2f} from {source.name}"
    )


async def demo_path_integration() -> None:
    """Run a comprehensive demonstration of the path integration system."""
    logger.info("Initializing Path Integration and Reconciliation System components...")
    components = await initialize_integration_components()

    # Demonstrate different scenarios
    scenarios = [
        ("harmonious", "general", IntegrationMode.BALANCED, 0.9),
        ("conflicting", "decision", IntegrationMode.ADAPTIVE, 0.7),
        ("divergent", "analytical", IntegrationMode.COMPUTATIONAL, 0.8),
        ("creative", "creative", IntegrationMode.SUBJECTIVE, 0.85),
        ("analytical", "analytical", IntegrationMode.COMPUTATIONAL, 0.95),
    ]

    integration_results = []

    for scenario, context_type, mode, quality in scenarios:
        logger.info("\n" + "=" * 80)
        logger.info(
            f"SCENARIO: {scenario.upper()} in {context_type.upper()} context with {mode.name} mode"
        )
        logger.info("=" * 80)

        result = await demonstrate_integration(components, scenario, context_type, mode)
        integration_results.append(result)

        # Provide feedback
        await provide_feedback(components, result, quality)

        # Short pause between scenarios
        await asyncio.sleep(0.5)

    # Demonstrate learning effect
    logger.info("\n" + "=" * 80)
    logger.info("DEMONSTRATING LEARNING EFFECT")
    logger.info("=" * 80)

    # Run the conflicting scenario again to see if system learned
    result = await demonstrate_integration(
        components, "conflicting", "decision", IntegrationMode.ADAPTIVE
    )

    # Get feedback stats
    feedback_system = components["feedback_system"]
    stats = await feedback_system.get_statistics()

    logger.info("\n" + "=" * 80)
    logger.info("FEEDBACK SYSTEM STATISTICS")
    logger.info("=" * 80)
    logger.info(f"Total feedback count: {stats['total_feedback_count']}")
    logger.info(f"Average overall score: {stats['average_overall_score']:.2f}")

    if "mode_performance" in stats:
        logger.info("\nIntegration Mode Performance:")
        for mode, perf in stats["mode_performance"].items():
            logger.info(
                f"  {mode}: average score = {perf['average_score']:.2f} ({perf['count']} occurrences)"
            )

    if "weighting_strategy_performance" in stats:
        logger.info("\nWeighting Strategy Performance:")
        for strategy, perf in stats["weighting_strategy_performance"].items():
            if perf["count"] > 0:
                logger.info(
                    f"  {strategy}: average score = {perf['average_score']:.2f} ({perf['count']} occurrences)"
                )

    logger.info("\nPath Integration System demonstration complete!")


if __name__ == "__main__":
    asyncio.run(demo_path_integration())
