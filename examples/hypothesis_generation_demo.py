#!/usr/bin/env python3
"""
Hypothesis Generation System Demo
================================

This script demonstrates the key features of the Hypothesis Generation System (PR-3).
It shows how to:

1. Initialize the system
2. Create hypotheses from different sources
3. Process hypotheses through their lifecycle
4. Query and filter hypotheses
5. Add evidence and test results
6. Get statistics on the system's operation

The demo simulates a pattern observation scenario where the system generates
and processes hypotheses about user behavior patterns.
"""

import asyncio
import logging
import uuid
from datetime import datetime

from person_suit.meta_systems.prediction.hypothesis_generation import EvidenceItem
from person_suit.meta_systems.prediction.hypothesis_generation import Hypothesis
from person_suit.meta_systems.prediction.hypothesis_generation import HypothesisPrediction
from person_suit.meta_systems.prediction.hypothesis_generation import HypothesisSource
from person_suit.meta_systems.prediction.hypothesis_generation import HypothesisStatus
from person_suit.meta_systems.prediction.hypothesis_generation import HypothesisType
from person_suit.meta_systems.prediction.hypothesis_generation import TestResult
from person_suit.meta_systems.prediction.hypothesis_generation import (
    get_hypothesis_generation_system,
)

# Configure logging
logging.basicConfig(
    level=logging.INFO, format="%(asctime)s - %(name)s - %(levelname)s - %(message)s"
)
logger = logging.getLogger("hypothesis_demo")


class PatternObservation:
    """Simulates a pattern observation from the Pattern Detection system."""

    def __init__(
        self,
        pattern_id: str,
        domain: str,
        description: str,
        confidence: float = 0.8,
        predictive_value: float = 0.6,
        is_causal: bool = False,
    ):
        self.pattern_id = pattern_id
        self.domain = domain
        self.description = description
        self.confidence = confidence
        self.predictive_value = predictive_value
        self.pattern_type = "temporal"
        self.is_causal = is_causal
        self.elements = ["element1", "element2"]
        self.metadata = {"source": "demo_script"}


class AnalogicalInsightObservation:
    """Simulates an analogical insight from the SEM system."""

    def __init__(
        self,
        insight_id: str,
        insight_type: str,
        description: str,
        confidence: float = 0.8,
        relevance: float = 0.7,
    ):
        self.insight_id = insight_id
        self.insight_type = insight_type
        self.description = description
        self.confidence = confidence
        self.relevance = relevance
        self.structure = {
            "source_domain": "source_domain",
            "target_domain": "target_domain",
            "correspondences": [
                {"source_id": "source1", "target_id": "target1", "confidence": 0.9}
            ],
            "systematicity": 0.85,
        }
        self.metadata = {"source_system": "sem_analogical_reasoning"}
        self.creation_timestamp = datetime.now()


class LogicalInsightObservation:
    """Simulates a logical insight from the CAM system."""

    def __init__(
        self,
        insight_id: str,
        insight_type: str,
        description: str,
        domain: str,
        confidence: float = 0.85,
        relevance: float = 0.8,
    ):
        self.insight_id = insight_id
        self.insight_type = insight_type
        self.description = description
        self.confidence = confidence
        self.relevance = relevance
        self.structure = {
            "domain": domain,
            "premises": [
                {"statement": "Premise 1", "confidence": 0.95},
                {"statement": "Premise 2", "confidence": 0.9},
            ],
            "conclusions": [{"statement": "Conclusion", "confidence": 0.85}],
        }
        self.metadata = {"source_system": "cam_logical_reasoning"}
        self.creation_timestamp = datetime.now()


async def create_sample_data():
    """Create sample data for the demo."""

    # Sample patterns
    patterns = [
        PatternObservation(
            pattern_id=f"pattern_{uuid.uuid4().hex[:8]}",
            domain="user_behavior",
            description="Users tend to log in more frequently on weekdays",
            confidence=0.85,
            predictive_value=0.75,
            is_causal=False,
        ),
        PatternObservation(
            pattern_id=f"pattern_{uuid.uuid4().hex[:8]}",
            domain="content_engagement",
            description="Users engage more with video content in the evenings",
            confidence=0.8,
            predictive_value=0.7,
            is_causal=True,
        ),
        PatternObservation(
            pattern_id=f"pattern_{uuid.uuid4().hex[:8]}",
            domain="user_behavior",
            description="New users explore more features in their first week",
            confidence=0.9,
            predictive_value=0.8,
            is_causal=True,
        ),
    ]

    # Sample analogical insights
    analogical_insights = [
        AnalogicalInsightObservation(
            insight_id=f"analogical_{uuid.uuid4().hex[:8]}",
            insight_type="structural_pattern",
            description="Structural pattern connecting user behavior and content engagement",
            confidence=0.8,
            relevance=0.75,
        ),
        AnalogicalInsightObservation(
            insight_id=f"analogical_{uuid.uuid4().hex[:8]}",
            insight_type="knowledge_transfer",
            description="Knowledge transfer from user navigation to search patterns",
            confidence=0.75,
            relevance=0.8,
        ),
    ]

    # Sample logical insights
    logical_insights = [
        LogicalInsightObservation(
            insight_id=f"logical_{uuid.uuid4().hex[:8]}",
            insight_type="deductive_pattern",
            description="Deductive reasoning about user authentication patterns",
            domain="user_behavior",
            confidence=0.9,
            relevance=0.85,
        ),
        LogicalInsightObservation(
            insight_id=f"logical_{uuid.uuid4().hex[:8]}",
            insight_type="causal_pattern",
            description="Causal reasoning about content recommendation effectiveness",
            domain="content_engagement",
            confidence=0.85,
            relevance=0.8,
        ),
    ]

    return {
        "patterns": patterns,
        "analogical_insights": analogical_insights,
        "logical_insights": logical_insights,
    }


async def simulate_hypothesis_generation(system, sample_data):
    """Simulate hypothesis generation from different sources."""
    logger.info("Simulating hypothesis generation...")

    # Get the sample data
    patterns = sample_data["patterns"]
    analogical_insights = sample_data["analogical_insights"]
    logical_insights = sample_data["logical_insights"]

    # Simulate pattern-based hypothesis generation
    for pattern in patterns:
        # In a real system, this would happen through the pattern_observer
        # Here we're directly creating hypotheses for the demo
        hypothesis = Hypothesis(
            statement=f"Hypothesis based on pattern: {pattern.description}",
            description=f"Generated from pattern {pattern.pattern_id}",
            domain=pattern.domain,
            type=HypothesisType.DESCRIPTIVE
            if not pattern.is_causal
            else HypothesisType.EXPLANATORY,
            source=HypothesisSource.PATTERN_DETECTION,
            confidence=pattern.confidence
            * 0.9,  # Slightly lower confidence than the pattern
            relevance=0.8,
            novelty=0.7,
            testability=pattern.predictive_value,
            source_patterns=[pattern.pattern_id],
            metadata={"pattern_type": pattern.pattern_type},
        )

        # Add a prediction if the pattern has good predictive value
        if pattern.predictive_value > 0.7:
            prediction = HypothesisPrediction(
                statement=f"Predicts continued {pattern.description.lower()}",
                timeframe="short_term",
                confidence=pattern.predictive_value,
                testability=0.8,
            )
            hypothesis.add_prediction(prediction)

        # Add the hypothesis to the system
        # In a real system, this would be done by the pattern_observer
        await system._storage.add_hypothesis(hypothesis)
        logger.info(f"Generated pattern-based hypothesis: {hypothesis.hypothesis_id}")

    # Simulate analogical insight-based hypothesis generation
    for insight in analogical_insights:
        hypothesis = Hypothesis(
            statement=f"Hypothesis based on analogical insight: {insight.description}",
            description=f"Generated from analogical insight {insight.insight_id}",
            domain=insight.structure["target_domain"],
            type=HypothesisType.DESCRIPTIVE,
            source=HypothesisSource.ANALOGICAL_REASONING,
            confidence=insight.confidence * 0.9,
            relevance=insight.relevance,
            novelty=0.85,
            testability=0.6,
            analogical_insights=[insight.insight_id],
            metadata={"insight_type": insight.insight_type},
        )

        await system._storage.add_hypothesis(hypothesis)
        logger.info(f"Generated analogical hypothesis: {hypothesis.hypothesis_id}")

    # Simulate logical insight-based hypothesis generation
    for insight in logical_insights:
        hypothesis = Hypothesis(
            statement=f"Hypothesis based on logical insight: {insight.description}",
            description=f"Generated from logical insight {insight.insight_id}",
            domain=insight.structure["domain"],
            type=HypothesisType.EXPLANATORY,
            source=HypothesisSource.LOGICAL_REASONING,
            confidence=insight.confidence * 0.9,
            relevance=insight.relevance,
            novelty=0.7,
            testability=0.75,
            logical_insights=[insight.insight_id],
            metadata={"insight_type": insight.insight_type},
        )

        await system._storage.add_hypothesis(hypothesis)
        logger.info(f"Generated logical hypothesis: {hypothesis.hypothesis_id}")


async def simulate_lifecycle_processing(system):
    """Simulate processing hypotheses through their lifecycle."""
    logger.info("Simulating hypothesis lifecycle processing...")

    # Get all hypotheses in GENERATED status
    generated_hypotheses = await system._storage.filter_by_status(
        {HypothesisStatus.GENERATED}
    )

    # Process each hypothesis through evaluation
    for hypothesis in generated_hypotheses:
        logger.info(f"Processing hypothesis: {hypothesis.hypothesis_id}")

        # Simulate evaluation
        hypothesis.update_status(HypothesisStatus.ENHANCED)
        hypothesis.confidence += 0.05  # Simulate confidence increase from enhancement
        hypothesis.log_enhancement(
            enhancer_type="analogical",
            success=True,
            details={"enhancement_factor": 1.1},
        )

        # Simulate validation
        hypothesis.update_status(HypothesisStatus.VALIDATED)
        hypothesis.relevance += 0.1  # Simulate relevance increase from validation
        hypothesis.log_enhancement(
            enhancer_type="logical", success=True, details={"logical_consistency": 0.95}
        )

        # Update the hypothesis in storage
        await system._storage.update_hypothesis(hypothesis)
        logger.info(
            f"Updated hypothesis status to VALIDATED: {hypothesis.hypothesis_id}"
        )

    # Select some hypotheses for testing
    validated_hypotheses = await system._storage.filter_by_status(
        {HypothesisStatus.VALIDATED}
    )

    # Select the first two hypotheses for testing if there are enough
    testing_candidates = validated_hypotheses[: min(2, len(validated_hypotheses))]

    for hypothesis in testing_candidates:
        # Simulate testing selection
        hypothesis.update_status(HypothesisStatus.TESTING)
        await system._storage.update_hypothesis(hypothesis)
        logger.info(f"Selected hypothesis for testing: {hypothesis.hypothesis_id}")

        # Simulate test results
        test_result = TestResult(
            test_id=f"test_{uuid.uuid4().hex[:8]}",
            hypothesis_id=hypothesis.hypothesis_id,
            test_type="validation",
            outcome="confirmed" if hypothesis.confidence > 0.8 else "refuted",
            confidence=0.85,
            details={"validation_criteria": "temporal_consistency"},
            metrics={"precision": 0.88, "recall": 0.92},
        )

        # Add the test result
        await system.add_test_result(hypothesis.hypothesis_id, test_result)
        logger.info(f"Added test result for hypothesis: {hypothesis.hypothesis_id}")


async def simulate_evidence_addition(system):
    """Simulate adding evidence to hypotheses."""
    logger.info("Simulating evidence addition...")

    # Get active/confirmed hypotheses
    active_hypotheses = await system._storage.filter_by_status(
        {HypothesisStatus.CONFIRMED, HypothesisStatus.VALIDATED}
    )

    for hypothesis in active_hypotheses:
        # Add supporting evidence
        support_evidence = EvidenceItem(
            evidence_id=f"evidence_{uuid.uuid4().hex[:8]}",
            description=f"Supporting evidence for {hypothesis.statement}",
            source_type="observation",
            confidence=0.85,
            is_supporting=True,
        )

        await system.add_evidence(hypothesis.hypothesis_id, support_evidence)
        logger.info(
            f"Added supporting evidence to hypothesis: {hypothesis.hypothesis_id}"
        )

        # Add counter evidence for some hypotheses (with lower confidence)
        if hypothesis.confidence < 0.85:
            counter_evidence = EvidenceItem(
                evidence_id=f"evidence_{uuid.uuid4().hex[:8]}",
                description=f"Counter evidence for {hypothesis.statement}",
                source_type="observation",
                confidence=0.6,
                is_supporting=False,
            )

            await system.add_evidence(hypothesis.hypothesis_id, counter_evidence)
            logger.info(
                f"Added counter evidence to hypothesis: {hypothesis.hypothesis_id}"
            )


async def demonstrate_querying(system):
    """Demonstrate querying and filtering hypotheses."""
    logger.info("Demonstrating hypothesis querying...")

    # Query by domain
    user_behavior_hypotheses = await system.get_active_hypotheses(
        domain="user_behavior", limit=10
    )
    logger.info(
        f"Found {len(user_behavior_hypotheses)} hypotheses in user_behavior domain"
    )

    # Query by type and confidence
    predictive_hypotheses = await system.get_active_hypotheses(
        hypothesis_type=HypothesisType.PREDICTIVE, min_confidence=0.7, limit=5
    )
    logger.info(
        f"Found {len(predictive_hypotheses)} predictive hypotheses with confidence ≥ 0.7"
    )

    # Get all hypotheses and print their details
    all_active = await system.get_active_hypotheses(limit=100)

    logger.info("\n--- Active Hypotheses ---")
    for h in all_active:
        logger.info(f"ID: {h.hypothesis_id}")
        logger.info(f"Statement: {h.statement}")
        logger.info(f"Domain: {h.domain}, Type: {h.type.value}")
        logger.info(f"Confidence: {h.confidence:.2f}, Relevance: {h.relevance:.2f}")
        logger.info(f"Status: {h.status.value}")
        logger.info(
            f"Evidence: {len(h.supporting_evidence)} supporting, {len(h.counter_evidence)} counter"
        )
        logger.info(f"Predictions: {len(h.predictions)}")
        logger.info("---")


async def print_statistics(system):
    """Print statistics about the hypothesis generation system."""
    stats = await system.get_statistics()

    logger.info("\n=== Hypothesis Generation System Statistics ===")
    logger.info(f"Total hypotheses: {stats['total_hypotheses']}")
    logger.info(f"Active hypotheses: {stats['active_hypotheses']}")
    logger.info(f"Archived hypotheses: {stats['archived_hypotheses']}")

    status_counts = stats.get("status_counts", {})
    logger.info("\nStatus distribution:")
    for status, count in status_counts.items():
        logger.info(f"  {status}: {count}")

    domain_counts = stats.get("domain_counts", {})
    logger.info("\nDomain distribution:")
    for domain, count in domain_counts.items():
        logger.info(f"  {domain}: {count}")

    type_counts = stats.get("type_counts", {})
    logger.info("\nType distribution:")
    for type_name, count in type_counts.items():
        logger.info(f"  {type_name}: {count}")

    source_counts = stats.get("source_counts", {})
    logger.info("\nSource distribution:")
    for source, count in source_counts.items():
        logger.info(f"  {source}: {count}")

    logger.info("\nConfidence metrics:")
    logger.info(f"  Average confidence: {stats.get('avg_confidence', 0):.2f}")
    logger.info(f"  Average relevance: {stats.get('avg_relevance', 0):.2f}")
    logger.info(f"  Average novelty: {stats.get('avg_novelty', 0):.2f}")
    logger.info(f"  Average testability: {stats.get('avg_testability', 0):.2f}")

    logger.info("=== End of Statistics ===\n")


async def main():
    """Run the hypothesis generation system demo."""
    logger.info("Starting Hypothesis Generation System Demo")

    # Initialize the hypothesis generation system
    system = await get_hypothesis_generation_system()
    await system.start()

    try:
        # Create sample data
        sample_data = await create_sample_data()

        # Simulate hypothesis generation
        await simulate_hypothesis_generation(system, sample_data)

        # Simulate lifecycle processing
        await simulate_lifecycle_processing(system)

        # Simulate evidence addition
        await simulate_evidence_addition(system)

        # Demonstrate querying
        await demonstrate_querying(system)

        # Print statistics
        await print_statistics(system)

    finally:
        # Stop the system at the end
        await system.stop()
        logger.info("Hypothesis Generation System Demo completed")


if __name__ == "__main__":
    asyncio.run(main())
