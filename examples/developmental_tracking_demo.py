#!/usr/bin/env python3
"""
Developmental Tracking Demo
------------------------
Purpose: Demonstrate the Developmental Tracking System (EV-3)

This script demonstrates how to use the Developmental Tracking System (EV-3)
for tracking and analyzing persona developmental progress over time.

Usage:
    python3 examples/developmental_tracking_demo.py
"""

import asyncio
import json
import logging
import random
import sys
from pathlib import Path

from person_suit.meta_systems.persona_core.evaluation.developmental import AnomalyDetector
from person_suit.meta_systems.persona_core.evaluation.developmental import Baseline
from person_suit.meta_systems.persona_core.evaluation.developmental import BaselineComparison
from person_suit.meta_systems.persona_core.evaluation.developmental import DevelopmentalDimension
from person_suit.meta_systems.persona_core.evaluation.developmental import DevelopmentalMetric
from person_suit.meta_systems.persona_core.evaluation.developmental import (
    DevelopmentalTrackingSystem,
)
from person_suit.meta_systems.persona_core.evaluation.developmental.storage import (
    DevelopmentalStore,
)

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format="%(asctime)s - %(name)s - %(levelname)s - %(message)s",
    handlers=[logging.StreamHandler(sys.stdout)],
)
logger = logging.getLogger("developmental_demo")


class SimpleDemoMetric(DevelopmentalMetric):
    """
    Simple demonstration metric that returns a deterministic value.

    This metric's value starts at 0.2 and increases by 0.01 each time
    it's computed, with some random noise added.
    """

    def __init__(self, name: str, dimension: DevelopmentalDimension):
        """
        Initialize the demo metric.

        Args:
            name: Name for the metric
            dimension: Developmental dimension
        """
        super().__init__(
            name=name,
            description=f"Demo metric for {dimension.value}",
            dimension=dimension,
            tags=["demo"],
        )
        self.current_value = 0.2
        self.calls = 0

    async def compute(self, context: dict) -> float:
        """
        Compute the metric value.

        Args:
            context: Evaluation context (not used)

        Returns:
            Current metric value
        """
        # Increment call count
        self.calls += 1

        # Calculate value with some growth and noise
        self.current_value += 0.01
        noise = random.uniform(-0.005, 0.005)
        value = min(1.0, max(0.0, self.current_value + noise))

        return value

    async def get_expected_growth_rate(self) -> float:
        """
        Get the expected growth rate for this metric.

        Returns:
            Expected daily growth rate
        """
        return 0.01  # 1% per day


async def setup_developmental_tracking():
    """
    Set up the developmental tracking system.

    Returns:
        Initialized developmental tracking system
    """
    # Initialize storage
    storage_path = Path("data/developmental")
    storage_path.mkdir(parents=True, exist_ok=True)

    store = DevelopmentalStore()
    await store.initialize()

    # Create tracking system
    system = DevelopmentalTrackingSystem()

    # Register demo metrics
    system.register_metric(
        SimpleDemoMetric(
            name="Conversation Complexity", dimension=DevelopmentalDimension.BEHAVIORAL
        )
    )

    system.register_metric(
        SimpleDemoMetric(
            name="Knowledge Integration", dimension=DevelopmentalDimension.KNOWLEDGE
        )
    )

    system.register_metric(
        SimpleDemoMetric(
            name="Emotional Intelligence", dimension=DevelopmentalDimension.EMOTIONAL
        )
    )

    system.register_metric(
        SimpleDemoMetric(
            name="Reasoning Capability", dimension=DevelopmentalDimension.CAPABILITY
        )
    )

    system.register_metric(
        SimpleDemoMetric(
            name="Relationship Building", dimension=DevelopmentalDimension.SOCIAL
        )
    )

    system.register_metric(
        SimpleDemoMetric(
            name="Learning Rate", dimension=DevelopmentalDimension.LEARNING
        )
    )

    # Initialize system
    await system.initialize()

    return system


async def run_simulated_tracking(system: DevelopmentalTrackingSystem, days: int):
    """
    Run simulated tracking for a number of days.

    Args:
        system: Developmental tracking system
        days: Number of days to simulate
    """
    logger.info(f"Starting simulated tracking for {days} days")

    # Create initial baseline
    baseline = await system.create_baseline(
        name="Initial Baseline", description="Baseline at the start of tracking"
    )

    # Run tracking for each day
    for day in range(1, days + 1):
        logger.info(f"Day {day}:")

        # Track metrics
        await system._track_metrics()

        # Every 7 days, run analysis
        if day % 7 == 0 or day == days:
            logger.info(f"  Running analysis on day {day}")
            await system._analyze_development()

        # Print some stats every 5 days
        if day % 5 == 0 or day == days:
            logger.info("  --- Current Stats ---")
            for metric_id, trajectory in system.trajectories.items():
                current = trajectory.get_current_value()
                growth = trajectory.get_growth_rate()
                phase = trajectory.detect_phase()
                logger.info(
                    f"  {trajectory.name}: {current:.2f} (growth: {growth:.4f}/day, phase: {phase.value})"
                )

            # Get overall phase
            logger.info(f"  Overall phase: {system.current_phase.value}")

    # Create final baseline
    final_baseline = await system.create_baseline(
        name="Final Baseline", description=f"Baseline after {days} days of tracking"
    )

    return baseline, final_baseline


async def analyze_results(
    system: DevelopmentalTrackingSystem,
    initial_baseline: Baseline,
    final_baseline: Baseline,
):
    """
    Analyze the results of the simulation.

    Args:
        system: Developmental tracking system
        initial_baseline: Initial baseline
        final_baseline: Final baseline
    """
    logger.info("\n=== Final Analysis ===")

    # Create comparison with initial baseline
    comparison = BaselineComparison(initial_baseline)

    # Get current metric values
    current_values = {}
    for metric_id, trajectory in system.trajectories.items():
        current_values[metric_id] = trajectory.get_current_value()

    # Get comparison summary
    summary = comparison.get_comparison_summary(current_values)

    # Print summary
    logger.info(f"Overall progress: {summary['overall_progress']:.2f}")
    logger.info(f"Improved metrics: {summary['improved_count']}")
    logger.info(f"Declined metrics: {summary['declined_count']}")
    logger.info(f"Unchanged metrics: {summary['unchanged_count']}")

    # Print improvement details
    logger.info("\nMetric improvements:")
    for metric in summary["improved_metrics"]:
        metric_id = metric["metric_id"]
        trajectory = system.trajectories.get(metric_id)
        if trajectory:
            logger.info(
                f"  {trajectory.name}: {metric['baseline_value']:.2f} -> "
                f"{metric['current_value']:.2f} "
                f"({metric['percent_change'] * 100:.1f}% improvement)"
            )

    # Get dimensional summary
    logger.info("\nDimensional summary:")
    for dimension in DevelopmentalDimension:
        dim_summary = await system.get_dimensional_summary(dimension)
        logger.info(
            f"  {dimension.value}: avg={dim_summary['average_value']:.2f}, "
            f"growth={dim_summary['average_growth_rate']:.4f}/day, "
            f"phase={dim_summary['phase']}"
        )

    # Check for anomalies
    logger.info("\nAnomaly detection:")
    anomaly_detector = AnomalyDetector()
    for metric_id, trajectory in system.trajectories.items():
        anomalies = anomaly_detector.detect_anomalies(trajectory)
        if anomalies:
            logger.info(f"  {trajectory.name}: {len(anomalies)} anomalies detected")
            for anomaly in anomalies:
                logger.info(
                    f"    - {anomaly.anomaly_type.value}: {anomaly.description} "
                    f"(severity: {anomaly.severity.value})"
                )

    # Save comprehensive summary to file
    comprehensive_summary = await system.get_developmental_summary()

    with open("developmental_summary.json", "w") as f:
        json.dump(comprehensive_summary, f, indent=2, default=str)

    logger.info("\nComprehensive summary saved to developmental_summary.json")


async def main():
    """Main entry point for the demo."""
    logger.info("Starting developmental tracking demo")

    # Setup tracking system
    system = await setup_developmental_tracking()

    # Run simulated tracking for 30 days
    initial_baseline, final_baseline = await run_simulated_tracking(system, 30)

    # Analyze results
    await analyze_results(system, initial_baseline, final_baseline)

    # Stop tracking
    await system.stop_tracking()

    logger.info("Developmental tracking demo completed")


if __name__ == "__main__":
    asyncio.run(main())
