"""
Example of context-sensitive probabilistic programming with the CAW paradigm.

This example demonstrates how to use the context-sensitive probabilistic programming
components that integrate with the Contextual Adaptive Wave (CAW) paradigm.
"""

import matplotlib.pyplot as plt
import numpy as np

from person_suit.core.infrastructure.probabilistic.inference import InferenceType
from person_suit.core.infrastructure.probabilistic.integration.caw.context_adapter import (
    get_probabilistic_context_adapter,
)
from person_suit.core.infrastructure.probabilistic.integration.caw.context_sensitive_inference import (
    analyze_context_sensitive_inference_results,
)
from person_suit.core.infrastructure.probabilistic.integration.caw.context_sensitive_inference import (
    run_context_sensitive_inference,
)
from person_suit.core.infrastructure.probabilistic.integration.caw.visualization import (
    plot_context_comparison,
)
from person_suit.core.infrastructure.probabilistic.integration.caw.visualization import (
    plot_context_sensitive_inference_results,
)
from person_suit.core.infrastructure.probabilistic.integration.caw.visualization import (
    plot_context_sensitivity_analysis,
)
from person_suit.core.infrastructure.probabilistic.models import ModelType


def main():
    """Run the example."""
    # Get the probabilistic context adapter
    adapter = get_probabilistic_context_adapter()

    # Create a simple linear regression model
    # y = a * x + b + noise
    # We observe (x, y) pairs and want to infer a and b

    # Generate synthetic data
    np.random.seed(42)
    true_a = 2.0
    true_b = 1.0
    x_data = np.linspace(-1, 1, 20)
    y_data = true_a * x_data + true_b + np.random.normal(0, 0.2, size=20)

    # Create distributions for the parameters
    a_dist = adapter.create_wave_distribution(
        dist_type="gaussian", context_name="balanced", mean=0.0, std=1.0
    )

    b_dist = adapter.create_wave_distribution(
        dist_type="gaussian", context_name="balanced", mean=0.0, std=1.0
    )

    adapter.create_wave_distribution(
        dist_type="gaussian", context_name="balanced", mean=0.0, std=0.2
    )

    # Create variables
    a = adapter.create_context_sensitive_variable(
        name="a", distribution=a_dist, context_name="balanced"
    )

    b = adapter.create_context_sensitive_variable(
        name="b", distribution=b_dist, context_name="balanced"
    )

    # Create observed variables for x and y
    x_vars = []
    y_vars = []

    for i in range(len(x_data)):
        x_var = adapter.create_context_sensitive_variable(
            name=f"x_{i}",
            distribution=adapter.create_wave_distribution(
                dist_type="gaussian", context_name="balanced", mean=x_data[i], std=1e-6
            ),
            observed=np.array([x_data[i]]),
            context_name="balanced",
        )

        y_var = adapter.create_context_sensitive_variable(
            name=f"y_{i}",
            distribution=adapter.create_wave_distribution(
                dist_type="gaussian", context_name="balanced", mean=0.0, std=0.2
            ),
            observed=np.array([y_data[i]]),
            context_name="balanced",
        )

        x_vars.append(x_var)
        y_vars.append(y_var)

    # Create model
    variables = [a, b] + x_vars + y_vars

    # Set up parent-child relationships
    for i in range(len(x_data)):
        y_vars[i]._parents = [a, b, x_vars[i]]
        a._children.append(y_vars[i])
        b._children.append(y_vars[i])
        x_vars[i]._children.append(y_vars[i])

    # Create model
    model = adapter.create_wave_model(
        model_type=ModelType.BAYESIAN_NETWORK,
        name="linear_regression",
        variables=variables,
        context_name="balanced",
    )

    # Create observed values
    observed = {}
    for i in range(len(x_data)):
        observed[f"x_{i}"] = np.array([x_data[i]])
        observed[f"y_{i}"] = np.array([y_data[i]])

    # Create query
    query = ["a", "b"]

    # Create true values dictionary
    true_values = {"a": np.array([true_a]), "b": np.array([true_b])}

    # Run inference with multiple contexts
    print("Running inference with multiple contexts...")
    results = run_context_sensitive_inference(
        model=model,
        observed=observed,
        query=query,
        algorithm_type=InferenceType.MCMC,
        contexts=[
            adapter.contexts["accuracy"],
            adapter.contexts["performance"],
            adapter.contexts["balanced"],
            adapter.contexts["exploration"],
            adapter.contexts["exploitation"],
        ],
        num_samples=1000,
        burn_in=100,
    )

    # Analyze results
    print("\nAnalyzing results...")
    analysis = analyze_context_sensitive_inference_results(results, true_values)

    # Print analysis summary
    print("\nAnalysis Summary:")
    print(f"True a: {true_a}, True b: {true_b}")

    for context_name, context_analysis in analysis.items():
        if context_name != "comparative":
            print(f"\n{context_name} context:")
            for var_name, var_analysis in context_analysis.items():
                print(
                    f"  {var_name} - mean: {var_analysis['mean']:.4f}, std: {var_analysis['std']:.4f}"
                )
                if "abs_error" in var_analysis:
                    print(
                        f"    abs_error: {var_analysis['abs_error']:.4f}, rel_error: {var_analysis['rel_error']:.4f}"
                    )

    if "comparative" in analysis:
        print("\nComparative Analysis:")
        for var_name, var_analysis in analysis["comparative"].items():
            print(f"  {var_name}:")
            for metric, value in var_analysis.items():
                print(f"    {metric}: {value}")

    # Plot data
    plt.figure(figsize=(10, 6))
    plt.scatter(x_data, y_data, label="Data")
    plt.plot(x_data, true_a * x_data + true_b, "r-", label="True line")
    plt.xlabel("x")
    plt.ylabel("y")
    plt.title("Data and true line")
    plt.legend()
    plt.grid(True, linestyle="--", alpha=0.7)
    plt.savefig("context_sensitive_data.png")

    # Plot context-sensitive inference results
    print("\nPlotting results...")
    context_colors = {
        "accuracy": "blue",
        "performance": "green",
        "balanced": "purple",
        "exploration": "orange",
        "exploitation": "red",
    }

    # Plot posterior distributions
    fig1, axes_dict1 = plot_context_sensitive_inference_results(
        results=results,
        true_values=true_values,
        figsize=(12, 8),
        context_colors=context_colors,
        save_path="context_sensitive_posteriors.png",
    )

    # Plot context comparison
    fig2, axes_dict2 = plot_context_comparison(
        analysis=analysis,
        figsize=(12, 8),
        context_colors=context_colors,
        save_path="context_sensitive_comparison.png",
    )

    # Plot comprehensive analysis
    figs, axes_dicts = plot_context_sensitivity_analysis(
        results=results,
        true_values=true_values,
        figsize=(15, 10),
        context_colors=context_colors,
        save_path="context_sensitive_analysis",
    )

    # Show plots
    plt.show()


if __name__ == "__main__":
    main()
