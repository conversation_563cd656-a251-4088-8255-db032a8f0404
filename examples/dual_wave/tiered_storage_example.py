"""
Tiered Storage Example
====================

This example demonstrates how to use the tiered storage system for wave-particle states
in conjunction with the Memory Orchestration Adapter.

Key Features Demonstrated:
- Configuring the tiered storage system
- Storing and retrieving wave-particle states across tiers
- Automatic migration of data between tiers based on access patterns
- Integration with the Memory Orchestration Adapter
"""

import asyncio
import uuid

import numpy as np

from person_suit.core.infrastructure.dual_wave.core import DualInformation
from person_suit.core.infrastructure.dual_wave.core import ParticleState
from person_suit.core.infrastructure.dual_wave.core import WaveState
from person_suit.core.infrastructure.dual_wave.persistence import MemoryOrchestrationAdapter
from person_suit.core.infrastructure.dual_wave.persistence import StorageTier
from person_suit.core.infrastructure.dual_wave.persistence import TierConfig
from person_suit.core.infrastructure.dual_wave.persistence import TieredStorageConfig
from person_suit.core.infrastructure.dual_wave.persistence import TieredStorageManager
from person_suit.core.infrastructure.dual_wave.persistence import get_tiered_storage_manager
from person_suit.meta_systems.persona_core.memory.orchestration import MemoryOrchestrationService


async def create_sample_data(count: int = 10):
    """
    Create sample wave-particle states.

    Args:
        count: Number of states to create

    Returns:
        List of DualInformation objects
    """
    dual_infos = []

    for i in range(count):
        # Create wave state with random vector
        wave_state = WaveState(
            vector=np.random.rand(2048),
            amplitude=np.random.uniform(0.5, 1.5),
            phase=np.random.uniform(0, 2 * np.pi),
            frequency=np.random.uniform(0.1, 10.0),
            wavelength=np.random.uniform(0.1, 10.0),
        )

        # Create particle state with random nodes and edges
        particle_state = ParticleState()
        for j in range(5):
            node_id = str(uuid.uuid4())
            particle_state.add_node(
                node_id=node_id,
                node_type=f"Type{j % 3}",
                properties={"value": np.random.rand()},
            )

        # Create dual information
        dual_info = DualInformation(
            wave_state=wave_state,
            particle_state=particle_state,
            state_ref=str(uuid.uuid4()),
        )

        dual_infos.append(dual_info)

    return dual_infos


async def demonstrate_tiered_storage():
    """Demonstrate the tiered storage system."""
    print("Initializing tiered storage system...")

    # Create custom tier configuration
    config = TieredStorageConfig(
        tier_configs={
            StorageTier.HOT: TierConfig(
                tier=StorageTier.HOT,
                description="In-memory storage for frequently accessed data",
                max_capacity_bytes=256 * 1024 * 1024,  # 256 MB
                promote_threshold=5,
                demote_threshold=1,
            )
        },
        enable_auto_migration=True,
        migration_interval_seconds=60,  # Run migration every minute
    )

    # Initialize tiered storage manager
    manager = TieredStorageManager(config)

    # Create sample data
    print("Creating sample data...")
    dual_infos = await create_sample_data(20)

    # Store data in different tiers
    print("Storing data in different tiers...")
    hot_items = dual_infos[:5]
    warm_items = dual_infos[5:10]
    cold_items = dual_infos[10:15]
    archive_items = dual_infos[15:]

    # Store items in their respective tiers
    for item in hot_items:
        await manager.store(str(item.state_ref), item, tier=StorageTier.HOT)

    for item in warm_items:
        await manager.store(str(item.state_ref), item, tier=StorageTier.WARM)

    for item in cold_items:
        await manager.store(str(item.state_ref), item, tier=StorageTier.COLD)

    for item in archive_items:
        await manager.store(str(item.state_ref), item, tier=StorageTier.ARCHIVE)

    # Print initial tier metrics
    print("\nInitial tier metrics:")
    metrics = manager.get_metrics()
    for tier in StorageTier:
        tier_str = str(tier)
        print(
            f"  {tier_str.upper()}: {metrics['tier_item_count'][tier_str]} items, "
            f"{metrics['tier_size_bytes'][tier_str] / (1024 * 1024):.2f} MB, "
            f"{metrics['tier_utilization'][tier_str] * 100:.1f}% utilized"
        )

    # Simulate access patterns to trigger migration
    print("\nSimulating access patterns...")

    # Access hot items infrequently (should be demoted)
    print("  Accessing hot items infrequently...")
    for item in hot_items:
        await manager.retrieve(str(item.state_ref))

    # Access warm items frequently (should be promoted)
    print("  Accessing warm items frequently...")
    for _ in range(10):
        for item in warm_items:
            await manager.retrieve(str(item.state_ref))

    # Access one cold item frequently (should be promoted)
    print("  Accessing one cold item frequently...")
    cold_item = cold_items[0]
    for _ in range(20):
        await manager.retrieve(str(cold_item.state_ref))

    # Run migration cycle
    print("\nRunning migration cycle...")
    await manager.run_migration_cycle()

    # Print updated tier metrics
    print("\nUpdated tier metrics:")
    metrics = manager.get_metrics()
    for tier in StorageTier:
        tier_str = str(tier)
        print(
            f"  {tier_str.upper()}: {metrics['tier_item_count'][tier_str]} items, "
            f"{metrics['tier_size_bytes'][tier_str] / (1024 * 1024):.2f} MB, "
            f"{metrics['tier_utilization'][tier_str] * 100:.1f}% utilized"
        )

    # Print migration metrics
    print("\nMigration metrics:")
    print(f"  Total migrations: {metrics['migration_count']}")
    print(f"  Promotions: {metrics['promotion_count']}")
    print(f"  Demotions: {metrics['demotion_count']}")

    # Close the manager
    await manager.close()


async def demonstrate_integration_with_mos():
    """Demonstrate integration with Memory Orchestration Service."""
    print("\nDemonstrating integration with Memory Orchestration Service...")

    # Initialize Memory Orchestration Service
    mos = MemoryOrchestrationService()
    await mos.initialize()

    # Initialize Memory Orchestration Adapter
    adapter = MemoryOrchestrationAdapter(mos)
    await adapter.initialize()

    # Initialize tiered storage manager
    manager = get_tiered_storage_manager()

    # Create sample data
    dual_infos = await create_sample_data(5)

    # Store data using the adapter
    print("Storing data using Memory Orchestration Adapter...")
    state_refs = []
    for dual_info in dual_infos:
        state_ref = await adapter.store(dual_info)
        state_refs.append(state_ref)

    # Store the state refs in tiered storage
    print("Storing state refs in tiered storage...")
    for i, state_ref in enumerate(state_refs):
        # Determine tier based on index
        if i < 2:
            tier = StorageTier.HOT
        elif i < 4:
            tier = StorageTier.WARM
        else:
            tier = StorageTier.COLD

        # Store in tiered storage
        await manager.store(str(state_ref), state_ref, tier=tier)

    # Simulate access patterns
    print("Simulating access patterns...")

    # Access some items frequently
    for _ in range(10):
        state_ref = state_refs[3]  # A warm tier item
        ref_from_storage = await manager.retrieve(str(state_ref))
        if ref_from_storage:
            await adapter.retrieve(ref_from_storage)

    # Run migration cycle
    print("Running migration cycle...")
    await manager.run_migration_cycle()

    # Print tier metrics
    print("\nTier metrics after migration:")
    metrics = manager.get_metrics()
    for tier in StorageTier:
        tier_str = str(tier)
        print(f"  {tier_str.upper()}: {metrics['tier_item_count'][tier_str]} items")

    # Close resources
    await manager.close()
    await adapter.close()


async def main():
    """Main function."""
    print("=== Tiered Storage Example ===\n")

    # Demonstrate basic tiered storage
    await demonstrate_tiered_storage()

    # Demonstrate integration with Memory Orchestration Service
    await demonstrate_integration_with_mos()

    print("\n=== Example Complete ===")


if __name__ == "__main__":
    asyncio.run(main())
