"""
Concrete Storage Backends Example
==============================

This example demonstrates how to use the concrete storage backends for the tiered
storage system, including in-memory, local file, SQLite, and object storage backends.

Key Features Demonstrated:
- Configuring and initializing different storage backends
- Storing and retrieving wave-particle states in different backends
- Serialization and compression options
- Performance characteristics of different backends
"""

import asyncio
import time
import uuid

import numpy as np

from person_suit.core.infrastructure.dual_wave.core import DualInformation
from person_suit.core.infrastructure.dual_wave.core import ParticleState
from person_suit.core.infrastructure.dual_wave.core import WaveState
from person_suit.core.infrastructure.dual_wave.persistence.storage_backends import (
    InMemoryStorageBackend,
)
from person_suit.core.infrastructure.dual_wave.persistence.storage_backends import (
    LocalFileStorageBackend,
)
from person_suit.core.infrastructure.dual_wave.persistence.storage_backends import (
    SerializationFormat,
)
from person_suit.core.infrastructure.dual_wave.persistence.storage_backends import (
    SQLiteStorageBackend,
)
from person_suit.core.infrastructure.dual_wave.persistence.storage_backends import (
    StorageBackendConfig,
)


async def create_sample_data(count: int = 5):
    """
    Create sample wave-particle states.

    Args:
        count: Number of states to create

    Returns:
        List of tuples (item_id, wave_state, particle_state, dual_info)
    """
    items = []

    for i in range(count):
        item_id = f"item_{i}"

        # Create wave state with random vector
        wave_state = WaveState(
            vector=np.random.rand(100),  # Smaller vector for example
            amplitude=np.random.uniform(0.5, 1.5),
            phase=np.random.uniform(0, 2 * np.pi),
            frequency=np.random.uniform(0.1, 10.0),
            wavelength=np.random.uniform(0.1, 10.0),
        )

        # Create particle state with random nodes and edges
        particle_state = ParticleState()
        for j in range(3):
            node_id = str(uuid.uuid4())
            particle_state.add_node(
                node_id=node_id,
                node_type=f"Type{j % 3}",
                properties={"value": np.random.rand()},
            )

        # Create dual information
        dual_info = DualInformation(
            wave_state=wave_state,
            particle_state=particle_state,
            state_ref=str(uuid.uuid4()),
        )

        items.append((item_id, wave_state, particle_state, dual_info))

    return items


async def benchmark_backend(backend, items, name):
    """
    Benchmark a storage backend.

    Args:
        backend: The storage backend to benchmark
        items: List of tuples (item_id, wave_state, particle_state, dual_info)
        name: Name of the backend for display
    """
    print(f"\n=== Benchmarking {name} ===")

    # Initialize backend
    print("Initializing backend...")
    start_time = time.time()
    await backend.initialize()
    init_time = time.time() - start_time
    print(f"Initialization time: {init_time:.6f} seconds")

    # Store wave states
    print("\nStoring wave states...")
    start_time = time.time()
    for item_id, wave_state, _, _ in items:
        await backend.store(f"{item_id}_wave", wave_state)
    store_wave_time = time.time() - start_time
    print(f"Store wave states time: {store_wave_time:.6f} seconds")

    # Store particle states
    print("\nStoring particle states...")
    start_time = time.time()
    for item_id, _, particle_state, _ in items:
        await backend.store(f"{item_id}_particle", particle_state)
    store_particle_time = time.time() - start_time
    print(f"Store particle states time: {store_particle_time:.6f} seconds")

    # Store dual information
    print("\nStoring dual information...")
    start_time = time.time()
    for item_id, _, _, dual_info in items:
        await backend.store(f"{item_id}_dual", dual_info)
    store_dual_time = time.time() - start_time
    print(f"Store dual information time: {store_dual_time:.6f} seconds")

    # Retrieve wave states
    print("\nRetrieving wave states...")
    start_time = time.time()
    for item_id, _, _, _ in items:
        await backend.retrieve(f"{item_id}_wave")
    retrieve_wave_time = time.time() - start_time
    print(f"Retrieve wave states time: {retrieve_wave_time:.6f} seconds")

    # Retrieve particle states
    print("\nRetrieving particle states...")
    start_time = time.time()
    for item_id, _, _, _ in items:
        await backend.retrieve(f"{item_id}_particle")
    retrieve_particle_time = time.time() - start_time
    print(f"Retrieve particle states time: {retrieve_particle_time:.6f} seconds")

    # Retrieve dual information
    print("\nRetrieving dual information...")
    start_time = time.time()
    for item_id, _, _, _ in items:
        await backend.retrieve(f"{item_id}_dual")
    retrieve_dual_time = time.time() - start_time
    print(f"Retrieve dual information time: {retrieve_dual_time:.6f} seconds")

    # Get metrics
    count = await backend.count_items()
    size = await backend.get_size()

    print(f"\nTotal items: {count}")
    print(f"Total size: {size / 1024:.2f} KB")

    # Close backend
    await backend.close()

    # Return benchmark results
    return {
        "init_time": init_time,
        "store_wave_time": store_wave_time,
        "store_particle_time": store_particle_time,
        "store_dual_time": store_dual_time,
        "retrieve_wave_time": retrieve_wave_time,
        "retrieve_particle_time": retrieve_particle_time,
        "retrieve_dual_time": retrieve_dual_time,
        "total_items": count,
        "total_size": size,
    }


async def demonstrate_serialization_formats():
    """Demonstrate different serialization formats."""
    print("\n=== Demonstrating Serialization Formats ===")

    # Create test data
    wave_state = WaveState(np.random.rand(1000))
    item_id = "serialization_test"

    # Test different serialization formats
    formats = [
        (SerializationFormat.PICKLE, "Pickle"),
        (SerializationFormat.JSON, "JSON"),
        (SerializationFormat.BINARY, "Binary"),
    ]

    for format, name in formats:
        # Create backend with this format
        config = StorageBackendConfig(
            serialization_format=format, use_compression=True, compression_level=6
        )
        backend = InMemoryStorageBackend(config)
        await backend.initialize()

        # Store the item
        await backend.store(item_id, wave_state)

        # Get size
        size = await backend.get_size()

        print(f"{name} format size: {size / 1024:.2f} KB")

        # Close backend
        await backend.close()


async def demonstrate_compression_levels():
    """Demonstrate different compression levels."""
    print("\n=== Demonstrating Compression Levels ===")

    # Create test data
    wave_state = WaveState(np.random.rand(1000))
    item_id = "compression_test"

    # Test different compression levels
    levels = [0, 3, 6, 9]

    for level in levels:
        # Create backend with this compression level
        config = StorageBackendConfig(
            serialization_format=SerializationFormat.BINARY,
            use_compression=True,
            compression_level=level,
        )
        backend = InMemoryStorageBackend(config)
        await backend.initialize()

        # Store the item
        await backend.store(item_id, wave_state)

        # Get size
        size = await backend.get_size()

        print(f"Compression level {level} size: {size / 1024:.2f} KB")

        # Close backend
        await backend.close()


async def main():
    """Main function."""
    print("=== Concrete Storage Backends Example ===\n")

    # Create sample data
    items = await create_sample_data(10)

    # Benchmark in-memory backend
    in_memory_config = StorageBackendConfig(
        serialization_format=SerializationFormat.PICKLE,
        use_compression=True,
        compression_level=6,
    )
    in_memory_backend = InMemoryStorageBackend(in_memory_config)
    in_memory_results = await benchmark_backend(
        in_memory_backend, items, "In-Memory Backend"
    )

    # Benchmark local file backend
    local_file_config = StorageBackendConfig(
        serialization_format=SerializationFormat.PICKLE,
        use_compression=True,
        compression_level=6,
    )
    local_file_backend = LocalFileStorageBackend(local_file_config)
    local_file_results = await benchmark_backend(
        local_file_backend, items, "Local File Backend"
    )

    # Benchmark SQLite backend
    sqlite_config = StorageBackendConfig(
        serialization_format=SerializationFormat.BINARY,
        use_compression=True,
        compression_level=9,
    )
    sqlite_backend = SQLiteStorageBackend(sqlite_config)
    sqlite_results = await benchmark_backend(sqlite_backend, items, "SQLite Backend")

    # Demonstrate serialization formats
    await demonstrate_serialization_formats()

    # Demonstrate compression levels
    await demonstrate_compression_levels()

    # Print comparison
    print("\n=== Performance Comparison ===")
    print(f"{'Operation':<30} {'In-Memory':<15} {'Local File':<15} {'SQLite':<15}")
    print("-" * 75)
    print(
        f"{'Initialization':<30} {in_memory_results['init_time']:<15.6f} {local_file_results['init_time']:<15.6f} {sqlite_results['init_time']:<15.6f}"
    )
    print(
        f"{'Store Wave States':<30} {in_memory_results['store_wave_time']:<15.6f} {local_file_results['store_wave_time']:<15.6f} {sqlite_results['store_wave_time']:<15.6f}"
    )
    print(
        f"{'Store Particle States':<30} {in_memory_results['store_particle_time']:<15.6f} {local_file_results['store_particle_time']:<15.6f} {sqlite_results['store_particle_time']:<15.6f}"
    )
    print(
        f"{'Store Dual Information':<30} {in_memory_results['store_dual_time']:<15.6f} {local_file_results['store_dual_time']:<15.6f} {sqlite_results['store_dual_time']:<15.6f}"
    )
    print(
        f"{'Retrieve Wave States':<30} {in_memory_results['retrieve_wave_time']:<15.6f} {local_file_results['retrieve_wave_time']:<15.6f} {sqlite_results['retrieve_wave_time']:<15.6f}"
    )
    print(
        f"{'Retrieve Particle States':<30} {in_memory_results['retrieve_particle_time']:<15.6f} {local_file_results['retrieve_particle_time']:<15.6f} {sqlite_results['retrieve_particle_time']:<15.6f}"
    )
    print(
        f"{'Retrieve Dual Information':<30} {in_memory_results['retrieve_dual_time']:<15.6f} {local_file_results['retrieve_dual_time']:<15.6f} {sqlite_results['retrieve_dual_time']:<15.6f}"
    )
    print(
        f"{'Total Size (KB)':<30} {in_memory_results['total_size'] / 1024:<15.2f} {local_file_results['total_size'] / 1024:<15.2f} {sqlite_results['total_size'] / 1024:<15.2f}"
    )

    print("\n=== Example Complete ===")


if __name__ == "__main__":
    asyncio.run(main())
