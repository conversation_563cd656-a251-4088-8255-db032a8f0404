"""
Distributed Storage Example
========================

This example demonstrates the distributed storage system for wave-particle states,
showing how data can be stored across multiple nodes with transparent access.

Key Features Demonstrated:
- Setting up a distributed storage system with multiple nodes
- Storing and retrieving wave-particle states across nodes
- Replication and consistency management
- Node discovery and management
- Handling node failures and recovery

Usage:
    # Run the first node
    python distributed_storage_example.py --port 8000 --seed

    # Run additional nodes (in separate terminals)
    python distributed_storage_example.py --port 8001 --seed-host localhost --seed-port 8000
    python distributed_storage_example.py --port 8002 --seed-host localhost --seed-port 8000
"""

import argparse
import asyncio
import logging
import time
from pathlib import Path

import numpy as np

from person_suit.core.infrastructure.dual_wave.core import DualInformation
from person_suit.core.infrastructure.dual_wave.core import ParticleState
from person_suit.core.infrastructure.dual_wave.core import WaveState
from person_suit.core.infrastructure.dual_wave.persistence.distributed.backend import (
    DistributedStorageBackend,
)
from person_suit.core.infrastructure.dual_wave.persistence.distributed.config import (
    ConsistencyLevel,
)
from person_suit.core.infrastructure.dual_wave.persistence.distributed.config import (
    DistributedStorageConfig,
)
from person_suit.core.infrastructure.dual_wave.persistence.distributed.config import (
    PartitioningStrategy,
)
from person_suit.core.infrastructure.dual_wave.persistence.distributed.config import (
    ReplicationStrategy,
)
from person_suit.core.infrastructure.dual_wave.persistence.distributed.replication import (
    ReplicationStatus,
)
from person_suit.core.infrastructure.dual_wave.persistence.storage_backends import (
    InMemoryStorageBackend,
)
from person_suit.core.infrastructure.dual_wave.persistence.storage_backends import (
    SerializationFormat,
)
from person_suit.core.infrastructure.dual_wave.persistence.storage_backends import (
    StorageBackendConfig,
)

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format="%(asctime)s [%(levelname)s] %(name)s: %(message)s",
    handlers=[logging.StreamHandler()],
)

logger = logging.getLogger("distributed_storage_example")


async def create_sample_data(count: int = 10, vector_size: int = 100):
    """
    Create sample wave-particle states.

    Args:
        count: Number of states to create
        vector_size: Size of wave vectors

    Returns:
        List of tuples (item_id, wave_state, particle_state, dual_info)
    """
    items = []

    for i in range(count):
        item_id = f"item_{i}"

        # Create wave state with random vector
        wave_state = WaveState(
            vector=np.random.rand(vector_size),
            amplitude=np.random.uniform(0.5, 1.5),
            phase=np.random.uniform(0, 2 * np.pi),
            frequency=np.random.uniform(0.1, 10.0),
            wavelength=np.random.uniform(0.1, 10.0),
        )

        # Create particle state with random nodes and edges
        particle_state = ParticleState()
        for j in range(3):
            node_id = f"node_{i}_{j}"
            particle_state.add_node(
                node_id=node_id,
                node_type=f"Type{j % 3}",
                properties={"value": np.random.rand()},
            )

        # Create dual information
        dual_info = DualInformation(
            wave_state=wave_state, particle_state=particle_state, state_ref=f"state_{i}"
        )

        items.append((item_id, wave_state, particle_state, dual_info))

    return items


async def run_node(
    port: int, is_seed: bool, seed_host: str = None, seed_port: int = None
):
    """
    Run a distributed storage node.

    Args:
        port: Port to listen on
        is_seed: Whether this node is a seed node
        seed_host: Hostname of a seed node to connect to
        seed_port: Port of a seed node to connect to
    """
    try:
        # Create data directory
        data_dir = Path(f"./data/node_{port}")
        data_dir.mkdir(parents=True, exist_ok=True)

        # Create distributed storage configuration
        config = DistributedStorageConfig.create_default_config(port, is_seed)
        config.data_directory = str(data_dir)

        # Add seed node if provided
        if seed_host and seed_port:
            config.add_seed_node(seed_host, seed_port)

        # Configure replication
        # Choose one of the following replication strategies:
        # - ReplicationStrategy.SIMPLE: Simple replication strategy
        # - ReplicationStrategy.RACK_AWARE: Rack-aware replication strategy
        # - ReplicationStrategy.REGION_AWARE: Region-aware replication strategy
        # - ReplicationStrategy.ADAPTIVE: Adaptive replication strategy
        config.replication.strategy = ReplicationStrategy.RACK_AWARE
        config.replication.factor = 2
        config.replication.read_consistency = ConsistencyLevel.ONE
        config.replication.write_consistency = ConsistencyLevel.QUORUM

        # Configure partitioning
        config.partitioning.strategy = PartitioningStrategy.CONSISTENT_HASHING
        config.partitioning.virtual_nodes = 256

        # Create local storage backend
        storage_backend_config = StorageBackendConfig(
            serialization_format=SerializationFormat.PICKLE,
            use_compression=True,
            compression_level=6,
            use_optimized_formats=True,
        )
        local_backend = InMemoryStorageBackend(storage_backend_config)
        await local_backend.initialize()

        # Create distributed storage backend
        backend = DistributedStorageBackend(
            config, local_backend, storage_backend_config
        )
        await backend.initialize()

        logger.info(f"Node started on port {port}")

        # If this is the first node, store some sample data
        if is_seed and not seed_host:
            # Create sample data
            items = await create_sample_data(10, 100)

            # Store items
            for item_id, wave_state, particle_state, dual_info in items:
                logger.info(f"Storing item {item_id}")
                await backend.store(item_id, dual_info)

            logger.info(f"Stored {len(items)} items")

        # Keep the node running
        while True:
            await asyncio.sleep(1)
    except KeyboardInterrupt:
        logger.info("Shutting down node...")
        await backend.close()
        logger.info("Node shut down")
    except Exception as e:
        logger.error(f"Error running node: {str(e)}")


async def run_client(seed_host: str, seed_port: int):
    """
    Run a client that interacts with the distributed storage system.

    Args:
        seed_host: Hostname of a seed node to connect to
        seed_port: Port of a seed node to connect to
    """
    try:
        # Create data directory
        data_dir = Path("./data/client")
        data_dir.mkdir(parents=True, exist_ok=True)

        # Create distributed storage configuration
        config = DistributedStorageConfig.create_default_config(0, False)
        config.data_directory = str(data_dir)

        # Add seed node
        config.add_seed_node(seed_host, seed_port)

        # Configure replication
        # Choose one of the following replication strategies:
        # - ReplicationStrategy.SIMPLE: Simple replication strategy
        # - ReplicationStrategy.RACK_AWARE: Rack-aware replication strategy
        # - ReplicationStrategy.REGION_AWARE: Region-aware replication strategy
        # - ReplicationStrategy.ADAPTIVE: Adaptive replication strategy
        config.replication.strategy = ReplicationStrategy.RACK_AWARE
        config.replication.factor = 2
        config.replication.read_consistency = ConsistencyLevel.ONE
        config.replication.write_consistency = ConsistencyLevel.QUORUM

        # Configure partitioning
        config.partitioning.strategy = PartitioningStrategy.CONSISTENT_HASHING
        config.partitioning.virtual_nodes = 256

        # Create local storage backend
        storage_backend_config = StorageBackendConfig(
            serialization_format=SerializationFormat.PICKLE,
            use_compression=True,
            compression_level=6,
            use_optimized_formats=True,
        )
        local_backend = InMemoryStorageBackend(storage_backend_config)
        await local_backend.initialize()

        # Create distributed storage backend
        backend = DistributedStorageBackend(
            config, local_backend, storage_backend_config
        )
        await backend.initialize()

        logger.info("Client started")

        # List items
        items = await backend.list_items()
        logger.info(f"Found {len(items)} items")

        # Retrieve items
        for item_id in items:
            logger.info(f"Retrieving item {item_id}")
            item = await backend.retrieve(item_id)
            if item:
                logger.info(f"Retrieved item {item_id}: {type(item).__name__}")
            else:
                logger.warning(f"Failed to retrieve item {item_id}")

        # Store a new item
        new_item_id = f"client_item_{int(time.time())}"
        new_wave_state = WaveState(
            vector=np.random.rand(100),
            amplitude=1.0,
            phase=0.5,
            frequency=2.0,
            wavelength=0.5,
        )

        logger.info(f"Storing new item {new_item_id}")
        success = await backend.store(new_item_id, new_wave_state)

        if success:
            logger.info(f"Successfully stored item {new_item_id}")

            # Demonstrate replication
            logger.info("\n=== Demonstrating Replication ===\n")

            # Get replication tasks for the item
            replication_tasks = backend.replication_manager.get_tasks_for_item(
                new_item_id
            )

            if replication_tasks:
                # Display replication status
                for task in replication_tasks:
                    logger.info(f"Replication Task: {task.task_id}")
                    logger.info(f"  Status: {task.status.value}")
                    logger.info(f"  Target Nodes: {task.target_nodes}")
                    logger.info(f"  Successful Nodes: {task.successful_nodes}")
                    logger.info(f"  Failed Nodes: {task.failed_nodes}")

                    if task.status == ReplicationStatus.COMPLETE:
                        logger.info("  Replication completed successfully")
                    elif task.status == ReplicationStatus.PARTIAL:
                        logger.info("  Replication partially completed")
                    elif task.status == ReplicationStatus.FAILED:
                        logger.info("  Replication failed")

                    logger.info(f"  Created: {task.created_at}")
                    logger.info(f"  Updated: {task.updated_at}")
                    if task.completed_at:
                        logger.info(f"  Completed: {task.completed_at}")
            else:
                logger.warning("No replication tasks found for the item")
        else:
            logger.warning(f"Failed to store item {new_item_id}")

        # Retrieve the new item
        logger.info(f"Retrieving new item {new_item_id}")
        item = await backend.retrieve(new_item_id)

        if item:
            logger.info(f"Retrieved new item {new_item_id}: {type(item).__name__}")
        else:
            logger.warning(f"Failed to retrieve new item {new_item_id}")

        # Delete the new item
        logger.info(f"Deleting new item {new_item_id}")
        success = await backend.delete(new_item_id)

        if success:
            logger.info(f"Successfully deleted item {new_item_id}")
        else:
            logger.warning(f"Failed to delete item {new_item_id}")

        # Close the backend
        await backend.close()

        logger.info("Client finished")
    except Exception as e:
        logger.error(f"Error running client: {str(e)}")


def parse_args():
    """Parse command line arguments."""
    parser = argparse.ArgumentParser(description="Distributed Storage Example")

    parser.add_argument("--port", type=int, default=8000, help="Port to listen on")
    parser.add_argument(
        "--seed", action="store_true", help="Whether this node is a seed node"
    )
    parser.add_argument(
        "--seed-host", type=str, help="Hostname of a seed node to connect to"
    )
    parser.add_argument(
        "--seed-port", type=int, help="Port of a seed node to connect to"
    )
    parser.add_argument("--client", action="store_true", help="Run as a client")

    return parser.parse_args()


async def main():
    """Main function."""
    args = parse_args()

    if args.client:
        if not args.seed_host or not args.seed_port:
            logger.error("Seed host and port are required for client mode")
            return

        await run_client(args.seed_host, args.seed_port)
    else:
        await run_node(args.port, args.seed, args.seed_host, args.seed_port)


if __name__ == "__main__":
    asyncio.run(main())
