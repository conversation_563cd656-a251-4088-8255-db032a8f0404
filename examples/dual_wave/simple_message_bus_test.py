from person_suit.core.constants.fixed_point_scale import SCAL<PERSON>, PRIO_HIGH, PRIO_LOW, PRIO_NORMAL, PRIO_CRITICAL
#!/usr/bin/env python3
"""
simple_message_bus_test.py - Simple Test of Hybrid Message Bus
=============================================================

This is a minimal test to verify the hybrid message bus works
without circular imports and can handle basic message routing.
"""

import asyncio
import sys
import os

# Add the parent directory to the path so we can import person_suit
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '../..')))

from person_suit.core.infrastructure.hybrid_message_bus import get_message_bus
from person_suit.core.infrastructure.hybrid_message import HybridMessage, Priority
from person_suit.core.infrastructure.deployment_profiles import DeploymentProfiles


async def simple_handler(message: HybridMessage):
    """Simple message handler that just logs what it received."""
    print(f"Handler received message: channel={message.channel}, operation={message.operation}")
    print(f"  Data: {message.data}")
    print(f"  Priority: {message.priority}")
    print(f"  ACF Fidelity: {message.acf_metadata.fidelity}")
    
    # Return a simple response
    return {"status": "processed", "echo": message.data}


async def main():
    """Run a simple test of the hybrid message bus."""
    print("Starting simple hybrid message bus test...")
    
    # Get message bus with desktop profile
    bus = get_message_bus(DeploymentProfiles.desktop())
    print("✓ Message bus created successfully")
    
    # Subscribe to a test channel
    subscription_id = bus.subscribe(
        channel_pattern="test.simple.echo",
        handler=simple_handler,
        subscriber_id="simple_test_handler"
    )
    print(f"✓ Subscribed to channel with ID: {subscription_id}")
    
    # Create and send a test message
    test_message = HybridMessage.create(
        channel="test.simple.echo",
        operation="echo",
        data={"text": "Hello, Hybrid Message Bus!", "timestamp": asyncio.get_event_loop().time()},
        priority=PRIO_NORMAL,
        response_expected=True
    )
    
    print(f"✓ Created test message: {test_message.message_id}")
    
    # Send the message
    print("Sending message...")
    result = await bus.send(test_message)
    
    if result and result.success:
        print(f"✓ Message processed successfully!")
        print(f"  Handler: {result.handler_id}")
        print(f"  Processing time: {result.processing_time_ms:.2f}ms")
        print(f"  Fidelity used: {result.fidelity_used}")
        if result.response:
            print(f"  Response data: {result.response.data}")
    else:
        print("✗ Message processing failed")
        if result:
            print(f"  Error: {result.error}")
    
    # Get bus statistics
    stats = bus.get_statistics()
    print(f"\nBus statistics:")
    for key, value in stats.items():
        print(f"  {key}: {value}")
    
    # Clean up
    await bus.stop()
    print("\n✓ Message bus stopped successfully")
    print("Simple test completed!")


if __name__ == "__main__":
    asyncio.run(main()) 