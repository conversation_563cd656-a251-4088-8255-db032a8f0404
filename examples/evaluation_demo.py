#!/usr/bin/env python3
"""
Evaluation Framework Demo
------------------------
This script demonstrates how to use the evaluation framework
to assess persona quality using believability metrics.
"""

import json
import logging
from typing import Any
from typing import Dict

from person_suit.meta_systems.persona_core.evaluation import EvaluationFramework
from person_suit.meta_systems.persona_core.evaluation import EvaluationResult
from person_suit.meta_systems.persona_core.evaluation.metrics.believability import (
    AppropriatenessMetric,
)
from person_suit.meta_systems.persona_core.evaluation.metrics.believability import (
    BeliavabilityComposite,
)
from person_suit.meta_systems.persona_core.evaluation.metrics.believability import ConsistencyMetric
from person_suit.meta_systems.persona_core.evaluation.metrics.believability import (
    EmotionalCoherenceMetric,
)
from person_suit.meta_systems.persona_core.evaluation.metrics.believability import (
    MemoryFidelityMetric,
)
from person_suit.meta_systems.persona_core.evaluation.metrics.believability import (
    ResponseQualityMetric,
)

# Configure logging
logging.basicConfig(
    level=logging.INFO, format="%(asctime)s - %(name)s - %(levelname)s - %(message)s"
)
logger = logging.getLogger("evaluation_demo")


def load_sample_data(filename: str) -> Dict[str, Any]:
    """
    Load sample interaction data from JSON file.

    Args:
        filename: Path to JSON file

    Returns:
        Dictionary containing sample data
    """
    try:
        with open(filename, "r") as f:
            return json.load(f)
    except (IOError, json.JSONDecodeError) as e:
        logger.error(f"Error loading sample data: {e}")
        return {"persona_state": {}, "interaction_history": [], "environment": {}}


def print_results(result: EvaluationResult) -> None:
    """
    Print evaluation results in a human-readable format.

    Args:
        result: Evaluation result
    """
    print("\n" + "=" * 50)
    print("EVALUATION RESULTS")
    print("=" * 50)

    # Print overall score
    print(f"\nOverall Score: {result.get_average_score():.2f}")

    # Print individual metric scores
    print("\nMetric Scores:")
    for metric_name, score in result.scores.items():
        print(f"  {metric_name}: {score:.2f}")

    # Print observations
    print("\nObservations:")
    for observation in result.observations[:5]:  # Limit to first 5 observations
        print(f"  - {observation.get('metric')}: {observation.get('observation', '')}")

    if len(result.observations) > 5:
        print(f"  ... and {len(result.observations) - 5} more observations")

    print("\n" + "=" * 50)


def main() -> None:
    """Main function to run the demo."""
    logger.info("Starting evaluation framework demo")

    # 1. Create the evaluation framework
    framework = EvaluationFramework()
    logger.info("Created evaluation framework")

    # 2. Register believability metrics
    framework.register_metric(ConsistencyMetric(), is_default=True)
    framework.register_metric(AppropriatenessMetric(), is_default=True)
    framework.register_metric(EmotionalCoherenceMetric(), is_default=True)
    framework.register_metric(MemoryFidelityMetric(), is_default=True)
    framework.register_metric(ResponseQualityMetric(), is_default=True)

    # Also register the composite metric
    framework.register_metric(BeliavabilityComposite())

    logger.info("Registered believability metrics")

    # 3. Register a benchmark
    framework.register_benchmark(
        name="believability_benchmark",
        metrics=[
            "consistency",
            "appropriateness",
            "emotional_coherence",
            "memory_fidelity",
            "response_quality",
        ],
        weights={
            "consistency": 1.2,
            "appropriateness": 1.0,
            "emotional_coherence": 1.1,
            "memory_fidelity": 0.9,
            "response_quality": 0.8,
        },
        description="Standard believability benchmark",
    )
    logger.info("Registered believability benchmark")

    # 4. Load sample data (or use mock data if file not found)
    try:
        sample_data = load_sample_data("examples/sample_interactions.json")
        logger.info("Loaded sample interaction data")
    except Exception:
        # Create mock data for demonstration
        sample_data = {
            "persona_state": {
                "name": "Alex",
                "traits": {
                    "extraversion": 0.7,
                    "agreeableness": 0.8,
                    "conscientiousness": 0.6,
                    "neuroticism": 0.4,
                    "openness": 0.9,
                },
            },
            "interaction_history": [
                {
                    "id": "1",
                    "timestamp": 1625097600,
                    "user_input": "Hi there! What's your name?",
                    "persona_response": "Hi! I'm Alex. It's nice to meet you!",
                },
                {
                    "id": "2",
                    "timestamp": 1625097660,
                    "user_input": "What do you like to do in your free time?",
                    "persona_response": "I love exploring nature, reading sci-fi books, "
                    "and trying new recipes. Cooking is my passion!",
                },
                {
                    "id": "3",
                    "timestamp": 1625097720,
                    "user_input": "Do you remember what you told me your name was?",
                    "persona_response": "Of course! I told you my name is Alex.",
                },
            ],
            "environment": {"platform": "chat", "context": "casual_conversation"},
        }
        logger.info("Created mock interaction data")

    # 5. Create evaluation context
    context = framework.create_evaluation_context(
        persona_state=sample_data["persona_state"],
        interaction_history=sample_data["interaction_history"],
        environment=sample_data["environment"],
    )
    logger.info("Created evaluation context")

    # 6. Run evaluation with the benchmark
    result = framework.evaluate(context, benchmark_name="believability_benchmark")
    logger.info("Completed evaluation")

    # 7. Print results
    print_results(result)

    # 8. Get benchmark-specific analysis
    benchmark_analysis = framework.get_benchmark_result(
        result, "believability_benchmark"
    )

    print("\nBenchmark Analysis:")
    print(f"Weighted Score: {benchmark_analysis['weighted_score']:.2f}")

    logger.info("Demo completed")


if __name__ == "__main__":
    main()
