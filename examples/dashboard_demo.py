#!/usr/bin/env python3
"""
Dashboard Demonstration
--------------------
Purpose: Demonstrate the use of the real-time monitoring dashboard

This script shows how to:
1. Create and configure a dashboard
2. Initialize and start the dashboard
3. Register custom panels and metrics
4. Set up alerts and notifications
5. Export dashboard data

Usage:
    python examples/dashboard_demo.py
"""

import asyncio
import logging
import sys
from datetime import timedelta
from pathlib import Path

from person_suit.shared_io.interfaces.admin.visualization.dashboard import AlertConfig
from person_suit.shared_io.interfaces.admin.visualization.dashboard import AlertSeverity
from person_suit.shared_io.interfaces.admin.visualization.dashboard import Dashboard
from person_suit.shared_io.interfaces.admin.visualization.dashboard import DataSource
from person_suit.shared_io.interfaces.admin.visualization.dashboard import DataSourceType
from person_suit.shared_io.interfaces.admin.visualization.dashboard import RefreshMode
from person_suit.shared_io.interfaces.admin.visualization.dashboard.config import (
    create_default_dashboard_config,
)
from person_suit.shared_io.interfaces.admin.visualization.dashboard.config import (
    create_performance_dashboard_config,
)
from person_suit.shared_io.interfaces.admin.visualization.dashboard.config import (
    save_dashboard_config,
)

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format="%(asctime)s - %(name)s - %(levelname)s - %(message)s",
    handlers=[logging.StreamHandler(sys.stdout)],
)
logger = logging.getLogger("dashboard_demo")


class DashboardDemo:
    """Demo class for the dashboard."""

    def __init__(self):
        """Initialize the demo."""
        self.dashboard = None

    async def setup(self):
        """Set up the dashboard demo."""
        # Create a dashboard with default configuration
        config = create_default_dashboard_config()

        # Add a custom alert
        config.alerts["high_cpu_usage"] = AlertConfig(
            name="high_cpu_usage",
            description="CPU usage is too high",
            severity=AlertSeverity.WARNING,
            condition="> 80",  # Trigger when CPU usage > 80%
            source=DataSource(
                type=DataSourceType.METRICS,
                path="system.cpu_usage",
                refresh_mode=RefreshMode.REAL_TIME,
            ),
            cooldown=timedelta(minutes=1),
        )

        # Create and initialize the dashboard
        self.dashboard = Dashboard(config)
        success = await self.dashboard.initialize()
        if not success:
            logger.error("Failed to initialize dashboard")
            return False

        logger.info("Dashboard initialized")
        return True

    async def run(self):
        """Run the dashboard demo."""
        if self.dashboard is None:
            logger.error("Dashboard not initialized")
            return

        try:
            # Start the dashboard
            await self.dashboard.start()
            logger.info("Dashboard started")

            # Save the dashboard configuration
            config_path = Path("examples/dashboard_config.json")
            await self.dashboard.save_config(config_path)
            logger.info(f"Dashboard configuration saved to {config_path}")

            # Get dashboard data
            data = await self.dashboard.get_dashboard_data()

            # Print dashboard summary
            print("\nDashboard Summary:")
            print(f"Name: {data['name']}")
            print(f"Description: {data['description']}")
            print(f"Panels: {len(data['panels'])}")
            print(f"Recent Alerts: {data['alerts']['count']}")

            # Wait for user input to exit
            print("\nDashboard is running. Press Ctrl+C to exit...")

            # Keep dashboard running
            while True:
                await asyncio.sleep(1)

        except KeyboardInterrupt:
            logger.info("Demo interrupted by user")

        except Exception as e:
            logger.error(f"Error in dashboard demo: {e}", exc_info=True)

        finally:
            # Stop the dashboard
            if self.dashboard is not None:
                await self.dashboard.stop()
                logger.info("Dashboard stopped")


async def create_and_save_configs():
    """Create and save example dashboard configurations."""
    # Create default dashboard config
    default_config = create_default_dashboard_config()
    await save_dashboard_config(
        default_config, Path("examples/default_dashboard_config.json")
    )
    logger.info("Default dashboard config saved")

    # Create performance dashboard config
    perf_config = create_performance_dashboard_config()
    await save_dashboard_config(
        perf_config, Path("examples/performance_dashboard_config.json")
    )
    logger.info("Performance dashboard config saved")


async def main():
    """Main entry point for the demo."""
    logger.info("Starting dashboard demo")

    # Create and save example configs
    await create_and_save_configs()

    # Run the dashboard demo
    demo = DashboardDemo()
    if await demo.setup():
        await demo.run()

    logger.info("Dashboard demo completed")


if __name__ == "__main__":
    asyncio.run(main())
