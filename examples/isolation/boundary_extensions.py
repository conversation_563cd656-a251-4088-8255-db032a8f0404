#!/usr/bin/env python3
"""
Boundary Extensions Example for Meta-System Isolation Framework

This example demonstrates how to implement custom boundary types and
boundary extensions for specialized isolation use cases, including:

1. Creating custom boundary types
2. Implementing specialized boundary validation logic
3. Creating boundary adapters for different isolation contexts
4. Integrating with the core isolation framework

Run this example with: python examples/isolation/boundary_extensions.py
"""

import enum
import logging
from dataclasses import dataclass
from dataclasses import field
from typing import Any
from typing import Dict
from typing import List
from typing import Optional

from person_suit.shared.isolation import BoundaryType
from person_suit.shared.isolation import PermissionLevel
from person_suit.shared.isolation.boundary_manager import BoundaryDefinition
from person_suit.shared.isolation.boundary_manager import SystemIdentifier
from person_suit.shared.isolation.boundary_manager import get_boundary_manager
from person_suit.shared.isolation.permissions import OperationType
from person_suit.shared.isolation.permissions import get_permission_manager

# Configure logging
logging.basicConfig(
    level=logging.INFO, format="%(asctime)s - %(name)s - %(levelname)s - %(message)s"
)
logger = logging.getLogger("boundary_extensions")


# Custom boundary types
class CognitiveBoundaryType(enum.Enum):
    """Custom boundary types for cognitive system isolation."""

    PERCEPTION = "perception"  # Boundary around perception systems
    REASONING = "reasoning"  # Boundary around reasoning systems
    MEMORY = "memory"  # Boundary around memory systems
    EMOTIONAL = "emotional"  # Boundary around emotional systems
    CREATIVE = "creative"  # Boundary around creative systems


# Custom boundary definition with additional attributes
@dataclass
class CognitiveBoundaryDefinition(BoundaryDefinition):
    """
    Extended boundary definition for cognitive systems.

    Includes cognitive-specific attributes beyond the base boundary definition.
    """

    cognitive_type: CognitiveBoundaryType = CognitiveBoundaryType.REASONING
    processing_priority: int = 5  # 1-10 scale for processing priority
    information_sensitivity: int = 5  # 1-10 scale for sensitivity
    requires_conscious_access: bool = False
    additional_metadata: Dict[str, Any] = field(default_factory=dict)

    def __post_init__(self):
        """Initialize after instance creation."""
        # Call parent initialization
        super().__post_init__()

        # Set boundary type to SUBSYSTEM if not explicitly set
        if self.boundary_type is None:
            self.boundary_type = BoundaryType.SUBSYSTEM

        # Add cognitive type to tags if not present
        if self.tags is None:
            self.tags = set()
        self.tags.add(f"cognitive:{self.cognitive_type.value}")

        # Add sensitivity level to tags
        sensitivity_tag = f"sensitivity:{self.information_sensitivity}"
        self.tags.add(sensitivity_tag)

        # Add consciousness access requirement to tags
        if self.requires_conscious_access:
            self.tags.add("requires_conscious_access")


class CognitiveBoundaryManager:
    """
    Manager for cognitive-specific boundary types.

    Extends the core boundary manager with cognitive-specific functionality.
    """

    def __init__(self):
        """Initialize the cognitive boundary manager."""
        self.boundary_manager = get_boundary_manager()
        self.permission_manager = get_permission_manager()
        self._cognitive_boundaries: Dict[str, CognitiveBoundaryDefinition] = {}

    def define_cognitive_boundary(self, boundary: CognitiveBoundaryDefinition) -> None:
        """
        Define a cognitive boundary.

        Args:
            boundary: Cognitive boundary definition
        """
        # Register with core boundary manager
        self.boundary_manager.define_boundary(boundary)

        # Store in cognitive-specific registry
        self._cognitive_boundaries[boundary.boundary_id] = boundary

        logger.info(
            f"Defined cognitive boundary: {boundary.boundary_id} "
            f"(type: {boundary.cognitive_type.value})"
        )

        # Set up default permissions based on cognitive type
        self._setup_default_permissions(boundary)

    def get_cognitive_boundary(
        self, boundary_id: str
    ) -> Optional[CognitiveBoundaryDefinition]:
        """
        Get a cognitive boundary by ID.

        Args:
            boundary_id: Boundary ID

        Returns:
            Cognitive boundary definition if found, None otherwise
        """
        return self._cognitive_boundaries.get(boundary_id)

    def get_boundaries_by_cognitive_type(
        self, cognitive_type: CognitiveBoundaryType
    ) -> List[CognitiveBoundaryDefinition]:
        """
        Get all boundaries of a specific cognitive type.

        Args:
            cognitive_type: Cognitive boundary type

        Returns:
            List of matching cognitive boundaries
        """
        return [
            b
            for b in self._cognitive_boundaries.values()
            if b.cognitive_type == cognitive_type
        ]

    def get_high_sensitivity_boundaries(
        self, threshold: int = 7
    ) -> List[CognitiveBoundaryDefinition]:
        """
        Get all high-sensitivity cognitive boundaries.

        Args:
            threshold: Sensitivity threshold (1-10)

        Returns:
            List of high-sensitivity boundaries
        """
        return [
            b
            for b in self._cognitive_boundaries.values()
            if b.information_sensitivity >= threshold
        ]

    def allow_cross_cognitive_communication(
        self,
        source_type: CognitiveBoundaryType,
        target_type: CognitiveBoundaryType,
        permission_level: PermissionLevel = PermissionLevel.NORMAL,
    ) -> None:
        """
        Allow communication between cognitive boundary types.

        Args:
            source_type: Source cognitive boundary type
            target_type: Target cognitive boundary type
            permission_level: Permission level
        """
        # Get all boundaries of each type
        source_boundaries = self.get_boundaries_by_cognitive_type(source_type)
        target_boundaries = self.get_boundaries_by_cognitive_type(target_type)

        # Allow communication between all pairs
        for source_boundary in source_boundaries:
            for target_boundary in target_boundaries:
                # Skip same boundary
                if source_boundary.boundary_id == target_boundary.boundary_id:
                    continue

                # Allow systems in source boundary to communicate with systems in target boundary
                systems_in_source = self.boundary_manager.get_systems_in_boundary(
                    source_boundary.boundary_id
                )

                systems_in_target = self.boundary_manager.get_systems_in_boundary(
                    target_boundary.boundary_id
                )

                for source_system in systems_in_source:
                    for target_system in systems_in_target:
                        # Allow read/write operations
                        self.permission_manager.allow(
                            source_system,
                            target_system,
                            OperationType.READ,
                            permission_level,
                        )

                        self.permission_manager.allow(
                            source_system,
                            target_system,
                            OperationType.WRITE,
                            permission_level,
                        )

        logger.info(
            f"Allowed communication from {source_type.value} to {target_type.value} "
            f"boundaries at {permission_level.value} level"
        )

    def _setup_default_permissions(self, boundary: CognitiveBoundaryDefinition) -> None:
        """
        Set up default permissions for a cognitive boundary.

        Args:
            boundary: Cognitive boundary definition
        """
        # Set default permissions based on cognitive type and sensitivity
        if boundary.cognitive_type == CognitiveBoundaryType.PERCEPTION:
            # Perception systems can communicate with reasoning systems
            # but with restricted access if sensitivity is high
            permission_level = (
                PermissionLevel.RESTRICTED
                if boundary.information_sensitivity >= 7
                else PermissionLevel.NORMAL
            )

            self.allow_cross_cognitive_communication(
                CognitiveBoundaryType.PERCEPTION,
                CognitiveBoundaryType.REASONING,
                permission_level,
            )

        elif boundary.cognitive_type == CognitiveBoundaryType.MEMORY:
            # Memory systems are more restricted based on sensitivity
            permission_level = (
                PermissionLevel.ELEVATED
                if boundary.information_sensitivity >= 7
                else PermissionLevel.NORMAL
            )

            # Memory systems can communicate with reasoning systems
            self.allow_cross_cognitive_communication(
                CognitiveBoundaryType.MEMORY,
                CognitiveBoundaryType.REASONING,
                permission_level,
            )

        elif boundary.requires_conscious_access:
            # Systems requiring conscious access are more restricted
            self.allow_cross_cognitive_communication(
                boundary.cognitive_type,
                CognitiveBoundaryType.REASONING,
                PermissionLevel.ELEVATED,
            )


def demonstrate_cognitive_boundaries():
    """Demonstrate the use of cognitive boundary extensions."""
    # Create cognitive boundary manager
    cognitive_manager = CognitiveBoundaryManager()

    # Define cognitive boundaries
    perception_boundary = CognitiveBoundaryDefinition(
        boundary_id="visual_perception",
        name="Visual Perception Boundary",
        description="Boundary for visual perception processing",
        cognitive_type=CognitiveBoundaryType.PERCEPTION,
        processing_priority=8,
        information_sensitivity=5,
        requires_conscious_access=False,
    )

    memory_boundary = CognitiveBoundaryDefinition(
        boundary_id="episodic_memory",
        name="Episodic Memory Boundary",
        description="Boundary for episodic memory access",
        cognitive_type=CognitiveBoundaryType.MEMORY,
        processing_priority=6,
        information_sensitivity=8,
        requires_conscious_access=True,
    )

    reasoning_boundary = CognitiveBoundaryDefinition(
        boundary_id="logical_reasoning",
        name="Logical Reasoning Boundary",
        description="Boundary for logical reasoning processes",
        cognitive_type=CognitiveBoundaryType.REASONING,
        processing_priority=9,
        information_sensitivity=4,
        requires_conscious_access=False,
    )

    # Register boundaries
    cognitive_manager.define_cognitive_boundary(perception_boundary)
    cognitive_manager.define_cognitive_boundary(memory_boundary)
    cognitive_manager.define_cognitive_boundary(reasoning_boundary)

    # Define systems within boundaries
    perception_system = SystemIdentifier(
        system_id="visual_analyzer", boundary_id="visual_perception"
    )

    memory_system = SystemIdentifier(
        system_id="memory_retrieval", boundary_id="episodic_memory"
    )

    reasoning_system = SystemIdentifier(
        system_id="inference_engine", boundary_id="logical_reasoning"
    )

    # Register systems with boundary manager
    boundary_manager = get_boundary_manager()
    boundary_manager.register_system(perception_system)
    boundary_manager.register_system(memory_system)
    boundary_manager.register_system(reasoning_system)

    logger.info("Cognitive boundaries and systems registered")

    # Check system registrations
    systems_in_perception = boundary_manager.get_systems_in_boundary(
        "visual_perception"
    )
    systems_in_memory = boundary_manager.get_systems_in_boundary("episodic_memory")
    systems_in_reasoning = boundary_manager.get_systems_in_boundary("logical_reasoning")

    logger.info(f"Systems in perception boundary: {len(systems_in_perception)}")
    logger.info(f"Systems in memory boundary: {len(systems_in_memory)}")
    logger.info(f"Systems in reasoning boundary: {len(systems_in_reasoning)}")

    # Check high sensitivity boundaries
    high_sensitivity = cognitive_manager.get_high_sensitivity_boundaries()
    logger.info(
        f"High sensitivity boundaries: {[b.boundary_id for b in high_sensitivity]}"
    )

    # Check permissions
    permission_manager = get_permission_manager()

    # Check if perception can communicate with reasoning
    perception_to_reasoning = permission_manager.can_communicate(
        perception_system, reasoning_system
    )

    # Check if memory can communicate with reasoning
    memory_to_reasoning = permission_manager.can_communicate(
        memory_system, reasoning_system
    )

    logger.info(
        f"Perception to reasoning communication allowed: {perception_to_reasoning}"
    )
    logger.info(f"Memory to reasoning communication allowed: {memory_to_reasoning}")

    return {
        "perception_system": perception_system,
        "memory_system": memory_system,
        "reasoning_system": reasoning_system,
    }


def main():
    """Run the boundary extensions example."""
    logger.info("Starting boundary extensions example")

    # Demonstrate cognitive boundaries
    demonstrate_cognitive_boundaries()

    logger.info("Example completed successfully")


if __name__ == "__main__":
    main()
