#!/usr/bin/env python3
"""
Basic Example of Meta-System Isolation Framework

This example demonstrates the basic usage of the Meta-System Isolation
Framework components, including:

1. Setting up system boundaries
2. Creating communication channels
3. Sending messages across boundaries
4. Managing permissions and resource limits
5. Monitoring boundary crossings
6. Handling faults

Run this example with: python examples/isolation/basic_isolation_example.py
"""

import logging
import time

from person_suit.shared.isolation import BoundaryType
from person_suit.shared.isolation import CommunicationMode
from person_suit.shared.isolation import FaultSeverity
from person_suit.shared.isolation import PermissionLevel
from person_suit.shared.isolation.boundary_manager import BoundaryDefinition
from person_suit.shared.isolation.boundary_manager import SystemIdentifier
from person_suit.shared.isolation.boundary_manager import get_boundary_manager
from person_suit.shared.isolation.communication import create_channel
from person_suit.shared.isolation.communication import create_typed_message
from person_suit.shared.isolation.communication import get_message_bus
from person_suit.shared.isolation.fault_handler import FaultType
from person_suit.shared.isolation.fault_handler import fault_boundary
from person_suit.shared.isolation.fault_handler import get_fault_manager
from person_suit.shared.isolation.monitoring import CrossingDirection
from person_suit.shared.isolation.monitoring import CrossingStatus
from person_suit.shared.isolation.monitoring import get_boundary_monitor
from person_suit.shared.isolation.monitoring import log_crossing
from person_suit.shared.isolation.permissions import OperationType
from person_suit.shared.isolation.permissions import get_permission_manager
from person_suit.shared.isolation.resource_control import ResourceUnit
from person_suit.shared.isolation.resource_control import set_resource_limit
from person_suit.shared.isolation.resource_control import track_resource_usage

# Configure logging
logging.basicConfig(
    level=logging.INFO, format="%(asctime)s - %(name)s - %(levelname)s - %(message)s"
)
logger = logging.getLogger("isolation_example")


def setup_test_boundaries():
    """Set up test system boundaries for the example."""
    # Get the boundary manager
    boundary_manager = get_boundary_manager()

    # Define system boundaries
    boundary_manager.define_boundary(
        BoundaryDefinition(
            boundary_id="cognitive_boundary",
            name="Cognitive Systems Boundary",
            description="Boundary between cognitive processing systems",
            boundary_type=BoundaryType.SUBSYSTEM,
        )
    )

    boundary_manager.define_boundary(
        BoundaryDefinition(
            boundary_id="io_boundary",
            name="I/O Boundary",
            description="Boundary between system and external I/O",
            boundary_type=BoundaryType.EXTERNAL,
        )
    )

    # Define systems within boundaries
    cognitive_system = SystemIdentifier(
        system_id="cognitive_system", boundary_id="cognitive_boundary"
    )

    perception_system = SystemIdentifier(
        system_id="perception_system", boundary_id="cognitive_boundary"
    )

    io_system = SystemIdentifier(system_id="io_system", boundary_id="io_boundary")

    logger.info("Test boundaries set up")

    return {
        "cognitive_system": cognitive_system,
        "perception_system": perception_system,
        "io_system": io_system,
    }


def setup_permissions(systems):
    """Set up permissions between test systems."""
    # Get permission manager
    permission_manager = get_permission_manager()

    # Allow communication from perception to cognitive
    permission_manager.allow(
        systems["perception_system"],
        systems["cognitive_system"],
        OperationType.WRITE,
        PermissionLevel.NORMAL,
    )

    permission_manager.allow(
        systems["cognitive_system"],
        systems["perception_system"],
        OperationType.READ,
        PermissionLevel.NORMAL,
    )

    # Allow communication from io to perception (but restricted)
    permission_manager.allow(
        systems["io_system"],
        systems["perception_system"],
        OperationType.WRITE,
        PermissionLevel.RESTRICTED,
    )

    permission_manager.allow(
        systems["perception_system"],
        systems["io_system"],
        OperationType.READ,
        PermissionLevel.RESTRICTED,
    )

    # Allow io system to connect to perception
    permission_manager.allow(
        systems["io_system"],
        systems["perception_system"],
        OperationType.CONNECT,
        PermissionLevel.RESTRICTED,
    )

    logger.info("Permissions set up")


def setup_resource_limits(systems):
    """Set up resource limits for test systems."""
    # Set resource limits for the cognitive system
    set_resource_limit(
        systems["cognitive_system"],
        "memory",
        soft_limit=1000,
        hard_limit=2000,
        unit=ResourceUnit.BYTES,
        description="Memory usage limit",
    )

    set_resource_limit(
        systems["cognitive_system"],
        "operations",
        soft_limit=100,
        hard_limit=150,
        unit=ResourceUnit.OPERATIONS,
        window_seconds=60,
        description="Operation rate limit",
    )

    # Set resource limits for the perception system
    set_resource_limit(
        systems["perception_system"],
        "api_calls",
        soft_limit=50,
        hard_limit=80,
        unit=ResourceUnit.REQUESTS,
        window_seconds=60,
        description="API call rate limit",
    )

    logger.info("Resource limits set up")


def handle_perception_message(message_payload):
    """
    Handle a message received by the perception system.

    Args:
        message_payload: Message payload

    Returns:
        Response payload
    """
    logger.info(f"Perception system received: {message_payload}")

    # Process the message
    if "raw_data" in message_payload:
        # Track resource usage
        track_resource_usage(
            SystemIdentifier(
                system_id="perception_system", boundary_id="cognitive_boundary"
            ),
            "api_calls",
            1,
        )

        # Process the raw data
        processed_data = {
            "source": message_payload["source"],
            "processed": True,
            "content": f"Processed: {message_payload['raw_data']}",
            "timestamp": time.time(),
        }

        return processed_data

    return {"error": "Invalid message format"}


def handle_cognitive_message(message_payload):
    """
    Handle a message received by the cognitive system.

    Args:
        message_payload: Message payload

    Returns:
        Response payload
    """
    logger.info(f"Cognitive system received: {message_payload}")

    # Track resource usage
    track_resource_usage(
        SystemIdentifier(
            system_id="cognitive_system", boundary_id="cognitive_boundary"
        ),
        "operations",
        1,
    )

    # Simulate memory usage
    memory_usage = len(str(message_payload)) * 2
    track_resource_usage(
        SystemIdentifier(
            system_id="cognitive_system", boundary_id="cognitive_boundary"
        ),
        "memory",
        memory_usage,
    )

    # Process the message
    if "content" in message_payload:
        # Complex cognitive processing
        with fault_boundary(
            SystemIdentifier(
                system_id="cognitive_system", boundary_id="cognitive_boundary"
            ),
            "cognitive_processor",
            "process_content",
            fault_type=FaultType.DATA_ERROR,
            severity=FaultSeverity.WARNING,
        ):
            # Simulate potential fault
            if "error" in message_payload["content"].lower():
                raise ValueError("Content processing error detected")

            result = {
                "understanding": f"Understood: {message_payload['content']}",
                "confidence": 0.95,
                "response": "This is a response from the cognitive system",
                "timestamp": time.time(),
            }

            return result

    return {"error": "Invalid message format"}


def demonstrate_communication(systems):
    """Demonstrate communication between systems."""
    get_message_bus()

    # Create channels
    io_to_perception = create_channel(
        systems["io_system"],
        systems["perception_system"],
        CommunicationMode.SYNCHRONOUS,
    )

    perception_to_cognitive = create_channel(
        systems["perception_system"],
        systems["cognitive_system"],
        CommunicationMode.SYNCHRONOUS,
    )

    # Register message handlers
    io_to_perception.register_handler("input_data", handle_perception_message)
    perception_to_cognitive.register_handler("processed_data", handle_cognitive_message)

    logger.info("Communication channels set up")

    # Simulate IO system sending data to perception
    input_data = {
        "raw_data": "User input text for processing",
        "source": "user_interface",
        "timestamp": time.time(),
    }

    io_message = create_typed_message(
        payload=input_data,
        source=systems["io_system"],
        destination=systems["perception_system"],
        message_type="input_data",
    )

    start_time = time.time()
    logger.info("Sending message from IO to Perception system")
    perception_response = io_to_perception.send(io_message)

    # Log the crossing
    log_crossing(
        boundary_id="io_boundary",
        source=systems["io_system"],
        destination=systems["perception_system"],
        direction=CrossingDirection.OUTBOUND,
        message_type="input_data",
        mode=CommunicationMode.SYNCHRONOUS,
        status=CrossingStatus.ALLOWED,
        duration_ms=(time.time() - start_time) * 1000,
    )

    logger.info(f"Perception system response: {perception_response}")

    if perception_response:
        # Forward from perception to cognitive
        perception_message = create_typed_message(
            payload=perception_response,
            source=systems["perception_system"],
            destination=systems["cognitive_system"],
            message_type="processed_data",
        )

        start_time = time.time()
        logger.info("Sending message from Perception to Cognitive system")
        cognitive_response = perception_to_cognitive.send(perception_message)

        # Log the crossing
        log_crossing(
            boundary_id="cognitive_boundary",
            source=systems["perception_system"],
            destination=systems["cognitive_system"],
            direction=CrossingDirection.INBOUND,
            message_type="processed_data",
            mode=CommunicationMode.SYNCHRONOUS,
            status=CrossingStatus.ALLOWED,
            duration_ms=(time.time() - start_time) * 1000,
        )

        logger.info(f"Cognitive system response: {cognitive_response}")

    # Demonstrate fault handling with an error message
    error_data = {
        "raw_data": "This input contains an error",
        "source": "user_interface",
        "timestamp": time.time(),
    }

    error_message = create_typed_message(
        payload=error_data,
        source=systems["io_system"],
        destination=systems["perception_system"],
        message_type="input_data",
    )

    logger.info("Sending error message from IO to Perception system")
    perception_response = io_to_perception.send(error_message)

    if perception_response:
        # Forward from perception to cognitive (will trigger fault)
        error_perception_message = create_typed_message(
            payload=perception_response,
            source=systems["perception_system"],
            destination=systems["cognitive_system"],
            message_type="processed_data",
        )

        logger.info(
            "Sending message from Perception to Cognitive system (will trigger fault)"
        )
        try:
            cognitive_response = perception_to_cognitive.send(error_perception_message)
            logger.info(f"Cognitive system response: {cognitive_response}")
        except Exception as e:
            logger.warning(f"Caught exception from cognitive system: {str(e)}")

            # Get fault information
            fault_manager = get_fault_manager()
            active_faults = fault_manager.get_active_faults(
                system=systems["cognitive_system"]
            )

            logger.info(f"Active faults in cognitive system: {len(active_faults)}")
            for fault in active_faults:
                logger.info(
                    f"Fault: {fault.fault_id} - {fault.fault_type.value} - {fault.error}"
                )

                # Resolve the fault
                fault_manager.resolve_fault(systems["cognitive_system"], fault.fault_id)


def display_monitoring_results():
    """Display monitoring results from the boundary monitor."""
    monitor = get_boundary_monitor()

    # Get metrics for all boundaries
    metrics = monitor.get_metrics()

    logger.info("=== Boundary Monitoring Results ===")
    for boundary_id, boundary_metrics in metrics.items():
        logger.info(f"Boundary: {boundary_id}")
        logger.info(f"  Total crossings: {boundary_metrics.total_crossings}")
        logger.info(f"  Allowed crossings: {boundary_metrics.allowed_crossings}")
        logger.info(f"  Rejected crossings: {boundary_metrics.rejected_crossings}")
        logger.info(f"  Error crossings: {boundary_metrics.error_crossings}")
        logger.info(
            f"  Avg crossing time: {boundary_metrics.avg_crossing_time_ms:.2f} ms"
        )
        logger.info(f"  Crossing rate: {boundary_metrics.crossing_rate:.2f} per second")

    # Get recent crossings
    recent = monitor.get_recent_crossings(limit=10)

    logger.info("\n=== Recent Boundary Crossings ===")
    for crossing in recent:
        logger.info(f"Crossing: {crossing.crossing_id}")
        logger.info(f"  From {crossing.source} to {crossing.destination}")
        logger.info(f"  Boundary: {crossing.boundary_id}")
        logger.info(f"  Status: {crossing.status.value}")
        logger.info(f"  Duration: {crossing.duration_ms:.2f} ms")
        logger.info("")


def main():
    """Run the isolation framework example."""
    logger.info("Starting Meta-System Isolation Framework example")

    # Set up test systems and boundaries
    systems = setup_test_boundaries()

    # Set up permissions
    setup_permissions(systems)

    # Set up resource limits
    setup_resource_limits(systems)

    # Demonstrate communication
    demonstrate_communication(systems)

    # Display monitoring results
    display_monitoring_results()

    logger.info("Example completed successfully")


if __name__ == "__main__":
    main()
