from person_suit.core.constants.fixed_point_scale import PRIO_HIGH
from person_suit.core.constants.fixed_point_scale import PRIO_NORMAL

#!/usr/bin/env python3
"""
Asynchronous and Event-Based Communication Example for Meta-System Isolation Framework

This example demonstrates the usage of asynchronous and event-based communication
patterns in the Meta-System Isolation Framework, including:

1. Setting up asynchronous communication channels
2. Implementing event-based publish-subscribe patterns
3. Processing events across boundaries asynchronously
4. Handling backpressure and flow control
5. Implementing timeout and retry logic

Run this example with: python examples/isolation/async_event_example.py
"""

import asyncio
import logging
import random
import time
import uuid
from typing import Any
from typing import Callable
from typing import Dict
from typing import List
from typing import Optional

from person_suit.shared.isolation import BoundaryType
from person_suit.shared.isolation import CommunicationMode
from person_suit.shared.isolation import FaultSeverity
from person_suit.shared.isolation import PermissionLevel
from person_suit.shared.isolation.boundary_manager import BoundaryDefinition
from person_suit.shared.isolation.boundary_manager import SystemIdentifier
from person_suit.shared.isolation.boundary_manager import get_boundary_manager
from person_suit.shared.isolation.communication import MessagePriority
from person_suit.shared.isolation.communication import create_channel
from person_suit.shared.isolation.communication import create_typed_message
from person_suit.shared.isolation.fault_handler import FaultType
from person_suit.shared.isolation.fault_handler import RecoveryStrategy
from person_suit.shared.isolation.fault_handler import handle_fault
from person_suit.shared.isolation.monitoring import CrossingDirection
from person_suit.shared.isolation.monitoring import CrossingStatus
from person_suit.shared.isolation.monitoring import get_boundary_monitor
from person_suit.shared.isolation.monitoring import log_crossing
from person_suit.shared.isolation.permissions import OperationType
from person_suit.shared.isolation.permissions import get_permission_manager
from person_suit.shared.isolation.resource_control import ResourceUnit
from person_suit.shared.isolation.resource_control import set_resource_limit
from person_suit.shared.isolation.resource_control import track_resource_usage

# Configure logging
logging.basicConfig(
    level=logging.INFO, format="%(asctime)s - %(name)s - %(levelname)s - %(message)s"
)
logger = logging.getLogger("async_event_example")


# Event topics for publish-subscribe pattern
class EventTopic:
    """Event topics for the publish-subscribe system."""

    SENSOR_DATA = "sensor_data"
    USER_ACTION = "user_action"
    SYSTEM_STATUS = "system_status"
    PROCESSING_RESULT = "processing_result"
    ERROR_EVENT = "error_event"


class EventSubscriber:
    """
    Event subscriber for the event-based communication system.

    Maintains topic subscriptions and handles incoming events.
    """

    def __init__(self, system: SystemIdentifier, name: str):
        """
        Initialize the event subscriber.

        Args:
            system: System identifier
            name: Subscriber name
        """
        self.system = system
        self.name = name
        self.handlers: Dict[str, List[Callable]] = {}
        self.pending_events: asyncio.Queue = asyncio.Queue()
        self.is_running = False
        self.worker_task = None

    def subscribe(self, topic: str, handler: Callable[[Dict[str, Any]], None]) -> None:
        """
        Subscribe to an event topic.

        Args:
            topic: Event topic
            handler: Event handler function
        """
        if topic not in self.handlers:
            self.handlers[topic] = []

        self.handlers[topic].append(handler)
        logger.info(f"Subscriber {self.name} subscribed to topic {topic}")

    def unsubscribe(self, topic: str, handler: Optional[Callable] = None) -> None:
        """
        Unsubscribe from an event topic.

        Args:
            topic: Event topic
            handler: Specific handler to unsubscribe (or all if None)
        """
        if topic not in self.handlers:
            return

        if handler is None:
            # Remove all handlers for this topic
            self.handlers.pop(topic)
        else:
            # Remove specific handler
            self.handlers[topic] = [h for h in self.handlers[topic] if h != handler]

    async def handle_event(self, topic: str, event_data: Dict[str, Any]) -> None:
        """
        Handle an incoming event.

        Args:
            topic: Event topic
            event_data: Event data
        """
        if topic not in self.handlers:
            return

        # Queue the event for processing
        await self.pending_events.put((topic, event_data))

        # Start worker if not running
        if not self.is_running:
            self.start_worker()

    def start_worker(self) -> None:
        """Start the event processing worker."""
        if self.worker_task is None or self.worker_task.done():
            self.is_running = True
            self.worker_task = asyncio.create_task(self._process_events())

    async def _process_events(self) -> None:
        """Process events from the queue."""
        try:
            while self.is_running:
                try:
                    # Get next event with timeout
                    topic, event_data = await asyncio.wait_for(
                        self.pending_events.get(), timeout=1.0
                    )

                    # Process the event
                    await self._dispatch_event(topic, event_data)

                    # Mark as done
                    self.pending_events.task_done()
                except asyncio.TimeoutError:
                    # No events for a while, check if should stop
                    if self.pending_events.empty():
                        self.is_running = False
                except Exception as e:
                    logger.error(f"Error processing event: {str(e)}")

                    # Report the fault
                    handle_fault(
                        fault_type=FaultType.DATA_ERROR,
                        severity=FaultSeverity.WARNING,
                        system=self.system,
                        component="event_subscriber",
                        operation="process_events",
                        error=str(e),
                        recovery_strategy=RecoveryStrategy.RETRY,
                    )
        finally:
            self.is_running = False

    async def _dispatch_event(self, topic: str, event_data: Dict[str, Any]) -> None:
        """
        Dispatch an event to all registered handlers.

        Args:
            topic: Event topic
            event_data: Event data
        """
        if topic not in self.handlers:
            return

        # Track resource usage for event processing
        track_resource_usage(self.system, "events_processed", 1)

        # Call all handlers for this topic
        for handler in self.handlers[topic]:
            try:
                # Call the handler
                if asyncio.iscoroutinefunction(handler):
                    # Async handler
                    await handler(event_data)
                else:
                    # Sync handler - run in thread pool
                    await asyncio.to_thread(handler, event_data)
            except Exception as e:
                logger.error(f"Error in event handler: {str(e)}")

                # Report the fault
                handle_fault(
                    fault_type=FaultType.DATA_ERROR,
                    severity=FaultSeverity.WARNING,
                    system=self.system,
                    component="event_handler",
                    operation="handle_event",
                    error=str(e),
                    recovery_strategy=RecoveryStrategy.CONTINUE,
                )


class EventPublisher:
    """
    Event publisher for the event-based communication system.

    Publishes events to subscribed channels.
    """

    def __init__(self, system: SystemIdentifier):
        """
        Initialize the event publisher.

        Args:
            system: System identifier
        """
        self.system = system
        self.channels: Dict[
            str, List[Any]
        ] = {}  # Any instead of Channel to avoid import issues
        self.subscribers: Dict[str, List[EventSubscriber]] = {}

    def register_channel(self, topic: str, channel: Any) -> None:
        """
        Register a channel for an event topic.

        Args:
            topic: Event topic
            channel: Communication channel
        """
        if topic not in self.channels:
            self.channels[topic] = []

        self.channels[topic].append(channel)

    def register_subscriber(self, topic: str, subscriber: EventSubscriber) -> None:
        """
        Register a subscriber for an event topic.

        Args:
            topic: Event topic
            subscriber: Event subscriber
        """
        if topic not in self.subscribers:
            self.subscribers[topic] = []

        self.subscribers[topic].append(subscriber)

    async def publish_event(
        self,
        topic: str,
        event_data: Dict[str, Any],
        priority: MessagePriority = PRIO_NORMAL,
    ) -> None:
        """
        Publish an event to all registered channels and subscribers.

        Args:
            topic: Event topic
            event_data: Event data
            priority: Event priority
        """
        logger.debug(f"Publishing event on topic {topic}")

        # Enrich event data with metadata
        full_event_data = {
            "topic": topic,
            "timestamp": time.time(),
            "publisher": str(self.system),
            "event_id": str(uuid.uuid4()),
            "data": event_data,
        }

        # Track resource usage
        track_resource_usage(self.system, "events_published", 1)

        # Publish to channels
        if topic in self.channels:
            await self._publish_to_channels(topic, full_event_data, priority)

        # Publish to direct subscribers
        if topic in self.subscribers:
            await self._publish_to_subscribers(topic, full_event_data)

    async def _publish_to_channels(
        self, topic: str, event_data: Dict[str, Any], priority: MessagePriority
    ) -> None:
        """
        Publish an event to registered channels.

        Args:
            topic: Event topic
            event_data: Event data
            priority: Event priority
        """
        for channel in self.channels[topic]:
            try:
                # Create message
                message = create_typed_message(
                    payload=event_data,
                    source=self.system,
                    destination=channel.destination,
                    message_type=f"event.{topic}",
                    priority=priority,
                )

                # Send message asynchronously
                await channel.async_send(message)

                # Log the crossing
                log_crossing(
                    boundary_id=self.system.boundary_id,
                    source=self.system,
                    destination=channel.destination,
                    direction=CrossingDirection.OUTBOUND,
                    message_type=f"event.{topic}",
                    mode=CommunicationMode.EVENT,
                    status=CrossingStatus.ALLOWED,
                    duration_ms=0.0,  # No meaningful duration for async events
                )
            except Exception as e:
                logger.error(f"Error publishing event to channel: {str(e)}")

                # Log the crossing as failed
                log_crossing(
                    boundary_id=self.system.boundary_id,
                    source=self.system,
                    destination=channel.destination,
                    direction=CrossingDirection.OUTBOUND,
                    message_type=f"event.{topic}",
                    mode=CommunicationMode.EVENT,
                    status=CrossingStatus.ERROR,
                    duration_ms=0.0,
                    error=str(e),
                )

                # Report the fault
                handle_fault(
                    fault_type=FaultType.CONNECTION_ERROR,
                    severity=FaultSeverity.WARNING,
                    system=self.system,
                    component="event_publisher",
                    operation="publish_event",
                    error=str(e),
                    recovery_strategy=RecoveryStrategy.CONTINUE,
                )

    async def _publish_to_subscribers(
        self, topic: str, event_data: Dict[str, Any]
    ) -> None:
        """
        Publish an event to direct subscribers.

        Args:
            topic: Event topic
            event_data: Event data
        """
        for subscriber in self.subscribers[topic]:
            try:
                await subscriber.handle_event(topic, event_data)
            except Exception as e:
                logger.error(f"Error publishing event to subscriber: {str(e)}")

                # Report the fault
                handle_fault(
                    fault_type=FaultType.CONNECTION_ERROR,
                    severity=FaultSeverity.WARNING,
                    system=self.system,
                    component="event_publisher",
                    operation="publish_to_subscriber",
                    error=str(e),
                    recovery_strategy=RecoveryStrategy.CONTINUE,
                )


async def setup_async_boundaries():
    """Set up boundaries for the asynchronous example."""
    # Get the boundary manager
    boundary_manager = get_boundary_manager()
    boundary_manager.reset()  # Reset for clean example

    # Define system boundaries
    sensor_boundary = BoundaryDefinition(
        boundary_id="sensor_boundary",
        name="Sensor Systems Boundary",
        description="Boundary for sensor input systems",
        boundary_type=BoundaryType.EXTERNAL,
    )

    processing_boundary = BoundaryDefinition(
        boundary_id="processing_boundary",
        name="Processing Systems Boundary",
        description="Boundary for data processing systems",
        boundary_type=BoundaryType.SUBSYSTEM,
    )

    output_boundary = BoundaryDefinition(
        boundary_id="output_boundary",
        name="Output Systems Boundary",
        description="Boundary for output systems",
        boundary_type=BoundaryType.EXTERNAL,
    )

    # Register boundaries
    boundary_manager.define_boundary(sensor_boundary)
    boundary_manager.define_boundary(processing_boundary)
    boundary_manager.define_boundary(output_boundary)

    # Define systems within boundaries
    sensor_system = SystemIdentifier(
        system_id="sensor_system", boundary_id="sensor_boundary"
    )

    processing_system = SystemIdentifier(
        system_id="processing_system", boundary_id="processing_boundary"
    )

    output_system = SystemIdentifier(
        system_id="output_system", boundary_id="output_boundary"
    )

    # Register systems
    boundary_manager.register_system(sensor_system)
    boundary_manager.register_system(processing_system)
    boundary_manager.register_system(output_system)

    logger.info("Async boundaries and systems set up")

    # Setup permissions
    permission_manager = get_permission_manager()

    # Sensor to processing
    permission_manager.allow(
        sensor_system, processing_system, OperationType.WRITE, PermissionLevel.NORMAL
    )

    permission_manager.allow(
        processing_system, sensor_system, OperationType.READ, PermissionLevel.NORMAL
    )

    # Processing to output
    permission_manager.allow(
        processing_system, output_system, OperationType.WRITE, PermissionLevel.NORMAL
    )

    permission_manager.allow(
        output_system, processing_system, OperationType.READ, PermissionLevel.NORMAL
    )

    # Allow connections for event-based communication
    permission_manager.allow(
        sensor_system, processing_system, OperationType.CONNECT, PermissionLevel.NORMAL
    )

    permission_manager.allow(
        processing_system, output_system, OperationType.CONNECT, PermissionLevel.NORMAL
    )

    # Set resource limits
    # Sensor system: limit event publication rate
    set_resource_limit(
        sensor_system,
        "events_published",
        soft_limit=50,
        hard_limit=100,
        unit=ResourceUnit.REQUESTS,
        window_seconds=60,
        description="Event publication rate limit",
    )

    # Processing system: limit event processing rate
    set_resource_limit(
        processing_system,
        "events_processed",
        soft_limit=100,
        hard_limit=200,
        unit=ResourceUnit.REQUESTS,
        window_seconds=60,
        description="Event processing rate limit",
    )

    return {
        "sensor_system": sensor_system,
        "processing_system": processing_system,
        "output_system": output_system,
    }


async def setup_async_channels(systems):
    """
    Set up communication channels for asynchronous example.

    Args:
        systems: Dictionary of system identifiers

    Returns:
        Dictionary of channels and event handlers
    """
    # Create channels
    sensor_to_processing = create_channel(
        systems["sensor_system"],
        systems["processing_system"],
        CommunicationMode.ASYNCHRONOUS,
    )

    processing_to_output = create_channel(
        systems["processing_system"],
        systems["output_system"],
        CommunicationMode.ASYNCHRONOUS,
    )

    # Create event publishers
    sensor_publisher = EventPublisher(systems["sensor_system"])
    processing_publisher = EventPublisher(systems["processing_system"])

    # Create event subscribers
    processing_subscriber = EventSubscriber(
        systems["processing_system"], "processing_subscriber"
    )

    output_subscriber = EventSubscriber(systems["output_system"], "output_subscriber")

    # Register channels with publishers
    sensor_publisher.register_channel(EventTopic.SENSOR_DATA, sensor_to_processing)
    processing_publisher.register_channel(
        EventTopic.PROCESSING_RESULT, processing_to_output
    )

    # Define event handlers
    async def process_sensor_data(event_data):
        """Process sensor data events."""
        data = event_data.get("data", {})
        sensor_id = data.get("sensor_id", "unknown")
        reading = data.get("reading", 0.0)

        logger.info(f"Processing sensor data from {sensor_id}: {reading}")

        # Simulate processing time
        await asyncio.sleep(0.1)

        # Check for anomalies
        is_anomaly = reading > 90.0

        # Create processing result
        result = {
            "sensor_id": sensor_id,
            "original_reading": reading,
            "processed_value": reading * 1.2,  # Apply some transformation
            "is_anomaly": is_anomaly,
            "processing_time": time.time(),
        }

        # Publish result
        await processing_publisher.publish_event(
            EventTopic.PROCESSING_RESULT,
            result,
            priority=PRIO_HIGH if is_anomaly else PRIO_NORMAL,
        )

        # If anomaly, also publish to error topic
        if is_anomaly:
            await processing_publisher.publish_event(
                EventTopic.ERROR_EVENT,
                {
                    "sensor_id": sensor_id,
                    "error_type": "anomaly",
                    "reading": reading,
                    "threshold": 90.0,
                },
                priority=PRIO_HIGH,
            )

    async def handle_processing_result(event_data):
        """Handle processing result events."""
        data = event_data.get("data", {})
        sensor_id = data.get("sensor_id", "unknown")
        processed_value = data.get("processed_value", 0.0)
        is_anomaly = data.get("is_anomaly", False)

        # Determine message based on anomaly status
        if is_anomaly:
            logger.info(
                f"ALERT: Anomaly detected for sensor {sensor_id}: {processed_value}"
            )
        else:
            logger.info(f"Normal reading from sensor {sensor_id}: {processed_value}")

    async def handle_error_event(event_data):
        """Handle error events."""
        data = event_data.get("data", {})
        sensor_id = data.get("sensor_id", "unknown")
        error_type = data.get("error_type", "unknown")

        logger.warning(f"Error event for sensor {sensor_id}: {error_type}")

    # Subscribe to events
    processing_subscriber.subscribe(EventTopic.SENSOR_DATA, process_sensor_data)
    output_subscriber.subscribe(EventTopic.PROCESSING_RESULT, handle_processing_result)
    output_subscriber.subscribe(EventTopic.ERROR_EVENT, handle_error_event)

    # Register subscribers with publishers
    sensor_publisher.register_subscriber(EventTopic.SENSOR_DATA, processing_subscriber)
    processing_publisher.register_subscriber(
        EventTopic.PROCESSING_RESULT, output_subscriber
    )
    processing_publisher.register_subscriber(EventTopic.ERROR_EVENT, output_subscriber)

    logger.info("Async channels and event handlers set up")

    return {
        "sensor_to_processing": sensor_to_processing,
        "processing_to_output": processing_to_output,
        "sensor_publisher": sensor_publisher,
        "processing_publisher": processing_publisher,
        "processing_subscriber": processing_subscriber,
        "output_subscriber": output_subscriber,
    }


async def simulate_sensor_data(publisher, num_events=20, interval=0.5):
    """
    Simulate sensor data events.

    Args:
        publisher: Event publisher
        num_events: Number of events to generate
        interval: Interval between events in seconds
    """
    sensors = ["temperature", "humidity", "pressure"]

    for i in range(num_events):
        # Choose a random sensor
        sensor_id = random.choice(sensors)

        # Generate reading (occasionally generate anomalies)
        reading = random.uniform(0, 100)

        # Create sensor data
        sensor_data = {
            "sensor_id": sensor_id,
            "reading": reading,
            "timestamp": time.time(),
            "sequence": i,
        }

        # Publish event
        await publisher.publish_event(EventTopic.SENSOR_DATA, sensor_data)

        # Occasionally inject delays to simulate varying loads
        if random.random() < 0.2:
            delay = random.uniform(0.5, 2.0)
            logger.debug(f"Injecting delay of {delay:.2f}s")
            await asyncio.sleep(delay)
        else:
            await asyncio.sleep(interval)


async def demonstrate_backpressure(publisher):
    """
    Demonstrate backpressure handling with a flood of events.

    Args:
        publisher: Event publisher
    """
    logger.info("Demonstrating backpressure handling with event flood")

    # Publish many events in rapid succession
    for i in range(50):
        sensor_data = {
            "sensor_id": "flood_sensor",
            "reading": random.uniform(0, 100),
            "timestamp": time.time(),
            "sequence": i,
        }

        # Publish without delay
        await publisher.publish_event(EventTopic.SENSOR_DATA, sensor_data)

    logger.info("Event flood complete")


async def demonstrate_timeout_retry():
    """Demonstrate timeout and retry logic."""
    logger.info("Demonstrating timeout and retry logic")

    # Setup a system that will experience timeouts
    timeout_system = SystemIdentifier(
        system_id="timeout_system", boundary_id="processing_boundary"
    )

    # Create a function that will timeout
    async def operation_with_timeout():
        """Simulated operation that may timeout."""
        # Randomly decide if operation will timeout
        if random.random() < 0.7:
            logger.info("Operation timing out...")
            await asyncio.sleep(3.0)  # Long delay to cause timeout
            return False
        else:
            logger.info("Operation succeeding immediately")
            return True

    # Implement retry logic
    max_retries = 3
    retry_delays = [0.5, 1.0, 2.0]  # Exponential backoff

    for attempt in range(max_retries + 1):
        try:
            # Set a timeout for the operation
            success = await asyncio.wait_for(operation_with_timeout(), timeout=1.0)

            if success:
                logger.info("Operation succeeded!")
                break
        except asyncio.TimeoutError:
            logger.warning(
                f"Timeout occurred (attempt {attempt + 1}/{max_retries + 1})"
            )

            # Report the fault
            handle_fault(
                fault_type=FaultType.TIMEOUT,
                severity=FaultSeverity.WARNING,
                system=timeout_system,
                component="timeout_demonstration",
                operation="operation_with_timeout",
                error="Operation timed out",
                recovery_strategy=RecoveryStrategy.RETRY,
            )

            # Check if we should retry
            if attempt < max_retries:
                retry_delay = retry_delays[attempt]
                logger.info(f"Retrying after {retry_delay}s...")
                await asyncio.sleep(retry_delay)
            else:
                logger.error("Max retries reached, giving up!")

                # Report the fatal fault
                handle_fault(
                    fault_type=FaultType.TIMEOUT,
                    severity=FaultSeverity.ERROR,
                    system=timeout_system,
                    component="timeout_demonstration",
                    operation="operation_with_timeout",
                    error="Max retries reached",
                    recovery_strategy=RecoveryStrategy.DEGRADE,
                )


async def run_async_example():
    """Run the asynchronous example."""
    logger.info("Starting asynchronous/event-based communication example")

    # Set up test systems and boundaries
    systems = await setup_async_boundaries()

    # Set up async channels and event subscriptions
    components = await setup_async_channels(systems)

    # Run the example in stages with small delays to see results clearly

    # Stage 1: Basic async communication
    logger.info("=== Stage 1: Basic Async Communication ===")
    await simulate_sensor_data(
        components["sensor_publisher"], num_events=5, interval=1.0
    )

    # Small delay to let events process
    await asyncio.sleep(2.0)

    # Stage 2: Demonstrate backpressure handling
    logger.info("=== Stage 2: Backpressure Handling ===")
    await demonstrate_backpressure(components["sensor_publisher"])

    # Wait for backpressure to resolve
    await asyncio.sleep(3.0)

    # Stage 3: Demonstrate timeout and retry logic
    logger.info("=== Stage 3: Timeout and Retry Logic ===")
    await demonstrate_timeout_retry()

    # Stage 4: More normal operation
    logger.info("=== Stage 4: Normal Operation ===")
    await simulate_sensor_data(
        components["sensor_publisher"], num_events=10, interval=0.5
    )

    # Wait for any pending events to be processed
    await asyncio.sleep(2.0)

    # Display monitoring results
    logger.info("=== Monitoring Results ===")
    monitor = get_boundary_monitor()

    # Get metrics for all boundaries
    metrics = monitor.get_metrics()

    for boundary_id, boundary_metrics in metrics.items():
        logger.info(f"Boundary: {boundary_id}")
        logger.info(f"  Total crossings: {boundary_metrics.total_crossings}")
        logger.info(f"  Allowed crossings: {boundary_metrics.allowed_crossings}")
        logger.info(f"  Error crossings: {boundary_metrics.error_crossings}")
        logger.info(
            f"  Avg crossing time: {boundary_metrics.avg_crossing_time_ms:.2f} ms"
        )

    # Get recent crossings
    recent = monitor.get_recent_crossings(limit=5)
    logger.info("Recent boundary crossings:")
    for crossing in recent:
        logger.info(
            f"  {crossing.source} -> {crossing.destination} ({crossing.status.value})"
        )

    logger.info("Example completed successfully")


def main():
    """Run the example."""
    asyncio.run(run_async_example())


if __name__ == "__main__":
    main()
