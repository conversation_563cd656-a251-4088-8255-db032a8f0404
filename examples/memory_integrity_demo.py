#!/usr/bin/env python3
"""
Memory Integrity Verification System Demo

This script demonstrates the functionality of the Memory Integrity Verification system,
including the core verifier, consistency checker, and temporal validator.

It creates sample memories with different types of inconsistencies and shows how
the system detects and repairs these issues.
"""

import asyncio
import logging
from datetime import datetime
from datetime import timed<PERSON><PERSON>
from typing import Dict
from typing import List

# Set up logging
logging.basicConfig(
    level=logging.INFO, format="%(asctime)s - %(name)s - %(levelname)s - %(message)s"
)
logger = logging.getLogger("memory_integrity_demo")

# Import memory classes
from person_suit.meta_systems.persona_core.core.memory.interfaces import Memory
from person_suit.meta_systems.persona_core.core.memory.interfaces import Memory<PERSON>ayerInterface
from person_suit.meta_systems.persona_core.core.memory.interfaces import MemoryLayerType
from person_suit.meta_systems.persona_core.memory.integrity.consistency import (
    ConsistencyCheckConfiguration,
)
from person_suit.meta_systems.persona_core.memory.integrity.consistency import Consistency<PERSON>hecker
from person_suit.meta_systems.persona_core.memory.integrity.temporal import (
    TemporalValidationConfiguration,
)
from person_suit.meta_systems.persona_core.memory.integrity.temporal import TemporalValidator

# Import the verification components
from person_suit.meta_systems.persona_core.memory.integrity.verifier import MemoryIntegrityVerifier
from person_suit.meta_systems.persona_core.memory.integrity.verifier import (
    VerificationConfiguration,
)
from person_suit.meta_systems.persona_core.memory.integrity.verifier import VerificationScope


class MockMemoryLayer(MemoryLayerInterface):
    """Mock memory layer for demonstration purposes."""

    def __init__(self, layer_type: MemoryLayerType):
        self.layer_type = layer_type
        self.memories = {}  # id -> memory

    async def store_memory(self, memory: Memory) -> str:
        """Store a memory and return its ID."""
        if not hasattr(memory, "id") or not memory.id:
            memory.id = f"{self.layer_type.name.lower()}_mem_{len(self.memories)}"
        self.memories[memory.id] = memory
        return memory.id

    async def get_memory(self, memory_id: str) -> Memory:
        """Get a memory by ID."""
        return self.memories.get(memory_id)

    async def update_memory(self, memory: Memory) -> bool:
        """Update an existing memory."""
        if hasattr(memory, "id") and memory.id in self.memories:
            self.memories[memory.id] = memory
            return True
        return False

    async def delete_memory(self, memory_id: str) -> bool:
        """Delete a memory by ID."""
        if memory_id in self.memories:
            del self.memories[memory_id]
            return True
        return False

    async def get_memories_by_ids(self, memory_ids: List[str]) -> List[Memory]:
        """Get multiple memories by their IDs."""
        return [
            self.memories.get(mem_id)
            for mem_id in memory_ids
            if mem_id in self.memories
        ]


def create_sample_memories() -> Dict[str, Memory]:
    """Create sample memories with various inconsistencies."""
    now = datetime.now()
    yesterday = now - timedelta(days=1)
    two_days_ago = now - timedelta(days=2)

    memories = {}

    # Create consistent memories
    memories["consistent_1"] = Memory(
        id="mem_1",
        type="episodic",
        content="Met with Sarah at the coffee shop",
        creation_time=now,
        timestamp=yesterday,
        location="Downtown Coffee Shop",
        participants=["self", "Sarah"],
        importance=0.7,
        entities=["Sarah", "coffee shop"],
    )

    memories["consistent_2"] = Memory(
        id="mem_2",
        type="episodic",
        content="Had lunch with David at the Italian restaurant",
        creation_time=now,
        timestamp=yesterday + timedelta(hours=2),
        location="Guido's Italian Restaurant",
        participants=["self", "David"],
        importance=0.6,
        entities=["David", "Italian restaurant", "lunch"],
    )

    # Create inconsistent memories

    # 1. Temporal inconsistency - impossible overlap
    memories["temporal_inconsistent_1"] = Memory(
        id="mem_3",
        type="episodic",
        content="Attended meeting with the team at the office",
        creation_time=now,
        timestamp=yesterday + timedelta(hours=1),  # Overlaps with coffee shop
        time_range=(yesterday + timedelta(hours=0.5), yesterday + timedelta(hours=2.5)),
        location="Office Building",
        participants=["self", "team"],
        importance=0.8,
        entities=["meeting", "team", "office"],
    )

    # 2. Logical inconsistency - contradictory facts
    memories["logical_inconsistent_1"] = Memory(
        id="mem_4",
        type="semantic",
        content="Sarah has brown hair",
        creation_time=now,
        confidence=0.9,
        importance=0.5,
        entities=["Sarah", "brown hair"],
    )

    memories["logical_inconsistent_2"] = Memory(
        id="mem_5",
        type="semantic",
        content="Sarah has blonde hair",
        creation_time=now,
        confidence=0.7,
        importance=0.5,
        entities=["Sarah", "blonde hair"],
    )

    # 3. Causal inconsistency - effect before cause
    memories["causal_inconsistent_1"] = Memory(
        id="mem_6",
        type="episodic",
        content="Got soaking wet from the rain",
        creation_time=now,
        timestamp=two_days_ago,
        importance=0.6,
        entities=["rain", "wet"],
    )

    memories["causal_inconsistent_2"] = Memory(
        id="mem_7",
        type="episodic",
        content="It started raining heavily while walking",
        creation_time=now,
        timestamp=two_days_ago + timedelta(hours=1),  # Rain started AFTER getting wet
        importance=0.6,
        entities=["rain", "walking"],
        referenced_memory_ids=["mem_6"],
        reference_types=["cause"],  # This indicates a causal relationship
    )

    return memories


async def demo_memory_integrity_verification():
    """Run a demonstration of the memory integrity verification system."""
    logger.info("Starting Memory Integrity Verification Demo")

    # Create sample data
    sample_memories = create_sample_memories()

    # Create memory layers
    memory_layers = {
        MemoryLayerType.LONG_TERM: MockMemoryLayer(MemoryLayerType.LONG_TERM),
        MemoryLayerType.SEMANTIC: MockMemoryLayer(MemoryLayerType.SEMANTIC),
    }

    # Store memories in appropriate layers
    for mem_id, memory in sample_memories.items():
        if memory.type == "semantic":
            await memory_layers[MemoryLayerType.SEMANTIC].store_memory(memory)
        else:
            await memory_layers[MemoryLayerType.LONG_TERM].store_memory(memory)

    # Create verification components

    # 1. Consistency checker
    consistency_config = ConsistencyCheckConfiguration(
        enabled=True, parallel_processing=True
    )
    consistency_checker = ConsistencyChecker(
        configuration=consistency_config, memory_layers=memory_layers
    )

    # 2. Temporal validator
    temporal_config = TemporalValidationConfiguration(
        enabled=True, validation_strictness=0.7
    )
    temporal_validator = TemporalValidator(
        configuration=temporal_config, memory_layers=memory_layers
    )

    # 3. Core verifier
    verification_config = VerificationConfiguration(
        enabled=True,
        auto_repair_enabled=True,
        repair_strategy="conservative",
        consistency_checks_enabled=True,
        temporal_validation_enabled=True,
    )
    verifier = MemoryIntegrityVerifier(
        configuration=verification_config,
        memory_layers=memory_layers,
        consistency_checker=consistency_checker,
        temporal_validator=temporal_validator,
    )

    # Initialize components
    await verifier.initialize()

    # Run verification
    logger.info("Running memory integrity verification...")

    # Get all memory IDs
    all_memory_ids = [mem.id for mem in sample_memories.values()]

    # Verify all memories
    inconsistencies = await verifier.verify_memories(
        memory_ids=all_memory_ids, verification_scope=VerificationScope.RELATED_MEMORIES
    )

    # Report results
    logger.info(
        f"Verification complete. Found {len(inconsistencies.inconsistencies)} inconsistencies:"
    )

    for i, inconsistency in enumerate(inconsistencies.inconsistencies):
        logger.info(f"\nInconsistency #{i + 1}:")
        logger.info(f"  Type: {inconsistency.type.name}")
        logger.info(f"  Description: {inconsistency.description}")
        logger.info(f"  Affected Memories: {inconsistency.affected_memory_ids}")
        logger.info(f"  Confidence: {inconsistency.confidence:.2f}")
        logger.info(f"  Repair Strategy: {inconsistency.repair_strategy}")

    # Run automatic repairs
    if inconsistencies.inconsistencies and verification_config.auto_repair_enabled:
        logger.info("\nRunning automatic repairs...")
        repair_results = await verifier.repair_inconsistencies(
            inconsistencies.inconsistencies
        )

        logger.info(
            f"Repairs complete. Applied {repair_results['repairs_applied']} repairs:"
        )
        for i, repair in enumerate(repair_results.get("applied_repairs", [])):
            logger.info(f"\nRepair #{i + 1}:")
            logger.info(f"  Memory ID: {repair.get('memory_id')}")
            logger.info(f"  Action: {repair.get('action')}")
            logger.info(f"  Details: {repair.get('details')}")

    logger.info("\nDemo Complete")


if __name__ == "__main__":
    asyncio.run(demo_memory_integrity_verification())
