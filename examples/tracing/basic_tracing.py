"""
Basic Tracing Example

This example demonstrates the basic usage of the tracing system in Person Suit.
It shows how to create spans, add attributes, and propagate context.
"""

import asyncio
from typing import Any
from typing import Dict

from person_suit.core.infrastructure.tracing import get_tracer
from person_suit.core.infrastructure.tracing import init_tracer


async def process_order(order_id: str, user_id: str) -> Dict[str, Any]:
    """Process an order with tracing."""
    tracer = get_tracer()
    
    # Start a new span for order processing
    with tracer.start_span("process_order") as span:
        span.attributes.update({
            "order.id": order_id,
            "user.id": user_id,
            "item.count": 3
        })
        
        # Simulate some work
        await asyncio.sleep(0.1)
        
        # Process payment in a child span
        payment_result = await process_payment(order_id, 100.0)
        
        # Process shipping in a child span
        shipping_result = await process_shipping(order_id, user_id)
        
        return {
            "order_id": order_id,
            "status": "completed",
            "payment": payment_result,
            "shipping": shipping_result
        }

async def process_payment(order_id: str, amount: float) -> Dict[str, Any]:
    """Process payment with tracing."""
    tracer = get_tracer()
    
    with tracer.start_span("process_payment") as span:
        span.attributes.update({
            "order.id": order_id,
            "payment.amount": amount,
            "payment.currency": "USD"
        })
        
        # Simulate payment processing
        await asyncio.sleep(0.05)
        
        # Simulate a successful payment
        return {
            "transaction_id": f"tx_{order_id}",
            "status": "completed",
            "amount_charged": amount
        }

async def process_shipping(order_id: str, user_id: str) -> Dict[str, Any]:
    """Process shipping with tracing."""
    tracer = get_tracer()
    
    with tracer.start_span("process_shipping") as span:
        span.attributes.update({
            "order.id": order_id,
            "user.id": user_id,
            "shipping.method": "standard"
        })
        
        # Simulate address validation
        with tracer.start_span("validate_address") as address_span:
            address_span.attributes["address.valid"] = True
            await asyncio.sleep(0.02)
        
        # Simulate shipping cost calculation
        with tracer.start_span("calculate_shipping") as calc_span:
            calc_span.attributes["shipping.cost"] = 5.99
            await asyncio.sleep(0.03)
        
        return {
            "tracking_number": f"TN-{order_id}",
            "status": "shipped",
            "shipping_cost": 5.99,
            "estimated_delivery": "2025-06-10"
        }

async def main():
    # Initialize the tracer
    init_tracer("order_processing_service")
    
    # Process some orders
    orders = [
        ("order_123", "user_456"),
        ("order_124", "user_789"),
    ]
    
    tasks = [process_order(order_id, user_id) for order_id, user_id in orders]
    results = await asyncio.gather(*tasks)
    
    # Print results
    for result in results:
        print(f"Order processed: {result}")
    
    # Export spans (in a real app, this would send to a tracing backend)
    tracer = get_tracer()
    print("\nTrace exported spans:")
    for span in tracer.export_spans():
        print(f"- {span.name} ({span.span_context.span_id}): {span.attributes}")

if __name__ == "__main__":
    asyncio.run(main())
