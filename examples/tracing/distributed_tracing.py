"""
Distributed Tracing Example

This example demonstrates distributed tracing across multiple services
using the message bus for communication.
"""

import asyncio
import uuid
from dataclasses import dataclass
from typing import Any
from typing import Dict

from person_suit.core.infrastructure.message_based_core import DecoupledMessage
from person_suit.core.infrastructure.message_based_core import DecoupledMessageBus
from person_suit.core.infrastructure.message_based_core import MessageType
from person_suit.core.infrastructure.message_based_core import get_message_bus
from person_suit.core.infrastructure.tracing import Span
from person_suit.core.infrastructure.tracing import SpanKind
from person_suit.core.infrastructure.tracing import get_tracer
from person_suit.core.infrastructure.tracing import init_tracer

# Service names
ORDER_SERVICE = "order_service"
PAYMENT_SERVICE = "payment_service"
INVENTORY_SERVICE = "inventory_service"

@dataclass
class Order:
    order_id: str
    user_id: str
    items: list[Dict[str, Any]]
    total: float

class OrderService:
    """Example order processing service."""
    
    def __init__(self, message_bus: DecoupledMessageBus):
        self.message_bus = message_bus
        self.orders: Dict[str, Order] = {}
        
    async def start(self):
        """Register message handlers."""
        await self.message_bus.register_message_handler(
            ORDER_SERVICE,
            MessageType.REQUEST,
            self.handle_create_order
        )
    
    async def handle_create_order(self, message: DecoupledMessage) -> DecoupledMessage:
        """Handle create order request."""
        tracer = get_tracer()
        
        with tracer.start_span("create_order", kind=SpanKind.SERVER) as span:
            try:
                # Extract order data from message
                order_data = message.data.get("order", {})
                order_id = str(uuid.uuid4())
                
                # Create order
                order = Order(
                    order_id=order_id,
                    user_id=order_data["user_id"],
                    items=order_data["items"],
                    total=sum(item["price"] * item["quantity"] for item in order_data["items"])
                )
                self.orders[order_id] = order
                
                # Update span attributes
                span.attributes.update({
                    "order.id": order_id,
                    "user.id": order.user_id,
                    "order.total": order.total,
                    "item.count": len(order.items)
                })
                
                # Reserve inventory
                inventory_response = await self.message_bus.send_message(DecoupledMessage(
                    message_type=MessageType.REQUEST,
                    target_service=INVENTORY_SERVICE,
                    source_service=ORDER_SERVICE,
                    data={
                        "action": "reserve",
                        "items": order.items
                    },
                    context=message.context
                ))
                
                if not inventory_response.data.get("success"):
                    raise Exception("Failed to reserve inventory")
                
                # Process payment
                payment_response = await self.message_bus.send_message(DecoupledMessage(
                    message_type=MessageType.REQUEST,
                    target_service=PAYMENT_SERVICE,
                    source_service=ORDER_SERVICE,
                    data={
                        "action": "charge",
                        "order_id": order_id,
                        "amount": order.total,
                        "currency": "USD"
                    },
                    context=message.context
                ))
                
                if not payment_response.data.get("success"):
                    raise Exception("Payment failed")
                
                return DecoupledMessage(
                    message_type=MessageType.RESPONSE,
                    target_service=message.source_service,
                    source_service=ORDER_SERVICE,
                    data={
                        "success": True,
                        "order_id": order_id,
                        "status": "completed"
                    },
                    context=message.context
                )
                
            except Exception as e:
                span.record_exception(e)
                return DecoupledMessage(
                    message_type=MessageType.ERROR,
                    target_service=message.source_service,
                    source_service=ORDER_SERVICE,
                    data={
                        "success": False,
                        "error": str(e)
                    },
                    context=message.context
                )

class PaymentService:
    """Example payment processing service."""
    
    def __init__(self, message_bus: DecoupledMessageBus):
        self.message_bus = message_bus
        
    async def start(self):
        """Register message handlers."""
        await self.message_bus.register_message_handler(
            PAYMENT_SERVICE,
            MessageType.REQUEST,
            self.handle_payment_request
        )
    
    async def handle_payment_request(self, message: DecoupledMessage) -> DecoupledMessage:
        """Handle payment request."""
        tracer = get_tracer()
        
        with tracer.start_span("process_payment", kind=SpanKind.SERVER) as span:
            try:
                data = message.data
                
                # Simulate payment processing
                await asyncio.sleep(0.1)
                
                # Simulate payment success
                transaction_id = f"tx_{uuid.uuid4().hex[:8]}"
                
                span.attributes.update({
                    "payment.transaction_id": transaction_id,
                    "payment.amount": data["amount"],
                    "payment.currency": data.get("currency", "USD"),
                    "payment.status": "completed"
                })
                
                return DecoupledMessage(
                    message_type=MessageType.RESPONSE,
                    target_service=message.source_service,
                    source_service=PAYMENT_SERVICE,
                    data={
                        "success": True,
                        "transaction_id": transaction_id,
                        "status": "completed"
                    },
                    context=message.context
                )
                
            except Exception as e:
                span.record_exception(e)
                return DecoupledMessage(
                    message_type=MessageType.ERROR,
                    target_service=message.source_service,
                    source_service=PAYMENT_SERVICE,
                    data={
                        "success": False,
                        "error": str(e)
                    },
                    context=message.context
                )

class InventoryService:
    """Example inventory management service."""
    
    def __init__(self, message_bus: DecoupledMessageBus):
        self.message_bus = message_bus
        self.inventory = {
            "item_1": 100,  # item_id: available_quantity
            "item_2": 50,
            "item_3": 200
        }
    
    async def start(self):
        """Register message handlers."""
        await self.message_bus.register_message_handler(
            INVENTORY_SERVICE,
            MessageType.REQUEST,
            self.handle_inventory_request
        )
    
    async def handle_inventory_request(self, message: DecoupledMessage) -> DecoupledMessage:
        """Handle inventory request."""
        tracer = get_tracer()
        
        with tracer.start_span("process_inventory", kind=SpanKind.SERVER) as span:
            try:
                action = message.data.get("action")
                
                if action == "reserve":
                    return await self._reserve_items(message, span)
                else:
                    raise ValueError(f"Unknown action: {action}")
                    
            except Exception as e:
                span.record_exception(e)
                return DecoupledMessage(
                    message_type=MessageType.ERROR,
                    target_service=message.source_service,
                    source_service=INVENTORY_SERVICE,
                    data={
                        "success": False,
                        "error": str(e)
                    },
                    context=message.context
                )
    
    async def _reserve_items(self, message: DecoupledMessage, span: Span) -> DecoupledMessage:
        """Reserve items in inventory."""
        items = message.data.get("items", [])
        reserved = []
        
        span.attributes["inventory.action"] = "reserve"
        span.attributes["inventory.item_count"] = len(items)
        
        # Simulate inventory check and reservation
        for item in items:
            item_id = item["item_id"]
            quantity = item["quantity"]
            
            if self.inventory.get(item_id, 0) >= quantity:
                self.inventory[item_id] -= quantity
                reserved.append({
                    "item_id": item_id,
                    "quantity": quantity,
                    "reserved": True
                })
            else:
                # Rollback previous reservations
                for r in reserved:
                    self.inventory[r["item_id"]] += r["quantity"]
                
                raise ValueError(f"Insufficient inventory for item {item_id}")
        
        return DecoupledMessage(
            message_type=MessageType.RESPONSE,
            target_service=message.source_service,
            source_service=INVENTORY_SERVICE,
            data={
                "success": True,
                "reserved_items": reserved
            },
            context=message.context
        )

async def run_example():
    """Run the distributed tracing example."""
    # Initialize tracing
    init_tracer("distributed_order_processing")
    
    # Create and start the message bus
    message_bus = get_message_bus()
    
    # Create and start services
    order_service = OrderService(message_bus)
    payment_service = PaymentService(message_bus)
    inventory_service = InventoryService(message_bus)
    
    await asyncio.gather(
        message_bus.start(),
        order_service.start(),
        payment_service.start(),
        inventory_service.start()
    )
    
    # Create a test order
    test_order = {
        "user_id": "user_123",
        "items": [
            {"item_id": "item_1", "quantity": 2, "price": 29.99},
            {"item_id": "item_2", "quantity": 1, "price": 19.99}
        ]
    }
    
    # Send the order request
    response = await message_bus.send_message(DecoupledMessage(
        message_type=MessageType.REQUEST,
        target_service=ORDER_SERVICE,
        source_service="test_client",
        data={"order": test_order}
    ))
    
    print("Order processing result:", response.data)
    
    # Export spans (in a real app, this would send to a tracing backend)
    tracer = get_tracer()
    print("\nTrace exported spans:")
    for span in tracer.export_spans():
        print(f"- {span.name} ({span.span_context.span_id}): {span.attributes}")
    
    # Clean up
    await message_bus.shutdown()

if __name__ == "__main__":
    asyncio.run(run_example())
