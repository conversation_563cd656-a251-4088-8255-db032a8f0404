"""
Tracing Performance Test

This script tests the performance impact of the tracing system.
It runs a series of operations with and without tracing to measure the overhead.
"""

import asyncio
import time
import statistics
from typing import List, Dict, Any, Callable, Awaitable

# Disable tracing for the control group
TRACING_ENABLED = False

# Re-export the tracing functions with a no-op implementation when disabled
if not TRACING_ENABLED:
    class NoOpSpan:
        def __init__(self, *args, **kwargs):
            self.attributes = {}
        
        def __enter__(self):
            return self
            
        def __exit__(self, *args):
            pass
            
        def set_attribute(self, key, value):
            pass
            
        def add_event(self, name, attributes=None):
            pass
    
    class NoOpTracer:
        def start_span(self, name, **kwargs):
            return NoOpSpan()
            
        def get_current_span(self):
            return NoOpSpan()
            
        def export_spans(self):
            return []
    
    _global_tracer = NoOpTracer()
    
    def init_tracer(service_name):
        pass
        
    def get_tracer():
        return _global_tracer
else:
    from person_suit.core.infrastructure.tracing import init_tracer, get_tracer

async def process_data(data: Dict[str, Any]) -> Dict[str, Any]:
    """Process data with tracing."""
    tracer = get_tracer()
    
    with tracer.start_span("process_data") as span:
        # Add some attributes
        span.attributes["data.size"] = len(str(data))
        
        # Simulate processing
        await asyncio.sleep(0.01)
        
        # Process nested data
        result = await _process_nested(data)
        
        # Add result to span
        span.attributes["result.size"] = len(str(result))
        
        return result

async def _process_nested(data: Dict[str, Any]) -> Dict[str, Any]:
    """Helper function to demonstrate nested spans."""
    tracer = get_tracer()
    
    with tracer.start_span("process_nested") as span:
        # Simulate nested processing
        await asyncio.sleep(0.005)
        
        # Add some attributes
        span.attributes["data.keys"] = ",".join(data.keys())
        
        # Return processed data
        return {"processed": True, **data}

async def run_benchmark(iterations: int) -> Dict[str, Any]:
    """Run the performance benchmark."""
    # Initialize tracing if enabled
    if TRACING_ENABLED:
        init_tracer("performance_test")
    
    test_data = {"test": "data", "value": 42, "nested": {"a": 1, "b": 2}}
    
    # Warm-up
    for _ in range(10):
        await process_data(test_data)
    
    # Run benchmark
    start_time = time.perf_counter()
    
    tasks = [process_data(test_data) for _ in range(iterations)]
    results = await asyncio.gather(*tasks)
    
    end_time = time.perf_counter()
    
    # Calculate metrics
    total_time = end_time - start_time
    ops_per_second = iterations / total_time
    
    return {
        "total_time": total_time,
        "iterations": iterations,
        "ops_per_second": ops_per_second,
        "avg_time_per_op": total_time / iterations
    }

async def main():
    """Run the performance test with and without tracing."""
    iterations = 1000
    
    print(f"Running performance test with {iterations} iterations...")
    
    # Test without tracing
    global TRACING_ENABLED
    TRACING_ENABLED = False
    no_tracing_result = await run_benchmark(iterations)
    
    # Test with tracing
    TRACING_ENABLED = True
    with_tracing_result = await run_benchmark(iterations)
    
    # Calculate overhead
    overhead_pct = ((with_tracing_result["avg_time_per_op"] / no_tracing_result["avg_time_per_op"]) - 1) * 100
    
    # Print results
    print("\nPerformance Results:")
    print("-" * 40)
    print(f"{'Metric':<25} {'No Tracing':<15} {'With Tracing':<15} {'Overhead':<10}")
    print("-" * 40)
    print(f"{'Total Time (s)':<25} {no_tracing_result['total_time']:<15.4f} {with_tracing_result['total_time']:<15.4f} {overhead_pct:.2f}%")
    print(f"{'Ops/Second':<25} {no_tracing_result['ops_per_second']:<15.0f} {with_tracing_result['ops_per_second']:<15.0f} {overhead_pct:.2f}%")
    print(f"{'Avg Time/Ops (ms)':<25} {no_tracing_result['avg_time_per_op']*1000:<15.4f} {with_tracing_result['avg_time_per_op']*1000:<15.4f} {overhead_pct:.2f}%")
    
    # Print a warning if overhead is too high
    if overhead_pct > 5:
        print("\nWARNING: Tracing overhead exceeds 5%. Consider optimizing the tracing implementation.")
    else:
        print("\nTracing overhead is within acceptable limits.")

if __name__ == "__main__":
    asyncio.run(main())
