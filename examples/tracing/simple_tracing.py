"""
Simple Tracing Example

A self-contained example demonstrating basic distributed tracing concepts
without external dependencies.
"""

import asyncio
import time
import uuid
from dataclasses import dataclass
from enum import Enum
from typing import Any
from typing import Dict
from typing import List
from typing import Optional


# Simple tracing implementation
class SpanKind(Enum):
    INTERNAL = "INTERNAL"
    SERVER = "SERVER"
    CLIENT = "CLIENT"
    PRODUCER = "PRODUCER"
    CONSUMER = "CONSUMER"

@dataclass
class TracingSpan:
    trace_id: str
    span_id: str
    trace_flags: int = 1
    trace_state: Optional[Dict[str, str]] = None

class Span:
    def __init__(self, name: str, kind: SpanKind = SpanKind.INTERNAL, parent: Optional['Span'] = None):
        self.name = name
        self.kind = kind
        self.parent = parent
        self.start_time = time.time()
        self.end_time: Optional[float] = None
        self.attributes: Dict[str, Any] = {}
        self.events: List[Dict[str, Any]] = []
        self.context = TracingSpan(
            trace_id=parent.context.trace_id if parent else str(uuid.uuid4()),
            span_id=str(uuid.uuid4().hex[:16]),
            trace_state=parent.context.trace_state if parent else {}
        )
        if parent:
            self.attributes["parent_span_id"] = parent.context.span_id

    def set_attribute(self, key: str, value: Any) -> None:
        self.attributes[key] = value

    def add_event(self, name: str, attributes: Optional[Dict[str, Any]] = None) -> None:
        self.events.append({
            "name": name,
            "timestamp": time.time(),
            "attributes": attributes or {}
        })

    def end(self) -> None:
        self.end_time = time.time()

    def __enter__(self):
        return self

    def __exit__(self, exc_type, exc_val, exc_tb):
        self.end()

class Tracer:
    def __init__(self, service_name: str):
        self.service_name = service_name
        self.spans: List[Span] = []

    def start_span(self, name: str, kind: SpanKind = SpanKind.INTERNAL, parent: Optional[Span] = None) -> Span:
        span = Span(name, kind, parent)
        self.spans.append(span)
        return span

    def get_current_span(self) -> Optional[Span]:
        return self.spans[-1] if self.spans else None

    def export_spans(self) -> List[Dict[str, Any]]:
        return [
            {
                "name": span.name,
                "span_id": span.context.span_id,
                "trace_id": span.context.trace_id,
                "parent_span_id": span.parent.context.span_id if span.parent else None,
                "start_time": span.start_time,
                "end_time": span.end_time,
                "duration": (span.end_time - span.start_time) * 1000 if span.end_time else None,
                "attributes": span.attributes,
                "events": span.events,
                "kind": span.kind.value
            }
            for span in self.spans
        ]

# Global tracer instance
_global_tracer: Optional[Tracer] = None

def init_tracer(service_name: str) -> None:
    global _global_tracer
    _global_tracer = Tracer(service_name)

def get_tracer() -> Tracer:
    if _global_tracer is None:
        raise RuntimeError("Tracer not initialized. Call init_tracer() first.")
    return _global_tracer

# Example usage
async def process_order(order_id: str, user_id: str) -> Dict[str, Any]:
    tracer = get_tracer()
    
    with tracer.start_span("process_order", SpanKind.SERVER) as span:
        span.set_attribute("order.id", order_id)
        span.set_attribute("user.id", user_id)
        
        # Simulate some work
        await asyncio.sleep(0.1)
        
        # Process payment
        payment_result = await process_payment(order_id, 100.0)
        
        # Process shipping
        shipping_result = await process_shipping(order_id, user_id)
        
        return {
            "order_id": order_id,
            "status": "completed",
            "payment": payment_result,
            "shipping": shipping_result
        }

async def process_payment(order_id: str, amount: float) -> Dict[str, Any]:
    tracer = get_tracer()
    
    with tracer.start_span("process_payment", SpanKind.CLIENT) as span:
        span.set_attribute("order.id", order_id)
        span.set_attribute("payment.amount", amount)
        
        # Simulate payment processing
        await asyncio.sleep(0.05)
        
        return {
            "transaction_id": f"tx_{uuid.uuid4().hex[:8]}",
            "status": "completed",
            "amount_charged": amount
        }

async def process_shipping(order_id: str, user_id: str) -> Dict[str, Any]:
    tracer = get_tracer()
    
    with tracer.start_span("process_shipping", SpanKind.CLIENT) as span:
        span.set_attribute("order.id", order_id)
        span.set_attribute("user.id", user_id)
        
        # Simulate address validation
        with tracer.start_span("validate_address", SpanKind.INTERNAL) as addr_span:
            await asyncio.sleep(0.02)
            addr_span.set_attribute("address.valid", True)
        
        # Simulate shipping cost calculation
        with tracer.start_span("calculate_shipping", SpanKind.INTERNAL) as calc_span:
            await asyncio.sleep(0.03)
            shipping_cost = 5.99
            calc_span.set_attribute("shipping.cost", shipping_cost)
        
        return {
            "tracking_number": f"TN-{uuid.uuid4().hex[:8]}",
            "status": "shipped",
            "shipping_cost": shipping_cost,
            "estimated_delivery": "2025-06-10"
        }

async def main():
    # Initialize the tracer
    init_tracer("order_processing_service")
    
    # Process some orders
    orders = [
        ("order_123", "user_456"),
        ("order_124", "user_789"),
    ]
    
    tasks = [process_order(order_id, user_id) for order_id, user_id in orders]
    results = await asyncio.gather(*tasks)
    
    # Print results
    for result in results:
        print(f"Order processed: {result}")
    
    # Print trace
    tracer = get_tracer()
    print("\nTrace exported spans:")
    for span in tracer.export_spans():
        print(f"- {span['name']} (trace_id={span['trace_id']}, span_id={span['span_id']})")
        print(f"  Parent: {span.get('parent_span_id')}")
        print(f"  Duration: {span['duration']:.2f}ms")
        print(f"  Attributes: {span['attributes']}")
        if span['events']:
            print(f"  Events: {span['events']}")
        print()

if __name__ == "__main__":
    asyncio.run(main())
