# Distributed Tracing Visualization

This example demonstrates a distributed tracing system with a web-based visualization dashboard.

## Features

- Real-time tracing visualization
- Interactive timeline view
- Detailed span inspection
- Support for hierarchical traces
- Web-based UI with WebSocket updates

## Prerequisites

- Python 3.8+
- pip

## Installation

1. Create a virtual environment (recommended):
   ```bash
   python -m venv venv
   source venv/bin/activate  # On Windows: venv\Scripts\activate
   ```

2. Install dependencies:
   ```bash
   pip install -r requirements.txt
   ```

## Running the Example

### 1. Start the Visualization Server

In one terminal, run:

```bash
python tracing_visualization.py
```

This will start the web server at http://localhost:8000

### 2. Generate Example Traces

In another terminal, run:

```bash
python demo.py
```

This will start generating example traces that will appear in the web interface.

## Using the Web Interface

1. Open http://localhost:8000 in your web browser.
2. The timeline will show all active and completed spans.
3. Click on a span to see detailed information.
4. Use the trace list on the right to filter by trace ID.
5. The timeline is interactive - you can zoom and pan.

## Key Components

- `tracing_visualization.py`: The main web application with tracing UI
- `demo.py`: Example application that generates traces
- `requirements.txt`: Python dependencies

## Architecture

The tracing system consists of:

1. **Tracer**: Creates and manages spans
2. **Span**: Represents a single operation in a trace
3. **Web Interface**: Visualizes traces in real-time
4. **WebSocket**: Enables live updates to the UI

## Customization

You can customize the tracing behavior by:

1. Modifying the `Tracer` class in `tracing_visualization.py`
2. Adding custom attributes to spans
3. Creating custom span types
4. Extending the web interface

## License

This example is provided under the MIT License.
