"""
Tracing Visualization Dashboard

A web-based visualization for the tracing system that shows live traces as they happen.
"""

import asyncio
import json
import time
import uuid
from dataclasses import dataclass
from dataclasses import field
from enum import Enum
from typing import Any
from typing import Dict
from typing import List
from typing import Optional
from typing import Set

import uvicorn
from fastapi import <PERSON><PERSON><PERSON>
from fastapi import WebSocket
from fastapi import WebSocketDisconnect
from fastapi.responses import HTMLResponse
from fastapi.staticfiles import StaticFiles

# --- Core Tracing Implementation ---

class SpanKind(str, Enum):
    INTERNAL = "INTERNAL"
    SERVER = "SERVER"
    CLIENT = "CLIENT"
    PRODUCER = "PRODUCER"
    CONSUMER = "CONSUMER"

@dataclass
class TracingSpan:
    trace_id: str
    span_id: str
    trace_flags: int = 1
    trace_state: Optional[Dict[str, str]] = None

@dataclass
class Span:
    name: str
    kind: SpanKind = SpanKind.INTERNAL
    parent: Optional['Span'] = None
    start_time: float = field(default_factory=time.time)
    end_time: Optional[float] = None
    attributes: Dict[str, Any] = field(default_factory=dict)
    events: List[Dict[str, Any]] = field(default_factory=list)
    context: TracingSpan = field(init=False)
    
    def __post_init__(self):
        self.context = TracingSpan(
            trace_id=self.parent.context.trace_id if self.parent else str(uuid.uuid4()),
            span_id=str(uuid.uuid4().hex[:16]),
            trace_state=self.parent.context.trace_state if self.parent else {}
        )
        if self.parent:
            self.attributes["parent_span_id"] = self.parent.context.span_id
    
    def set_attribute(self, key: str, value: Any) -> None:
        self.attributes[key] = value
    
    def add_event(self, name: str, attributes: Optional[Dict[str, Any]] = None) -> None:
        self.events.append({
            "name": name,
            "timestamp": time.time(),
            "attributes": attributes or {}
        })
    
    def end(self) -> None:
        self.end_time = time.time()
    
    def to_dict(self) -> Dict[str, Any]:
        return {
            "name": self.name,
            "span_id": self.context.span_id,
            "trace_id": self.context.trace_id,
            "parent_span_id": self.parent.context.span_id if self.parent else None,
            "start_time": self.start_time,
            "end_time": self.end_time,
            "duration": (self.end_time - self.start_time) * 1000 if self.end_time else None,
            "attributes": self.attributes,
            "events": self.events,
            "kind": self.kind.value,
            "status": "completed" if self.end_time else "active"
        }

class Tracer:
    def __init__(self, service_name: str):
        self.service_name = service_name
        self.spans: List[Span] = []
        self._current_span = None
        self._active_spans: Dict[str, Span] = {}
    
    def start_span(self, name: str, kind: SpanKind = SpanKind.INTERNAL, parent: Optional[Span] = None) -> Span:
        parent_span = parent or self._current_span
        span = Span(name, kind, parent_span)
        self.spans.append(span)
        self._current_span = span
        self._active_spans[span.context.span_id] = span
        return span
    
    def end_span(self, span: Span) -> None:
        span.end()
        if span.context.span_id in self._active_spans:
            del self._active_spans[span.context.span_id]
        if span == self._current_span:
            self._current_span = span.parent
    
    def get_current_span(self) -> Optional[Span]:
        return self._current_span
    
    def get_spans(self) -> List[Dict[str, Any]]:
        return [span.to_dict() for span in self.spans]
    
    def get_active_spans(self) -> List[Dict[str, Any]]:
        return [span.to_dict() for span in self._active_spans.values()]

# --- Web Interface ---

app = FastAPI()
app.mount("/static", StaticFiles(directory="static"), name="static")

# Store active WebSocket connections
active_connections: Set[WebSocket] = set()

# Global tracer instance
_tracer = Tracer("tracing_visualization")

def get_tracer() -> Tracer:
    return _tracer

# WebSocket manager
class ConnectionManager:
    def __init__(self):
        self.active_connections: List[WebSocket] = []

    async def connect(self, websocket: WebSocket):
        await websocket.accept()
        self.active_connections.append(websocket)

    def disconnect(self, websocket: WebSocket):
        self.active_connections.remove(websocket)

    async def broadcast(self, message: str):
        for connection in self.active_connections:
            try:
                await connection.send_text(message)
            except Exception as e:
                print(f"Error broadcasting message: {e}")
                self.disconnect(connection)

manager = ConnectionManager()

# HTML Template
html = """
<!DOCTYPE html>
<html>
<head>
    <title>Live Tracing Dashboard</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/vis/4.21.0/vis.min.css" rel="stylesheet">
    <style>
        body { padding: 20px; }
        #timeline {
            width: 100%;
            height: 300px;
            border: 1px solid #ccc;
            margin-bottom: 20px;
        }
        .span-details {
            margin-top: 20px;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
            background-color: #f9f9f9;
        }
        .trace-list {
            max-height: 400px;
            overflow-y: auto;
        }
        .trace-item {
            cursor: pointer;
            padding: 8px;
            margin: 2px 0;
            border-radius: 4px;
            border-left: 4px solid #0d6efd;
        }
        .trace-item:hover {
            background-color: #f0f7ff;
        }
        .badge {
            margin-right: 5px;
        }
    </style>
</head>
<body>
    <div class="container-fluid">
        <h1 class="mb-4">Live Tracing Dashboard</h1>
        
        <div class="row">
            <div class="col-md-8">
                <div class="card mb-4">
                    <div class="card-header">
                        <h5 class="mb-0">Trace Timeline</h5>
                    </div>
                    <div class="card-body">
                        <div id="timeline"></div>
                    </div>
                </div>
                
                <div class="card">
                    <div class="card-header">
                        <h5 class="mb-0">Span Details</h5>
                    </div>
                    <div class="card-body">
                        <div id="span-details" class="span-details">
                            <p class="text-muted">Select a span to view details</p>
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="col-md-4">
                <div class="card">
                    <div class="card-header d-flex justify-content-between align-items-center">
                        <h5 class="mb-0">Active Traces</h5>
                        <span class="badge bg-primary" id="active-count">0</span>
                    </div>
                    <div class="card-body">
                        <div id="trace-list" class="trace-list">
                            <p class="text-muted">No active traces</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <script src="https://cdn.jsdelivr.net/npm/vis-timeline@7.4.9/dist/vis-timeline-graph2d.min.js"></script>
    <script>
        const ws = new WebSocket(`ws://${window.location.host}/ws`);
        let timeline;
        let items = new vis.DataSet();
        let groups = new vis.DataSet();
        let selectedTraceId = null;
        
        // Initialize timeline
        function initTimeline() {
            const container = document.getElementById('timeline');
            const options = {
                groupOrder: 'content',
                stack: false,
                showCurrentTime: true,
                zoomable: true,
                minHeight: '300px',
                tooltip: {
                    followMouse: true,
                    overflowMethod: 'cap'
                },
                format: {
                    minorLabels: {
                        millisecond: 'SSS',
                        second: 's',
                        minute: 'HH:mm',
                        hour: 'HH:mm',
                        weekday: 'ddd D',
                        day: 'D',
                        week: 'w',
                        month: 'MMM',
                        year: 'YYYY'
                    },
                    majorLabels: {
                        millisecond: 'HH:mm:ss',
                        second: 'D MMMM YYYY',
                        minute: 'D MMMM YYYY',
                        hour: 'D MMMM YYYY',
                        weekday: 'MMMM YYYY',
                        day: 'MMMM YYYY',
                        week: 'MMMM YYYY',
                        month: 'YYYY',
                        year: ''
                    }
                },
                margin: {
                    item: {
                        horizontal: 0,
                        vertical: 5
                    }
                }
            };
            
            timeline = new vis.Timeline(container, items, groups, options);
            
            // Handle selection
            timeline.on('select', function(properties) {
                const selectedIds = properties.items;
                if (selectedIds.length > 0) {
                    const selectedId = selectedIds[0];
                    const item = items.get(selectedId);
                    showSpanDetails(item);
                }
            });
        }
        
        // Show span details
        function showSpanDetails(span) {
            const detailsDiv = document.getElementById('span-details');
            
            let html = `
                <h5>${span.content}</h5>
                <p><strong>Trace ID:</strong> ${span.trace_id}</p>
                <p><strong>Span ID:</strong> ${span.id}</p>
                <p><strong>Parent ID:</strong> ${span.parent_span_id || 'None'}</p>
                <p><strong>Duration:</strong> ${span.duration ? span.duration.toFixed(2) + 'ms' : 'In progress'}</p>
                <p><strong>Kind:</strong> ${span.kind}</p>
                <p><strong>Status:</strong> ${span.status}</p>
            `;
            
            if (Object.keys(span.attributes).length > 0) {
                html += '<div class="mt-3"><h6>Attributes:</h6><pre class="bg-light p-2">' + 
                    JSON.stringify(span.attributes, null, 2) + '</pre></div>';
            }
            
            if (span.events && span.events.length > 0) {
                html += '<div class="mt-3"><h6>Events:</h6><pre class="bg-light p-2">' + 
                    JSON.stringify(span.events, null, 2) + '</pre></div>';
            }
            
            detailsDiv.innerHTML = html;
        }
        
        // Update trace list
        function updateTraceList(traces) {
            const traceList = document.getElementById('trace-list');
            const activeCount = document.getElementById('active-count');
            
            if (traces.length === 0) {
                traceList.innerHTML = '<p class="text-muted">No active traces</p>';
                activeCount.textContent = '0';
                return;
            }
            
            // Group spans by trace
            const tracesMap = new Map();
            traces.forEach(span => {
                if (!tracesMap.has(span.trace_id)) {
                    tracesMap.set(span.trace_id, []);
                }
                tracesMap.get(span.trace_id).push(span);
            });
            
            // Update active count
            activeCount.textContent = tracesMap.size;
            
            // Build trace list
            let html = '';
            tracesMap.forEach((spans, traceId) => {
                const rootSpan = spans.find(s => !s.parent_span_id) || spans[0];
                const duration = spans.some(s => s.status === 'active') ? 
                    'Active' : 
                    `${Math.max(...spans.map(s => s.duration || 0)).toFixed(2)}ms`;
                
                html += `
                    <div class="trace-item" data-trace-id="${traceId}">
                        <div class="d-flex justify-content-between">
                            <strong>${rootSpan.name}</strong>
                            <span class="badge bg-secondary">${spans.length} spans</span>
                        </div>
                        <div class="text-muted small">
                            ${traceId.substring(0, 8)}... • ${duration}
                        </div>
                    </div>
                `;
            });
            
            traceList.innerHTML = html;
            
            // Add click handlers
            document.querySelectorAll('.trace-item').forEach(item => {
                item.addEventListener('click', () => {
                    const traceId = item.getAttribute('data-trace-id');
                    filterByTrace(traceId);
                });
            });
        }
        
        // Filter timeline by trace
        function filterByTrace(traceId) {
            selectedTraceId = traceId;
            const filteredItems = items.get({
                filter: item => item.trace_id === traceId
            });
            
            // Update groups
            const groupIds = new Set(filteredItems.map(item => item.group));
            const filteredGroups = groups.get({
                filter: group => groupIds.has(group.id)
            });
            
            // Update timeline
            timeline.setItems(filteredItems);
            timeline.setGroups(filteredGroups);
            
            // Fit to items
            if (filteredItems.length > 0) {
                timeline.fit({
                    animation: {
                        duration: 1000,
                        easingFunction: 'easeInOutQuart'
                    }
                });
            }
        }
        
        // Handle WebSocket messages
        ws.onmessage = function(event) {
            const data = JSON.parse(event.data);
            
            if (data.type === 'spans') {
                // Update items
                data.spans.forEach(span => {
                    const id = span.span_id;
                    const existing = items.get(id);
                    
                    const item = {
                        id: id,
                        group: span.trace_id,
                        content: span.name,
                        start: new Date(span.start_time * 1000),
                        className: `span-kind-${span.kind.toLowerCase()}`,
                        trace_id: span.trace_id,
                        parent_span_id: span.parent_span_id,
                        duration: span.duration,
                        kind: span.kind,
                        status: span.status,
                        attributes: span.attributes,
                        events: span.events
                    };
                    
                    if (span.end_time) {
                        item.end = new Date(span.end_time * 1000);
                        item.type = 'range';
                    } else {
                        item.type = 'point';
                    }
                    
                    if (existing) {
                        items.update(item);
                    } else {
                        items.add(item);
                        
                        // Add group if it doesn't exist
                        if (!groups.get(span.trace_id)) {
                            groups.add({
                                id: span.trace_id,
                                content: `Trace: ${span.trace_id.substring(0, 8)}...`
                            });
                        }
                    }
                });
                
                // Update trace list
                updateTraceList(data.spans);
                
                // If a trace is selected, update the filter
                if (selectedTraceId) {
                    filterByTrace(selectedTraceId);
                } else if (data.spans.length > 0) {
                    // Auto-select the first trace
                    filterByTrace(data.spans[0].trace_id);
                }
            }
        };
        
        // Initialize when the page loads
        document.addEventListener('DOMContentLoaded', initTimeline);
    </script>
</body>
</html>
"""

@app.get("/")
async def get():
    return HTMLResponse(html)

# WebSocket endpoint
@app.websocket("/ws")
async def websocket_endpoint(websocket: WebSocket):
    await manager.connect(websocket)
    try:
        while True:
            # Keep the connection alive
            await asyncio.sleep(10)
    except WebSocketDisconnect:
        manager.disconnect(websocket)

# Background task to send updates
async def send_updates():
    while True:
        try:
            spans = _tracer.get_spans()
            active_spans = _tracer.get_active_spans()
            
            # Send all spans (both active and completed)
            await manager.broadcast(json.dumps({
                "type": "spans",
                "spans": spans + active_spans
            }))
            
            await asyncio.sleep(0.5)  # Update twice per second
        except Exception as e:
            print(f"Error in send_updates: {e}")
            await asyncio.sleep(1)

# Example usage
async def example_trace():
    tracer = get_tracer()
    
    while True:
        with tracer.start_span("root_operation", SpanKind.SERVER) as root_span:
            root_span.set_attribute("user.id", "user123")
            
            # Simulate some work
            await asyncio.sleep(0.1)
            
            # Nested span
            with tracer.start_span("database_query"):
                await asyncio.sleep(0.2)
                
            # Another nested span
            with tracer.start_span("external_api_call"):
                await asyncio.sleep(0.15)
                
                # Nested under external_api_call
                with tracer.start_span("process_response"):
                    await asyncio.sleep(0.05)
        
        # Wait before starting a new trace
        await asyncio.sleep(1)

# Start the background tasks
@app.on_event("startup")
async def startup_event():
    asyncio.create_task(send_updates())
    asyncio.create_task(example_trace())

if __name__ == "__main__":
    import uvicorn
    uvicorn.run(app, host="0.0.0.0", port=8000)
