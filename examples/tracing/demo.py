"""
Tracing System Demo

This script demonstrates the enhanced tracing system with a simple workflow.
Run this along with the tracing_visualization.py to see live traces.
"""

import asyncio
import random
from datetime import datetime
from typing import Any
from typing import Dict

from tracing_visualization import Span<PERSON>ind
from tracing_visualization import Tracer

# Global tracer instance
tracer = Tracer("demo-service")

async def process_order(order_id: str, user_id: str) -> Dict[str, Any]:
    """Process an order with tracing."""
    with tracer.start_span("process_order", SpanKind.SERVER) as span:
        span.set_attribute("order.id", order_id)
        span.set_attribute("user.id", user_id)
        
        # Simulate some work
        await asyncio.sleep(random.uniform(0.1, 0.3))
        
        # Process payment
        payment_result = await process_payment(order_id, 100.0)
        
        # Process shipping
        shipping_result = await process_shipping(order_id, user_id)
        
        return {
            "order_id": order_id,
            "status": "completed",
            "payment": payment_result,
            "shipping": shipping_result
        }

async def process_payment(order_id: str, amount: float) -> Dict[str, Any]:
    """Process payment with tracing."""
    with tracer.start_span("process_payment", SpanKind.CLIENT) as span:
        span.set_attribute("order.id", order_id)
        span.set_attribute("payment.amount", amount)
        
        # Simulate payment processing
        await asyncio.sleep(random.uniform(0.1, 0.2))
        
        # Randomly fail 10% of the time
        if random.random() < 0.1:
            span.set_attribute("error", "Payment failed")
            raise Exception("Payment processing failed")
        
        return {
            "transaction_id": f"tx_{order_id}",
            "status": "completed",
            "amount_charged": amount
        }

async def process_shipping(order_id: str, user_id: str) -> Dict[str, Any]:
    """Process shipping with tracing."""
    with tracer.start_span("process_shipping", SpanKind.CLIENT) as span:
        span.set_attribute("order.id", order_id)
        span.set_attribute("user.id", user_id)
        
        # Simulate address validation
        with tracer.start_span("validate_address") as addr_span:
            await asyncio.sleep(random.uniform(0.05, 0.1))
            addr_span.set_attribute("address.valid", True)
        
        # Simulate shipping cost calculation
        with tracer.start_span("calculate_shipping") as calc_span:
            await asyncio.sleep(random.uniform(0.05, 0.1))
            shipping_cost = round(random.uniform(5.0, 15.0), 2)
            calc_span.set_attribute("shipping.cost", shipping_cost)
        
        return {
            "tracking_number": f"TN-{order_id}",
            "status": "shipped",
            "shipping_cost": shipping_cost,
            "estimated_delivery": (datetime.now() + timedelta(days=3)).strftime("%Y-%m-%d")
        }

async def generate_traces():
    """Generate example traces."""
    order_id = 1000
    
    while True:
        try:
            user_id = f"user_{random.randint(1, 1000)}"
            print(f"Processing order {order_id} for user {user_id}")
            
            result = await process_order(str(order_id), user_id)
            print(f"Order {order_id} completed: {result['status']}")
            
            order_id += 1
            await asyncio.sleep(random.uniform(0.5, 2.0))
            
        except Exception as e:
            print(f"Error processing order {order_id}: {e}")
            await asyncio.sleep(1)

if __name__ == "__main__":
    from datetime import timedelta
    
    # Start the demo
    try:
        asyncio.run(generate_traces())
    except KeyboardInterrupt:
        print("\nShutting down...")
