#!/usr/bin/env python3
"""
Memory Consolidation System Demo

This script demonstrates the functionality of the Memory Consolidation System,
showing how it processes memories across different layers, strengthens important
memories, extracts semantic knowledge, creates schemas, and summarizes episodic
memories into higher-level autobiographical memories.
"""

import asyncio
import logging
from datetime import datetime
from datetime import timed<PERSON><PERSON>
from typing import Dict
from typing import List
from typing import Optional

# Set up logging
logging.basicConfig(
    level=logging.INFO, format="%(asctime)s - %(name)s - %(levelname)s - %(message)s"
)
logger = logging.getLogger("memory_consolidation_demo")

# Import memory classes
from person_suit.meta_systems.persona_core.core.memory.interfaces import Memory
from person_suit.meta_systems.persona_core.core.memory.interfaces import Memory<PERSON>ayerInterface
from person_suit.meta_systems.persona_core.core.memory.interfaces import MemoryLayerType

# Import consolidation system
from person_suit.meta_systems.persona_core.memory.consolidation import ConsolidationConfig
from person_suit.meta_systems.persona_core.memory.consolidation import ConsolidationStrategy
from person_suit.meta_systems.persona_core.memory.consolidation import ConsolidationTrigger
from person_suit.meta_systems.persona_core.memory.consolidation import MemoryConsolidationSystem


class MockMemoryLayer(MemoryLayerInterface):
    """Mock memory layer for demonstration purposes."""

    def __init__(self, layer_type: MemoryLayerType):
        self.layer_type = layer_type
        self.memories = {}  # id -> memory
        logger.info(f"Created MockMemoryLayer for {layer_type.name}")

    async def store_memory(self, memory: Memory) -> str:
        """Store a memory and return its ID."""
        if not hasattr(memory, "id") or not memory.id:
            memory.id = f"{self.layer_type.name.lower()}_mem_{len(self.memories)}"
        memory.last_accessed = datetime.now()
        self.memories[memory.id] = memory
        logger.debug(f"Stored memory {memory.id} in {self.layer_type.name}")
        return memory.id

    async def get_memory(self, memory_id: str) -> Optional[Memory]:
        """Get a memory by ID."""
        memory = self.memories.get(memory_id)
        if memory:
            memory.last_accessed = datetime.now()
        return memory

    async def update_memory(self, memory: Memory) -> bool:
        """Update an existing memory."""
        if hasattr(memory, "id") and memory.id in self.memories:
            memory.last_accessed = datetime.now()
            self.memories[memory.id] = memory
            logger.debug(f"Updated memory {memory.id} in {self.layer_type.name}")
            return True
        return False

    async def delete_memory(self, memory_id: str) -> bool:
        """Delete a memory by ID."""
        if memory_id in self.memories:
            del self.memories[memory_id]
            logger.debug(f"Deleted memory {memory_id} from {self.layer_type.name}")
            return True
        return False

    async def get_memories_by_ids(self, memory_ids: List[str]) -> List[Memory]:
        """Get multiple memories by their IDs."""
        result = []
        for mem_id in memory_ids:
            memory = self.memories.get(mem_id)
            if memory:
                memory.last_accessed = datetime.now()
                result.append(memory)
        return result

    async def get_all_memories(
        self, limit: int = 100, sort_by: str = "last_accessed", sort_order: str = "desc"
    ) -> List[Memory]:
        """Get all memories with sorting options."""
        memories = list(self.memories.values())

        # Sort memories
        reverse = sort_order.lower() == "desc"
        if sort_by == "last_accessed":
            memories.sort(
                key=lambda m: getattr(m, "last_accessed", datetime.min), reverse=reverse
            )
        elif sort_by == "importance":
            memories.sort(key=lambda m: getattr(m, "importance", 0.0), reverse=reverse)
        elif sort_by == "creation_time":
            memories.sort(
                key=lambda m: getattr(m, "creation_time", datetime.min), reverse=reverse
            )

        # Apply limit
        memories = memories[:limit]

        # Update last_accessed
        for memory in memories:
            memory.last_accessed = datetime.now()

        return memories

    async def get_memories_by_type(
        self, memory_type: str, limit: int = 100
    ) -> List[Memory]:
        """Get memories by type."""
        result = []
        for memory in self.memories.values():
            if getattr(memory, "type", "") == memory_type:
                memory.last_accessed = datetime.now()
                result.append(memory)
                if len(result) >= limit:
                    break
        return result

    async def search(
        self,
        query: str,
        memory_type: Optional[str] = None,
        limit: int = 10,
        semantic_search: bool = False,
    ) -> List[Memory]:
        """Search memories by content."""
        result = []
        for memory in self.memories.values():
            if memory_type and getattr(memory, "type", "") != memory_type:
                continue

            # Simple substring search for demo purposes
            if hasattr(memory, "content") and query.lower() in memory.content.lower():
                memory.last_accessed = datetime.now()
                result.append(memory)
                if len(result) >= limit:
                    break
        return result

    async def search_by_vector(
        self,
        vector: List[float],
        memory_type: Optional[str] = None,
        limit: int = 10,
        similarity_threshold: float = 0.0,
    ) -> List[Memory]:
        """Search memories by vector similarity."""
        # For demo purposes, return random memories of the right type
        result = []
        for memory in self.memories.values():
            if memory_type and getattr(memory, "type", "") != memory_type:
                continue

            if hasattr(memory, "embedding") and memory.embedding is not None:
                # In a real implementation, this would calculate vector similarity
                # For demo purposes, we'll just return any memory with an embedding
                memory.last_accessed = datetime.now()
                result.append(memory)
                if len(result) >= limit:
                    break
        return result


class MockConsolidationManager:
    """Mock consolidation manager for demo purposes."""

    def __init__(self, memory_layers: Dict[MemoryLayerType, MemoryLayerInterface]):
        self.memory_layers = memory_layers
        self.consolidation_interval = 3600  # 1 hour
        self._running = False

    async def initialize(self) -> None:
        """Initialize the consolidation manager."""
        logger.info("MockConsolidationManager initialized")

    async def start_background_consolidation(self) -> None:
        """Start background consolidation processing."""
        self._running = True
        logger.info("MockConsolidationManager started background consolidation")

    async def stop_background_consolidation(self) -> None:
        """Stop background consolidation processing."""
        self._running = False
        logger.info("MockConsolidationManager stopped background consolidation")

    async def select_memories_by_importance(
        self, limit: int = 10, importance_threshold: float = 0.5
    ) -> List[Memory]:
        """Select memories based on importance."""
        result = []

        # Get memories from long-term memory
        if MemoryLayerType.LONG_TERM in self.memory_layers:
            ltm = self.memory_layers[MemoryLayerType.LONG_TERM]
            memories = await ltm.get_all_memories(
                limit=100, sort_by="importance", sort_order="desc"
            )

            # Filter by importance
            for memory in memories:
                if getattr(memory, "importance", 0.0) >= importance_threshold:
                    result.append(memory)
                    if len(result) >= limit:
                        break

        return result

    async def select_memories_by_emotional_significance(
        self, limit: int = 10, emotional_threshold: float = 0.5
    ) -> List[Memory]:
        """Select memories based on emotional significance."""
        result = []

        # Get memories from long-term memory
        if MemoryLayerType.LONG_TERM in self.memory_layers:
            ltm = self.memory_layers[MemoryLayerType.LONG_TERM]
            memories = await ltm.get_all_memories(limit=100)

            # Filter by emotional significance
            for memory in memories:
                if (
                    getattr(memory, "emotional_significance", 0.0)
                    >= emotional_threshold
                ):
                    result.append(memory)
                    if len(result) >= limit:
                        break

        return result


def create_sample_memories() -> Dict[str, Memory]:
    """Create sample memories for consolidation."""
    now = datetime.now()
    yesterday = now - timedelta(days=1)
    last_week = now - timedelta(days=7)

    memories = {}

    # Create working memory items (recent experiences)
    memories["working_1"] = Memory(
        id="wmem_1",
        type="episodic",
        content="Had coffee with Alex at Starbucks and discussed the project deadline",
        creation_time=now - timedelta(hours=3),
        timestamp=now - timedelta(hours=3),
        last_accessed=now - timedelta(minutes=30),
        location="Starbucks Downtown",
        participants=["self", "Alex"],
        importance=0.7,
        emotional_significance=0.6,
        entities=["Alex", "Starbucks", "project", "deadline"],
        embedding=[0.1, 0.2, 0.3, 0.4],  # Simplified embedding
    )

    memories["working_2"] = Memory(
        id="wmem_2",
        type="episodic",
        content="Received email from boss about the quarterly review",
        creation_time=now - timedelta(hours=2),
        timestamp=now - timedelta(hours=2),
        last_accessed=now - timedelta(minutes=15),
        location="Home Office",
        participants=["self", "boss"],
        importance=0.8,
        emotional_significance=0.7,
        entities=["boss", "email", "quarterly review"],
        embedding=[0.2, 0.3, 0.4, 0.5],  # Simplified embedding
    )

    memories["working_3"] = Memory(
        id="wmem_3",
        type="episodic",
        content="Watched documentary about climate change",
        creation_time=now - timedelta(hours=4),
        timestamp=now - timedelta(hours=5),
        last_accessed=now - timedelta(hours=1),
        location="Living Room",
        participants=["self"],
        importance=0.5,
        emotional_significance=0.4,
        entities=["documentary", "climate change"],
        embedding=[0.3, 0.4, 0.5, 0.6],  # Simplified embedding
    )

    # Create long-term memory items (older experiences)
    memories["longterm_1"] = Memory(
        id="ltmem_1",
        type="episodic",
        content="First day at new job, met the team",
        creation_time=last_week,
        timestamp=last_week,
        last_accessed=yesterday,
        location="Office Building",
        participants=["self", "team", "boss"],
        importance=0.9,
        emotional_significance=0.8,
        entities=["job", "team", "first day", "boss"],
        embedding=[0.4, 0.5, 0.6, 0.7],  # Simplified embedding
    )

    memories["longterm_2"] = Memory(
        id="ltmem_2",
        type="episodic",
        content="Team lunch at Italian restaurant",
        creation_time=last_week + timedelta(days=1),
        timestamp=last_week + timedelta(days=1),
        last_accessed=last_week + timedelta(days=2),
        location="Guido's Italian Restaurant",
        participants=["self", "team", "Alex", "Sarah"],
        importance=0.6,
        emotional_significance=0.7,
        entities=["team", "lunch", "restaurant", "Alex", "Sarah"],
        embedding=[0.5, 0.6, 0.7, 0.8],  # Simplified embedding
    )

    memories["longterm_3"] = Memory(
        id="ltmem_3",
        type="episodic",
        content="Meeting with Alex about project requirements",
        creation_time=last_week + timedelta(days=2),
        timestamp=last_week + timedelta(days=2, hours=3),
        last_accessed=last_week + timedelta(days=3),
        location="Conference Room B",
        participants=["self", "Alex"],
        importance=0.7,
        emotional_significance=0.5,
        entities=["Alex", "meeting", "project", "requirements"],
        embedding=[0.6, 0.7, 0.8, 0.9],  # Simplified embedding
    )

    # Create some semantic memories
    memories["semantic_1"] = Memory(
        id="smem_1",
        type="semantic",
        content="Alex is the lead developer on the project",
        creation_time=last_week,
        confidence=0.9,
        importance=0.7,
        entities=["Alex", "lead developer", "project"],
        embedding=[0.1, 0.3, 0.5, 0.7],  # Simplified embedding
    )

    memories["semantic_2"] = Memory(
        id="smem_2",
        type="semantic",
        content="The quarterly review happens every three months",
        creation_time=last_week,
        confidence=0.95,
        importance=0.6,
        entities=["quarterly review", "three months"],
        embedding=[0.2, 0.4, 0.6, 0.8],  # Simplified embedding
    )

    return memories


async def demo_memory_consolidation():
    """Run a demonstration of the memory consolidation system."""
    logger.info("Starting Memory Consolidation Demo")

    # Create sample data
    sample_memories = create_sample_memories()

    # Create memory layers
    memory_layers = {
        MemoryLayerType.WORKING: MockMemoryLayer(MemoryLayerType.WORKING),
        MemoryLayerType.LONG_TERM: MockMemoryLayer(MemoryLayerType.LONG_TERM),
        MemoryLayerType.SEMANTIC: MockMemoryLayer(MemoryLayerType.SEMANTIC),
    }

    # Store memories in appropriate layers
    for mem_id, memory in sample_memories.items():
        if "working" in mem_id:
            await memory_layers[MemoryLayerType.WORKING].store_memory(memory)
        elif "semantic" in mem_id:
            await memory_layers[MemoryLayerType.SEMANTIC].store_memory(memory)
        else:
            await memory_layers[MemoryLayerType.LONG_TERM].store_memory(memory)

    # Create consolidation manager
    consolidation_manager = MockConsolidationManager(memory_layers)
    await consolidation_manager.initialize()

    # Create consolidation configuration
    config = ConsolidationConfig(
        enabled=True,
        consolidation_interval=3600,  # 1 hour
        default_strategy=ConsolidationStrategy.HYBRID,
        verify_coherence=True,
        max_memories_per_consolidation=20,
        importance_threshold=0.6,
        emotional_threshold=0.6,
        strengthening_threshold=0.5,
        sleep_duration_seconds=7200,  # 2 hours
        contradiction_resolution=True,
        use_m3_optimizations=True,
    )

    # Create consolidation system
    consolidation_system = MemoryConsolidationSystem(
        config=config,
        memory_layers=memory_layers,
        consolidation_manager=consolidation_manager,
    )

    # Initialize and start the consolidation system
    await consolidation_system.initialize()
    await consolidation_system.start()

    # Show initial state
    logger.info("Initial memory counts:")
    logger.info(
        f"  Working Memory: {len(memory_layers[MemoryLayerType.WORKING].memories)}"
    )
    logger.info(
        f"  Long-Term Memory: {len(memory_layers[MemoryLayerType.LONG_TERM].memories)}"
    )
    logger.info(
        f"  Semantic Memory: {len(memory_layers[MemoryLayerType.SEMANTIC].memories)}"
    )

    # Run explicit consolidation
    logger.info("\nRunning explicit consolidation with HYBRID strategy...")
    results = await consolidation_system.consolidate(
        trigger=ConsolidationTrigger.EXPLICIT, strategy=ConsolidationStrategy.HYBRID
    )

    # Show results
    logger.info("Consolidation results:")
    logger.info(f"  Status: {results['status']}")
    logger.info(f"  Memories Processed: {results['memories_processed']}")
    logger.info(f"  Elapsed Time: {results['elapsed_ms']:.2f}ms")

    if "results" in results:
        detail = results["results"]
        logger.info(
            f"  Strengthened Memories: {detail.get('strengthened_memories', 0)}"
        )
        logger.info(f"  Semantic Extractions: {detail.get('semantic_extractions', 0)}")
        logger.info(f"  Schema Integrations: {detail.get('schema_integrations', 0)}")
        logger.info(f"  Episodic Summaries: {detail.get('episodic_summaries', 0)}")

    # Show post-consolidation state
    logger.info("\nPost-consolidation memory counts:")
    logger.info(
        f"  Working Memory: {len(memory_layers[MemoryLayerType.WORKING].memories)}"
    )
    logger.info(
        f"  Long-Term Memory: {len(memory_layers[MemoryLayerType.LONG_TERM].memories)}"
    )
    logger.info(
        f"  Semantic Memory: {len(memory_layers[MemoryLayerType.SEMANTIC].memories)}"
    )

    # Run sleep consolidation
    logger.info("\nStarting sleep consolidation (short demo duration)...")
    await consolidation_system.start_sleep_consolidation(
        duration=10.0
    )  # Short duration for demo

    # Wait for sleep consolidation to complete
    while consolidation_system._sleeping:
        await asyncio.sleep(1)
        logger.info("Sleep consolidation in progress...")

    # Show final state
    logger.info("\nFinal memory counts after sleep consolidation:")
    logger.info(
        f"  Working Memory: {len(memory_layers[MemoryLayerType.WORKING].memories)}"
    )
    logger.info(
        f"  Long-Term Memory: {len(memory_layers[MemoryLayerType.LONG_TERM].memories)}"
    )
    logger.info(
        f"  Semantic Memory: {len(memory_layers[MemoryLayerType.SEMANTIC].memories)}"
    )

    # Show statistics
    stats = await consolidation_system.get_statistics()
    logger.info("\nConsolidation System Statistics:")
    logger.info(f"  Total Consolidations: {stats['total_consolidations']}")
    logger.info(f"  Memories Consolidated: {stats['memories_consolidated']}")
    logger.info(f"  Sleep Cycles Completed: {stats['sleep_cycles_completed']}")
    logger.info(f"  Semantic Extractions: {stats['semantic_extractions']}")
    logger.info(f"  Schema Integrations: {stats['schema_integrations']}")
    logger.info(
        f"  Average Consolidation Time: {stats['average_consolidation_time_ms']:.2f}ms"
    )

    # Stop the consolidation system
    await consolidation_system.stop()
    logger.info("\nDemo Complete")


if __name__ == "__main__":
    asyncio.run(demo_memory_consolidation())
