#!/usr/bin/env python3
"""
Episodic Future Thinking (MEM-EFT) Usage Example

This script demonstrates how to use the MEM-EFT system to construct future scenarios
based on episodic memory elements. It shows the entire process from initialization
to scenario construction, validation, and optimization.
"""

import asyncio
import logging
from typing import Any
from typing import Dict
from typing import List

from person_suit.meta_systems.persona_core.memory.specialized.episodic_future import EpisodicElement
from person_suit.meta_systems.persona_core.memory.specialized.episodic_future import (
    EpisodicFutureThinkingSystem,
)
from person_suit.meta_systems.persona_core.memory.specialized.episodic_future import FutureScenario
from person_suit.meta_systems.persona_core.memory.specialized.episodic_future import (
    TemporalDistance,
)
from person_suit.meta_systems.persona_core.memory.specialized.episodic_future import (
    TemporalProjection,
)

# Set up logging
logging.basicConfig(
    level=logging.INFO, format="%(asctime)s - %(name)s - %(levelname)s - %(message)s"
)
logger = logging.getLogger("eft_example")


# Mock function to simulate retrieving episodic memories
async def get_mock_episodic_elements() -> List[EpisodicElement]:
    """
    Generate mock episodic elements for testing.

    Returns:
        List of mock episodic elements
    """
    # Create sample elements of different types
    elements = []

    # Spatial settings
    elements.append(
        EpisodicElement(
            element_id="place_1",
            element_type="spatial_setting",
            content={
                "location": "Home Office",
                "description": "A cozy room with a desk, computer, and bookshelves",
                "lighting": "Bright natural light from windows",
                "atmosphere": "Quiet and productive",
            },
            metadata={
                "familiarity": 0.9,
                "frequency": 0.8,
                "last_accessed": "2023-04-15",
            },
        )
    )

    elements.append(
        EpisodicElement(
            element_id="place_2",
            element_type="spatial_setting",
            content={
                "location": "Coffee Shop",
                "description": "Busy cafe with wooden tables and the aroma of coffee",
                "lighting": "Warm ambient lighting",
                "atmosphere": "Lively but not too loud",
            },
            metadata={
                "familiarity": 0.7,
                "frequency": 0.4,
                "last_accessed": "2023-04-10",
            },
        )
    )

    # Agents/People
    elements.append(
        EpisodicElement(
            element_id="person_1",
            element_type="agent",
            content={
                "name": "Alex",
                "role": "Colleague",
                "appearance": "Tall with glasses and usually wearing blue",
                "personality": "Detail-oriented and thoughtful",
            },
            metadata={
                "familiarity": 0.8,
                "frequency": 0.6,
                "last_accessed": "2023-04-12",
            },
        )
    )

    elements.append(
        EpisodicElement(
            element_id="person_2",
            element_type="agent",
            content={
                "name": "Sam",
                "role": "Friend",
                "appearance": "Medium height with curly hair",
                "personality": "Energetic and creative",
            },
            metadata={
                "familiarity": 0.9,
                "frequency": 0.5,
                "last_accessed": "2023-04-05",
            },
        )
    )

    # Objects
    elements.append(
        EpisodicElement(
            element_id="object_1",
            element_type="object",
            content={
                "name": "Laptop",
                "description": "Silver laptop with stickers on the cover",
                "function": "Work and communication",
            },
            metadata={
                "familiarity": 0.95,
                "frequency": 0.9,
                "last_accessed": "2023-04-15",
            },
        )
    )

    elements.append(
        EpisodicElement(
            element_id="object_2",
            element_type="object",
            content={
                "name": "Coffee Mug",
                "description": "Large ceramic mug with a mountain design",
                "function": "Drinking coffee or tea",
            },
            metadata={
                "familiarity": 0.8,
                "frequency": 0.7,
                "last_accessed": "2023-04-15",
            },
        )
    )

    # Events
    elements.append(
        EpisodicElement(
            element_id="event_1",
            element_type="event",
            content={
                "description": "Brainstorming session with team",
                "outcome": "Generated several new project ideas",
                "emotional_valence": "Positive",
                "participants": ["person_1", "self"],
            },
            metadata={
                "familiarity": 0.7,
                "frequency": 0.3,
                "last_accessed": "2023-04-01",
            },
        )
    )

    elements.append(
        EpisodicElement(
            element_id="event_2",
            element_type="event",
            content={
                "description": "Morning coffee routine",
                "outcome": "Feeling energized to start the day",
                "emotional_valence": "Positive",
                "participants": ["self"],
            },
            metadata={
                "familiarity": 0.9,
                "frequency": 0.8,
                "last_accessed": "2023-04-15",
            },
        )
    )

    elements.append(
        EpisodicElement(
            element_id="event_3",
            element_type="event",
            content={
                "description": "Discussing project with Alex",
                "outcome": "Resolved technical issues",
                "emotional_valence": "Neutral",
                "participants": ["person_1", "self"],
            },
            metadata={
                "familiarity": 0.8,
                "frequency": 0.4,
                "last_accessed": "2023-04-10",
            },
        )
    )

    # Emotions
    elements.append(
        EpisodicElement(
            element_id="emotion_1",
            element_type="emotion",
            content={
                "type": "Satisfaction",
                "intensity": 0.7,
                "trigger": "Completing a challenging task",
                "bodily_sensations": "Relaxed shoulders, slight smile",
            },
            metadata={
                "familiarity": 0.8,
                "frequency": 0.6,
                "last_accessed": "2023-04-12",
            },
        )
    )

    elements.append(
        EpisodicElement(
            element_id="emotion_2",
            element_type="emotion",
            content={
                "type": "Excitement",
                "intensity": 0.8,
                "trigger": "Starting a new project",
                "bodily_sensations": "Increased heart rate, energetic feeling",
            },
            metadata={
                "familiarity": 0.7,
                "frequency": 0.4,
                "last_accessed": "2023-04-08",
            },
        )
    )

    # Return all elements
    return elements


# Mock memory access function
async def mock_memory_access(
    query: Dict[str, Any], context: Dict[str, Any]
) -> List[EpisodicElement]:
    """
    Mock function to simulate memory access.

    Args:
        query: Search query
        context: Memory access context

    Returns:
        List of elements
    """
    logger.info(f"Memory access with query: {query}")
    return await get_mock_episodic_elements()


# Main example function
async def run_eft_example():
    """Run the MEM-EFT example."""
    logger.info("Starting MEM-EFT example")

    # Initialize EFT system
    eft_system = EpisodicFutureThinkingSystem(
        config={
            "construction_engine": {
                "perform_validation": True,
                "perform_optimization": True,
                "validator": {
                    "plausibility_threshold": 0.4,
                    "coherence_threshold": 0.4,
                },
                "optimizer": {"max_iterations": 2, "min_improvement": 0.05},
            }
        }
    )

    # Register mock memory access function
    eft_system.register_memory_access_function(mock_memory_access)

    # Setup query for a work-related future scenario
    query = {
        "theme": "work collaboration",
        "goal": "Complete project with colleague",
        "context": "Professional environment",
        "mood": "Productive",
    }

    # Create temporal projection for near future (tomorrow)
    temporal_projection = TemporalProjection(
        distance=TemporalDistance.NEAR, timeframe="tomorrow", reference_point="now"
    )

    # Create constraints
    constraints = {
        "required_element_types": ["spatial_setting", "agent", "event"],
        "emotional_valence": "positive",
        "minimum_coherence": 0.5,
    }

    # Construct a future scenario
    logger.info("Constructing single future scenario")
    scenario = await eft_system.construct_future_scenario(
        query=query, temporal_projection=temporal_projection, constraints=constraints
    )

    # Display the constructed scenario
    print_scenario(scenario)

    # Construct multiple alternative scenarios
    logger.info("Constructing multiple alternative scenarios")
    scenarios = await eft_system.construct_alternative_scenarios(
        query=query,
        temporal_projection=temporal_projection,
        num_scenarios=2,
        constraints=constraints,
    )

    # Display alternative scenarios
    for i, alt_scenario in enumerate(scenarios):
        print(f"\n--- Alternative Scenario {i + 1} ---")
        print_scenario(alt_scenario)

    logger.info("MEM-EFT example completed")


def print_scenario(scenario: FutureScenario):
    """Print a formatted representation of a scenario."""
    print(f"\n=== Future Scenario: {scenario.title} ===")
    print(f"Description: {scenario.description}")
    print(f"Timeframe: {scenario.temporal_projection.timeframe}")

    print("\nSpatial Setting:")
    if scenario.spatial_setting:
        for key, value in scenario.spatial_setting.items():
            print(f"  - {key}: {value}")
    else:
        print("  No spatial setting defined")

    print("\nAgents:")
    if scenario.agents:
        for agent in scenario.agents:
            print(f"  - {agent.get('name', 'Unnamed')}: {agent.get('role', 'No role')}")
    else:
        print("  No agents defined")

    print("\nEvents:")
    if scenario.events:
        for i, event in enumerate(scenario.events):
            print(f"  {i + 1}. {event.get('description', 'No description')}")
    else:
        print("  No events defined")

    print("\nValidation Scores:")
    if hasattr(scenario, "validation_scores") and scenario.validation_scores:
        for dimension, score in scenario.validation_scores.items():
            print(f"  - {dimension}: {score:.2f}")
    else:
        print("  No validation scores available")


if __name__ == "__main__":
    asyncio.run(run_eft_example())
