#!/usr/bin/env python3
"""
Check for legacy bootstrap references in the codebase.
"""

import subprocess
import sys

def check_legacy_references():
    """Check for legacy bootstrap references."""
    print("🔍 Checking for legacy bootstrap references...")
    
    # Check for SystemBootstrap references
    try:
        result1 = subprocess.run([
            'grep', '-r', 'SystemBootstrap', 'person_suit/',
            '--include=*.py'
        ], capture_output=True, text=True, cwd='.')
        
        result2 = subprocess.run([
            'grep', '-r', 'ActorSystemBootstrap', 'person_suit/',
            '--include=*.py'
        ], capture_output=True, text=True, cwd='.')
        
        result3 = subprocess.run([
            'grep', '-r', 'from.*bootstrap import', 'person_suit/',
            '--include=*.py'
        ], capture_output=True, text=True, cwd='.')
        
        all_results = []
        
        if result1.returncode == 0 and result1.stdout.strip():
            print("\n❌ Found SystemBootstrap references:")
            lines = result1.stdout.strip().split('\n')
            for line in lines:
                if 'canonical_bootstrap' not in line.lower():
                    print(f"  - {line}")
                    all_results.append(line)
        
        if result2.returncode == 0 and result2.stdout.strip():
            print("\n❌ Found ActorSystemBootstrap references:")
            lines = result2.stdout.strip().split('\n')
            for line in lines:
                print(f"  - {line}")
                all_results.append(line)
        
        if result3.returncode == 0 and result3.stdout.strip():
            print("\n❌ Found legacy bootstrap imports:")
            lines = result3.stdout.strip().split('\n')
            for line in lines:
                if 'canonical_bootstrap' not in line.lower() and '_deprecated' not in line:
                    print(f"  - {line}")
                    all_results.append(line)
        
        if not all_results:
            print("\n✅ No legacy bootstrap references found!")
            return True
        else:
            print(f"\n❌ Found {len(all_results)} legacy references that need cleanup")
            return False
            
    except Exception as e:
        print(f"Error checking references: {e}")
        return False

if __name__ == "__main__":
    success = check_legacy_references()
    sys.exit(0 if success else 1)
